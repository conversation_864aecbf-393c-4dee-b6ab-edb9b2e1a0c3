const fs = require('fs');
const path = require('path');

function generateReportsIndex() {
  const reportsDir = path.join(__dirname, '../reports');

  // Ensure reports directory exists
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  const htmlContent = generateIndexHTML();
  fs.writeFileSync(path.join(reportsDir, 'index.html'), htmlContent);

  console.log('✅ Reports index.html generated successfully!');
}

function generateIndexHTML() {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elder Wand Project Reports Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 2rem;
        }

        .section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }

        .section h2 {
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section h3 {
            color: #6b7280;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .report-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .report-card {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .report-card h4 {
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .report-card p {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status.available {
            background: #d1fae5;
            color: #065f46;
        }

        .status.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status.loading {
            background: #dbeafe;
            color: #1e40af;
        }

        .icon {
            width: 24px;
            height: 24px;
            display: inline-block;
        }

        .footer {
            background: #f9fafb;
            padding: 1.5rem 2rem;
            text-align: center;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
        }

        .timestamp {
            font-size: 0.9rem;
            color: #9ca3af;
            margin-top: 0.5rem;
        }

        .refresh-btn {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .refresh-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <button class="refresh-btn" onclick="refreshReports()">
        <span id="refresh-icon">🔄</span>
        Refresh Reports
    </button>

    <div class="container">
        <div class="header">
            <h1>📊 Elder Wand Project Reports Dashboard</h1>
            <p>Comprehensive analysis and insights for the Elder Wand project</p>
        </div>

        <div class="content">
            <!-- Linting Reports Section -->
            <div class="section">
                <h2>
                    <span class="icon">🔍</span>
                    Code Quality Reports
                </h2>
                <p>Static analysis reports for code quality, linting issues, and best practices compliance.</p>
                
                <div class="report-grid">
                    <a href="linting/eslint-report.html" class="report-card" id="eslint-card">
                        <h4>ESLint Analysis Report</h4>
                        <p>Detailed analysis of code quality issues, warnings, and errors across the codebase.</p>
                        <span class="status loading" id="eslint-status">
                            <span class="loading-spinner"></span>
                            Checking...
                        </span>
                    </a>
                </div>
            </div>

            <!-- Testing Reports Section -->
            <div class="section">
                <h2>
                    <span class="icon">🧪</span>
                    Testing Reports
                </h2>
                <p>Test coverage reports and analysis for comprehensive code testing.</p>
                
                <div class="report-grid">
                    <a href="coverage/test-report.html" class="report-card" id="test-card">
                        <h4>Test Coverage Report</h4>
                        <p>Comprehensive test coverage analysis with detailed metrics and insights.</p>
                        <span class="status loading" id="test-status">
                            <span class="loading-spinner"></span>
                            Checking...
                        </span>
                    </a>
                    <a href="coverage/lcov-report/index.html" class="report-card" id="lcov-card">
                        <h4>Detailed Coverage Report</h4>
                        <p>Line-by-line coverage analysis with interactive navigation.</p>
                        <span class="status loading" id="lcov-status">
                            <span class="loading-spinner"></span>
                            Checking...
                        </span>
                    </a>
                </div>
            </div>

            <!-- Performance Reports Section -->
            <div class="section">
                <h2>
                    <span class="icon">⚡</span>
                    Performance Reports
                </h2>
                <p>Performance analysis and optimization insights for application performance.</p>
                
                <div class="report-grid">
                    <a href="performance/lighthouse-report.html" class="report-card" id="lighthouse-card">
                        <h4>Lighthouse Performance Audit</h4>
                        <p>Comprehensive performance, accessibility, and best practices analysis.</p>
                        <span class="status loading" id="lighthouse-status">
                            <span class="loading-spinner"></span>
                            Checking...
                        </span>
                    </a>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Generated by Elder Wand Project Reports System</p>
            <div class="timestamp">
                Last updated: <span id="timestamp"></span>
            </div>
        </div>
    </div>

    <script>
        // Update timestamp
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
        
        // Check report availability
        async function checkReportAvailability() {
            const reports = [
                { 
                    url: 'linting/eslint-report.html', 
                    element: document.getElementById('eslint-status'),
                    card: document.getElementById('eslint-card')
                },
                { 
                    url: 'coverage/test-report.html', 
                    element: document.getElementById('test-status'),
                    card: document.getElementById('test-card')
                },
                { 
                    url: 'coverage/lcov-report/index.html', 
                    element: document.getElementById('lcov-status'),
                    card: document.getElementById('lcov-card')
                },
                { 
                    url: 'performance/lighthouse-report.html', 
                    element: document.getElementById('lighthouse-status'),
                    card: document.getElementById('lighthouse-card')
                }
            ];

            for (const report of reports) {
                try {
                    const response = await fetch(report.url, { method: 'HEAD' });
                    if (response.ok) {
                        report.element.innerHTML = 'Available';
                        report.element.className = 'status available';
                        report.card.style.opacity = '1';
                    } else {
                        report.element.innerHTML = 'Not Found';
                        report.element.className = 'status error';
                        report.card.style.opacity = '0.6';
                    }
                } catch (error) {
                    report.element.innerHTML = 'Error';
                    report.element.className = 'status error';
                    report.card.style.opacity = '0.6';
                }
            }
        }

        // Refresh reports function
        async function refreshReports() {
            const refreshIcon = document.getElementById('refresh-icon');
            const originalText = refreshIcon.textContent;
            
            // Show loading state
            refreshIcon.textContent = '⏳';
            refreshIcon.style.animation = 'spin 1s linear infinite';
            
            // Reset all statuses to loading
            const statusElements = document.querySelectorAll('.status');
            statusElements.forEach(el => {
                el.innerHTML = '<span class="loading-spinner"></span>Checking...';
                el.className = 'status loading';
            });
            
            // Check availability
            await checkReportAvailability();
            
            // Update timestamp
            document.getElementById('timestamp').textContent = new Date().toLocaleString();
            
            // Reset refresh button
            refreshIcon.textContent = originalText;
            refreshIcon.style.animation = '';
        }

        // Check availability on page load
        checkReportAvailability();
        
        // Auto-refresh every 30 seconds
        setInterval(checkReportAvailability, 30000);
    </script>
</body>
</html>`;
}

// Run the index generation
generateReportsIndex();
