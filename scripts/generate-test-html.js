const fs = require('fs');
const path = require('path');

function generateTestHTMLReport() {
  const coverageDir = path.join(__dirname, '../coverage');
  const reportsDir = path.join(__dirname, '../reports/coverage');

  // Check if coverage directory exists
  if (!fs.existsSync(coverageDir)) {
    console.log(
      '⚠️  No coverage directory found. Run tests with coverage first.'
    );
    return;
  }

  // Copy coverage files to reports directory
  if (fs.existsSync(path.join(coverageDir, 'lcov-report'))) {
    const lcovReportDir = path.join(coverageDir, 'lcov-report');
    const targetDir = path.join(reportsDir, 'lcov-report');

    // Create target directory
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }

    // Copy coverage files
    copyDirectory(lcovReportDir, targetDir);
  }

  // Generate summary HTML
  const htmlContent = generateTestSummaryHTML();
  fs.writeFileSync(path.join(reportsDir, 'test-report.html'), htmlContent);

  console.log('✅ Test report generated successfully!');
}

function copyDirectory(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      copyDirectory(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

function generateTestSummaryHTML() {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome Project - Test Coverage Report</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }
        .stat-card .value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-card .label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .coverage-section {
            margin: 30px 0;
        }
        .coverage-section h2 {
            color: #333;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .coverage-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .coverage-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .coverage-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .coverage-item .percentage {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }
        .actions {
            margin-top: 30px;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .info-box h4 {
            margin: 0 0 10px 0;
            color: #0056b3;
        }
        .info-box p {
            margin: 0;
            color: #333;
        }
        .timestamp {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Coverage Report</h1>
            <p>Chrome Multi-Project Application Suite</p>
        </div>

        <div class="info-box">
            <h4>📋 Report Information</h4>
            <p>This report contains test coverage information for all projects in the Chrome application suite. 
            Coverage data is generated using Karma and Istanbul for comprehensive code analysis.</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>📊 Overall Coverage</h3>
                <div class="value">--%</div>
                <div class="label">Code Coverage</div>
            </div>
            <div class="stat-card">
                <h3>✅ Tests Passed</h3>
                <div class="value">--</div>
                <div class="label">Successful Tests</div>
            </div>
            <div class="stat-card">
                <h3>❌ Tests Failed</h3>
                <div class="value">--</div>
                <div class="label">Failed Tests</div>
            </div>
            <div class="stat-card">
                <h3>📁 Files Tested</h3>
                <div class="value">--</div>
                <div class="label">Covered Files</div>
            </div>
        </div>

        <div class="coverage-section">
            <h2>📈 Project Coverage Breakdown</h2>
            <div class="coverage-grid">
                <div class="coverage-item">
                    <h4>Marketing</h4>
                    <div class="percentage">--%</div>
                    <p>Frontend marketing application</p>
                </div>
                <div class="coverage-item">
                    <h4>Launchpad</h4>
                    <div class="percentage">--%</div>
                    <p>Main application launcher</p>
                </div>
                <div class="coverage-item">
                    <h4>Console</h4>
                    <div class="percentage">--%</div>
                    <p>Administrative console</p>
                </div>
                <div class="coverage-item">
                    <h4>Experience Studio</h4>
                    <div class="percentage">--%</div>
                    <p>User experience management</p>
                </div>
                <div class="coverage-item">
                    <h4>Product Studio</h4>
                    <div class="percentage">--%</div>
                    <p>Product management interface</p>
                </div>
                <div class="coverage-item">
                    <h4>Shared Library</h4>
                    <div class="percentage">--%</div>
                    <p>Common components & services</p>
                </div>
            </div>
        </div>

        <div class="actions">
            <a href="../index.html" class="btn btn-secondary">← Back to Dashboard</a>
            <a href="lcov-report/index.html" class="btn">📊 Detailed Coverage</a>
            <a href="../../" class="btn btn-secondary">🏠 Project Root</a>
        </div>

        <div class="timestamp">
            <p>Report generated on: ${new Date().toLocaleString()}</p>
            <p>Generated by Chrome Project Test Suite</p>
        </div>
    </div>

    <script>
        // Add interactive features here if needed
        console.log('Test Coverage Report loaded successfully!');
    </script>
</body>
</html>`;
}

// Run the report generation
generateTestHTMLReport();
