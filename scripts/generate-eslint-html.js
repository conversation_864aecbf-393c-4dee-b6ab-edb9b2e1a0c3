const fs = require('fs');
const path = require('path');

// Read the ESLint JSON report
const eslintReportPath = path.join(
  __dirname,
  '../reports/linting/eslint-report.json'
);
const outputPath = path.join(
  __dirname,
  '../reports/linting/eslint-report.html'
);

try {
  const eslintData = JSON.parse(fs.readFileSync(eslintReportPath, 'utf8'));

  // Generate HTML report
  const html = generateHTMLReport(eslintData);

  // Write HTML file
  fs.writeFileSync(outputPath, html);

  console.log('✅ ESLint HTML report generated successfully!');
  console.log(`📄 Report saved to: ${outputPath}`);
} catch (error) {
  console.error('❌ Error generating ESLint HTML report:', error.message);
  process.exit(1);
}

function generateHTMLReport(data) {
  const totalIssues = data.length;
  const errors = data.filter(issue => issue.severity === 2).length;
  const warnings = data.filter(issue => issue.severity === 1).length;

  const files = [...new Set(data.map(issue => issue.filePath))];
  const rules = [...new Set(data.map(issue => issue.ruleId))];

  const severityColors = {
    2: '#dc3545', // Error - Red
    1: '#ffc107', // Warning - Yellow
  };

  const severityLabels = {
    2: 'Error',
    1: 'Warning',
  };

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESLint Report - Chrome Project</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .total-issues .stat-number { color: #dc3545; }
        .errors .stat-number { color: #dc3545; }
        .warnings .stat-number { color: #ffc107; }
        .files .stat-number { color: #28a745; }
        .rules .stat-number { color: #17a2b8; }
        
        .summary {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .summary h2 {
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .issues-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .file-issues {
            border-bottom: 1px solid #eee;
        }
        
        .file-header {
            background: #f8f9fa;
            padding: 15px 20px;
            font-weight: bold;
            color: #495057;
            border-left: 4px solid #667eea;
        }
        
        .issue {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }
        
        .issue:last-child {
            border-bottom: none;
        }
        
        .issue-severity {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            min-width: 60px;
            text-align: center;
        }
        
        .issue-content {
            flex: 1;
        }
        
        .issue-rule {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .issue-message {
            color: #666;
            margin-bottom: 5px;
        }
        
        .issue-location {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #999;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
        }
        
        .no-issues {
            text-align: center;
            padding: 40px;
            color: #28a745;
            font-size: 1.2em;
        }
        
        .filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .filter-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s;
        }
        
        .filter-btn.active {
            background: #667eea;
            color: white;
        }
        
        .filter-btn:not(.active) {
            background: #f8f9fa;
            color: #666;
        }
        
        .filter-btn:hover:not(.active) {
            background: #e9ecef;
        }
        
        .hidden {
            display: none;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .issue {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 ESLint Code Quality Report</h1>
            <p>Chrome Project - Comprehensive Code Analysis</p>
        </div>
        
        <div class="stats">
            <div class="stat-card total-issues">
                <div class="stat-number">${totalIssues}</div>
                <div class="stat-label">Total Issues</div>
            </div>
            <div class="stat-card errors">
                <div class="stat-number">${errors}</div>
                <div class="stat-label">Errors</div>
            </div>
            <div class="stat-card warnings">
                <div class="stat-number">${warnings}</div>
                <div class="stat-label">Warnings</div>
            </div>
            <div class="stat-card files">
                <div class="stat-number">${files.length}</div>
                <div class="stat-label">Files Analyzed</div>
            </div>
            <div class="stat-card rules">
                <div class="stat-number">${rules.length}</div>
                <div class="stat-label">Rules Violated</div>
            </div>
        </div>
        
        <div class="filters">
            <h3>Filters:</h3>
            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all">All Issues (${totalIssues})</button>
                <button class="filter-btn" data-filter="error">Errors Only (${errors})</button>
                <button class="filter-btn" data-filter="warning">Warnings Only (${warnings})</button>
            </div>
        </div>
        
        <div class="summary">
            <h2>📋 Summary</h2>
            <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
            <p><strong>Files with issues:</strong> ${files.length}</p>
            <p><strong>Most common rule violations:</strong></p>
            <ul>
                ${getTopRules(data)
                  .map(
                    rule =>
                      `<li><strong>${rule.name}</strong>: ${rule.count} violations</li>`
                  )
                  .join('')}
            </ul>
        </div>
        
        <div class="issues-container">
            ${
              totalIssues === 0
                ? '<div class="no-issues">🎉 No ESLint issues found! Your code is clean and follows all rules.</div>'
                : generateIssuesHTML(
                    data,
                    files,
                    severityColors,
                    severityLabels
                  )
            }
        </div>
    </div>
    
    <script>
        // Filter functionality
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const filter = btn.dataset.filter;
                
                // Update active button
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // Filter issues
                document.querySelectorAll('.issue').forEach(issue => {
                    const severity = issue.dataset.severity;
                    if (filter === 'all' || 
                        (filter === 'error' && severity === '2') ||
                        (filter === 'warning' && severity === '1')) {
                        issue.classList.remove('hidden');
                    } else {
                        issue.classList.add('hidden');
                    }
                });
            });
        });
    </script>
</body>
</html>`;
}

function generateIssuesHTML(data, files, severityColors, severityLabels) {
  let html = '';

  files.forEach(file => {
    const fileIssues = data.filter(issue => issue.filePath === file);
    if (fileIssues.length === 0) return;

    html += `
      <div class="file-issues">
        <div class="file-header">📄 ${file.replace(process.cwd(), '')}</div>
    `;

    fileIssues.forEach(issue => {
      html += `
        <div class="issue" data-severity="${issue.severity}">
          <div class="issue-severity" style="background-color: ${severityColors[issue.severity]}; color: white;">
            ${severityLabels[issue.severity]}
          </div>
          <div class="issue-content">
            <div class="issue-rule">${issue.ruleId || 'Unknown Rule'}</div>
            <div class="issue-message">${issue.message}</div>
            <div class="issue-location">Line ${issue.line}, Column ${issue.column}</div>
          </div>
        </div>
      `;
    });

    html += '</div>';
  });

  return html;
}

function getTopRules(data) {
  const ruleCounts = {};
  data.forEach(issue => {
    const rule = issue.ruleId || 'Unknown Rule';
    ruleCounts[rule] = (ruleCounts[rule] || 0) + 1;
  });

  return Object.entries(ruleCounts)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
}
