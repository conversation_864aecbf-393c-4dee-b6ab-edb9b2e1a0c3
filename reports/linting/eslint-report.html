<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ESLint Report - Chrome Project</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f8f9fa;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
      }

      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .stat-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-align: center;
      }

      .stat-number {
        font-size: 2em;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .stat-label {
        color: #666;
        font-size: 0.9em;
      }

      .total-issues .stat-number {
        color: #dc3545;
      }
      .errors .stat-number {
        color: #dc3545;
      }
      .warnings .stat-number {
        color: #ffc107;
      }
      .files .stat-number {
        color: #28a745;
      }
      .rules .stat-number {
        color: #17a2b8;
      }

      .summary {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
      }

      .summary h2 {
        color: #333;
        margin-bottom: 15px;
        border-bottom: 2px solid #667eea;
        padding-bottom: 10px;
      }

      .issues-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .file-issues {
        border-bottom: 1px solid #eee;
      }

      .file-header {
        background: #f8f9fa;
        padding: 15px 20px;
        font-weight: bold;
        color: #495057;
        border-left: 4px solid #667eea;
      }

      .issue {
        padding: 15px 20px;
        border-bottom: 1px solid #f1f3f4;
        display: flex;
        align-items: flex-start;
        gap: 15px;
      }

      .issue:last-child {
        border-bottom: none;
      }

      .issue-severity {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8em;
        font-weight: bold;
        min-width: 60px;
        text-align: center;
      }

      .issue-content {
        flex: 1;
      }

      .issue-rule {
        font-weight: bold;
        color: #495057;
        margin-bottom: 5px;
      }

      .issue-message {
        color: #666;
        margin-bottom: 5px;
      }

      .issue-location {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.9em;
        color: #999;
        background: #f8f9fa;
        padding: 2px 6px;
        border-radius: 3px;
      }

      .no-issues {
        text-align: center;
        padding: 40px;
        color: #28a745;
        font-size: 1.2em;
      }

      .filters {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }

      .filter-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }

      .filter-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9em;
        transition: all 0.3s;
      }

      .filter-btn.active {
        background: #667eea;
        color: white;
      }

      .filter-btn:not(.active) {
        background: #f8f9fa;
        color: #666;
      }

      .filter-btn:hover:not(.active) {
        background: #e9ecef;
      }

      .hidden {
        display: none;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .header h1 {
          font-size: 2em;
        }

        .stats {
          grid-template-columns: repeat(2, 1fr);
        }

        .issue {
          flex-direction: column;
          gap: 10px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🔍 ESLint Code Quality Report</h1>
        <p>Chrome Project - Comprehensive Code Analysis</p>
      </div>

      <div class="stats">
        <div class="stat-card total-issues">
          <div class="stat-number">117</div>
          <div class="stat-label">Total Issues</div>
        </div>
        <div class="stat-card errors">
          <div class="stat-number">0</div>
          <div class="stat-label">Errors</div>
        </div>
        <div class="stat-card warnings">
          <div class="stat-number">0</div>
          <div class="stat-label">Warnings</div>
        </div>
        <div class="stat-card files">
          <div class="stat-number">117</div>
          <div class="stat-label">Files Analyzed</div>
        </div>
        <div class="stat-card rules">
          <div class="stat-number">1</div>
          <div class="stat-label">Rules Violated</div>
        </div>
      </div>

      <div class="filters">
        <h3>Filters:</h3>
        <div class="filter-buttons">
          <button class="filter-btn active" data-filter="all">
            All Issues (117)
          </button>
          <button class="filter-btn" data-filter="error">
            Errors Only (0)
          </button>
          <button class="filter-btn" data-filter="warning">
            Warnings Only (0)
          </button>
        </div>
      </div>

      <div class="summary">
        <h2>📋 Summary</h2>
        <p><strong>Generated:</strong> 8/22/2025, 9:37:03 PM</p>
        <p><strong>Files with issues:</strong> 117</p>
        <p><strong>Most common rule violations:</strong></p>
        <ul>
          <li><strong>Unknown Rule</strong>: 117 violations</li>
        </ul>
      </div>

      <div class="issues-container">
        <div class="file-issues">
          <div class="file-header">📄 /.eslintrc.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/console/block-navigation.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/console/environment.ts.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /coverage/console/index.html</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /coverage/console/prettify.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /coverage/console/sorter.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/experience-studio/block-navigation.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/experience-studio/index.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/experience-studio/prettify.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/experience-studio/sorter.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/experience-studio/src/environments/environment.ts.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/experience-studio/src/environments/index.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/experience-studio/src/index.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/experience-studio/src/polyfills.ts.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/launchpad/block-navigation.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/launchpad/environment.ts.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /coverage/launchpad/index.html</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /coverage/launchpad/prettify.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /coverage/launchpad/sorter.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/marketing/block-navigation.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/marketing/environment.ts.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /coverage/marketing/index.html</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /coverage/marketing/prettify.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /coverage/marketing/sorter.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/product-studio/block-navigation.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /coverage/product-studio/environment.ts.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /coverage/product-studio/index.html</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /coverage/product-studio/prettify.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /coverage/product-studio/sorter.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /env-config-template.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /karma.conf.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/console/public/localEnvSetup.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/console/src/app/app.component.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/console/src/app/app.component.spec.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/console/src/app/app.config.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/console/src/app/app.routes.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/console/src/bootstrap.ts</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/console/src/environments/environment.prod.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/console/src/environments/environment.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/console/src/index.html</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/console/src/main.ts</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/console/webpack.config.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/console/webpack.prod.config.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/public/localEnvSetup.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/src/app/app.component.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/src/app/app.component.spec.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/src/app/app.config.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/src/app/app.routes.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/src/bootstrap.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/src/environments/environment.prod.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/src/environments/environment.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/src/index.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/src/main.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/src/polyfills.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/webpack.config.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/experience-studio/webpack.prod.config.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/launchpad/public/localEnvSetup.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/launchpad/src/app/app.component.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/launchpad/src/app/app.component.spec.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/launchpad/src/app/app.config.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/launchpad/src/app/app.routes.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/launchpad/src/bootstrap.ts</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/launchpad/src/environments/environment.prod.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/launchpad/src/environments/environment.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/launchpad/src/index.html</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/launchpad/src/main.ts</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/launchpad/webpack.config.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/launchpad/webpack.prod.config.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/marketing/public/localEnvSetup.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/marketing/src/app/app.component.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/marketing/src/app/app.component.spec.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/marketing/src/app/app.config.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/marketing/src/app/app.routes.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/marketing/src/bootstrap.ts</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/marketing/src/environments/environment.prod.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/marketing/src/environments/environment.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/marketing/src/index.html</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/marketing/src/main.ts</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/marketing/src/polyfills.ts</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/marketing/src/test.ts</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/marketing/webpack.config.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/marketing/webpack.prod.config.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/product-studio/public/localEnvSetup.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/product-studio/src/app/app.component.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/product-studio/src/app/app.component.spec.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/product-studio/src/app/app.config.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/product-studio/src/app/app.routes.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/product-studio/src/bootstrap.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/product-studio/src/environments/environment.prod.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/product-studio/src/environments/environment.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/product-studio/src/index.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/product-studio/src/main.ts</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/product-studio/webpack.config.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/product-studio/webpack.prod.config.js
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/shared/auth/auth.module.ts</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/auth/components/callback/callback.component.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄
            /projects/shared/auth/components/callback/callback.component.spec.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/auth/components/callback/callback.component.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/auth/components/login/login.component.html
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/auth/components/login/login.component.spec.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/auth/guards/auth.guard.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/shared/auth/index.ts</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/auth/interceptors/auth.interceptor.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/auth/interfaces/auth-config.interface.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/auth/services/auth-state.service.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/auth/services/auth-token.service.spec.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/auth/services/auth-token.service.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/auth/services/auth.service.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/auth/services/token-storage.service.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/environments/environment.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /projects/shared/index.ts</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/services/centralized-redirect.service.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/services/environment.service.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">
            📄 /projects/shared/services/user-details.service.ts
          </div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /scripts/generate-eslint-html.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /scripts/generate-reports-index.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
        <div class="file-issues">
          <div class="file-header">📄 /scripts/generate-test-html.js</div>

          <div class="issue" data-severity="undefined">
            <div
              class="issue-severity"
              style="background-color: undefined; color: white"
            >
              undefined
            </div>
            <div class="issue-content">
              <div class="issue-rule">Unknown Rule</div>
              <div class="issue-message">undefined</div>
              <div class="issue-location">Line undefined, Column undefined</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Filter functionality
      document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', () => {
          const filter = btn.dataset.filter;

          // Update active button
          document
            .querySelectorAll('.filter-btn')
            .forEach(b => b.classList.remove('active'));
          btn.classList.add('active');

          // Filter issues
          document.querySelectorAll('.issue').forEach(issue => {
            const severity = issue.dataset.severity;
            if (
              filter === 'all' ||
              (filter === 'error' && severity === '2') ||
              (filter === 'warning' && severity === '1')
            ) {
              issue.classList.remove('hidden');
            } else {
              issue.classList.add('hidden');
            }
          });
        });
      });
    </script>
  </body>
</html>
