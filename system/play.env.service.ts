import { Injectable } from '@angular/core';

export interface EnvironmentConfig {
  production: boolean;
  apiUrl: string;
  appName: string;
  version: string;
  debug: boolean;
}

@Injectable({
  providedIn: 'root',
})
export class PlayEnvService {
  private config: EnvironmentConfig;

  constructor() {
    this.config = {
      production: false,
      apiUrl: '',
      appName: 'Play+ Angular App',
      version: '1.0.0',
      debug: false,
    };
  }

  setConfig(config: Partial<EnvironmentConfig>): void {
    this.config = { ...this.config, ...config };
  }

  get(key: keyof EnvironmentConfig): any {
    return this.config[key];
  }

  isProduction(): boolean {
    return this.config.production;
  }

  isDevelopment(): boolean {
    return !this.config.production;
  }

  isDebug(): boolean {
    return this.config.debug;
  }

  getApiUrl(): string {
    return this.config.apiUrl;
  }

  getAppName(): string {
    return this.config.appName;
  }

  getVersion(): string {
    return this.config.version;
  }
}
