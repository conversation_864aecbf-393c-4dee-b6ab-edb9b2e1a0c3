/**
 * Play+ Environment Helper: playenv
 * Type-safe, secure access to environment variables with fallback and coercion.
 */

// This will work for both browser and Node (Angular/React/SSR)
const ENV: Record<string, string | undefined> =
  typeof process !== 'undefined' && process.env
    ? process.env
    : (window as any)?.__env || {};

function coerce<T>(value: string | undefined, defaultValue: T): T {
  if (value === undefined) return defaultValue;
  if (typeof defaultValue === 'boolean') {
    return (value === 'true' || value === '1') as any;
  }
  if (typeof defaultValue === 'number') {
    const n = parseFloat(value);
    return (isNaN(n) ? defaultValue : n) as any;
  }
  return value as any;
}

export const playenv = {
  /**
   * Get an environment variable with type coercion and fallback.
   * @param key The environment variable name
   * @param defaultValue The fallback value if not set
   */
  get<T = string>(key: string, defaultValue?: T): T {
    const value = ENV[key];
    if (value === undefined) {
      if (defaultValue !== undefined) return defaultValue;
      throw new Error(`Missing required environment variable: ${key}`);
    }
    if (defaultValue !== undefined) {
      return coerce(value, defaultValue);
    }
    return value as any;
  },

  /**
   * Validate that required environment variables are set.
   * Throws an error if any are missing.
   */
  validate(requiredKeys: string[]): void {
    const missing = requiredKeys.filter(key => ENV[key] === undefined);
    if (missing.length > 0) {
      throw new Error(
        `Missing required environment variables: ${missing.join(', ')}`
      );
    }
  },

  /**
   * Expose all loaded environment variables (for debugging only)
   */
  all(): Record<string, string | undefined> {
    return { ...ENV };
  },
};
