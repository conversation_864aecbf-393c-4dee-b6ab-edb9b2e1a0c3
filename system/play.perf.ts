/**
 * Play+ Performance Helper: `playperf`
 *
 * A comprehensive performance management system that provides utilities for
 * deferring tasks and monitoring real-world performance metrics.
 */

export interface PerformanceMetric {
  name: string;
  value: number;
  id: string;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta?: number;
}

export interface MonitorOptions {
  onReport: (metric: PerformanceMetric) => void;
  enableWebVitals?: boolean;
  enableLongTasks?: boolean;
  enableBundleMonitoring?: boolean;
}

export interface DeferOptions {
  priority?: 'high' | 'normal' | 'low';
  timeout?: number;
}

export interface PerformanceConfig {
  lighthouse: {
    lcp: number;
    cls: number;
    inp: number;
    maxBundleSize: number;
  };
  enforce: {
    blockBuildOnFail: boolean;
    enableBundleDiff: boolean;
  };
  monitor: {
    webVitals: boolean;
    sentry: boolean;
  };
  assist: {
    highlightHeavyComponents: boolean;
  };
}

// Default configuration
const DEFAULT_CONFIG: PerformanceConfig = {
  lighthouse: {
    lcp: 2500,
    cls: 0.1,
    inp: 200,
    maxBundleSize: 250,
  },
  enforce: {
    blockBuildOnFail: true,
    enableBundleDiff: true,
  },
  monitor: {
    webVitals: true,
    sentry: true,
  },
  assist: {
    highlightHeavyComponents: true,
  },
};

class PlayPerformance {
  private config: PerformanceConfig;
  private isMonitoring = false;
  private observers: PerformanceObserver[] = [];
  private longTaskThreshold = 50; // 50ms threshold for long tasks

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Schedules a function to run during browser idle time, preventing it from blocking critical rendering.
   */
  defer(callback: () => void, options: DeferOptions = {}): void {
    const { priority = 'normal', timeout } = options;

    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      // In SSR, execute immediately
      try {
        callback();
      } catch (error) {
        console.warn('Error in deferred callback:', error);
      }
      return;
    }

    if ('requestIdleCallback' in window) {
      // Use requestIdleCallback for modern browsers
      const idleOptions: IdleRequestOptions = {
        timeout: timeout || 1000,
      };

      if (priority === 'high') {
        // For high priority, use a shorter timeout
        idleOptions.timeout = 100;
      }

      window.requestIdleCallback(() => {
        try {
          callback();
        } catch (error) {
          console.warn('Error in deferred callback:', error);
        }
      }, idleOptions);
    } else {
      // Fallback for older browsers
      setTimeout(
        () => {
          try {
            callback();
          } catch (error) {
            console.warn('Error in deferred callback:', error);
          }
        },
        priority === 'high' ? 0 : 100
      );
    }
  }

  /**
   * Initializes real-user monitoring of Core Web Vitals and performance metrics.
   */
  monitor(options: MonitorOptions): void {
    if (this.isMonitoring) {
      console.warn('Performance monitoring is already active');
      return;
    }

    this.isMonitoring = true;

    // Monitor Core Web Vitals
    if (options.enableWebVitals !== false) {
      this.monitorWebVitals(options.onReport);
    }

    // Monitor Long Tasks
    if (options.enableLongTasks !== false) {
      this.monitorLongTasks(options.onReport);
    }

    // Monitor Bundle Size (if available)
    if (options.enableBundleMonitoring !== false) {
      this.monitorBundleSize(options.onReport);
    }

    // Monitor First Input Delay
    this.monitorFirstInputDelay(options.onReport);

    // Monitor Largest Contentful Paint
    this.monitorLCP(options.onReport);

    // Monitor Cumulative Layout Shift
    this.monitorCLS(options.onReport);
  }

  /**
   * Stops performance monitoring and cleans up observers.
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }

  /**
   * Measures the performance of a function execution.
   */
  measure<T>(name: string, fn: () => T): T {
    const start = performance.now();
    try {
      const result = fn();
      const duration = performance.now() - start;

      this.reportMetric({
        name: `measure:${name}`,
        value: duration,
        id: `${name}-${Date.now()}`,
        rating: this.getRating(duration, 16, 50), // 16ms for 60fps, 50ms for long task
      });

      return result;
    } catch (error) {
      const duration = performance.now() - start;
      console.warn(`Error in measured function ${name}:`, error);

      this.reportMetric({
        name: `measure:${name}:error`,
        value: duration,
        id: `${name}-error-${Date.now()}`,
        rating: 'poor',
      });

      throw error;
    }
  }

  /**
   * Measures async function performance.
   */
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    try {
      const result = await fn();
      const duration = performance.now() - start;

      this.reportMetric({
        name: `measure:${name}`,
        value: duration,
        id: `${name}-${Date.now()}`,
        rating: this.getRating(duration, 16, 50),
      });

      return result;
    } catch (error) {
      const duration = performance.now() - start;
      console.warn(`Error in measured async function ${name}:`, error);

      this.reportMetric({
        name: `measure:${name}:error`,
        value: duration,
        id: `${name}-error-${Date.now()}`,
        rating: 'poor',
      });

      throw error;
    }
  }

  /**
   * Highlights heavy components in development mode.
   */
  highlightHeavyComponent(componentName: string, renderTime: number): void {
    if (this.config.assist.highlightHeavyComponents && renderTime > 16) {
      console.warn(
        `🚨 Heavy Component Detected: ${componentName} took ${renderTime.toFixed(2)}ms to render. ` +
          `Consider optimizing with OnPush change detection or memoization.`
      );
    }
  }

  /**
   * Gets current performance configuration.
   */
  getConfig(): PerformanceConfig {
    return { ...this.config };
  }

  /**
   * Updates performance configuration.
   */
  updateConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Private methods

  private monitorWebVitals(
    onReport: (metric: PerformanceMetric) => void
  ): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    try {
      const observer = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          const metric: PerformanceMetric = {
            name: entry.name,
            value: (entry as any).value || 0,
            id: (entry as any).id || `${entry.name}-${Date.now()}`,
            rating: this.getWebVitalsRating(
              entry.name,
              (entry as any).value || 0
            ),
          };
          onReport(metric);
        }
      });

      observer.observe({ entryTypes: ['navigation', 'resource', 'paint'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Failed to initialize Web Vitals monitoring:', error);
    }
  }

  private monitorLongTasks(
    onReport: (metric: PerformanceMetric) => void
  ): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    try {
      const observer = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          const longTaskEntry = entry as PerformanceEntry & {
            duration: number;
          };
          if (longTaskEntry.duration > this.longTaskThreshold) {
            const metric: PerformanceMetric = {
              name: 'long-task',
              value: longTaskEntry.duration,
              id: `${longTaskEntry.name}-${Date.now()}`,
              rating: 'poor',
            };
            onReport(metric);
          }
        }
      });

      observer.observe({ entryTypes: ['longtask'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Failed to initialize long task monitoring:', error);
    }
  }

  private monitorFirstInputDelay(
    onReport: (metric: PerformanceMetric) => void
  ): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    try {
      const observer = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          const fidEntry = entry as PerformanceEntry & {
            processingStart: number;
            startTime: number;
          };
          const fid = fidEntry.processingStart - fidEntry.startTime;

          const metric: PerformanceMetric = {
            name: 'first-input-delay',
            value: fid,
            id: `fid-${Date.now()}`,
            rating: this.getRating(fid, 100, 300),
          };
          onReport(metric);
        }
      });

      observer.observe({ entryTypes: ['first-input'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Failed to initialize FID monitoring:', error);
    }
  }

  private monitorLCP(onReport: (metric: PerformanceMetric) => void): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    try {
      const observer = new PerformanceObserver(list => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];

        const metric: PerformanceMetric = {
          name: 'largest-contentful-paint',
          value: lastEntry.startTime,
          id: `lcp-${Date.now()}`,
          rating: this.getRating(lastEntry.startTime, 2500, 4000),
        };
        onReport(metric);
      });

      observer.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Failed to initialize LCP monitoring:', error);
    }
  }

  private monitorCLS(onReport: (metric: PerformanceMetric) => void): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    let clsValue = 0;

    try {
      const observer = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          const layoutShiftEntry = entry as PerformanceEntry & {
            value: number;
            hadRecentInput?: boolean;
          };
          if (!(layoutShiftEntry as any).hadRecentInput) {
            clsValue += layoutShiftEntry.value;

            const metric: PerformanceMetric = {
              name: 'cumulative-layout-shift',
              value: clsValue,
              id: `cls-${Date.now()}`,
              rating: this.getRating(clsValue, 0.1, 0.25),
            };
            onReport(metric);
          }
        }
      });

      observer.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Failed to initialize CLS monitoring:', error);
    }
  }

  private monitorBundleSize(
    onReport: (metric: PerformanceMetric) => void
  ): void {
    // Monitor bundle size through performance entries
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    try {
      const observer = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          const resourceEntry = entry as PerformanceEntry & {
            transferSize: number;
          };
          if (resourceEntry.transferSize) {
            const sizeKB = resourceEntry.transferSize / 1024;

            const metric: PerformanceMetric = {
              name: 'bundle-size',
              value: sizeKB,
              id: `bundle-${Date.now()}`,
              rating: this.getRating(sizeKB, 250, 500),
            };
            onReport(metric);
          }
        }
      });

      observer.observe({ entryTypes: ['resource'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Failed to initialize bundle size monitoring:', error);
    }
  }

  private getRating(
    value: number,
    good: number,
    poor: number
  ): 'good' | 'needs-improvement' | 'poor' {
    if (value <= good) return 'good';
    if (value <= poor) return 'needs-improvement';
    return 'poor';
  }

  private getWebVitalsRating(
    name: string,
    value: number
  ): 'good' | 'needs-improvement' | 'poor' {
    switch (name) {
      case 'largest-contentful-paint':
        return this.getRating(value, 2500, 4000);
      case 'first-input-delay':
        return this.getRating(value, 100, 300);
      case 'cumulative-layout-shift':
        return this.getRating(value, 0.1, 0.25);
      default:
        return 'good';
    }
  }

  private reportMetric(metric: PerformanceMetric): void {
    // In a real implementation, this would send to your analytics/logging service
    if (this.config.monitor.webVitals) {
      console.log('Performance Metric:', metric);
    }
  }
}

// Export singleton instance
export const playperf = new PlayPerformance();

// Export the class for testing or custom instances
export { PlayPerformance };
