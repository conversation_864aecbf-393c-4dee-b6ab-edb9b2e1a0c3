// System Services
export { apiProxy } from './api/api.proxy';
export { apiRoutes } from './api/api.routes';
export { ApiService, apiService } from './api/api.service';
export { PlayCacheService } from './cache/cache.service';
export { PlayErrorService } from './error/error.service';
export { PlayLogService } from './log/log.service';

// Play+ Logging System
export { playlog } from './play.log';

// Play+ Security System
export { playguard } from './play.security';

// Play+ Performance System
export { playperf, PlayPerformance } from './play.perf';

// Play+ Accessibility System
export { PlayA11y, playa11y } from './play.a11y';

// Play+ Feature Flag System
export { PlayFeature, playfeature } from './play.feature';
export type { FeatureConfig, FeatureFlag } from './play.feature';

// Play+ Error Handling System
export { PlayError, playerror } from './play.error';
export type {
  ErrorAction,
  ErrorConfig,
  ErrorContext,
  IPlayError,
} from './play.error';

// Re-export commonly used types
export type {
  ApiResponse,
  CacheRequestOptions,
  RequestOptions,
} from './api/api.proxy';
export type { A11yConfig } from './play.a11y';
export type {
  DeferOptions,
  MonitorOptions,
  PerformanceConfig,
  PerformanceMetric,
} from './play.perf';
