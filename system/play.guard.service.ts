import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';

export interface GuardConfig {
  enabled: boolean;
  authentication: {
    required: boolean;
    redirectTo: string;
    tokenKey: string;
  };
  authorization: {
    enabled: boolean;
    roleBased: boolean;
    permissionBased: boolean;
  };
  routes: {
    public: string[];
    protected: string[];
    admin: string[];
  };
}

@Injectable({
  providedIn: 'root',
})
export class PlayGuardService implements CanActivate {
  private config: GuardConfig;

  constructor(private router: Router) {
    this.config = {
      enabled: true,
      authentication: {
        required: true,
        redirectTo: '/login',
        tokenKey: 'auth_token',
      },
      authorization: {
        enabled: true,
        roleBased: true,
        permissionBased: false,
      },
      routes: {
        public: ['/login', '/register', '/forgot-password'],
        protected: ['/dashboard', '/profile', '/settings'],
        admin: ['/admin', '/users', '/analytics'],
      },
    };
  }

  setConfig(config: Partial<GuardConfig>): void {
    this.config = { ...this.config, ...config };
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    if (!this.config.enabled) return true;

    const url = state.url;

    // Check if route is public
    if (this.isPublicRoute(url)) {
      return true;
    }

    // Check authentication
    if (this.config.authentication.required && !this.isAuthenticated()) {
      this.redirectToLogin();
      return false;
    }

    // Check authorization
    if (this.config.authorization.enabled) {
      if (this.config.authorization.roleBased && !this.hasRequiredRole(route)) {
        this.redirectToUnauthorized();
        return false;
      }

      if (
        this.config.authorization.permissionBased &&
        !this.hasRequiredPermission(route)
      ) {
        this.redirectToUnauthorized();
        return false;
      }
    }

    return true;
  }

  isAuthenticated(): boolean {
    const token = this.getAuthToken();
    return !!token && !this.isTokenExpired(token);
  }

  isPublicRoute(url: string): boolean {
    return this.config.routes.public.some(route => url.startsWith(route));
  }

  isProtectedRoute(url: string): boolean {
    return this.config.routes.protected.some(route => url.startsWith(route));
  }

  isAdminRoute(url: string): boolean {
    return this.config.routes.admin.some(route => url.startsWith(route));
  }

  hasRequiredRole(route: ActivatedRouteSnapshot): boolean {
    const requiredRoles = route.data['roles'] as string[];
    if (!requiredRoles) return true;

    const userRoles = this.getUserRoles();
    return requiredRoles.some(role => userRoles.includes(role));
  }

  hasRequiredPermission(route: ActivatedRouteSnapshot): boolean {
    const requiredPermissions = route.data['permissions'] as string[];
    if (!requiredPermissions) return true;

    const userPermissions = this.getUserPermissions();
    return requiredPermissions.some(permission =>
      userPermissions.includes(permission)
    );
  }

  private getAuthToken(): string | null {
    return localStorage.getItem(this.config.authentication.tokenKey);
  }

  private isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }

  private getUserRoles(): string[] {
    const token = this.getAuthToken();
    if (!token) return [];

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.roles || [];
    } catch {
      return [];
    }
  }

  private getUserPermissions(): string[] {
    const token = this.getAuthToken();
    if (!token) return [];

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.permissions || [];
    } catch {
      return [];
    }
  }

  private redirectToLogin(): void {
    this.router.navigate([this.config.authentication.redirectTo]);
  }

  private redirectToUnauthorized(): void {
    this.router.navigate(['/unauthorized']);
  }

  // Utility methods
  setAuthToken(token: string): void {
    localStorage.setItem(this.config.authentication.tokenKey, token);
  }

  clearAuthToken(): void {
    localStorage.removeItem(this.config.authentication.tokenKey);
  }

  logout(): void {
    this.clearAuthToken();
    this.redirectToLogin();
  }
}
