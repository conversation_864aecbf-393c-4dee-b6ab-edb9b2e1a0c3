/**
 * Play+ Accessibility Helper: `playa11y`
 *
 * A comprehensive accessibility management system that provides utilities for
 * focus management, live announcements, reduced motion detection, and contrast checking.
 */

export interface A11yConfig {
  liveRegionPoliteness: 'polite' | 'assertive';
  focusRing: {
    color: string;
    offset: string;
    width: string;
  };
  announce: {
    defaultPoliteness: 'polite' | 'assertive';
    timeout: number;
  };
  contrast: {
    enabled: boolean;
    warningThreshold: number;
    errorThreshold: number;
  };
}

// Default configuration
const DEFAULT_CONFIG: A11yConfig = {
  liveRegionPoliteness: 'polite',
  focusRing: {
    color: '#E91E63',
    offset: '2px',
    width: '2px',
  },
  announce: {
    defaultPoliteness: 'polite',
    timeout: 5000,
  },
  contrast: {
    enabled: true,
    warningThreshold: 4.5,
    errorThreshold: 3.0,
  },
};

class PlayA11y {
  private config: A11yConfig;
  private liveRegion: HTMLElement | null = null;
  private focusHistory: HTMLElement[] = [];
  private isInitialized = false;

  constructor(config: Partial<A11yConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Initializes the accessibility helper.
   * Must be called before using other methods.
   */
  init(): void {
    if (this.isInitialized) {
      console.warn('PlayA11y is already initialized');
      return;
    }

    this.createLiveRegion();
    this.setupFocusTracking();
    this.isInitialized = true;
  }

  /**
   * Announces a message to screen readers.
   */
  announce(
    message: string,
    politeness: 'polite' | 'assertive' = this.config.announce.defaultPoliteness
  ): void {
    if (!this.isInitialized) {
      console.warn('PlayA11y not initialized. Call init() first.');
      return;
    }

    if (!this.liveRegion) {
      console.warn('Live region not available');
      return;
    }

    // Clear previous content
    this.liveRegion.textContent = '';

    // Set politeness
    this.liveRegion.setAttribute('aria-live', politeness);

    // Announce the message
    this.liveRegion.textContent = message;

    // Clear after timeout
    setTimeout(() => {
      if (this.liveRegion) {
        this.liveRegion.textContent = '';
      }
    }, this.config.announce.timeout);
  }

  /**
   * Safely moves focus to an element.
   */
  focus(element: HTMLElement | null | undefined): void {
    if (!element) {
      console.warn('Cannot focus null or undefined element');
      return;
    }

    // Store current focus for potential restoration
    const currentFocus = document.activeElement as HTMLElement;
    if (currentFocus) {
      this.focusHistory.push(currentFocus);
    }

    // Focus the element
    try {
      element.focus();

      // Ensure focus is visible
      this.ensureFocusVisible(element);
    } catch (error) {
      console.warn('Failed to focus element:', error);
    }
  }

  /**
   * Restores focus to the previous element.
   */
  restoreFocus(): void {
    const previousFocus = this.focusHistory.pop();
    if (previousFocus) {
      this.focus(previousFocus);
    }
  }

  /**
   * Traps focus within a container element.
   */
  trapFocus(container: HTMLElement): () => void {
    const focusableElements = this.getFocusableElements(container);

    if (focusableElements.length === 0) {
      return () => {}; // No-op if no focusable elements
    }

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Tab') {
        if (event.shiftKey) {
          // Shift + Tab: move backwards
          if (document.activeElement === firstElement) {
            event.preventDefault();
            lastElement.focus();
          }
        } else {
          // Tab: move forwards
          if (document.activeElement === lastElement) {
            event.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);

    // Focus the first element
    this.focus(firstElement);

    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  }

  /**
   * Detects if the user prefers reduced motion.
   */
  prefersReducedMotion(): boolean {
    if (typeof window === 'undefined') {
      return false;
    }

    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  /**
   * Calculates contrast ratio between two colors (dev only).
   */
  getContrast(color1: string, color2: string): number {
    if (!this.config.contrast.enabled) {
      return 0;
    }

    const luminance1 = this.getLuminance(color1);
    const luminance2 = this.getLuminance(color2);

    const lighter = Math.max(luminance1, luminance2);
    const darker = Math.min(luminance1, luminance2);

    return (lighter + 0.05) / (darker + 0.05);
  }

  /**
   * Validates contrast ratio and logs warnings/errors.
   */
  validateContrast(
    foreground: string,
    background: string,
    context: string = 'text'
  ): boolean {
    const contrast = this.getContrast(foreground, background);

    if (contrast < this.config.contrast.errorThreshold) {
      console.error(
        `🚨 Critical contrast issue in ${context}: ${contrast.toFixed(2)}:1 ratio`
      );
      return false;
    } else if (contrast < this.config.contrast.warningThreshold) {
      console.warn(
        `⚠️ Contrast warning in ${context}: ${contrast.toFixed(2)}:1 ratio`
      );
      return false;
    }

    return true;
  }

  /**
   * Checks if an element is visible to screen readers.
   */
  isScreenReaderVisible(element: HTMLElement): boolean {
    const style = window.getComputedStyle(element);

    // Check if element is hidden
    if (style.display === 'none' || style.visibility === 'hidden') {
      return false;
    }

    // Check aria-hidden
    if (element.getAttribute('aria-hidden') === 'true') {
      return false;
    }

    // Check if element has accessible content
    const hasText =
      element.textContent && element.textContent.trim().length > 0;
    const hasAriaLabel =
      element.getAttribute('aria-label') ||
      element.getAttribute('aria-labelledby');
    const hasAlt = element.getAttribute('alt');

    return hasText || !!hasAriaLabel || hasAlt !== null;
  }

  /**
   * Validates semantic HTML structure.
   */
  validateSemanticStructure(element: HTMLElement): string[] {
    const issues: string[] = [];

    // Check for proper heading hierarchy
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;

    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));

      if (index === 0 && level !== 1) {
        issues.push('Page should start with h1 heading');
      }

      if (level > previousLevel + 1) {
        issues.push(`Skipped heading level: ${previousLevel} to ${level}`);
      }

      previousLevel = level;
    });

    // Check for proper form labels
    const inputs = element.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      const id = input.getAttribute('id');
      const label = element.querySelector(`label[for="${id}"]`);

      if (
        !label &&
        !input.getAttribute('aria-label') &&
        !input.getAttribute('aria-labelledby')
      ) {
        issues.push(`Input missing label: ${input.tagName}`);
      }
    });

    // Check for proper button labels
    const buttons = element.querySelectorAll('button');
    buttons.forEach(button => {
      const hasText =
        button.textContent && button.textContent.trim().length > 0;
      const hasAriaLabel = button.getAttribute('aria-label');

      if (!hasText && !hasAriaLabel) {
        issues.push('Button missing accessible label');
      }
    });

    return issues;
  }

  /**
   * Gets current accessibility configuration.
   */
  getConfig(): A11yConfig {
    return { ...this.config };
  }

  /**
   * Updates accessibility configuration.
   */
  updateConfig(newConfig: Partial<A11yConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Private methods

  private createLiveRegion(): void {
    if (typeof document === 'undefined') {
      return;
    }

    // Check if live region already exists
    this.liveRegion = document.getElementById('play-a11y-live-region');

    if (!this.liveRegion) {
      this.liveRegion = document.createElement('div');
      this.liveRegion.id = 'play-a11y-live-region';
      this.liveRegion.setAttribute(
        'aria-live',
        this.config.liveRegionPoliteness
      );
      this.liveRegion.setAttribute('aria-atomic', 'true');
      this.liveRegion.style.cssText = `
        position: absolute;
        left: -10000px;
        width: 1px;
        height: 1px;
        overflow: hidden;
      `;

      document.body.appendChild(this.liveRegion);
    }
  }

  private setupFocusTracking(): void {
    if (typeof document === 'undefined') {
      return;
    }

    // Track focus changes for debugging
    document.addEventListener('focusin', event => {
      const target = event.target as HTMLElement;
      if (target && target !== document.body) {
        console.debug('Focus moved to:', target);
      }
    });
  }

  private ensureFocusVisible(element: HTMLElement): void {
    // Add focus ring styles if not already present
    if (!element.style.outline) {
      element.style.outline = `${this.config.focusRing.width} solid ${this.config.focusRing.color}`;
      element.style.outlineOffset = this.config.focusRing.offset;
    }
  }

  private getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ];

    const elements = container.querySelectorAll(focusableSelectors.join(', '));
    return Array.from(elements) as HTMLElement[];
  }

  private getLuminance(color: string): number {
    // Convert hex to RGB
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    // Apply gamma correction
    const rsRGB = r <= 0.03928 ? r / 12.92 : Math.pow((r + 0.055) / 1.055, 2.4);
    const gsRGB = g <= 0.03928 ? g / 12.92 : Math.pow((g + 0.055) / 1.055, 2.4);
    const bsRGB = b <= 0.03928 ? b / 12.92 : Math.pow((b + 0.055) / 1.055, 2.4);

    return 0.2126 * rsRGB + 0.7152 * gsRGB + 0.0722 * bsRGB;
  }
}

// Export singleton instance
export const playa11y = new PlayA11y();

// Export the class for testing or custom instances
export { PlayA11y };
