// Play+ Logging Helper - Structured, environment-aware logging
// Based on the Play+ logging guidelines

interface LogContext {
  [key: string]: any;
}

interface LogConfig {
  level: 'debug' | 'info' | 'warn' | 'error';
  redact: string[];
  transport: {
    type: 'console' | 'remote';
    apiUrl?: string;
    apiKey?: string;
  };
}

interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  context?: LogContext;
  error?: Error;
  hostname?: string;
  appName?: string;
}

class PlayLog {
  private config: LogConfig;
  private appName: string;
  private hostname: string;

  constructor() {
    this.config = this.loadConfig();
    this.appName = this.getAppName();
    this.hostname = this.getHostname();
  }

  private loadConfig(): LogConfig {
    // Priority: Environment Variable > Config File > Default
    const envLevel = process.env['LOG_LEVEL'] as LogConfig['level'];

    const defaultConfig: LogConfig = {
      level: 'info',
      redact: ['password', 'token', 'authorization', 'secret', 'key'],
      transport: {
        type: 'console',
      },
    };

    // In a real implementation, you'd load from config/play.log.config.json
    // For now, we'll use environment variables and defaults
    return {
      ...defaultConfig,
      level: envLevel || defaultConfig.level,
      transport: {
        type:
          (process.env['LOG_TRANSPORT'] as 'console' | 'remote') || 'console',
        apiUrl: process.env['LOG_API_URL'],
        apiKey: process.env['LOG_API_KEY'],
      },
    };
  }

  private getAppName(): string {
    return process.env['APP_NAME'] || 'play-angular-seed';
  }

  private getHostname(): string {
    return process.env['HOSTNAME'] || 'localhost';
  }

  private shouldLog(level: string): boolean {
    const levels = { debug: 0, info: 1, warn: 2, error: 3 };
    return levels[level as keyof typeof levels] >= levels[this.config.level];
  }

  private redactSensitiveData(obj: any): any {
    if (!obj || typeof obj !== 'object') return obj;

    const redacted = { ...obj };

    for (const key in redacted) {
      if (
        this.config.redact.some(redactKey =>
          key.toLowerCase().includes(redactKey.toLowerCase())
        )
      ) {
        redacted[key] = '[REDACTED]';
      } else if (typeof redacted[key] === 'object') {
        redacted[key] = this.redactSensitiveData(redacted[key]);
      }
    }

    return redacted;
  }

  private createLogEntry(
    level: string,
    message: string,
    context?: LogContext,
    error?: Error
  ): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      hostname: this.hostname,
      appName: this.appName,
    };

    if (context) {
      entry.context = this.redactSensitiveData(context);
    }

    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
      };
    }

    return entry;
  }

  private formatLog(entry: LogEntry): string {
    const isDevelopment = process.env['NODE_ENV'] !== 'production';

    if (isDevelopment) {
      // Pretty print for development
      const colors = {
        debug: '\x1b[36m', // Cyan
        info: '\x1b[32m', // Green
        warn: '\x1b[33m', // Yellow
        error: '\x1b[31m', // Red
        reset: '\x1b[0m', // Reset
      };

      let output = `${
        colors[entry.level as keyof typeof colors]
      }[${entry.level.toUpperCase()}]${colors.reset} ${entry.message}`;

      if (entry.context) {
        output += ` ${JSON.stringify(entry.context, null, 2)}`;
      }

      if (entry.error) {
        output += `\n${colors.error}Error: ${entry.error.message}${colors.reset}`;
        if (entry.error.stack) {
          output += `\n${entry.error.stack}`;
        }
      }

      return output;
    } else {
      // JSON format for production
      return JSON.stringify(entry);
    }
  }

  private async sendToRemote(entry: LogEntry): Promise<void> {
    if (
      this.config.transport.type !== 'remote' ||
      !this.config.transport.apiUrl
    ) {
      return;
    }

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (this.config.transport.apiKey) {
        headers['Authorization'] = `Bearer ${this.config.transport.apiKey}`;
      }

      await fetch(this.config.transport.apiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(entry),
      });
    } catch (error) {
      // Fallback to console if remote logging fails
      console.error('Failed to send log to remote service:', error);
      console.log(this.formatLog(entry));
    }
  }

  private async log(
    level: string,
    message: string,
    context?: LogContext,
    error?: Error
  ): Promise<void> {
    if (!this.shouldLog(level)) return;

    const entry = this.createLogEntry(level, message, context, error);
    const formatted = this.formatLog(entry);

    // Always log to console in development or as fallback
    if (
      this.config.transport.type === 'console' ||
      process.env['NODE_ENV'] !== 'production'
    ) {
      console.log(formatted);
    } else {
      // Send to remote service in production
      await this.sendToRemote(entry);
    }
  }

  // Public API methods
  debug(message: string, context?: LogContext): void {
    this.log('debug', message, context);
  }

  info(message: string, context?: LogContext): void {
    this.log('info', message, context);
  }

  warn(message: string, context?: LogContext): void {
    this.log('warn', message, context);
  }

  error(error: Error, message?: string, context?: LogContext): void {
    this.log('error', message || error.message, context, error);
  }

  // Universal method for flexible logging
  add(
    messageOrError: string | Error,
    context?: LogContext,
    message?: string
  ): void {
    if (messageOrError instanceof Error) {
      this.error(messageOrError, message, context);
    } else {
      this.info(messageOrError, context);
    }
  }

  // Configuration methods
  setLevel(level: LogConfig['level']): void {
    this.config.level = level;
  }

  setConfig(config: Partial<LogConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

// Export singleton instance
export const playlog = new PlayLog();
