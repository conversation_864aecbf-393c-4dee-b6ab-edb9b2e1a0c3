import { Injectable } from '@angular/core';

export interface FeatureConfig {
  enabled: boolean;
  storage: {
    type: 'localStorage' | 'sessionStorage';
    prefix: string;
  };
  defaults: {
    [key: string]: boolean;
  };
  remote: {
    enabled: boolean;
    endpoint: string;
    refreshInterval: number;
  };
}

@Injectable({
  providedIn: 'root',
})
export class PlayFeatureService {
  private config: FeatureConfig;
  private features = new Map<string, boolean>();

  constructor() {
    this.config = {
      enabled: true,
      storage: {
        type: 'localStorage',
        prefix: 'play_features_',
      },
      defaults: {
        newUI: false,
        betaFeatures: false,
        analytics: true,
      },
      remote: {
        enabled: false,
        endpoint: '',
        refreshInterval: 300000,
      },
    };

    this.loadFeatures();
  }

  setConfig(config: Partial<FeatureConfig>): void {
    this.config = { ...this.config, ...config };
    this.loadFeatures();
  }

  isEnabled(featureName: string): boolean {
    if (!this.config.enabled) return false;

    // Check if feature is explicitly set
    if (this.features.has(featureName)) {
      return this.features.get(featureName)!;
    }

    // Check defaults
    if (featureName in this.config.defaults) {
      return this.config.defaults[featureName];
    }

    return false;
  }

  enable(featureName: string): void {
    this.setFeature(featureName, true);
  }

  disable(featureName: string): void {
    this.setFeature(featureName, false);
  }

  setFeature(featureName: string, enabled: boolean): void {
    this.features.set(featureName, enabled);
    this.saveFeature(featureName, enabled);
  }

  getFeatures(): Map<string, boolean> {
    return new Map(this.features);
  }

  resetFeatures(): void {
    this.features.clear();
    this.clearStorage();
    this.loadFeatures();
  }

  private loadFeatures(): void {
    if (!this.config.enabled) return;

    try {
      const storage =
        this.config.storage.type === 'localStorage'
          ? localStorage
          : sessionStorage;
      const keys = Object.keys(storage);

      keys.forEach(key => {
        if (key.startsWith(this.config.storage.prefix)) {
          const featureName = key.replace(this.config.storage.prefix, '');
          const value = storage.getItem(key);
          if (value !== null) {
            this.features.set(featureName, JSON.parse(value));
          }
        }
      });
    } catch (error) {
      console.warn('Failed to load features from storage:', error);
    }
  }

  private saveFeature(featureName: string, enabled: boolean): void {
    try {
      const storage =
        this.config.storage.type === 'localStorage'
          ? localStorage
          : sessionStorage;
      const key = `${this.config.storage.prefix}${featureName}`;
      storage.setItem(key, JSON.stringify(enabled));
    } catch (error) {
      console.warn('Failed to save feature to storage:', error);
    }
  }

  private clearStorage(): void {
    try {
      const storage =
        this.config.storage.type === 'localStorage'
          ? localStorage
          : sessionStorage;
      const keys = Object.keys(storage);

      keys.forEach(key => {
        if (key.startsWith(this.config.storage.prefix)) {
          storage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('Failed to clear feature storage:', error);
    }
  }

  // Remote feature flag methods
  async loadRemoteFeatures(): Promise<void> {
    if (!this.config.remote.enabled || !this.config.remote.endpoint) return;

    try {
      const response = await fetch(this.config.remote.endpoint);
      const remoteFeatures = await response.json();

      Object.entries(remoteFeatures).forEach(([featureName, enabled]) => {
        this.setFeature(featureName, enabled as boolean);
      });
    } catch (error) {
      console.warn('Failed to load remote features:', error);
    }
  }

  startRemoteSync(): void {
    if (!this.config.remote.enabled) return;

    setInterval(() => {
      this.loadRemoteFeatures();
    }, this.config.remote.refreshInterval);
  }
}
