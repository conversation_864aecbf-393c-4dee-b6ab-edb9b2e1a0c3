import { Injectable } from '@angular/core';

export interface ErrorConfig {
  enabled: boolean;
  logging: {
    console: boolean;
    remote: boolean;
    level: 'error' | 'warn' | 'info';
  };
  userFeedback: {
    showMessages: boolean;
    timeout: number;
    position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  };
  recovery: {
    autoRetry: boolean;
    maxRetries: number;
    retryDelay: number;
  };
  boundary: {
    fallbackUI: boolean;
    reportErrors: boolean;
  };
}

export interface ErrorInfo {
  message: string;
  stack?: string;
  context?: any;
  timestamp: number;
  level: 'error' | 'warn' | 'info';
}

@Injectable({
  providedIn: 'root',
})
export class PlayErrorService {
  private config: ErrorConfig;
  private errorCount = 0;

  constructor() {
    this.config = {
      enabled: true,
      logging: {
        console: true,
        remote: false,
        level: 'error',
      },
      userFeedback: {
        showMessages: true,
        timeout: 5000,
        position: 'top-right',
      },
      recovery: {
        autoRetry: true,
        maxRetries: 3,
        retryDelay: 1000,
      },
      boundary: {
        fallbackUI: true,
        reportErrors: true,
      },
    };
  }

  setConfig(config: Partial<ErrorConfig>): void {
    this.config = { ...this.config, ...config };
  }

  handleError(error: Error | string, context?: any): void {
    if (!this.config.enabled) return;

    const errorInfo: ErrorInfo = {
      message: typeof error === 'string' ? error : error.message,
      stack: error instanceof Error ? error.stack : undefined,
      context,
      timestamp: Date.now(),
      level: 'error',
    };

    this.logError(errorInfo);
    this.showUserFeedback(errorInfo);
  }

  handleWarning(message: string, context?: any): void {
    if (!this.config.enabled) return;

    const errorInfo: ErrorInfo = {
      message,
      context,
      timestamp: Date.now(),
      level: 'warn',
    };

    this.logError(errorInfo);
  }

  private logError(errorInfo: ErrorInfo): void {
    if (this.config.logging.console) {
      console[errorInfo.level](`[Play+ Error] ${errorInfo.message}`, {
        context: errorInfo.context,
        timestamp: new Date(errorInfo.timestamp).toISOString(),
      });
    }

    if (this.config.logging.remote) {
      this.sendToRemoteLogger(errorInfo);
    }
  }

  private showUserFeedback(errorInfo: ErrorInfo): void {
    if (!this.config.userFeedback.showMessages || errorInfo.level !== 'error')
      return;

    this.createToast(errorInfo.message);
  }

  private createToast(message: string): void {
    const toast = document.createElement('div');
    toast.className = 'play-error-toast';
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      ${this.config.userFeedback.position.includes('top') ? 'top: 20px' : 'bottom: 20px'};
      ${this.config.userFeedback.position.includes('right') ? 'right: 20px' : 'left: 20px'};
      background: #f44336;
      color: white;
      padding: 12px 16px;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      z-index: 10000;
      max-width: 300px;
      word-wrap: break-word;
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, this.config.userFeedback.timeout);
  }

  private sendToRemoteLogger(errorInfo: ErrorInfo): void {
    // Implementation for sending errors to remote logging service
    console.log('Sending error to remote logger:', errorInfo);
  }

  getErrorCount(): number {
    return this.errorCount;
  }

  resetErrorCount(): void {
    this.errorCount = 0;
  }
}
