import { apiProxy, RequestOptions, CacheRequestOptions } from './api.proxy';
import { apiRoutes } from './api.routes';

// Developer-facing API service - combines routes + proxy
export class ApiService {
  // Standard HTTP methods
  async get<T>(route: string, options?: RequestOptions): Promise<T> {
    return apiProxy.get<T>(route, options);
  }

  async post<T>(
    route: string,
    body: any,
    options?: RequestOptions
  ): Promise<T> {
    return apiProxy.post<T>(route, body, options);
  }

  async put<T>(route: string, body: any, options?: RequestOptions): Promise<T> {
    return apiProxy.put<T>(route, body, options);
  }

  async del<T>(route: string, options?: RequestOptions): Promise<T> {
    return apiProxy.del<T>(route, options);
  }

  // Cached GET requests
  cached = {
    async get<T>(route: string, options?: CacheRequestOptions): Promise<T> {
      return apiProxy.cachedGet<T>(route, options);
    },
  };

  // Convenience methods for common operations
  users = {
    getAll: () => this.get(apiRoutes.users.getAll),
    getById: (id: string) => this.get(apiRoutes.users.getById(id)),
    create: (userData: any) => this.post(apiRoutes.users.create, userData),
    update: (id: string, userData: any) =>
      this.put(apiRoutes.users.update(id), userData),
    delete: (id: string) => this.del(apiRoutes.users.delete(id)),
    getProfile: () => this.get(apiRoutes.users.profile),
    updateProfile: (profileData: any) =>
      this.put(apiRoutes.users.updateProfile, profileData),
  };

  auth = {
    login: (credentials: any) =>
      this.post(apiRoutes.auth.login, credentials, { auth: false }),
    register: (userData: any) =>
      this.post(apiRoutes.auth.register, userData, { auth: false }),
    logout: () => this.post(apiRoutes.auth.logout, {}),
    refresh: () => this.post(apiRoutes.auth.refresh, {}),
    forgotPassword: (email: string) =>
      this.post(apiRoutes.auth.forgotPassword, { email }, { auth: false }),
    resetPassword: (token: string, password: string) =>
      this.post(
        apiRoutes.auth.resetPassword,
        { token, password },
        { auth: false }
      ),
    verifyEmail: (token: string) =>
      this.post(apiRoutes.auth.verifyEmail, { token }, { auth: false }),
  };

  products = {
    getAll: () => this.get(apiRoutes.products.getAll),
    getById: (id: string) => this.get(apiRoutes.products.getById(id)),
    create: (productData: any) =>
      this.post(apiRoutes.products.create, productData),
    update: (id: string, productData: any) =>
      this.put(apiRoutes.products.update(id), productData),
    delete: (id: string) => this.del(apiRoutes.products.delete(id)),
    search: (query: string) => this.get(apiRoutes.products.search(query)),
    getByCategory: (categoryId: string) =>
      this.get(apiRoutes.products.byCategory(categoryId)),
  };

  categories = {
    getAll: () => this.get(apiRoutes.categories.getAll),
    getById: (id: string) => this.get(apiRoutes.categories.getById(id)),
    create: (categoryData: any) =>
      this.post(apiRoutes.categories.create, categoryData),
    update: (id: string, categoryData: any) =>
      this.put(apiRoutes.categories.update(id), categoryData),
    delete: (id: string) => this.del(apiRoutes.categories.delete(id)),
  };

  orders = {
    getAll: () => this.get(apiRoutes.orders.getAll),
    getById: (id: string) => this.get(apiRoutes.orders.getById(id)),
    create: (orderData: any) => this.post(apiRoutes.orders.create, orderData),
    update: (id: string, orderData: any) =>
      this.put(apiRoutes.orders.update(id), orderData),
    delete: (id: string) => this.del(apiRoutes.orders.delete(id)),
    getUserOrders: () => this.get(apiRoutes.orders.userOrders),
    cancel: (id: string) => this.post(apiRoutes.orders.cancel(id), {}),
  };

  cart = {
    get: () => this.get(apiRoutes.cart.get),
    addItem: (itemData: any) => this.post(apiRoutes.cart.addItem, itemData),
    updateItem: (itemId: string, itemData: any) =>
      this.put(apiRoutes.cart.updateItem(itemId), itemData),
    removeItem: (itemId: string) => this.del(apiRoutes.cart.removeItem(itemId)),
    clear: () => this.del(apiRoutes.cart.clear),
  };

  reviews = {
    getByProduct: (productId: string) =>
      this.get(apiRoutes.reviews.getByProduct(productId)),
    create: (productId: string, reviewData: any) =>
      this.post(apiRoutes.reviews.create(productId), reviewData),
    update: (reviewId: string, reviewData: any) =>
      this.put(apiRoutes.reviews.update(reviewId), reviewData),
    delete: (reviewId: string) => this.del(apiRoutes.reviews.delete(reviewId)),
  };

  uploads = {
    uploadImage: (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      return this.post(apiRoutes.uploads.image, formData, {
        headers: {}, // Let browser set Content-Type for FormData
      });
    },
    uploadDocument: (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      return this.post(apiRoutes.uploads.document, formData, {
        headers: {}, // Let browser set Content-Type for FormData
      });
    },
    delete: (fileId: string) => this.del(apiRoutes.uploads.delete(fileId)),
  };

  notifications = {
    getAll: () => this.get(apiRoutes.notifications.getAll),
    getById: (id: string) => this.get(apiRoutes.notifications.getById(id)),
    markAsRead: (id: string) =>
      this.post(apiRoutes.notifications.markAsRead(id), {}),
    markAllAsRead: () => this.post(apiRoutes.notifications.markAllAsRead, {}),
    delete: (id: string) => this.del(apiRoutes.notifications.delete(id)),
  };

  settings = {
    get: () => this.get(apiRoutes.settings.get),
    update: (settingsData: any) =>
      this.put(apiRoutes.settings.update, settingsData),
    updatePassword: (passwordData: any) =>
      this.put(apiRoutes.settings.updatePassword, passwordData),
    updateEmail: (emailData: any) =>
      this.put(apiRoutes.settings.updateEmail, emailData),
  };

  analytics = {
    getDashboard: () => this.get(apiRoutes.analytics.dashboard),
    getSales: () => this.get(apiRoutes.analytics.sales),
    getUsers: () => this.get(apiRoutes.analytics.users),
    getProducts: () => this.get(apiRoutes.analytics.products),
  };
}

// Export singleton instance
export const apiService = new ApiService();
