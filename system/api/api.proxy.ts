import { PlayCacheService } from '../cache/cache.service';
import { PlayErrorService } from '../error/error.service';
import { PlayLogService } from '../log/log.service';
import { playguard } from '../play.security';

export interface RequestOptions {
  timeout?: number;
  retry?: {
    maxAttempts?: number;
    delayMs?: number;
  };
  auth?: boolean;
  headers?: Record<string, string>;
}

export interface CacheRequestOptions extends RequestOptions {
  ttl?: number;
  forceRefresh?: boolean;
}

export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: Date;
}

class ApiProxy {
  private baseUrl: string;
  private defaultTimeout: number = 10000;
  private defaultRetryAttempts: number = 3;
  private defaultRetryDelay: number = 500;
  private defaultCacheTtl: number = 60000;

  constructor(
    private logService: PlayLogService,
    private cacheService: PlayCacheService,
    private errorService: PlayErrorService
  ) {
    this.baseUrl = this.getBaseUrl();
  }

  private getBaseUrl(): string {
    // Use a default API base URL for browser environment
    return 'https://api.example.com';
  }

  private getAuthHeaders(): Record<string, string> {
    const authHeader = playguard.auth.getAuthHeader();
    const csrfHeaders = playguard.auth.getCSRFHeader();
    const securityHeaders = playguard.getSecurityHeaders();

    return {
      ...(authHeader ? { Authorization: authHeader } : {}),
      ...csrfHeaders,
      ...securityHeaders,
    };
  }

  private async makeRequest<T>(
    url: string,
    options: RequestInit & { timeout?: number } = {}
  ): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      options.timeout || this.defaultTimeout
    );

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...this.getAuthHeaders(),
          ...options.headers,
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private async retryRequest<T>(
    requestFn: () => Promise<T>,
    maxAttempts: number = this.defaultRetryAttempts,
    delayMs: number = this.defaultRetryDelay
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error as Error;

        if (attempt === maxAttempts) {
          break;
        }

        // Don't retry on client errors (4xx)
        if (lastError.message.includes('HTTP 4')) {
          break;
        }

        // Exponential backoff
        const delay = delayMs * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));

        this.logService.warn(
          `API request failed, retrying (${attempt}/${maxAttempts})`,
          {
            error: lastError.message,
            attempt,
            delay,
          }
        );
      }
    }

    throw lastError!;
  }

  async get<T>(route: string, options: RequestOptions = {}): Promise<T> {
    const url = `${this.baseUrl}${route}`;

    this.logService.info('API GET request', { url, options });

    const requestFn = () =>
      this.makeRequest<T>(url, {
        method: 'GET',
        timeout: options.timeout || this.defaultTimeout,
        headers: options.auth === false ? {} : this.getAuthHeaders(),
        ...options.headers,
      });

    try {
      const result = await this.retryRequest(
        requestFn,
        options.retry?.maxAttempts || this.defaultRetryAttempts,
        options.retry?.delayMs || this.defaultRetryDelay
      );

      this.logService.info('API GET request successful', { url });
      return result;
    } catch (error) {
      this.errorService.handleError(error as Error, {
        context: 'API GET',
        url,
      });
      throw error;
    }
  }

  async post<T>(
    route: string,
    body: any,
    options: RequestOptions = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${route}`;

    this.logService.info('API POST request', { url, options });

    const requestFn = () =>
      this.makeRequest<T>(url, {
        method: 'POST',
        body: JSON.stringify(body),
        timeout: options.timeout || this.defaultTimeout,
        headers: options.auth === false ? {} : this.getAuthHeaders(),
        ...options.headers,
      });

    try {
      const result = await this.retryRequest(
        requestFn,
        options.retry?.maxAttempts || this.defaultRetryAttempts,
        options.retry?.delayMs || this.defaultRetryDelay
      );

      this.logService.info('API POST request successful', { url });
      return result;
    } catch (error) {
      this.errorService.handleError(error as Error, {
        context: 'API POST',
        url,
      });
      throw error;
    }
  }

  async put<T>(
    route: string,
    body: any,
    options: RequestOptions = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${route}`;

    this.logService.info('API PUT request', { url, options });

    const requestFn = () =>
      this.makeRequest<T>(url, {
        method: 'PUT',
        body: JSON.stringify(body),
        timeout: options.timeout || this.defaultTimeout,
        headers: options.auth === false ? {} : this.getAuthHeaders(),
        ...options.headers,
      });

    try {
      const result = await this.retryRequest(
        requestFn,
        options.retry?.maxAttempts || this.defaultRetryAttempts,
        options.retry?.delayMs || this.defaultRetryDelay
      );

      this.logService.info('API PUT request successful', { url });
      return result;
    } catch (error) {
      this.errorService.handleError(error as Error, {
        context: 'API PUT',
        url,
      });
      throw error;
    }
  }

  async del<T>(route: string, options: RequestOptions = {}): Promise<T> {
    const url = `${this.baseUrl}${route}`;

    this.logService.info('API DELETE request', { url, options });

    const requestFn = () =>
      this.makeRequest<T>(url, {
        method: 'DELETE',
        timeout: options.timeout || this.defaultTimeout,
        headers: options.auth === false ? {} : this.getAuthHeaders(),
        ...options.headers,
      });

    try {
      const result = await this.retryRequest(
        requestFn,
        options.retry?.maxAttempts || this.defaultRetryAttempts,
        options.retry?.delayMs || this.defaultRetryDelay
      );

      this.logService.info('API DELETE request successful', { url });
      return result;
    } catch (error) {
      this.errorService.handleError(error as Error, {
        context: 'API DELETE',
        url,
      });
      throw error;
    }
  }

  // Cached GET requests
  async cachedGet<T>(
    route: string,
    options: CacheRequestOptions = {}
  ): Promise<T> {
    const cacheKey = `api_cache_${route}`;
    const ttl = options.ttl || this.defaultCacheTtl;

    // Check cache first (unless force refresh)
    if (!options.forceRefresh) {
      const cached = this.cacheService.get<T>(cacheKey);
      if (cached) {
        this.logService.info('API cache hit', { route });
        return cached;
      }
    }

    // Fetch from API
    const result = await this.get<T>(route, options);

    // Cache the result
    this.cacheService.set(cacheKey, result, ttl);

    this.logService.info('API cache miss, stored in cache', { route });
    return result;
  }
}

// Export singleton instance
const logService = new PlayLogService();
export const apiProxy = new ApiProxy(
  logService,
  new PlayCacheService(),
  new PlayErrorService(logService)
);
