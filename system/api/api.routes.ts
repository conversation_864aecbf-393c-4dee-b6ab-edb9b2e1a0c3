// Central route map - defines all API endpoints to prevent magic strings

export const apiRoutes = {
  // User management
  users: {
    getAll: '/users',
    getById: (id: string) => `/users/${id}`,
    create: '/users',
    update: (id: string) => `/users/${id}`,
    delete: (id: string) => `/users/${id}`,
    profile: '/users/profile',
    updateProfile: '/users/profile',
  },

  // Authentication
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    logout: '/auth/logout',
    refresh: '/auth/refresh',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    verifyEmail: '/auth/verify-email',
  },

  // Products
  products: {
    getAll: '/products',
    getById: (id: string) => `/products/${id}`,
    create: '/products',
    update: (id: string) => `/products/${id}`,
    delete: (id: string) => `/products/${id}`,
    search: (query: string) =>
      `/products/search?q=${encodeURIComponent(query)}`,
    byCategory: (categoryId: string) => `/products/category/${categoryId}`,
  },

  // Categories
  categories: {
    getAll: '/categories',
    getById: (id: string) => `/categories/${id}`,
    create: '/categories',
    update: (id: string) => `/categories/${id}`,
    delete: (id: string) => `/categories/${id}`,
  },

  // Orders
  orders: {
    getAll: '/orders',
    getById: (id: string) => `/orders/${id}`,
    create: '/orders',
    update: (id: string) => `/orders/${id}`,
    delete: (id: string) => `/orders/${id}`,
    userOrders: '/orders/my-orders',
    cancel: (id: string) => `/orders/${id}/cancel`,
  },

  // Cart
  cart: {
    get: '/cart',
    addItem: '/cart/items',
    updateItem: (itemId: string) => `/cart/items/${itemId}`,
    removeItem: (itemId: string) => `/cart/items/${itemId}`,
    clear: '/cart/clear',
  },

  // Reviews
  reviews: {
    getByProduct: (productId: string) => `/products/${productId}/reviews`,
    create: (productId: string) => `/products/${productId}/reviews`,
    update: (reviewId: string) => `/reviews/${reviewId}`,
    delete: (reviewId: string) => `/reviews/${reviewId}`,
  },

  // File uploads
  uploads: {
    image: '/uploads/image',
    document: '/uploads/document',
    delete: (fileId: string) => `/uploads/${fileId}`,
  },

  // Notifications
  notifications: {
    getAll: '/notifications',
    getById: (id: string) => `/notifications/${id}`,
    markAsRead: (id: string) => `/notifications/${id}/read`,
    markAllAsRead: '/notifications/read-all',
    delete: (id: string) => `/notifications/${id}`,
  },

  // Settings
  settings: {
    get: '/settings',
    update: '/settings',
    updatePassword: '/settings/password',
    updateEmail: '/settings/email',
  },

  // Analytics (admin only)
  analytics: {
    dashboard: '/analytics/dashboard',
    sales: '/analytics/sales',
    users: '/analytics/users',
    products: '/analytics/products',
  },
};
