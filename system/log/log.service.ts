// PlayLogService - Wrapper for Play+ logging system
// This maintains backward compatibility while enforcing Play+ logging rules
import { playlog } from '../play.log';

export class PlayLogService {
  debug(message: string, context?: any): void {
    playlog.debug(message, context);
  }

  info(message: string, context?: any): void {
    playlog.info(message, context);
  }

  warn(message: string, context?: any): void {
    playlog.warn(message, context);
  }

  error(message: string, context?: any): void {
    // For backward compatibility, create an Error object if context is an Error
    if (context instanceof Error) {
      playlog.error(context, message);
    } else {
      const error = new Error(message);
      playlog.error(error, message, context);
    }
  }

  // Additional methods for Play+ compliance
  logError(error: Error, message?: string, context?: any): void {
    playlog.error(error, message, context);
  }

  setLevel(level: 'debug' | 'info' | 'warn' | 'error'): void {
    playlog.setLevel(level);
  }
}
