// Play+ Security Helper - Security by Design
// Based on the Play+ security guidelines

interface SecurityConfig {
  csp: {
    'default-src': string[];
    'script-src': string[];
    'style-src': string[];
    'img-src': string[];
    'connect-src': string[];
    'font-src': string[];
    'object-src': string[];
    'media-src': string[];
    'frame-src': string[];
  };
  csrf: {
    cookieName: string;
    headerName: string;
  };
  auth: {
    storage: 'cookie' | 'localStorage';
    tokenName: string;
    scheme: string;
  };
}

interface JWTPayload {
  sub: string;
  exp: number;
  iat: number;
  [key: string]: any;
}

class PlaySecurity {
  private config: SecurityConfig;

  constructor() {
    this.config = this.loadConfig();
    this.initializeSecurity();
  }

  private loadConfig(): SecurityConfig {
    // In a real implementation, load from config/play.security.config.json
    const defaultConfig: SecurityConfig = {
      csp: {
        'default-src': ["'self'"],
        'script-src': ["'self'", "'unsafe-inline'"],
        'style-src': ["'self'", "'unsafe-inline'"],
        'img-src': ["'self'", 'data:', 'https:'],
        'connect-src': ["'self'", 'https:'],
        'font-src': ["'self'", 'https:'],
        'object-src': ["'none'"],
        'media-src': ["'self'"],
        'frame-src': ["'none'"],
      },
      csrf: {
        cookieName: 'csrf_token',
        headerName: 'X-CSRF-Token',
      },
      auth: {
        storage: 'cookie',
        tokenName: 'accessToken',
        scheme: 'Bearer',
      },
    };

    // Override with environment variables if available
    return {
      ...defaultConfig,
      auth: {
        ...defaultConfig.auth,
        storage:
          (process.env['AUTH_STORAGE'] as 'cookie' | 'localStorage') ||
          defaultConfig.auth.storage,
        tokenName:
          process.env['AUTH_TOKEN_NAME'] || defaultConfig.auth.tokenName,
        scheme: process.env['AUTH_SCHEME'] || defaultConfig.auth.scheme,
      },
      csrf: {
        ...defaultConfig.csrf,
        cookieName:
          process.env['CSRF_COOKIE_NAME'] || defaultConfig.csrf.cookieName,
        headerName:
          process.env['CSRF_HEADER_NAME'] || defaultConfig.csrf.headerName,
      },
    };
  }

  private initializeSecurity(): void {
    // Set up Content Security Policy
    this.setupCSP();

    // Set up CSRF protection
    this.setupCSRF();
  }

  private setupCSP(): void {
    if (typeof document !== 'undefined') {
      const cspHeader = this.buildCSPHeader();
      const meta = document.createElement('meta');
      meta.httpEquiv = 'Content-Security-Policy';
      meta.content = cspHeader;
      document.head.appendChild(meta);
    }
  }

  private buildCSPHeader(): string {
    const directives = [];

    for (const [directive, sources] of Object.entries(this.config.csp)) {
      directives.push(`${directive} ${sources.join(' ')}`);
    }

    return directives.join('; ');
  }

  private setupCSRF(): void {
    // Generate CSRF token if not exists
    if (typeof document !== 'undefined' && !this.getCSRFToken()) {
      this.generateCSRFToken();
    }
  }

  private generateCSRFToken(): string {
    const token = this.generateSecureToken();
    this.setCSRFToken(token);
    return token;
  }

  private generateSecureToken(): string {
    const array = new Uint8Array(32);
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(array);
    } else {
      // Fallback for environments without crypto
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join(
      ''
    );
  }

  private setCSRFToken(token: string): void {
    if (typeof document !== 'undefined') {
      document.cookie = `${this.config.csrf.cookieName}=${token}; path=/; SameSite=Strict`;
    }
  }

  private getCSRFToken(): string | null {
    if (typeof document === 'undefined') return null;

    const cookies = document.cookie.split(';');
    const csrfCookie = cookies.find(cookie =>
      cookie.trim().startsWith(`${this.config.csrf.cookieName}=`)
    );

    return csrfCookie ? csrfCookie.split('=')[1] : null;
  }

  // Public API methods

  /**
   * Sanitizes a string to make it safe for rendering as HTML text content.
   * Strips HTML tags and encodes special characters.
   */
  sanitize(input: string): string {
    if (!input || typeof input !== 'string') return '';

    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Strips dangerous HTML (like <script> tags and onclick attributes) from a string.
   * Allows safe HTML while removing potentially malicious content.
   */
  purify(html: string): string {
    if (!html || typeof html !== 'string') return '';

    // Remove script tags and their content
    let purified = html.replace(
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      ''
    );

    // Remove event handler attributes
    purified = purified.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '');

    // Remove javascript: URLs
    purified = purified.replace(/javascript:/gi, '');

    // Remove data: URLs in potentially dangerous contexts
    purified = purified.replace(/data:text\/html/gi, '');
    purified = purified.replace(/data:application\/javascript/gi, '');

    // Remove iframe tags
    purified = purified.replace(
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      ''
    );

    // Remove object and embed tags
    purified = purified.replace(
      /<(object|embed)\b[^<]*(?:(?!<\/(object|embed)>)<[^<]*)*<\/(object|embed)>/gi,
      ''
    );

    return purified;
  }

  // Authentication methods
  auth = {
    /**
     * Securely stores an authentication token.
     */
    saveToken: (token: string): void => {
      if (!token || typeof token !== 'string') {
        throw new Error('Invalid token provided');
      }

      if (this.config.auth.storage === 'cookie') {
        // Store in secure cookie
        if (typeof document !== 'undefined') {
          const secure = window.location.protocol === 'https:';
          const cookie = `${
            this.config.auth.tokenName
          }=${token}; path=/; SameSite=Strict${secure ? '; Secure' : ''}`;
          document.cookie = cookie;
        }
      } else {
        // Store in localStorage (less secure, but supported)
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem(this.config.auth.tokenName, token);
        }
      }
    },

    /**
     * Retrieves the stored authentication token.
     */
    getToken: (): string | null => {
      if (this.config.auth.storage === 'cookie') {
        if (typeof document === 'undefined') return null;

        const cookies = document.cookie.split(';');
        const tokenCookie = cookies.find(cookie =>
          cookie.trim().startsWith(`${this.config.auth.tokenName}=`)
        );

        return tokenCookie ? tokenCookie.split('=')[1] : null;
      } else {
        if (typeof localStorage === 'undefined') return null;
        return localStorage.getItem(this.config.auth.tokenName);
      }
    },

    /**
     * Deletes the authentication token, effectively logging the user out.
     */
    removeToken: (): void => {
      if (this.config.auth.storage === 'cookie') {
        if (typeof document !== 'undefined') {
          document.cookie = `${this.config.auth.tokenName}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
        }
      } else {
        if (typeof localStorage !== 'undefined') {
          localStorage.removeItem(this.config.auth.tokenName);
        }
      }
    },

    /**
     * Decodes a JWT payload to read claims like user roles or expiration.
     */
    decodeToken: <T = JWTPayload>(): T | null => {
      const token = this.auth.getToken();
      if (!token) return null;

      try {
        const payload = token.split('.')[1];
        const decoded = atob(payload);
        return JSON.parse(decoded) as T;
      } catch (error) {
        return null;
      }
    },

    /**
     * Returns true if a valid, non-expired token exists.
     */
    isAuthenticated: (): boolean => {
      const token = this.auth.getToken();
      if (!token) return false;

      const payload = this.auth.decodeToken();
      if (!payload) return false;

      // Check if token is expired
      const now = Math.floor(Date.now() / 1000);
      return payload.exp > now;
    },

    /**
     * Gets the authorization header value for API requests.
     */
    getAuthHeader: (): string | null => {
      const token = this.auth.getToken();
      if (!token) return null;

      return `${this.config.auth.scheme} ${token}`;
    },

    /**
     * Gets the CSRF header for API requests.
     */
    getCSRFHeader: (): Record<string, string> => {
      const token = this.getCSRFToken();
      if (!token) return {};

      return { [this.config.csrf.headerName]: token };
    },
  };

  /**
   * Validates and sanitizes user input for safe processing.
   */
  validateInput(input: any, type: 'string' | 'email' | 'url' | 'number'): any {
    if (input === null || input === undefined) return null;

    switch (type) {
      case 'string':
        return typeof input === 'string' ? this.sanitize(input) : null;

      case 'email':
        if (typeof input !== 'string') return null;
        const email = input.trim().toLowerCase();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email) ? email : null;

      case 'url':
        if (typeof input !== 'string') return null;
        try {
          const url = new URL(input);
          return ['http:', 'https:'].includes(url.protocol)
            ? url.toString()
            : null;
        } catch {
          return null;
        }

      case 'number':
        const num = Number(input);
        return isNaN(num) ? null : num;

      default:
        return null;
    }
  }

  /**
   * Checks if the current environment is secure (HTTPS).
   */
  isSecureEnvironment(): boolean {
    if (typeof window === 'undefined') return false;
    return window.location.protocol === 'https:';
  }

  /**
   * Gets security headers for API requests.
   */
  getSecurityHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
    };

    // Add CSRF header
    const csrfHeaders = this.auth.getCSRFHeader();
    Object.assign(headers, csrfHeaders);

    return headers;
  }

  /**
   * Updates security configuration.
   */
  setConfig(config: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...config };
    this.initializeSecurity();
  }

  /**
   * Gets current security configuration.
   */
  getConfig(): SecurityConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const playguard = new PlaySecurity();
