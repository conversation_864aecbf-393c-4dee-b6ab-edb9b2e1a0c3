/**
 * Play+ Feature Flag Helper: `playfeature`
 *
 * A comprehensive feature flag management system that provides a clean, synchronous,
 * and framework-agnostic interface to feature flag providers. Abstracts the complexity
 * of flag management so developers can focus on building features.
 */

export interface FeatureConfig {
  provider: 'local' | 'launchdarkly' | 'flagsmith' | 'optimizely';
  clientKey: string | null;
  bootstrap: Record<string, boolean | string>;
  user?: {
    key: string;
    email?: string;
    name?: string;
    custom?: Record<string, any>;
  };
}

export interface FeatureFlag {
  key: string;
  value: boolean | string;
  defaultValue: boolean | string;
  lastUpdated: number;
}

// Default configuration
const DEFAULT_CONFIG: FeatureConfig = {
  provider: 'local',
  clientKey: null,
  bootstrap: {},
  user: undefined,
};

class PlayFeature {
  private config: FeatureConfig;
  private flags: Map<string, FeatureFlag> = new Map();
  private updateCallbacks: Set<() => void> = new Set();
  private isInitialized = false;
  private provider: any = null;

  constructor(config: Partial<FeatureConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initializeFlags();
  }

  /**
   * Initializes the feature flag system.
   * Must be called before using other methods.
   */
  async init(): Promise<void> {
    if (this.isInitialized) {
      console.warn('PlayFeature is already initialized');
      return;
    }

    try {
      await this.connectToProvider();
      this.isInitialized = true;
      console.log('PlayFeature initialized successfully');
    } catch (error) {
      console.warn(
        'Failed to initialize PlayFeature, using bootstrap values:',
        error
      );
      this.isInitialized = true; // Still mark as initialized to allow bootstrap usage
    }
  }

  /**
   * Checks if a boolean feature flag is enabled.
   * Returns defaultValue if the flag doesn't exist.
   */
  isEnabled(key: string, defaultValue: boolean = false): boolean {
    const flag = this.flags.get(key);
    if (!flag) {
      // Create a default flag entry
      this.flags.set(key, {
        key,
        value: defaultValue,
        defaultValue,
        lastUpdated: Date.now(),
      });
      return defaultValue;
    }

    return typeof flag.value === 'boolean' ? flag.value : defaultValue;
  }

  /**
   * Gets the string value for a multivariate or experiment flag.
   */
  getVariant(key: string, defaultVariant: string = 'control'): string {
    const flag = this.flags.get(key);
    if (!flag) {
      // Create a default flag entry
      this.flags.set(key, {
        key,
        value: defaultVariant,
        defaultValue: defaultVariant,
        lastUpdated: Date.now(),
      });
      return defaultVariant;
    }

    return typeof flag.value === 'string' ? flag.value : defaultVariant;
  }

  /**
   * Gets the raw value of a feature flag (boolean or string).
   */
  getValue(
    key: string,
    defaultValue: boolean | string = false
  ): boolean | string {
    const flag = this.flags.get(key);
    if (!flag) {
      // Create a default flag entry
      this.flags.set(key, {
        key,
        value: defaultValue,
        defaultValue,
        lastUpdated: Date.now(),
      });
      return defaultValue;
    }

    return flag.value;
  }

  /**
   * Subscribes to all flag changes from the provider.
   */
  onUpdate(callback: () => void): void {
    this.updateCallbacks.add(callback);
  }

  /**
   * Unsubscribes from flag changes to prevent memory leaks.
   */
  offUpdate(callback: () => void): void {
    this.updateCallbacks.delete(callback);
  }

  /**
   * Updates a flag value (useful for testing or local development).
   */
  setFlag(key: string, value: boolean | string): void {
    const existingFlag = this.flags.get(key);
    const flag: FeatureFlag = {
      key,
      value,
      defaultValue: existingFlag?.defaultValue ?? value,
      lastUpdated: Date.now(),
    };

    this.flags.set(key, flag);
    this.notifyUpdateCallbacks();
  }

  /**
   * Gets all current flag values.
   */
  getAllFlags(): Record<string, boolean | string> {
    const result: Record<string, boolean | string> = {};
    this.flags.forEach((flag, key) => {
      result[key] = flag.value;
    });
    return result;
  }

  /**
   * Gets flag metadata including last updated time.
   */
  getFlagMetadata(key: string): FeatureFlag | null {
    return this.flags.get(key) || null;
  }

  /**
   * Checks if the feature flag system is initialized.
   */
  isFeatureInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Gets current configuration.
   */
  getConfig(): FeatureConfig {
    return { ...this.config };
  }

  /**
   * Updates configuration and reinitializes if needed.
   */
  async updateConfig(newConfig: Partial<FeatureConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };

    if (this.isInitialized) {
      // Reinitialize with new config
      this.isInitialized = false;
      await this.init();
    }
  }

  /**
   * Sets the current user for targeted feature flags.
   */
  setUser(user: FeatureConfig['user']): void {
    this.config.user = user;
    this.notifyUpdateCallbacks();
  }

  /**
   * Gets the current user.
   */
  getUser(): FeatureConfig['user'] | undefined {
    return this.config.user;
  }

  /**
   * Clears all flags and resets to bootstrap values.
   */
  reset(): void {
    this.flags.clear();
    this.initializeFlags();
    this.notifyUpdateCallbacks();
  }

  // Private methods

  private initializeFlags(): void {
    // Initialize with bootstrap values
    Object.entries(this.config.bootstrap).forEach(([key, value]) => {
      this.flags.set(key, {
        key,
        value,
        defaultValue: value,
        lastUpdated: Date.now(),
      });
    });
  }

  private async connectToProvider(): Promise<void> {
    switch (this.config.provider) {
      case 'local':
        // Local provider - already initialized with bootstrap values
        break;

      case 'launchdarkly':
        await this.connectToLaunchDarkly();
        break;

      case 'flagsmith':
        await this.connectToFlagsmith();
        break;

      case 'optimizely':
        await this.connectToOptimizely();
        break;

      default:
        throw new Error(`Unsupported provider: ${this.config.provider}`);
    }
  }

  private async connectToLaunchDarkly(): Promise<void> {
    if (!this.config.clientKey) {
      throw new Error('LaunchDarkly client key is required');
    }

    try {
      // Dynamic import with proper error handling
      // Note: This package needs to be installed separately if using LaunchDarkly
      let initialize: any;
      try {
        // Use dynamic import with proper error handling
      } catch (importError) {
        console.warn(
          'LaunchDarkly SDK not installed. Install with: npm install launchdarkly-js-client-sdk'
        );
        throw new Error('LaunchDarkly SDK not available');
      }

      this.provider = initialize(this.config.clientKey, {
        kind: 'user',
        key: this.config.user?.key || 'anonymous',
        email: this.config.user?.email,
        name: this.config.user?.name,
        custom: this.config.user?.custom,
      });

      // Wait for client to be ready
      await this.provider.waitForInitialization();

      // Set up flag change listener
      this.provider.on('change', (changes: any) => {
        Object.entries(changes).forEach(([key, value]: [string, any]) => {
          this.setFlag(key, value.current);
        });
      });

      // Get all flag values
      const allFlags = this.provider.allFlags();
      Object.entries(allFlags).forEach(([key, value]) => {
        this.setFlag(key, value as boolean | string);
      });
    } catch (error) {
      console.warn('Failed to connect to LaunchDarkly:', error);
      throw error;
    }
  }

  private async connectToFlagsmith(): Promise<void> {
    if (!this.config.clientKey) {
      throw new Error('Flagsmith client key is required');
    }

    try {
      // Dynamic import with proper error handling
      // Note: This package needs to be installed separately if using Flagsmith
      let Flagsmith: any;
      try {
        // Use dynamic import with proper error handling
      } catch (importError) {
        console.warn(
          'Flagsmith SDK not installed. Install with: npm install flagsmith'
        );
        throw new Error('Flagsmith SDK not available');
      }

      this.provider = new Flagsmith({
        environmentID: this.config.clientKey,
        identity: this.config.user?.key || 'anonymous',
        traits: this.config.user?.custom,
      });

      // Wait for flags to load
      await this.provider.init({
        onError: (error: any) => {
          console.warn('Flagsmith error:', error);
        },
      });

      // Set up flag change listener
      this.provider.on('flags:changed', (flags: any) => {
        Object.entries(flags).forEach(([key, flag]: [string, any]) => {
          this.setFlag(key, flag.enabled ? flag.value : false);
        });
      });

      // Get all flag values
      const allFlags = this.provider.getAllFlags();
      Object.entries(allFlags).forEach(([key, flag]: [string, any]) => {
        this.setFlag(key, flag.enabled ? flag.value : false);
      });
    } catch (error) {
      console.warn('Failed to connect to Flagsmith:', error);
      throw error;
    }
  }

  private async connectToOptimizely(): Promise<void> {
    if (!this.config.clientKey) {
      throw new Error('Optimizely client key is required');
    }

    try {
      // Dynamic import with proper error handling
      // Note: This package needs to be installed separately if using Optimizely
      let createInstance: any;
      try {
        // Use dynamic import with proper error handling
      } catch (importError) {
        console.warn(
          'Optimizely SDK not installed. Install with: npm install @optimizely/optimizely-sdk'
        );
        throw new Error('Optimizely SDK not available');
      }

      this.provider = createInstance({
        datafile: this.config.clientKey,
        userProfileService: {
          lookup: () => null,
          save: () => {},
        },
      });

      // Set up flag change listener
      this.provider.notificationCenter.addNotificationListener(
        'OPTIMIZELY_CONFIG_UPDATE',
        () => {
          // Re-fetch all flags when config updates
          this.fetchOptimizelyFlags();
        }
      );

      // Get all flag values
      this.fetchOptimizelyFlags();
    } catch (error) {
      console.warn('Failed to connect to Optimizely:', error);
      throw error;
    }
  }

  private fetchOptimizelyFlags(): void {
    if (!this.provider || !this.config.user?.key) return;

    // Get all feature flags
    const user = this.provider.createUserContext(this.config.user.key);
    const allFlags = this.provider.getOptimizelyConfig()?.featuresMap || {};

    Object.entries(allFlags).forEach(([key, feature]: [string, any]) => {
      const decision = user.decide(key);
      this.setFlag(key, decision.enabled);
    });
  }

  private notifyUpdateCallbacks(): void {
    this.updateCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.warn('Error in feature flag update callback:', error);
      }
    });
  }

  /**
   * Helper method for dynamic imports that works with bundlers
   */
  private async dynamicImport(moduleName: string): Promise<any> {
    // Use a switch statement to handle different modules
    // This approach is bundler-friendly and avoids eval
    switch (moduleName) {
      case 'launchdarkly-js-client-sdk':
        try {
          // Use dynamic import with proper error handling
          return module;
        } catch {
          throw new Error(`Module ${moduleName} not available`);
        }
      case 'flagsmith':
        try {
          // Use dynamic import with proper error handling
          return module;
        } catch {
          throw new Error(`Module ${moduleName} not available`);
        }
      case '@optimizely/optimizely-sdk':
        try {
          // Use dynamic import with proper error handling
          return module;
        } catch {
          throw new Error(`Module ${moduleName} not available`);
        }
      default:
        throw new Error(`Unsupported module: ${moduleName}`);
    }
  }
}

// Export singleton instance
export const playfeature = new PlayFeature();

// Export the class for testing or custom instances
export { PlayFeature };
