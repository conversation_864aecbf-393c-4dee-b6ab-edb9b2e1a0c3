import { Injectable, OnDestroy } from '@angular/core';

export interface PerfConfig {
  enabled: boolean;
  metrics: {
    pageLoad: boolean;
    apiCalls: boolean;
    userInteractions: boolean;
    memoryUsage: boolean;
  };
  thresholds: {
    pageLoadTime: number;
    apiResponseTime: number;
    memoryLimit: number;
  };
  reporting: {
    console: boolean;
    analytics: boolean;
    realUserMonitoring: boolean;
  };
  optimization: {
    lazyLoading: boolean;
    imageOptimization: boolean;
    bundleAnalysis: boolean;
  };
}

export interface PerfMetric {
  name: string;
  value: number;
  timestamp: number;
  context?: any;
}

@Injectable({
  providedIn: 'root',
})
export class PlayPerfService implements OnDestroy {
  private config: PerfConfig;
  private metrics: PerfMetric[] = [];
  private observers: Map<string, PerformanceObserver> = new Map();

  constructor() {
    this.config = {
      enabled: true,
      metrics: {
        pageLoad: true,
        apiCalls: true,
        userInteractions: true,
        memoryUsage: false,
      },
      thresholds: {
        pageLoadTime: 3000,
        apiResponseTime: 1000,
        memoryLimit: 50,
      },
      reporting: {
        console: true,
        analytics: false,
        realUserMonitoring: false,
      },
      optimization: {
        lazyLoading: true,
        imageOptimization: true,
        bundleAnalysis: false,
      },
    };

    this.initializeObservers();
  }

  setConfig(config: Partial<PerfConfig>): void {
    this.config = { ...this.config, ...config };
  }

  private initializeObservers(): void {
    if (!this.config.enabled) return;

    if (this.config.metrics.pageLoad) {
      this.observePageLoad();
    }

    if (this.config.metrics.userInteractions) {
      this.observeUserInteractions();
    }

    if (this.config.metrics.memoryUsage) {
      this.observeMemoryUsage();
    }
  }

  private observePageLoad(): void {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver(list => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming;
          this.recordPageLoadMetrics(navEntry);
        }
      });
    });

    observer.observe({ entryTypes: ['navigation'] });
    this.observers.set('navigation', observer);
  }

  private observeUserInteractions(): void {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver(list => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (entry.entryType === 'interaction') {
          this.recordInteractionMetric(entry as PerformanceEntry);
        }
      });
    });

    observer.observe({ entryTypes: ['interaction'] });
    this.observers.set('interaction', observer);
  }

  private observeMemoryUsage(): void {
    if (!('memory' in performance)) return;

    setInterval(() => {
      const memory = (performance as any).memory;
      if (memory) {
        this.recordMemoryMetric(memory);
      }
    }, 30000); // Check every 30 seconds
  }

  private recordPageLoadMetrics(navEntry: PerformanceNavigationTiming): void {
    const metrics = {
      domContentLoaded:
        navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
      loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
      firstPaint: this.getFirstPaint(),
      firstContentfulPaint: this.getFirstContentfulPaint(),
    };

    Object.entries(metrics).forEach(([name, value]) => {
      this.addMetric(name, value);

      if (value > this.config.thresholds.pageLoadTime) {
        this.reportSlowPageLoad(name, value);
      }
    });
  }

  private recordInteractionMetric(entry: PerformanceEntry): void {
    this.addMetric('interaction', entry.duration);
  }

  private recordMemoryMetric(memory: any): void {
    const usedMB = memory.usedJSHeapSize / 1024 / 1024;
    this.addMetric('memoryUsage', usedMB);

    if (usedMB > this.config.thresholds.memoryLimit) {
      this.reportHighMemoryUsage(usedMB);
    }
  }

  private getFirstPaint(): number {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : 0;
  }

  private getFirstContentfulPaint(): number {
    const paintEntries = performance.getEntriesByType('paint');
    const firstContentfulPaint = paintEntries.find(
      entry => entry.name === 'first-contentful-paint'
    );
    return firstContentfulPaint ? firstContentfulPaint.startTime : 0;
  }

  addMetric(name: string, value: number, context?: any): void {
    if (!this.config.enabled) return;

    const metric: PerfMetric = {
      name,
      value,
      timestamp: Date.now(),
      context,
    };

    this.metrics.push(metric);
    this.reportMetric(metric);
  }

  measureApiCall(url: string, startTime: number, endTime: number): void {
    if (!this.config.metrics.apiCalls) return;

    const duration = endTime - startTime;
    this.addMetric('apiCall', duration, { url });

    if (duration > this.config.thresholds.apiResponseTime) {
      this.reportSlowApiCall(url, duration);
    }
  }

  private reportMetric(metric: PerfMetric): void {
    if (this.config.reporting.console) {
      console.log(
        `[Play+ Performance] ${metric.name}: ${metric.value}ms`,
        metric.context
      );
    }

    if (this.config.reporting.analytics) {
      this.sendToAnalytics(metric);
    }

    if (this.config.reporting.realUserMonitoring) {
      this.sendToRUM(metric);
    }
  }

  private reportSlowPageLoad(metricName: string, value: number): void {
    console.warn(
      `[Play+ Performance] Slow page load detected: ${metricName} took ${value}ms`
    );
  }

  private reportSlowApiCall(url: string, duration: number): void {
    console.warn(
      `[Play+ Performance] Slow API call detected: ${url} took ${duration}ms`
    );
  }

  private reportHighMemoryUsage(usage: number): void {
    console.warn(
      `[Play+ Performance] High memory usage detected: ${usage.toFixed(2)}MB`
    );
  }

  private sendToAnalytics(metric: PerfMetric): void {
    // Implementation for sending to analytics service
    console.log('Sending to analytics:', metric);
  }

  private sendToRUM(metric: PerfMetric): void {
    // Implementation for sending to RUM service
    console.log('Sending to RUM:', metric);
  }

  // Public methods for performance management
  getMetrics(name?: string): PerfMetric[] {
    if (name) {
      return this.metrics.filter(metric => metric.name === name);
    }
    return [...this.metrics];
  }

  clearMetrics(): void {
    this.metrics = [];
  }

  exportMetrics(): string {
    return JSON.stringify(this.metrics, null, 2);
  }

  getAverageMetric(name: string): number {
    const metrics = this.getMetrics(name);
    if (metrics.length === 0) return 0;

    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0);
    return sum / metrics.length;
  }

  // Cleanup
  ngOnDestroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }
}
