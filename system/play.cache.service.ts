import { Injectable } from '@angular/core';

export interface CacheConfig {
  enabled: boolean;
  storage: {
    type: 'localStorage' | 'sessionStorage';
    prefix: string;
    expiration: number;
  };
  memory: {
    maxSize: number;
    ttl: number;
  };
  api: {
    cacheResponses: boolean;
    cacheTime: number;
  };
}

interface CacheItem {
  value: any;
  timestamp: number;
  ttl: number;
}

@Injectable({
  providedIn: 'root',
})
export class PlayCacheService {
  private config: CacheConfig;
  private memoryCache = new Map<string, CacheItem>();

  constructor() {
    this.config = {
      enabled: true,
      storage: {
        type: 'localStorage',
        prefix: 'play_cache_',
        expiration: 3600000,
      },
      memory: {
        maxSize: 100,
        ttl: 300000,
      },
      api: {
        cacheResponses: true,
        cacheTime: 300000,
      },
    };
  }

  setConfig(config: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...config };
  }

  set(key: string, value: any, ttl?: number): void {
    if (!this.config.enabled) return;

    const cacheKey = `${this.config.storage.prefix}${key}`;
    const item: CacheItem = {
      value,
      timestamp: Date.now(),
      ttl: ttl || this.config.storage.expiration,
    };

    // Store in memory cache
    if (this.memoryCache.size >= this.config.memory.maxSize) {
      const firstKey = this.memoryCache.keys().next().value;
      if (firstKey) {
        this.memoryCache.delete(firstKey);
      }
    }
    this.memoryCache.set(cacheKey, item);

    // Store in localStorage
    try {
      localStorage.setItem(cacheKey, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to store in localStorage:', error);
    }
  }

  get<T>(key: string): T | null {
    if (!this.config.enabled) return null;

    const cacheKey = `${this.config.storage.prefix}${key}`;

    // Check memory cache first
    const memoryItem = this.memoryCache.get(cacheKey);
    if (memoryItem && !this.isExpired(memoryItem)) {
      return memoryItem.value;
    }

    // Check localStorage
    try {
      const stored = localStorage.getItem(cacheKey);
      if (stored) {
        const item: CacheItem = JSON.parse(stored);
        if (!this.isExpired(item)) {
          // Update memory cache
          this.memoryCache.set(cacheKey, item);
          return item.value;
        } else {
          this.remove(key);
        }
      }
    } catch (error) {
      console.warn('Failed to retrieve from localStorage:', error);
    }

    return null;
  }

  remove(key: string): void {
    const cacheKey = `${this.config.storage.prefix}${key}`;

    this.memoryCache.delete(cacheKey);

    try {
      localStorage.removeItem(cacheKey);
    } catch (error) {
      console.warn('Failed to remove from localStorage:', error);
    }
  }

  clear(): void {
    this.memoryCache.clear();

    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.config.storage.prefix)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('Failed to clear localStorage:', error);
    }
  }

  private isExpired(item: CacheItem): boolean {
    return Date.now() - item.timestamp > item.ttl;
  }

  // API cache methods
  cacheApiResponse(url: string, response: any): void {
    if (!this.config.api.cacheResponses) return;
    this.set(`api_${url}`, response, this.config.api.cacheTime);
  }

  getCachedApiResponse<T>(url: string): T | null {
    if (!this.config.api.cacheResponses) return null;
    return this.get<T>(`api_${url}`);
  }
}
