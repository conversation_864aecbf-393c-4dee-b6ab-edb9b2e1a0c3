import { PlayLogService } from '../log/log.service';
import { ErrorContext } from '../play.error';

export class PlayErrorService {
  constructor(private logService: PlayLogService) {}

  handleError(error: Error, context?: ErrorContext): void {
    // Log the error with context
    this.logService.error('Error occurred', {
      message: error.message,
      stack: error.stack,
      context,
    });

    // In a real application, you might want to:
    // - Send to error reporting service (Sentry, etc.)
    // - Show user-friendly error messages
    // - Track error metrics
    // - Handle specific error types differently
  }

  createError(message: string, context?: ErrorContext): Error {
    const error = new Error(message);
    this.handleError(error, context);
    return error;
  }

  handleApiError(error: Error, url?: string): void {
    this.handleError(error, {
      context: 'API',
      url,
    });
  }

  handleComponentError(error: Error, component: string): void {
    this.handleError(error, {
      context: 'Component',
      component,
    });
  }
}
