import { Injectable } from '@angular/core';

export interface A11yConfig {
  enabled: boolean;
  ariaLabels: {
    required: boolean;
    descriptions: boolean;
  };
  focusManagement: {
    trapFocus: boolean;
    restoreFocus: boolean;
  };
  colorContrast: {
    minimumRatio: number;
    largeTextRatio: number;
  };
  keyboardNavigation: {
    enabled: boolean;
    skipLinks: boolean;
  };
}

@Injectable({
  providedIn: 'root',
})
export class PlayA11yService {
  private config: A11yConfig;

  constructor() {
    this.config = {
      enabled: true,
      ariaLabels: {
        required: true,
        descriptions: true,
      },
      focusManagement: {
        trapFocus: true,
        restoreFocus: true,
      },
      colorContrast: {
        minimumRatio: 4.5,
        largeTextRatio: 3.0,
      },
      keyboardNavigation: {
        enabled: true,
        skipLinks: true,
      },
    };
  }

  setConfig(config: Partial<A11yConfig>): void {
    this.config = { ...this.config, ...config };
  }

  isEnabled(): boolean {
    return this.config.enabled;
  }

  addAriaLabel(element: HTMLElement, label: string): void {
    if (this.config.ariaLabels.required) {
      element.setAttribute('aria-label', label);
    }
  }

  addAriaDescription(element: HTMLElement, description: string): void {
    if (this.config.ariaLabels.descriptions) {
      element.setAttribute('aria-describedby', description);
    }
  }

  trapFocus(container: HTMLElement): void {
    if (!this.config.focusManagement.trapFocus) return;

    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[
      focusableElements.length - 1
    ] as HTMLElement;

    container.addEventListener('keydown', e => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
    });
  }

  restoreFocus(): void {
    if (this.config.focusManagement.restoreFocus) {
      // Implementation for restoring focus to previously focused element
    }
  }

  checkColorContrast(foreground: string, background: string): number {
    // Implementation for color contrast calculation
    return 4.5; // Placeholder
  }

  addSkipLink(): void {
    if (!this.config.keyboardNavigation.skipLinks) return;

    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
      position: absolute;
      top: -40px;
      left: 6px;
      background: #000;
      color: #fff;
      padding: 8px;
      text-decoration: none;
      z-index: 1000;
    `;

    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '6px';
    });

    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-40px';
    });

    document.body.insertBefore(skipLink, document.body.firstChild);
  }
}
