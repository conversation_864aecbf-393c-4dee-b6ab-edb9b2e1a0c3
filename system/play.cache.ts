/**
 * Play+ Caching & Storage Helper
 *
 * A smart and resilient wrapper around the browser's localStorage API.
 * Provides safe, intuitive caching for non-sensitive data with TTL support,
 * key scoping, and reactivity across tabs.
 */

export interface CacheOptions {
  ttl?: number; // Time-to-Live in seconds
}

export interface CacheItem<T = any> {
  value: T;
  timestamp: number;
  ttl?: number;
}

export interface CacheConfig {
  keyPrefix: string;
  defaultTtl: number | null;
}

// Default configuration
const DEFAULT_CONFIG: CacheConfig = {
  keyPrefix: 'playcache',
  defaultTtl: null,
};

// Event listeners for reactivity
const listeners = new Map<string, Set<(value: any) => void>>();

class PlayCache {
  private config: CacheConfig;
  private storage: Storage;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.storage = this.getStorage();
  }

  /**
   * Stores a serializable value in the cache, with optional TTL (in seconds).
   */
  set<T>(key: string, value: T, options: CacheOptions = {}): void {
    try {
      const prefixedKey = this.getPrefixedKey(key);
      const item: CacheItem<T> = {
        value,
        timestamp: Date.now(),
        ttl: options.ttl || this.config.defaultTtl || undefined,
      };

      this.storage.setItem(prefixedKey, JSON.stringify(item));
      this.notifyListeners(key, value);
    } catch (error) {
      console.warn('Failed to set cache item:', { key, error });
    }
  }

  /**
   * Retrieves a value from the cache. Returns null if key doesn't exist or has expired.
   */
  get<T>(key: string): T | null {
    try {
      const prefixedKey = this.getPrefixedKey(key);
      const itemData = this.storage.getItem(prefixedKey);

      if (!itemData) {
        return null;
      }

      const item: CacheItem<T> = JSON.parse(itemData);

      // Check if item has expired
      if (item.ttl && this.isExpired(item)) {
        this.remove(key);
        return null;
      }

      return item.value;
    } catch (error) {
      console.warn('Failed to get cache item:', { key, error });
      return null;
    }
  }

  /**
   * Deletes a specific item from the cache by its key.
   */
  remove(key: string): void {
    try {
      const prefixedKey = this.getPrefixedKey(key);
      this.storage.removeItem(prefixedKey);
      this.notifyListeners(key, null);
    } catch (error) {
      console.warn('Failed to remove cache item:', { key, error });
    }
  }

  /**
   * Removes all items from the cache that have the Play+ key prefix.
   */
  clear(): void {
    try {
      const keysToRemove: string[] = [];

      // Find all keys with our prefix
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i);
        if (key && key.startsWith(this.config.keyPrefix)) {
          keysToRemove.push(key);
        }
      }

      // Remove all prefixed keys
      keysToRemove.forEach(key => {
        const originalKey = key.replace(`${this.config.keyPrefix}:`, '');
        this.storage.removeItem(key);
        this.notifyListeners(originalKey, null);
      });
    } catch (error) {
      console.warn('Failed to clear cache:', { error });
    }
  }

  /**
   * Subscribes to changes for a specific key. Callback is triggered on updates (even across tabs).
   */
  on(key: string, callback: (newValue: any) => void): void {
    if (!listeners.has(key)) {
      listeners.set(key, new Set());
    }
    listeners.get(key)!.add(callback);

    // Also listen to storage events from other tabs (only in browser)
    if (typeof window !== 'undefined') {
      window.addEventListener('storage', this.handleStorageEvent);
    }
  }

  /**
   * Unsubscribes a callback for a specific key to prevent memory leaks.
   */
  off(key: string, callback: (newValue: any) => void): void {
    const keyListeners = listeners.get(key);
    if (keyListeners) {
      keyListeners.delete(callback);
      if (keyListeners.size === 0) {
        listeners.delete(key);
      }
    }

    // Remove storage event listener if no more listeners (only in browser)
    if (listeners.size === 0 && typeof window !== 'undefined') {
      window.removeEventListener('storage', this.handleStorageEvent);
    }
  }

  /**
   * Checks if a key exists and is not expired.
   */
  has(key: string): boolean {
    return this.get(key) !== null;
  }

  /**
   * Gets all keys that match a pattern (supports wildcards).
   */
  keys(pattern?: string): string[] {
    const keys: string[] = [];
    const prefix = this.config.keyPrefix;

    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i);
      if (key && key.startsWith(prefix)) {
        const originalKey = key.replace(`${prefix}:`, '');
        if (!pattern || this.matchesPattern(originalKey, pattern)) {
          keys.push(originalKey);
        }
      }
    }

    return keys;
  }

  /**
   * Gets the size of cached data in bytes.
   */
  getSize(): number {
    let size = 0;
    const prefix = this.config.keyPrefix;

    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i);
      if (key && key.startsWith(prefix)) {
        const value = this.storage.getItem(key);
        if (value) {
          size += new Blob([key, value]).size;
        }
      }
    }

    return size;
  }

  /**
   * Cleans up expired items from the cache.
   */
  cleanup(): void {
    const keys = this.keys();
    keys.forEach(key => {
      // This will automatically remove expired items
      this.get(key);
    });
  }

  // Private helper methods

  private getStorage(): Storage {
    // Check if we're in a browser environment
    if (typeof window !== 'undefined' && window.localStorage) {
      return window.localStorage;
    }

    // Fallback for SSR or environments without localStorage
    return {
      length: 0,
      clear: () => {},
      getItem: () => null,
      key: () => null,
      removeItem: () => {},
      setItem: () => {},
      [Symbol.iterator]: function* () {},
    } as Storage;
  }

  private getPrefixedKey(key: string): string {
    return `${this.config.keyPrefix}:${key}`;
  }

  private isExpired(item: CacheItem): boolean {
    if (!item.ttl) return false;
    const now = Date.now();
    return now - item.timestamp > item.ttl * 1000;
  }

  private notifyListeners(key: string, value: any): void {
    const keyListeners = listeners.get(key);
    if (keyListeners) {
      keyListeners.forEach(callback => {
        try {
          callback(value);
        } catch (error) {
          console.warn('Error in cache listener:', { key, error });
        }
      });
    }
  }

  private handleStorageEvent = (event: StorageEvent): void => {
    if (event.key && event.key.startsWith(this.config.keyPrefix)) {
      const originalKey = event.key.replace(`${this.config.keyPrefix}:`, '');
      let value = null;

      if (event.newValue) {
        try {
          const item = JSON.parse(event.newValue);
          if (!item.ttl || !this.isExpired(item)) {
            value = item.value;
          }
        } catch (error) {
          console.warn('Failed to parse storage event:', { error });
        }
      }

      this.notifyListeners(originalKey, value);
    }
  };

  private matchesPattern(key: string, pattern: string): boolean {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return regex.test(key);
  }
}

// Export singleton instance
export const playcache = new PlayCache();

// Export the class for testing or custom instances
export { PlayCache };
