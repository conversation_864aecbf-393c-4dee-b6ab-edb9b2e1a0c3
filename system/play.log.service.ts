import { Injectable } from '@angular/core';

export interface LogConfig {
  enabled: boolean;
  level: 'error' | 'warn' | 'info' | 'debug';
  outputs: {
    console: boolean;
    file: boolean;
    remote: boolean;
  };
  format: {
    timestamp: boolean;
    level: boolean;
    context: boolean;
    stackTrace: boolean;
  };
  filters: {
    excludePatterns: string[];
    includePatterns: string[];
  };
  retention: {
    maxEntries: number;
    maxAge: number;
  };
}

export interface LogEntry {
  message: string;
  level: 'error' | 'warn' | 'info' | 'debug';
  timestamp: number;
  context?: any;
  stackTrace?: string;
}

@Injectable({
  providedIn: 'root',
})
export class PlayLogService {
  private config: LogConfig;
  private logBuffer: LogEntry[] = [];
  private readonly levels = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3,
  };

  constructor() {
    this.config = {
      enabled: true,
      level: 'info',
      outputs: {
        console: true,
        file: false,
        remote: false,
      },
      format: {
        timestamp: true,
        level: true,
        context: true,
        stackTrace: true,
      },
      filters: {
        excludePatterns: ['password', 'token', 'secret'],
        includePatterns: [],
      },
      retention: {
        maxEntries: 1000,
        maxAge: 86400000,
      },
    };

    this.cleanupOldLogs();
  }

  setConfig(config: Partial<LogConfig>): void {
    this.config = { ...this.config, ...config };
  }

  error(message: string, context?: any): void {
    this.log('error', message, context);
  }

  warn(message: string, context?: any): void {
    this.log('warn', message, context);
  }

  info(message: string, context?: any): void {
    this.log('info', message, context);
  }

  debug(message: string, context?: any): void {
    this.log('debug', message, context);
  }

  private log(
    level: 'error' | 'warn' | 'info' | 'debug',
    message: string,
    context?: any
  ): void {
    if (
      !this.config.enabled ||
      this.levels[level] > this.levels[this.config.level]
    ) {
      return;
    }

    if (this.shouldFilter(message)) {
      return;
    }

    const entry: LogEntry = {
      message: this.sanitizeMessage(message),
      level,
      timestamp: Date.now(),
      context: this.sanitizeContext(context),
      stackTrace: this.config.format.stackTrace ? new Error().stack : undefined,
    };

    this.addToBuffer(entry);
    this.outputLog(entry);
  }

  private shouldFilter(message: string): boolean {
    const lowerMessage = message.toLowerCase();

    // Check exclude patterns
    if (
      this.config.filters.excludePatterns.some(pattern =>
        lowerMessage.includes(pattern.toLowerCase())
      )
    ) {
      return true;
    }

    // Check include patterns (if any are specified)
    if (this.config.filters.includePatterns.length > 0) {
      return !this.config.filters.includePatterns.some(pattern =>
        lowerMessage.includes(pattern.toLowerCase())
      );
    }

    return false;
  }

  private sanitizeMessage(message: string): string {
    // Remove sensitive information from message
    let sanitized = message;
    this.config.filters.excludePatterns.forEach(pattern => {
      const regex = new RegExp(pattern, 'gi');
      sanitized = sanitized.replace(regex, '[REDACTED]');
    });
    return sanitized;
  }

  private sanitizeContext(context: any): any {
    if (!context) return context;

    const sanitized = { ...context };
    this.config.filters.excludePatterns.forEach(pattern => {
      if (sanitized[pattern]) {
        sanitized[pattern] = '[REDACTED]';
      }
    });
    return sanitized;
  }

  private addToBuffer(entry: LogEntry): void {
    this.logBuffer.push(entry);

    // Maintain buffer size
    if (this.logBuffer.length > this.config.retention.maxEntries) {
      this.logBuffer.shift();
    }
  }

  private outputLog(entry: LogEntry): void {
    const formattedMessage = this.formatMessage(entry);

    if (this.config.outputs.console) {
      console[entry.level](formattedMessage, entry.context);
    }

    if (this.config.outputs.file) {
      this.writeToFile(formattedMessage);
    }

    if (this.config.outputs.remote) {
      this.sendToRemote(formattedMessage);
    }
  }

  private formatMessage(entry: LogEntry): string {
    let message = '';

    if (this.config.format.timestamp) {
      message += `[${new Date(entry.timestamp).toISOString()}] `;
    }

    if (this.config.format.level) {
      message += `[${entry.level.toUpperCase()}] `;
    }

    message += entry.message;

    return message;
  }

  private writeToFile(message: string): void {
    // Implementation for writing to file
    console.log('Writing to file:', message);
  }

  private sendToRemote(message: string): void {
    // Implementation for sending to remote logging service
    console.log('Sending to remote:', message);
  }

  private cleanupOldLogs(): void {
    const cutoff = Date.now() - this.config.retention.maxAge;
    this.logBuffer = this.logBuffer.filter(entry => entry.timestamp > cutoff);
  }

  // Public methods for log management
  getLogs(level?: 'error' | 'warn' | 'info' | 'debug'): LogEntry[] {
    if (level) {
      return this.logBuffer.filter(entry => entry.level === level);
    }
    return [...this.logBuffer];
  }

  clearLogs(): void {
    this.logBuffer = [];
  }

  exportLogs(): string {
    return JSON.stringify(this.logBuffer, null, 2);
  }
}
