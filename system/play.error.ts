/**
 * Play+ Error Handling Helper: `playerror`
 *
 * A comprehensive error handling system that automates the entire lifecycle of an error:
 * capturing, classifying, reporting, and presenting feedback. Provides graceful degradation
 * and recovery paths to build trust and demonstrate resilience.
 */

export interface ErrorConfig {
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  piiFields: string[];
  darkMode: boolean;
  retryBudget: {
    maxRetries: number;
  };
  defaultMessages: {
    server: string;
    notFound: string;
    auth: string;
    forbidden: string;
    timeout: string;
    network: string;
    rateLimit: string;
  };
}

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  timestamp?: number;
  [key: string]: any;
}

export interface ErrorAction {
  label: string;
  action: () => void;
  primary?: boolean;
}

export interface IPlayError {
  report(error: Error, context?: ErrorContext): void;
  reportCritical(error: Error, context?: ErrorContext): void;
  batch(errors: Error[]): void;
  showToast(message: string, type?: 'error' | 'warn' | 'info'): void;
  showModal(title: string, message: string, actions?: ErrorAction[]): void;
  showInline(
    containerId: string,
    message: string,
    type?: 'error' | 'warn'
  ): void;
  getFriendlyMessage(error: Error): string;
  withContext(context: ErrorContext): IPlayError;
}

// Default configuration
const DEFAULT_CONFIG: ErrorConfig = {
  logLevel: 'error',
  piiFields: ['password', 'email', 'token', 'apiKey', 'secret'],
  darkMode: true,
  retryBudget: {
    maxRetries: 5,
  },
  defaultMessages: {
    server: "We're fixing it!",
    notFound: 'Item not found.',
    auth: 'Please log in again.',
    forbidden: 'You lack permission.',
    timeout: 'Connection timed out.',
    network: 'Check your internet connection.',
    rateLimit: 'Too many requests.',
  },
};

class PlayError implements IPlayError {
  private config: ErrorConfig;
  private errorQueue: Array<{
    error: Error;
    context?: ErrorContext;
    critical: boolean;
  }> = [];
  private baseContext: ErrorContext = {};
  private isInitialized = false;

  constructor(config: Partial<ErrorConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initialize();
  }

  /**
   * Initialize the error handling system
   */
  private initialize(): void {
    if (this.isInitialized) return;

    // Set up global error handlers
    this.setupGlobalHandlers();

    // Set up unhandled promise rejection handler
    this.setupPromiseRejectionHandler();

    this.isInitialized = true;
  }

  /**
   * Report a non-critical error to the logging service with context
   */
  report(error: Error, context?: ErrorContext): void {
    this.queueError(error, context, false);
  }

  /**
   * Report a critical error, potentially triggering high-priority alerts
   */
  reportCritical(error: Error, context?: ErrorContext): void {
    this.queueError(error, context, true);
  }

  /**
   * Efficiently sends multiple queued errors in a single network request
   */
  batch(errors: Error[]): void {
    errors.forEach(error => this.report(error));
  }

  /**
   * Displays a non-blocking error notification
   */
  showToast(message: string, type: 'error' | 'warn' | 'info' = 'error'): void {
    this.createToast(message, type);
  }

  /**
   * Displays a blocking modal for critical errors
   */
  showModal(title: string, message: string, actions?: ErrorAction[]): void {
    this.createModal(title, message, actions);
  }

  /**
   * Renders a contextual error message directly into a specified container in the UI
   */
  showInline(
    containerId: string,
    message: string,
    type: 'error' | 'warn' = 'error'
  ): void {
    this.createInlineMessage(containerId, message, type);
  }

  /**
   * Translates a technical error object into a pre-configured, user-safe message
   */
  getFriendlyMessage(error: Error): string {
    return this.translateError(error);
  }

  /**
   * Returns a new playerror instance with pre-scoped context for cleaner reporting
   */
  withContext(context: ErrorContext): IPlayError {
    const scopedError = new PlayError(this.config);
    scopedError.baseContext = { ...this.baseContext, ...context };
    return scopedError;
  }

  /**
   * Set the base context for all error reports
   */
  setBaseContext(context: ErrorContext): void {
    this.baseContext = { ...this.baseContext, ...context };
  }

  /**
   * Get current configuration
   */
  getConfig(): ErrorConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ErrorConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Clear the error queue
   */
  clearQueue(): void {
    this.errorQueue = [];
  }

  /**
   * Get current error queue
   */
  getQueue(): Array<{
    error: Error;
    context?: ErrorContext;
    critical: boolean;
  }> {
    return [...this.errorQueue];
  }

  // Private methods

  private setupGlobalHandlers(): void {
    if (typeof window === 'undefined') return;
    // Handle uncaught exceptions
    window.addEventListener('error', event => {
      this.reportCritical(event.error || new Error(event.message), {
        source: 'GlobalErrorHandler',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', event => {
      this.reportCritical(
        event.reason instanceof Error
          ? event.reason
          : new Error(String(event.reason)),
        {
          source: 'UnhandledPromiseRejection',
          promise: event.promise,
        }
      );
    });
  }

  private setupPromiseRejectionHandler(): void {
    if (typeof window !== 'undefined' && 'PromiseRejectionEvent' in window) {
      window.addEventListener('rejectionhandled', event => {
        this.report(
          event.reason instanceof Error
            ? event.reason
            : new Error(String(event.reason)),
          {
            source: 'RejectionHandled',
            promise: event.promise,
          }
        );
      });
    }
  }

  private queueError(
    error: Error,
    context?: ErrorContext,
    critical: boolean = false
  ): void {
    const enrichedContext = this.enrichContext(context);
    const redactedContext = this.redactPII(enrichedContext);

    // Add to queue
    this.errorQueue.push({
      error,
      context: redactedContext,
      critical,
    });

    // Log immediately for critical errors
    if (critical) {
      this.logError(error, redactedContext, critical);
    }

    // Process queue (in a real implementation, this would batch and send to a logging service)
    this.processQueue();
  }

  private enrichContext(context?: ErrorContext): ErrorContext {
    const isBrowser =
      typeof window !== 'undefined' && typeof navigator !== 'undefined';
    const enriched: ErrorContext = {
      ...this.baseContext,
      ...context,
      timestamp: Date.now(),
      userAgent: isBrowser ? navigator.userAgent : '',
      url: isBrowser ? window.location.href : '',
      sessionId: this.getSessionId(),
    };

    return enriched;
  }

  private redactPII(context: ErrorContext): ErrorContext {
    const redacted = { ...context };

    this.config.piiFields.forEach(field => {
      if (redacted[field]) {
        redacted[field] = '[REDACTED]';
      }
    });

    // Recursively redact PII from nested objects
    Object.keys(redacted).forEach(key => {
      if (typeof redacted[key] === 'object' && redacted[key] !== null) {
        redacted[key] = this.redactPII(redacted[key] as ErrorContext);
      }
    });

    return redacted;
  }

  private logError(
    error: Error,
    context: ErrorContext,
    critical: boolean
  ): void {
    const logData = {
      message: error.message,
      stack: error.stack,
      name: error.name,
      context,
      critical,
      timestamp: new Date().toISOString(),
    };

    // In a real implementation, this would send to a logging service
    if (critical || this.config.logLevel === 'error') {
      console.error('PlayError Critical:', logData);
    } else {
      console.warn('PlayError:', logData);
    }
  }

  private processQueue(): void {
    // In a real implementation, this would batch errors and send them to a logging service
    // For now, we'll just log them
    if (this.errorQueue.length > 0) {
      console.log('Processing error queue:', this.errorQueue.length, 'errors');
      this.errorQueue = [];
    }
  }

  private translateError(error: Error): string {
    const errorMessage = error.message.toLowerCase();
    const errorName = error.name.toLowerCase();

    // Check for specific error types
    if (errorMessage.includes('404') || errorMessage.includes('not found')) {
      return this.config.defaultMessages.notFound;
    }

    if (errorMessage.includes('401') || errorMessage.includes('unauthorized')) {
      return this.config.defaultMessages.auth;
    }

    if (errorMessage.includes('403') || errorMessage.includes('forbidden')) {
      return this.config.defaultMessages.forbidden;
    }

    if (errorMessage.includes('429') || errorMessage.includes('rate limit')) {
      return this.config.defaultMessages.rateLimit;
    }

    if (
      errorMessage.includes('timeout') ||
      errorMessage.includes('timed out')
    ) {
      return this.config.defaultMessages.timeout;
    }

    if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
      return this.config.defaultMessages.network;
    }

    if (errorMessage.includes('500') || errorMessage.includes('server error')) {
      return this.config.defaultMessages.server;
    }

    // Default to a generic message
    return 'Something went wrong. Please try again.';
  }

  private createToast(message: string, type: 'error' | 'warn' | 'info'): void {
    if (typeof document === 'undefined') return;
    // Create a toast notification
    const toast = document.createElement('div');
    toast.className = `play-error-toast play-error-toast-${type}`;
    toast.innerHTML = `
      <div class="play-error-toast-content">
        <span class="play-error-toast-message">${this.escapeHtml(message)}</span>
        <button class="play-error-toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;

    // Add styles
    this.addToastStyles();

    // Add to DOM
    document.body.appendChild(toast);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (toast.parentElement) {
        toast.remove();
      }
    }, 5000);
  }

  private createModal(
    title: string,
    message: string,
    actions?: ErrorAction[]
  ): void {
    if (typeof document === 'undefined') return;
    // Create a modal dialog
    const modal = document.createElement('div');
    modal.className = 'play-error-modal-overlay';
    modal.innerHTML = `
      <div class="play-error-modal">
        <div class="play-error-modal-header">
          <h2>${this.escapeHtml(title)}</h2>
          <button class="play-error-modal-close" onclick="this.closest('.play-error-modal-overlay').remove()">×</button>
        </div>
        <div class="play-error-modal-body">
          <p>${this.escapeHtml(message)}</p>
        </div>
        <div class="play-error-modal-footer">
          ${
            actions
              ? actions
                  .map(
                    action =>
                      `<button class="play-error-modal-btn ${action.primary ? 'primary' : ''}" onclick="this.closest('.play-error-modal-overlay').remove(); (${action.action.toString()})()">${this.escapeHtml(action.label)}</button>`
                  )
                  .join('')
              : '<button class="play-error-modal-btn primary" onclick="this.closest(\'.play-error-modal-overlay\').remove()">OK</button>'
          }
        </div>
      </div>
    `;

    // Add styles
    this.addModalStyles();

    // Add to DOM
    document.body.appendChild(modal);
  }

  private createInlineMessage(
    containerId: string,
    message: string,
    type: 'error' | 'warn'
  ): void {
    if (typeof document === 'undefined') return;
    const container = document.getElementById(containerId);
    if (!container) {
      console.warn(
        `Container with id '${containerId}' not found for inline error message`
      );
      return;
    }

    // Create inline message
    const messageElement = document.createElement('div');
    messageElement.className = `play-error-inline play-error-inline-${type}`;
    messageElement.innerHTML = `
      <span class="play-error-inline-message">${this.escapeHtml(message)}</span>
    `;

    // Add styles
    this.addInlineStyles();

    // Add to container
    container.appendChild(messageElement);
  }

  private addToastStyles(): void {
    if (typeof document === 'undefined') return;
    if (document.getElementById('play-error-toast-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'play-error-toast-styles';
    styles.textContent = `
      .play-error-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        animation: slideIn 0.3s ease-out;
      }
      .play-error-toast-error { background: #fee; border: 1px solid #fcc; color: #c33; }
      .play-error-toast-warn { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
      .play-error-toast-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
      .play-error-toast-content {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        gap: 12px;
      }
      .play-error-toast-message { flex: 1; }
      .play-error-toast-close {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        opacity: 0.7;
      }
      .play-error-toast-close:hover { opacity: 1; }
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `;
    document.head.appendChild(styles);
  }

  private addModalStyles(): void {
    if (typeof document === 'undefined') return;
    if (document.getElementById('play-error-modal-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'play-error-modal-styles';
    styles.textContent = `
      .play-error-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      }
      .play-error-modal {
        background: white;
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow: hidden;
      }
      .play-error-modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 24px;
        border-bottom: 1px solid #eee;
      }
      .play-error-modal-header h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }
      .play-error-modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        opacity: 0.7;
      }
      .play-error-modal-close:hover { opacity: 1; }
      .play-error-modal-body {
        padding: 24px;
        line-height: 1.5;
      }
      .play-error-modal-footer {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        padding: 20px 24px;
        border-top: 1px solid #eee;
      }
      .play-error-modal-btn {
        padding: 8px 16px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
        cursor: pointer;
        font-size: 14px;
      }
      .play-error-modal-btn:hover { background: #f8f9fa; }
      .play-error-modal-btn.primary {
        background: #007bff;
        color: white;
        border-color: #007bff;
      }
      .play-error-modal-btn.primary:hover { background: #0056b3; }
    `;
    document.head.appendChild(styles);
  }

  private addInlineStyles(): void {
    if (typeof document === 'undefined') return;
    if (document.getElementById('play-error-inline-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'play-error-inline-styles';
    styles.textContent = `
      .play-error-inline {
        padding: 8px 12px;
        border-radius: 4px;
        margin: 8px 0;
        font-size: 14px;
      }
      .play-error-inline-error {
        background: #fee;
        border: 1px solid #fcc;
        color: #c33;
      }
      .play-error-inline-warn {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
      }
      .play-error-inline-message {
        display: block;
      }
    `;
    document.head.appendChild(styles);
  }

  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  private getSessionId(): string {
    // Generate or retrieve session ID
    let sessionId = sessionStorage.getItem('play-error-session-id');
    if (!sessionId) {
      sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('play-error-session-id', sessionId);
    }
    return sessionId;
  }
}

// Export singleton instance
export const playerror = new PlayError();

// Export the class for testing or custom instances
export { PlayError };
