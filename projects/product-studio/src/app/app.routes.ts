import { Routes } from '@angular/router';
import { authGuard, CallbackComponent } from '@shared';
import { LoginComponent } from '@shared';

export const routes: Routes = [
  { path: 'login', component: LoginComponent },
  { path: 'callback', component: CallbackComponent },

  {
    path: '',
    canActivate: [authGuard],
    loadChildren: () =>
      import('./product/product-studio-routes').then(
        m => m.PRODUCT_STUDIO_ROUTES
      ),
  },
  {
    path: '**',
    redirectTo: '/',
  },
];
