/* eslint-disable @typescript-eslint/no-unused-vars */
import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject } from 'rxjs';

import { AppStateService } from './app-state.service';

@Injectable({
  providedIn: 'root',
})
export class NavigationGuardService implements OnDestroy {
  private readonly showConfirmationModalSubject = new BehaviorSubject<boolean>(
    false
  );
  public readonly showConfirmationModal$ =
    this.showConfirmationModalSubject.asObservable();

  private pendingNavigation: (() => void) | null = null;

  constructor(
    private appStateService: AppStateService,
    private router: Router
  ) {
    this.setupBeforeUnloadListener();
  }

  ngOnDestroy(): void {
    this.removeBeforeUnloadListener();
  }

  /**
   * Setup beforeunload event listener to prevent accidental navigation
   */
  private setupBeforeUnloadListener(): void {
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
  }

  /**
   * Remove beforeunload event listener
   */
  private removeBeforeUnloadListener(): void {
    window.removeEventListener(
      'beforeunload',
      this.handleBeforeUnload.bind(this)
    );
  }

  /**
   * Handle beforeunload event - DISABLED to allow seamless page reloads
   * Page reloads (F5, Ctrl+R, browser refresh) should happen without any confirmation
   * The hybrid state management handles data persistence automatically
   */
  private handleBeforeUnload(_event: BeforeUnloadEvent): void {
    // DISABLED: No beforeunload confirmation to allow seamless page reloads
    // The custom navigation confirmation modal handles route-based navigation
    // Page reloads should always be allowed for proper data persistence
    return;
  }

  /**
   * Check if confirmation should be shown based on app state
   */
  private shouldShowConfirmation(): boolean {
    return this.appStateService.hasStoredData();
  }

  /**
   * Show the custom confirmation modal for internal navigation
   */
  public requestNavigation(navigationCallback?: () => void): void {
    if (this.shouldShowConfirmation()) {
      this.pendingNavigation = navigationCallback || null;
      this.showConfirmationModalSubject.next(true);
    } else {
      // No data to lose, allow navigation immediately
      if (navigationCallback) {
        navigationCallback();
      }
    }
  }

  /**
   * Handle user canceling the navigation
   */
  public cancelNavigation(): void {
    this.showConfirmationModalSubject.next(false);
    this.pendingNavigation = null;
  }

  /**
   * Handle user confirming the navigation
   */
  public confirmNavigation(): void {
    this.showConfirmationModalSubject.next(false);

    // Clear all app state data
    this.appStateService.resetState();

    // Execute pending navigation if any, otherwise redirect to brainstormer prompt
    if (this.pendingNavigation) {
      this.pendingNavigation();
      this.pendingNavigation = null;
    } else {
      // Default redirect to brainstormer prompt page
      this.router.navigate(['/brainstormer']);
    }

    // Navigation confirmed and completed
  }

  /**
   * Temporarily disable navigation confirmation (useful for programmatic navigation)
   * Note: Since beforeunload is disabled, these methods are no longer needed
   * but kept for backward compatibility
   */
  public disableConfirmation(): void {
    // No-op: beforeunload confirmation is disabled
  }

  /**
   * Re-enable navigation confirmation
   * Note: Since beforeunload is disabled, these methods are no longer needed
   * but kept for backward compatibility
   */
  public enableConfirmation(): void {
    // No-op: beforeunload confirmation is disabled
  }

  /**
   * Check if there's currently a pending navigation
   */
  public hasPendingNavigation(): boolean {
    return this.pendingNavigation !== null;
  }

  /**
   * Get current confirmation modal visibility state
   */
  public get isModalVisible(): boolean {
    return this.showConfirmationModalSubject.value;
  }
}
