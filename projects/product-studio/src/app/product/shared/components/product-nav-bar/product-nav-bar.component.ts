/* eslint-disable import/order */
/* eslint-disable prettier/prettier */

import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import {
  ButtonComponent,
  HeaderComponent,
  HeadingComponent,
} from '@awe/play-comp-library';
import { CommonModule } from '@angular/common';
import { UserProfile } from '../../models/user-profile.model';
import { ThemeServiceService } from '../../services/auth-config-service/theme-service.service';
import { AuthService, TokenStorageService, CentralizedRedirectService } from '@shared';
// import { ToastService } from 'projects/experience-studio/src/app/shared/services/toast.service';
import { Router } from '@angular/router';


@Component({
  selector: 'app-product-nav-bar',
  standalone: true,
  imports: [HeaderComponent, CommonModule, ButtonComponent, HeadingComponent],
  templateUrl: './product-nav-bar.component.html',
  styleUrl: './product-nav-bar.component.scss',
})
export class ProductNavBarComponent implements OnInit {
  @Input() userProfile?: UserProfile;
  showProfileMenu = false;
  themeToggleIcon = '';
  themeMenuIcon = '';
  logoSrc = '';
  dashboardNavigation = '';
  constructor(
    private themeService: ThemeServiceService,
    private authService: AuthService,
    private cdr: ChangeDetectorRef,
    private tokenStorageService: TokenStorageService,
    private router: Router,
    private centralizedRedirectService: CentralizedRedirectService,
    // private toastService: ToastService,
  ) {}

  public redirectUrl = '';

  /**
   * Returns the capitalized initials of the user's display name (e.g., "Sairam Ugge" -> "SU")
   */
  getInitials(): string {
    const name = this.getDisplayName();
    if (!name || name === 'User') {
      // Try to get actual user name from token storage
      const actualName = this.tokenStorageService.getDaName();
      if (actualName && actualName !== 'User') {
        return actualName
          .split(' ')
          .filter((word: string) => word.length > 0)
          .map((word: string) => word[0].toUpperCase())
          .join('');
      }
      return 'U'; // Fallback to 'U' instead of empty string
    }
    return name
      .split(' ')
      .filter((word: string) => word.length > 0)
      .map((word: string) => word[0].toUpperCase())
      .join('');
  }

  private updateNavigationTitle() {
    const currentRoute = this.router.url;
    switch (true) {
      case currentRoute.includes('brainstormer'):
        this.dashboardNavigation = '|  Brainstormer';
        break;
      case currentRoute.includes('user-story'):
        this.dashboardNavigation = '|  User Story Creation';
        break;
      case currentRoute.includes('mini-interventions'):
        this.dashboardNavigation = '|  Mini Interventions';
        break;
      default:
        this.dashboardNavigation = '';
    }
    this.cdr.detectChanges();
  }

  ngOnInit(): void {
    const authConfig = this.authService.getAuthConfig();
    this.redirectUrl = authConfig?.redirectUrl || window.location.origin;
    this.updateThemeAssets();
  
    // Subscribe to route changes to update navigation title
    this.router.events.subscribe(() => {
      this.updateNavigationTitle();
    });
    new MutationObserver(() => this.updateThemeAssets()).observe(
      document.body,
      {
        attributes: true,
        attributeFilter: ['class'],
      },
    );
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.updateThemeAssets();
  }

  toggleProfileMenu(): void {
    this.showProfileMenu = !this.showProfileMenu;
  }

   

  // Handle logout
  onLogout() {
    if (this.tokenStorageService.getLoginType() === 'basic') {
      this.authService.basicLogout().subscribe({
        next: () => {
          // Use centralized redirect service
          this.centralizedRedirectService.redirectToMarketingLogin();
          this.tokenStorageService.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('Basic logout failed:', error);
          // Still try to navigate to login even if logout fails
          this.centralizedRedirectService.redirectToMarketingLogin();
        },
      });
    } else {
      this.authService.logout(this.redirectUrl).subscribe({
        next: () => {
          this.tokenStorageService.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('SSO logout failed:', error);
        },
      });
    }
  }
  onLogin(): void {
    try {
      // this.logger.info('Triggering login...');
      const currentUrl = window.location.origin;
      this.authService.loginSSO(currentUrl).subscribe({
        // next: () => this.logger.debug('Login successful'),
        // error: (error) => this.logger.error('Login failed:', error),
      });
    } catch (error) {
      // this.logger.error('Error during login:', error);
      // this.toastService.error('Login failed');
    }
  }

  getProfileImage(): string {
    return this.userProfile?.photoUrl || `svgs/user-avatar.svg`;
  }

  getDisplayName(): string {
    return this.tokenStorageService.getDaName() ||  this.userProfile?.displayName || 'User Profile';
  }

  getEmail(): string {
    return this.tokenStorageService.getDaUsername() || this.userProfile?.userPrincipalName || '<EMAIL>';
  }

  private updateThemeAssets(): void {
    const currentTheme = this.themeService.getCurrentTheme();
    this.logoSrc = `svgs/product-studio-logo-${currentTheme}.svg`;
    this.themeToggleIcon = `svgs/theme-toggle-${currentTheme}.svg`;
    this.themeMenuIcon = `svgs/menu-${currentTheme}.svg`;
  }
}
