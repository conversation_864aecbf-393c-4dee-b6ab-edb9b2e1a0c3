<!-- eslint-disable @angular-eslint/template/click-events-have-key-events -->
<awe-header theme="light" class="py-1">
  <div left-content class="mt-4 d-flex align-items-center">
     <!-- <img [src]="logoSrc" class="px-2" alt="Ascendion Logo" loading="eager" /> -->
     <!-- Uncomment the following line to use the logo text instead of the image -->
    <img [src]="logoSrc" class="px-2" alt="Product-Studio Logo" loading="eager" />
     <!-- <awe-heading variant="h6" type="bold" class="logo-text">PRODUCT STUDIO </awe-heading> -->
     <span class="sub-title"> {{dashboardNavigation}}</span>
  </div>
  <div right-content class="gap-4 d-flex align-items-center">
    <div class="d-flex justify-content-center align-items-center cursor-pointer">
      <img [src]="themeMenuIcon" alt="Theme Menu" loading="lazy" />
    </div>
    <div
      class="d-flex justify-content-center align-items-center cursor-pointer"
      (click)="toggleTheme()">
      <img [src]="themeToggleIcon" alt="Toggle Theme" loading="lazy" />
    </div>
    <div>
      <div
        class="cursor-pointer profile-icon d-flex justify-content-center align-items-center"
        (click)="toggleProfileMenu()">
        {{ getInitials() }}
      </div>
      <div
        *ngIf="showProfileMenu"
        class="p-3 card profile-flyout"
        role="menu"
        aria-label="Profile menu">
        <div class="d-flex align-items-center gap-2">
          <div class="profile-avatar d-flex justify-content-center align-items-center">
            {{ getInitials() }}
          </div>
          <div>
            <awe-heading variant="s2" type="bold" class="profile-name">{{ getDisplayName() }}</awe-heading>
            <awe-heading variant="s2" type="regular" class="profile-email">{{ getEmail() }}</awe-heading>
          </div>
        </div>
        <hr />
        <awe-button
          label="Sign Out"
          variant="primary"
          animation="ripple"
          size="medium"
          class="gradient-button"
          (click)="onLogout()"></awe-button>
      </div>
    </div>
  </div>
</awe-header>
