@use "../../../../../../src/assets/styles/_mixins.scss" as mixins;

#prompt-content-container {
  margin-top: 7%;
  #suggetions-contianer {
    .invisible {
      visibility: hidden !important;
    }
  }

  #divider-section-container {
    padding-top: 4rem;
    .divider-image {
      width: 100%;
      @media (max-width: 767px) {
        max-width: 90%;
      }
    }
  }
}
:host ::ng-deep .prompt-bar.light .prompt-text {
  color: var(--prompt-bar-text-color) !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  // height: 90px
}
:host ::ng-deep .prompt-bar.dark .prompt-text {
  color: var(--prompt-bar-text-color) !important;
}

.disabled-prompt-bar {
  @include mixins.disabledProperty(0.7, none, not-allowed);
  ::ng-deep textarea {
    @include mixins.disabledProperty(0.7, none, not-allowed);
  }
}

.custom-content {
  awe-file-attach-pill.disabled {
    @include mixins.disabledProperty(0.5, none, not-allowed);
  }
  .selected-files {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 8px;

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: var(--preview-page-bg-color) !important;
      border: 1px solid var(--code-viewer-search-border) !important;
      border-radius: 8px;
      padding: 8px 12px;
      max-width: 220px;
      min-width: 180px;
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--primary-color, #007bff);
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
        transform: translateY(-1px);
      }

      .file-preview {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        flex: 1;
        min-width: 0;

        .file-preview-image {
          width: 24px;
          height: 24px;
          object-fit: cover;
          border-radius: 4px;
          transition: transform 0.2s ease;
          flex-shrink: 0;

          &.previewable-image:hover {
            transform: scale(1.1);
          }
        }

        .file-name {
          font-size: 12px;
          font-weight: 500;
          color: var(--text-primary-color);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      // Document-specific styling
      &.document-item {
        .file-info {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
          min-width: 0;

          .file-icon {
            flex-shrink: 0;
            width: 20px;
            height: 20px;
          }

          .file-name {
            font-size: 12px;
            font-weight: 500;
            color: var(--text-primary-color);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex: 1;
          }

          .file-size {
            font-size: 10px;
            color: var(--text-secondary-color);
            opacity: 0.7;
            flex-shrink: 0;
            margin-left: 4px;
          }
        }
      }

      awe-icons {
        flex-shrink: 0;
        cursor: pointer;
        transition: all 0.2s ease;
        opacity: 0.6;

        &:hover {
          opacity: 1;
          transform: scale(1.1);
        }
      }
    }
  }

  .enhance-icons {
    justify-content: flex-end;
    flex-shrink: 0;
    awe-icons {
      cursor: pointer;
      transition: all 0.3s ease-in-out;
      font-size: 24px;
      color: var(--icon-enabled-color) !important;
      &.disabled {
        @include mixins.disabledProperty(0.4, none, not-allowed);
        color: var(--icon-disabled-color) !important;
      }
    }
    .loading-spinner {
      min-width: auto;
      width: 24px;
      height: 24px;
      .spinner {
        width: 20px;
        height: 20px;
        border: 2px solid var(--icon-enabled-color);
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

.preview-overlay {
  animation: fadeIn 0.3s ease-in-out;
  .preview-content {
    background: var(--preview-page-bg-color);
    border-radius: 8px;
    animation: scaleIn 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
    .preview-header .preview-title {
      color: var(--pill-text-color);
    }
  }
}

:host ::ng-deep button.secondary {
  border: 1px solid #C3C3DB;
  color: var(--button-text-color);
  border-radius: 0.5rem;
  background: var(--prompt-bar-suggestion-button-bg);
  &:hover {
    border-color: var(--prompt-bar-hover-color);
  }
}

:host {
  ::ng-deep {
    .prompt-bar {
      &.dark {
        @include mixins.prompt-bar-style(
          none,
          breatheDark,
          rgba(255, 92, 163, 1)
        );
      }
      &.light {
        @include mixins.prompt-bar-style(
          var(--Light---60, rgba(240, 240, 245, 0.5)),
          breatheLight,
          rgba(66, 68, 194, 0.8)
        );
      }
    }
  }
}

:host ::ng-deep .prompt-bar.disabled {
  @include mixins.disabledProperty(0.7, none, not-allowed);
  border-color: var(--prompt-bar-disabled-border, #ccc) !important;
  background-color: var(
    --prompt-bar-disabled-bg,
    rgba(240, 240, 245, 0.3)
  ) !important;
  animation: none !important;
}

:host ::ng-deep .prompt-bar.disabled .prompt-text {
  color: var(--prompt-bar-disabled-text, #999) !important;
}

::ng-deep .file-attach-pill {
  min-width: unset !important;
  transition: all 0.4s ease-in-out !important;
  background: none !important;
  cursor: not-allowed  !important;
  awe-icons path {
    fill: var(--pill-text-color) !important;
  }
}

::ng-deep.icon-pill {
  min-width: unset !important;
  transition: all 0.4s ease-in-out !important;
}

::ng-deep awe-icons path {
  fill: var(--pill-text-color) !important;
}

::ng-deep {
  .file-attach-pill .icon-wrapper {
    margin-top: 8px !important;
  }
  .file-attach-pill .text,
  .icon-pill .text,
  .dropdown-item .dropdown-item-text {
    color: var(--pill-text-color) !important;
  }
  .dropdown-item .dropdown-item-text {
    width: max-content !important;
  }
  .dropdown-item {
    width: 100% !important;
  }
  .dropdown.show {
    background-color: var(--preview-page-bg-color) !important;
  }
  .dropdown-item:hover,
  .dropdown-item:focus {
    background-color: transparent !important;
  }
}
.attached-files {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;

  .file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: var(--preview-page-bg-color, #f8f9fa);
    border: 1px solid var(--code-viewer-search-border, #e0e0e0);
    border-radius: 6px;
    font-size: 12px;
    max-width: 200px;

    .file-info {
      display: flex;
      align-items: center;
      gap: 6px;
      flex: 1;
      min-width: 0;

      .file-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: var(--text-color, #333);
      }
    }

    .remove-file {
      background: none;
      border: none;
      cursor: pointer;
      padding: 2px;
      border-radius: 2px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.tools-container {
  .pills-container {
    awe-file-attach-pill.disabled {
      @include mixins.disabledProperty(0.5, none, not-allowed);
    }
  }
}



// Enhanced loading container for contextual text
.enhance-loading-container {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: auto;

  .enhance-loading-text {
    font-size: 12px;
    font-weight: 500;
    color: var(--color-text-secondary, #666);
    white-space: nowrap;
    animation: enhance-text-fade 1.5s ease-in-out infinite;
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  .loading-spinner {
    min-width: auto;
    width: 24px;
    height: 24px;

    .spinner {
      width: 20px;
      height: 20px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

// Animation for enhance loading text
@keyframes enhance-text-fade {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.4; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Dark theme support for enhanced loading text
:host-context(.dark-theme) {
  .enhance-icons {
    .enhance-loading-container {
      .enhance-loading-text {
        color: var(--color-text-secondary-dark, #ccc);
      }
    }
  }
}

:host ::ng-deep button:disabled {
  /* background: var(--button-primary-bg-disable); */
  color: var(--button-primary-label-disable);
  cursor: not-allowed;
}

// Modal styles
.modal-content-body {
  text-align: center;
  padding: 1rem;

  .modal-icon {
    margin-bottom: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;

    &.error {
      color: #dc3545;
    }

    &.success {
      color: #28a745;
    }

    &.warning {
      color: #ffc107;
    }

    &.info {
      color: #17a2b8;
    }

    awe-icons {
      font-size: 48px;
    }
  }

  .modal-message {
    margin-bottom: 1.5rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #333;
  }

  .modal-actions {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
  }
}

// Modal type specific styles
:host ::ng-deep {
  .modal-error {
    border-left: 4px solid #dc3545;
  }

  .modal-success {
    border-left: 4px solid #28a745;
  }

  .modal-warning {
    border-left: 4px solid #ffc107;
  }

  .modal-info {
    border-left: 4px solid #17a2b8;
  }
}