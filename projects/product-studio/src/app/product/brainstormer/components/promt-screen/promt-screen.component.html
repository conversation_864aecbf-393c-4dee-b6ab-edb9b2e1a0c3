<!-- eslint-disable @angular-eslint/template/click-events-have-key-events -->
<div id="prompt-content-container">
  <!-- First Screen: Prompt Content Container -->
  <section id="prompt-section-container">
    <div class="container-fluid">
      <app-hero-section-header
        [headerTitle]="'Hi ' + userName"
        [headerDescription]="'From understanding your product to the roadmap'"
        [subHeading]="'What do you want to brainstorm today?'"
      >
      </app-hero-section-header>

      <div id="prompt-bar-container" class="container-fluid px-2">
        <div class="row mt-2 d-flex align-items-center justify-content-center">
          <div class="col-12 d-flex justify-content-center">
            <awe-prompt-bar
              id="prompt-bar"
              [staticText]="'Start creating with AI:'"
              (enterPressed)="handleEnterPressed()"
              [(textValue)]="currentPrompt"
              (ngModelChange)="handlePromptChange($event)"
              (iconClicked)="handleIconClick($event)"
              [animatedTexts]="animatedTexts"
              (fileRemoved)="onFileRemoved($event)"
              (filePreviewClosed)="closePreview()"
              (enterPressed)="handleEnterPressed()"
              appTypewriterPlaceholder
            >
              <div class="custom-content">
                <!-- Selected Files Display -->
                <div class="mb-2 selected-files" *ngIf="selectedFiles.length > 0 || attachedDocuments.length > 0">
                  <!-- Image Files -->
                  <div class="py-1 px-2 file-item" *ngFor="let file of selectedFiles">
                    <div class="file-preview" (click)="showFilePreview(file)">
                      <img
                        class="mr-2 file-preview-image previewable-image"
                        [src]="file.url"
                        [alt]="file.name"
                        title="Preview image"
                        loading="lazy"
                        decoding="async"
                      />
                      <span class="file-name">{{ truncateFileName(file.name) }}</span>
                    </div>
                    <awe-icons
                      iconName="awe_close"
                      (click)="removeFile(file.id)"
                      class="pt-2"
                      role="button"
                      tabindex="0"
                      iconColor="blue"
                      [attr.aria-label]="'Remove ' + file.name"
                    ></awe-icons>
                  </div>

                  <!-- Document Files -->
                  <div class="py-1 px-2 file-item document-item" *ngFor="let doc of attachedDocuments; let i = index">
                    <div class="file-info">
                      <awe-icons [iconName]="getFileIcon(doc.type)" [iconColor]="'neutralIcon'" class="file-icon"></awe-icons>
                      <span class="file-name">{{ truncateFileName(doc.name) }}</span>
                      <span class="file-size">({{ (doc.size / 1024 / 1024).toFixed(1) }}MB)</span>
                    </div>
                    <awe-icons
                      iconName="awe_close"
                      (click)="removeDocument(i)"
                      class="pt-2"
                      role="button"
                      tabindex="0"
                      iconColor="blue"
                      [attr.aria-label]="'Remove ' + doc.name"
                    ></awe-icons>
                  </div>
                </div>



                <div
                  class="d-flex align-items-center justify-content-between pe-2 tools-container"
                >
                  <div
                    class="d-flex align-items-center gap-2 me-3 pills-container"
                  >
                    <awe-file-attach-pill
                      [options]="fileOptions"
                      [currentTheme]="theme"
                      (optionSelected)="onFileOptionSelected($event)"
                      [class.disabled]="isFileAttachDisabled || isEnhancing || isGenerating"
                      [mainText]="'Attach File'"
                      [mainIcon]="'awe_attach_file'">
                    </awe-file-attach-pill>
                  </div>
                  <div class="d-flex align-items-center gap-3 enhance-icons">
                    <!-- Show loading spinner when enhancing, otherwise show enhance icon -->
                    <ng-container *ngIf="!isEnhancing; else loadingEnhance">
                      <awe-icons
                        iconName="awe_enhance"
                        (click)="handleEnhanceText()"
                        role="button"
                        tabindex="0"
                        [attr.aria-label]="'Enhance'"
                        [class.disabled]="
                          !currentPrompt ||
                          currentPrompt.trim() === '' ||
                          enhanceClickCount >= maxEnhanceClicks
                        "
                        [style.cursor]="
                          !currentPrompt ||
                          currentPrompt.trim() === '' ||
                          enhanceClickCount >= maxEnhanceClicks
                            ? 'not-allowed'
                            : 'pointer'
                        "
                        [color]="getIconColor()"
                      ></awe-icons>
                    </ng-container>
                    <ng-template #loadingEnhance>
                      <div class="enhance-loading-container">
                        <!-- Contextual loading text to the LEFT of spinner -->
                        <div class="enhance-loading-text">
                          {{ getEnhanceLoadingText() }}
                        </div>
                        <!-- Spinner -->
                        <div class="d-flex align-items-center justify-content-center loading-spinner">
                          <div class="spinner"></div>
                        </div>
                      </div>
                    </ng-template>
                    <awe-icons
                      iconName="awe_enhanced_send"
                      (click)="handleEnhancedSend()"
                      class="cursor-pointer"
                      role="button"
                      tabindex="0"
                      [attr.aria-label]="'Enhanced Send'"
                      [class.disabled]="
                        !currentPrompt ||
                        currentPrompt.trim() === '' ||
                        isEnhancing
                      "
                      [style.cursor]="
                        !currentPrompt ||
                        currentPrompt.trim() === '' ||
                        isEnhancing
                          ? 'not-allowed'
                          : 'pointer'
                      "
                      [color]="getIconColor()"
                    ></awe-icons>
                  </div>
                </div>
              </div>
            </awe-prompt-bar>
          </div>
        </div>
      </div>
      <!-- Suggestion Buttons -->
      <div id="suggetions-contianer" class="px-2">
        <div
          class="row mt-5 d-flex justify-content-center align-items-center flex-wrap g-2"
          [class.invisible]="currentPrompt && currentPrompt.trim() !== ''"
        >
          <div
            class="col-12 col-sm-auto text-center mb-2"
            *ngFor="let button of buttons"
          >
            <awe-button
            id="suggestion-button"
              [label]="button.label"
              [variant]="button.variant"
              [loadingType]="button.loadingType"
              [animation]="button.buttonAnimation"
              (click)="handleSuggestionClick(button.label)"
            >
            </awe-button>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

<!-- File Preview Overlay -->
<div (click)="closePreview()" class="preview-overlay" *ngIf="showPreview && previewFile">
  <div class="preview-content">
    <div class="p-3 preview-header">
      <div class="preview-title">{{ previewFile.name }}</div>
      <awe-icons
        iconName="awe_close"
        (click)="closePreview()"
        role="button"
        tabindex="0"
        [attr.aria-label]="'Close preview'"
        [color]="getIconColor()"
      ></awe-icons>
    </div>
    <div class="p-3 preview-body">
      <img [src]="previewFile.url" [alt]="previewFile.name" />
    </div>
  </div>
</div>

<!-- Loading Component -->
<app-loading
  [isVisible]="showLoading"
  [title]="loadingTitle"
  [message]="loadingMessage"
  [showSteps]="true"
  [steps]="loadingSteps"
  [currentStep]="currentLoadingStep"
>
</app-loading>

<!-- Error Modal -->
<awe-modal
  [isOpen]="isErrorModalOpen"
  [title]="errorModalTitle"
  [modalClass]="getModalClass()"
  [showFooter]="false"
  [width]="'400px'"
  (closed)="closeErrorModal()"
>
  <div class="modal-content-body">
    <div class="modal-icon" [ngClass]="errorModalType">
      <awe-icons
        [iconName]="errorModalType === 'error' ? 'awe_error' : errorModalType === 'success' ? 'awe_check_circle' : errorModalType === 'warning' ? 'awe_warning' : 'awe_info'"
      ></awe-icons>
    </div>
    <p class="modal-message">{{ errorModalMessage }}</p>
    <div class="modal-actions">
      <awe-button
        variant="primary"
        size="medium"
        (click)="closeErrorModal()"
      >
        OK
      </awe-button>
    </div>
  </div>
</awe-modal>
