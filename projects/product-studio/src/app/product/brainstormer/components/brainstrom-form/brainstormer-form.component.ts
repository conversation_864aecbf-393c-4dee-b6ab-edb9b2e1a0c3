/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-empty */
import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';

import {
  ProjectDetailsResponse,
  PipelineStartResponse,
} from '../../interfaces/pipeline-api.interface';
import { ProductPipelineService } from '../../services/product-pipeline.service';
import { AweCardComponent } from '../awe-card/awe-card.component';
import { AweModalComponent } from '../awe-modal/awe-modal.component';

@Component({
  selector: 'app-brainstormer-form',
  standalone: true,
  imports: [CommonModule, FormsModule, AweCardComponent, AweModalComponent],
  templateUrl: './brainstormer-form.component.html',
  styleUrls: ['./brainstormer-form.component.scss'],
})
export class BrainstormerFormComponent implements OnInit, OnDestroy {
  sparkleIcon: string = 'icons/sparkle-icon.svg';
  addIcon: string = 'icons/add-icon.svg';
  projectName = '';
  userIdea = '';
  industry = '';

  // Loading states
  isSubmitting = false;

  // Subscriptions for cleanup
  private subscriptions: Subscription[] = [];

  // Modal properties
  isErrorModalOpen = false;
  errorModalTitle = 'Error';
  errorModalMessage = '';
  errorModalType: 'error' | 'success' | 'info' | 'warning' = 'error';

  // Dynamic form data based on API response
  industries: string[] = [];
  userGroups: { label: string; selected: boolean }[] = [];

  // Pre-populated data from identify details API
  private projectDetails: ProjectDetailsResponse | null = null;

  constructor(
    private router: Router,
    private pipelineService: ProductPipelineService
  ) {}

  ngOnInit(): void {
    // Initialize default form data first
    this.initializeDefaultFormData();

    // Subscribe to original prompt from the service
    this.pipelineService.originalPrompt$.subscribe(prompt => {
      if (prompt) {
        this.userIdea = prompt; // Set the original prompt as user_idea
      }
    });

    // Subscribe to project details from the service
    this.pipelineService.projectDetails$.subscribe(details => {
      if (details) {
        this.projectDetails = details;
        this.populateFormWithDetails();
      } else {
      }
    });

    // Also check navigation state as fallback (for direct navigation)
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras?.state?.['projectDetails']) {
      const navDetails = navigation.extras.state[
        'projectDetails'
      ] as ProjectDetailsResponse;
      this.projectDetails = navDetails;
      // Store in service for persistence
      this.pipelineService.setProjectDetails(navDetails);
      this.populateFormWithDetails();
    }

    // Subscribe to project name changes from pipeline state to keep form in sync
    this.subscriptions.push(
      this.pipelineService.getProjectName$().subscribe(projectName => {
        if (projectName && projectName !== this.projectName) {
          this.projectName = projectName;
        }
      })
    );
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    // this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Handle project name changes and sync to pipeline state
   */
  onProjectNameChange(): void {
    if (this.projectName && this.projectName.trim()) {
      this.pipelineService.setProjectName(this.projectName.trim());
    }
  }

  /**
   * Populate form fields with data from identify details API
   */
  private populateFormWithDetails(): void {
    if (!this.projectDetails) {
      console.warn('⚠️ No project details available for form population');
      this.initializeDefaultFormData();
      return;
    }

    // Pre-populate basic form fields
    this.projectName = this.projectDetails.project_name || '';

    // Use original prompt if available, otherwise use API response user_idea
    const originalPrompt = this.pipelineService.getOriginalPrompt();
    if (originalPrompt) {
      this.userIdea = originalPrompt;
    } else {
      this.userIdea = this.projectDetails.user_idea || '';
    }

    this.industry = this.projectDetails.industry || '';

    // Project details loaded

    // Dynamically populate industries dropdown
    this.populateIndustriesDropdown();

    // Dynamically populate user groups
    this.populateUserGroups();

    // Form data populated
  }

  /**
   * Initialize default form data when no project details are available
   */
  private initializeDefaultFormData(): void {
    // Default industries list
    this.industries = [
      'Banking',
      'Financial Services',
      'Healthcare',
      'Lifesciences',
      'Manufacturing',
      'Mortgage',
      'Retail',
      'HI-Tech',
      'Travel and Hospitality',
      'Education',
      'Other',
    ];

    // Default user groups
    this.userGroups = [
      { label: 'Business Users', selected: true },
      { label: 'End Consumers', selected: true },
      { label: 'Technical Users', selected: false },
      { label: 'Creative Users', selected: false },
    ];
  }

  /**
   * Dynamically populate industries dropdown based on API response
   */
  private populateIndustriesDropdown(): void {
    // Base industries list
    const baseIndustries = [
      'Banking',
      'Financial Services',
      'Healthcare',
      'Lifesciences',
      'Manufacturing',
      'Mortgage',
      'Retail',
      'HI-Tech',
      'Travel and Hospitality',
      'Education',
      'Other',
    ];

    // If API response has an industry not in base list, add it
    if (
      this.projectDetails?.industry &&
      !baseIndustries.includes(this.projectDetails.industry)
    ) {
      this.industries = [...baseIndustries, this.projectDetails.industry];
    } else {
      this.industries = baseIndustries;
    }
  }

  /**
   * Dynamically populate user groups based on API response
   */
  private populateUserGroups(): void {
    if (
      this.projectDetails?.user_groups &&
      this.projectDetails.user_groups.length > 0
    ) {
      // Create user groups directly from API response
      this.userGroups = this.projectDetails.user_groups.map(group => ({
        label: group,
        selected: true, // All API groups are pre-selected
      }));

      // Add common additional options that users might want to select
      const additionalGroups = [
        'Technical Users',
        'Creative Users',
        'Administrative Users',
      ];
      additionalGroups.forEach(additionalGroup => {
        // Only add if not already present
        if (
          !this.userGroups.some(
            group => group.label.toLowerCase() === additionalGroup.toLowerCase()
          )
        ) {
          this.userGroups.push({
            label: additionalGroup,
            selected: false,
          });
        }
      });
    } else {
      this.initializeDefaultFormData();
    }
  }

  toggleGroup(index: number) {
    if (index >= 0 && index < this.userGroups.length) {
      this.userGroups[index].selected = !this.userGroups[index].selected;
    }
  }

  /**
   * Add a custom user group (for future enhancement)
   */
  addCustomUserGroup(groupName: string): void {
    if (groupName && groupName.trim()) {
      const trimmedName = groupName.trim();
      // Check if group already exists
      if (
        !this.userGroups.some(
          group => group.label.toLowerCase() === trimmedName.toLowerCase()
        )
      ) {
        this.userGroups.push({
          label: trimmedName,
          selected: true,
        });
      }
    }
  }

  /**
   * Handle form submission - start the pipeline with complete project details
   */
  onSubmitRecipeForm() {
    if (this.isSubmitting) return;

    const selectedUsers = this.userGroups
      .filter(group => group.selected)
      .map(group => group.label);

    // Enhanced validation for dynamic form
    const validationErrors: string[] = [];

    if (!this.projectName.trim()) {
      validationErrors.push('Project name is required');
    }

    if (!this.industry) {
      validationErrors.push('Industry selection is required');
    }

    if (selectedUsers.length === 0) {
      validationErrors.push('At least one user group must be selected');
    }

    if (validationErrors.length > 0) {
      this.showErrorModal(
        'Validation Error',
        `Please fix the following issues:\n• ${validationErrors.join('\n• ')}`,
        'warning'
      );
      return;
    }

    this.isSubmitting = true;

    // Prepare the pipeline start request with complete details
    // Use original prompt (from getIdentifyDetails) as the primary user_idea
    const originalPrompt = this.pipelineService.getOriginalPrompt();
    const pipelineRequest = {
      user_idea: originalPrompt || this.userIdea.trim(),
      project_name: this.projectName || 'Untitled Project',
      user_groups: selectedUsers || ['Business Users'],
      industry: this.industry || 'Other',
    };

    // Call the pipeline start API with complete details
    this.pipelineService.startPipelineWithDetails(pipelineRequest).subscribe({
      next: (response: PipelineStartResponse) => {
        this.isSubmitting = false;

        // Clear project details since they're no longer needed
        // this.pipelineService.clearProjectDetails();

        // Navigate to brainstorming flow
        this.router.navigate(['brainstorming'], {
          relativeTo:
            this.router.routerState.root.firstChild?.firstChild?.firstChild,
        });
      },
      error: error => {
        console.error('Error starting pipeline:', error);
        this.isSubmitting = false;
        this.showErrorModal(
          'Session Failed',
          'Failed to start brainstorming session. Please try again.',
          'error'
        );
      },
    });
  }

  // Modal methods
  showErrorModal(
    title: string,
    message: string,
    type: 'error' | 'success' | 'info' | 'warning' = 'error'
  ): void {
    this.errorModalTitle = title;
    this.errorModalMessage = message;
    this.errorModalType = type;
    this.isErrorModalOpen = true;
  }

  closeErrorModal(): void {
    this.isErrorModalOpen = false;
  }

  getModalClass(): string {
    switch (this.errorModalType) {
      case 'error':
        return 'modal-error';
      case 'success':
        return 'modal-success';
      case 'warning':
        return 'modal-warning';
      case 'info':
      default:
        return 'modal-info';
    }
  }
}
