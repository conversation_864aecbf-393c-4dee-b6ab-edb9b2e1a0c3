<!-- eslint-disable @angular-eslint/template/click-events-have-key-events -->
<div class="custom-stepper d-flex justify-content-center align-items-center">
  <div class="stepper-container ">
    <div class="stepper-header">
      <div class="steps-wrapper">
        @for (step of steps; track step.id; let i = $index) {
          <div class="step-item"
               [class.completed]="step.state === 'completed'"
               [class.active]="step.state === 'active'"
               [class.inactive]="step.state === 'inactive'"
               [class.loading]="step.state === 'loading'"
               [class.clickable]="isStepClickable(i)"
               [class.disabled]="!isStepClickable(i)"
               (click)="onStepClick(i)">

            <!-- Step Circle -->
            <div class="step-circle">
              @if (step.state === 'completed') {
                <!-- Show checkmark for completed steps -->
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              } @else if (step.state === 'loading') {
                <!-- Show spinner for loading steps -->
                <div class="loading-spinner"></div>
              } @else if (step.state === 'active') {
                <!-- Show step number or icon for active step -->
                <span class="step-number">{{ $index + 1 }}</span>
              } @else {
                <!-- Show step number for inactive steps -->
                <span class="step-number inactive">{{ $index + 1 }}</span>
              }
            </div>

            <!-- Step Label -->
            <div class="step-label">
              <span class="step-title">{{ step.label }}</span>
            </div>

            <!-- Connector Line -->
            @if (i < steps.length - 1) {
              <div class="step-connector"
                   [class.completed]="steps[i + 1].state === 'completed' || steps[i + 1].state === 'active'">
              </div>
            }
          </div>
        }
      </div>
    </div>
  </div>
</div>