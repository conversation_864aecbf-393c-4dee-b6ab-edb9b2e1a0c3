<!-- eslint-disable @angular-eslint/template/click-events-have-key-events -->
<div class="page-container">
  <!-- Top Decorative Header Bar -->
  <!-- <div class="top-header-gradient"></div> -->

  <!-- Main Header with Navigation -->
  <header class="page-header px-3 py-2">

    <button class="btn btn-light d-flex align-items-center" (click)="goBack()">
      <span class=" mt-2 d-flex align-items-center">
        <awe-icons iconName="awe_chevron_left" iconSize="20px"></awe-icons>
      </span>
      Persona List
    </button>
    <div class="persona-filter" *ngIf="selectedPersona">
      <button class="mb-3 p-2 btn btn-light border dropdown-toggle d-flex align-items-center"
        (click)="togglePersonaSelector()">
        <img [src]="selectedPersona.avatar" class="avatar-sm me-2" />
        <span>{{ selectedPersona.name || selectedPersona.role }}</span>
      </button>
      <div *ngIf="isPersonaSelectorOpen" class="persona-selector-card justify-content-center align-items-center">
        <div class="">
          <div class="p-2">
            <button *ngFor="let p of personas" (click)="selectPersona(p.id)" class="filter-btn mb-1 p-3">
              <div class="d-flex align-items-center">
                <img [src]="p.avatar" class="avatar-md me-3" />
                <div>
                  <div class="fw-bold">{{ p.name }}</div>
                  <!-- <div class="text-muted small">{{ p.role }}</div> -->
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content Grid with Bootstrap Layout -->
  <div class="container-fluid px-4 py-4">
    <div class="row g-3">
      <!-- LEFT COLUMN: PERSONA SUMMARY CARD -->
      <div class="col-12 col-md-4 col-lg-3" *ngIf="selectedPersona">
        <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="persona-summary-card h-100">
          <!-- Header Content -->
          <div awe-card-header-content class="d-flex justify-content-between align-items-center">
            <h3 class="mb-0">Profile</h3>
            <button class="btn btn-sm btn-link p-1" (click)="openEditModal('profile', 'Profile')"
              aria-label="Edit Profile">
              <awe-icons iconName="awe_edit" iconColor="blue"></awe-icons>
            </button>
          </div>

          <!-- Body Content -->
          <div class="text-center">
            <div class="avatar-wrapper mb-3">
              <img [src]="selectedPersona.avatar" class="avatar-img rounded-circle" />
            </div>
            <h2 class="role-title h4 mb-4">{{ selectedPersona.role }}</h2>

            <div class="info-section">
              <div class="info-row d-flex align-items-center">
                <h5>Age</h5>
                <h5>{{ selectedPersona.age }}</h5>
              </div>
              <div class="info-row d-flex align-items-center ">
                <h5>Education</h5>
                <h5>{{ selectedPersona.education.slice(0, 21) }}...</h5>
              </div>
              <div class="info-row d-flex align-items-center">
                <h5>Status</h5>
                <h5>{{ selectedPersona.status }}</h5>
              </div>
              <div class="info-row d-flex align-items-center">
                <h5>Location</h5>
                <h5>{{ selectedPersona.location.slice(0, 21) }}...</h5>
              </div>
              <div class="info-row d-flex align-items-center">
                <h5>Tech Literacy</h5>
                <h5>{{ selectedPersona.techLiteracy }}</h5>
              </div>
              <div class="quote-section">
                <img [src]="colon" alt="Quote" class="quote-icon" />
                <p class="quote-text">{{ selectedPersona.quote }}</p>
              </div>

              <div class="personality-section">
                <h3 class="personality-title">Personality</h3>
                <div class="personality-tags">
                  <h5 *ngFor="let trait of selectedPersona.personality" class="tag">{{ trait }}</h5>
                </div>
              </div>
            </div>
          </div>
        </awe-card>
      </div>

      <!-- RIGHT COLUMN: DETAIL CARDS -->
      <div class="col-12 col-md-8 col-lg-9" *ngIf="selectedPersona">
        <div class="row g-3">
          <!-- Row 1: Pain Points and Motivation (2 columns) -->
          <div class="col-12 col-lg-6 p-1">
            <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="detail-card h-100">
              <!-- Header Content -->
              <div awe-card-header-content class="d-flex justify-content-between align-items-center">
                <h3 class="mb-0">Pain Points</h3>
                <button class="btn btn-sm btn-link p-1" (click)="openEditModal('painPoints', 'Pain Points')"
                  aria-label="Edit Pain Points">
                  <awe-icons iconName="three-dot-vertical" iconColor="blue"></awe-icons>
                </button>
              </div>

              <!-- Body Content -->
              <ul class="list-unstyled">
                <li *ngFor="let item of selectedPersona.painPoints" class="mb-2 d-flex align-items-start">
                  <!-- <span class="badge bg-danger me-2 mt-1">•</span> -->
                  {{ item }}
                </li>
              </ul>
            </awe-card>
          </div>

          <div class="col-12 col-lg-6 p-1">
            <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="detail-card h-100">
              <!-- Header Content -->
              <div awe-card-header-content class="d-flex justify-content-between align-items-center">
                <h3 class="mb-0">Motivation</h3>
                <button class="btn btn-sm btn-link p-1" (click)="openEditModal('motivation', 'Motivation')"
                  aria-label="Edit Motivation">
                  <awe-icons iconName="three-dot-vertical" iconColor="blue"></awe-icons>
                </button>
              </div>

              <!-- Body Content -->
              <ul class="list-unstyled">
                <li *ngFor="let item of selectedPersona.motivation" class="mb-2 d-flex align-items-start">
                  <span class="badge bg-success me-2 mt-1">•</span>
                  {{ item }}
                </li>
              </ul>
            </awe-card>
          </div>

          <!-- Row 2: Goals and Expectations (2 columns) -->
          <div class="col-12 col-lg-6 p-1">
            <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="detail-card h-100">
              <!-- Header Content -->
              <div awe-card-header-content class="d-flex justify-content-between align-items-center">
                <h3 class="mb-0">Goals</h3>
                <button class="btn btn-sm btn-link p-1" (click)="openEditModal('goals', 'Goals')"
                  aria-label="Edit Goals">
                  <awe-icons iconName="three-dot-vertical" iconColor="blue"></awe-icons>
                </button>
              </div>

              <!-- Body Content -->
              <ul class="list-unstyled">
                <li *ngFor="let item of selectedPersona.goals" class="mb-2 d-flex align-items-start">
                  <span class="badge bg-primary me-2 mt-1">•</span>
                  {{ item }}
                </li>
              </ul>
            </awe-card>
          </div>

          <div class="col-12 col-lg-6 p-1">
            <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="detail-card h-100">
              <!-- Header Content -->
              <div awe-card-header-content class="d-flex justify-content-between align-items-center">
                <h3 class="mb-0">Expectations</h3>
                <button class="btn btn-sm btn-link p-1" (click)="openEditModal('expectations', 'Expectations')"
                  aria-label="Edit Expectations">
                  <awe-icons iconName="three-dot-vertical" iconColor="blue"></awe-icons>
                </button>
              </div>

              <!-- Body Content -->
              <ul class="list-unstyled">
                <li *ngFor="let item of selectedPersona.expectations" class="mb-2 d-flex align-items-start">
                  <span class="badge bg-info me-2 mt-1">•</span>
                  {{ item }}
                </li>
              </ul>
            </awe-card>
          </div>

          <!-- Row 3: Skills and Devices (2 columns with different widths) -->
          <div class="col-12 col-lg-8 p-1">
            <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="detail-card h-100">
              <!-- Header Content -->
              <div awe-card-header-content class="d-flex justify-content-between align-items-center">
                <h3 class="mb-0">Skills</h3>
                <button class="btn btn-sm btn-link p-1" (click)="openEditModal('skills', 'Skills')"
                  aria-label="Edit Skills">
                  <awe-icons iconName="three-dot-vertical" iconColor="blue"></awe-icons>
                </button>
              </div>

              <!-- Body Content -->
              <div class="d-flex flex-column gap-3">
                <div *ngFor="let skill of selectedPersona.skills; let i = index">
                  <div class="d-flex justify-content-between align-items-center mb-1">
                    <span class="small fw-medium">{{ skill.name }}</span>
                    <span class="small text-secondary">{{ skill.level }}%</span>
                  </div>
                  <div class="progress" style="height: 8px">
                    <div class="progress-bar" [ngClass]="getProgressBarClass(i)" [style.width.%]="skill.level">
                    </div>
                  </div>
                </div>
              </div>
            </awe-card>
          </div>

          <div class="col-12 col-lg-4 p-1">
            <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="detail-card h-100">
              <!-- Header Content -->
              <div awe-card-header-content class="d-flex justify-content-between align-items-center">
                <h3 class="mb-0">Devices</h3>
                <button class="btn btn-sm btn-link p-1" (click)="openEditModal('devices', 'Devices')"
                  aria-label="Edit Devices">
                  <awe-icons iconName="three-dot-vertical" iconColor="blue"></awe-icons>
                </button>
              </div>

              <!-- Body Content -->
              <div class="d-flex justify-content-center align-items-center gap-3">
                <img *ngIf="selectedPersona.devices.includes('mobile')" [src]="MobileIcon" alt="Mobile"
                  class="device-icon" />
                <img *ngIf="selectedPersona.devices.includes('laptop')" [src]="LaptopIcon" alt="Laptop"
                  class="device-icon" />
              </div>
            </awe-card>
          </div>
        </div>
      </div>
    </div>

    <!-- Click Outside Handler -->
    <div *ngIf="openDropdownId" class="click-outside-overlay" (click)="closeAllDropdowns()"></div>
      <!-- Edit Modal -->
    <awe-modal [isOpen]="isEditModalOpen" [showHeader]="true" [showFooter]="true" [width]="'35vw'" [maxWidth]="'90vw'"
      (closed)="closeEditModal()">
      <div class="mb-2" awe-modal-header>
        <awe-heading variant="s1" type="bold">Edit {{ selectedCardForEdit?.title }}</awe-heading>
      </div>

      <div awe-modal-body>
        <div class="modal-form">
          <!-- Profile Data -->
          <div *ngIf="selectedCardForEdit?.type === 'profile'">
            <div class="profile-data row g-3">
              <div class="col-md-12 inp-container">
                <div class="label">
                  <label for="name">Name:</label>
                </div>
                <div class="input-wrapper">
                  <!-- <awe-input  label="Name:" [(ngModel)]="editData.name" placeholder="Enter name"
                    class="w-100"></awe-input> -->

                  <ava-textbox [(ngModel)]="editData.name" id="name" name="Name" placeholder="Enter name" icon
                    [required]="true" size="md">
                  </ava-textbox>
                </div>
              </div>
              <div class="col-md-12 inp-container">
                <div class="label">
                  <label for="role">Role:</label>
                </div>
                <div class="input-wrapper">
                  <!-- <awe-input  label="Role:" [(ngModel)]="editData.role" placeholder="Enter role"
                    class="w-100"></awe-input> -->
                  <ava-textbox [(ngModel)]="editData.role" id="role" name="
                    Role" placeholder="Enter role" icon [required]="true" size="md">
                  </ava-textbox>
                </div>
              </div>
              <div class="col-md-12 inp-container">
                <div class="label">
                  <label for="age">Age:</label>
                </div>
                <div class="input-wrapper">
                  <!-- <awe-input  label="Age:" [(ngModel)]="editData.age" type="number"
                    placeholder="Enter age" class="w-100"></awe-input> -->
                  <ava-textbox [(ngModel)]="editData.age" id="age" name="
                    Age" placeholder="Enter age" icon [required]="true" size="md" type="number">
                  </ava-textbox>
                </div>
              </div>
              <div class="col-md-12 inp-container">
                <div class="label">
                  <label for="education">Education:</label>
                </div>
                <div class="input-wrapper">
                  <!-- <awe-input label="Education:"  [(ngModel)]="editData.education"
                  placeholder="Enter education" class="w-100"></awe-input> -->

                  <ava-textbox [(ngModel)]="editData.education" id="education" name="Education"
                    placeholder="Enter education" icon [required]="true" size="md">
                  </ava-textbox>
                </div>
              </div>
              <div class="col-md-12 inp-container">
                <div class="label">
                  <label for="status">Status:</label>
                </div>
                <div class="input-wrapper">
                  <!-- <awe-input label="Status:"  [(ngModel)]="editData.status" placeholder="Enter status"
                    class="w-100"></awe-input> -->
                  <ava-textbox [(ngModel)]="editData.status" id="status" name="
                    Status" placeholder="Enter status" icon [required]="true" size="md">
                  </ava-textbox>
                </div>
              </div>
              <div class="col-md-12 inp-container">
                <div class="label">
                  <label for="location">Location:</label>
                </div>
                <div class="input-wrapper">
                  <!-- <awe-input label="Location:"  [(ngModel)]="editData.location"
                    placeholder="Enter location" class="w-100"></awe-input> -->
                  <ava-textbox [(ngModel)]="editData.location" id="location" name="
                    Location" placeholder="Enter location" icon [required]="true" size="md" type="text">
                  </ava-textbox>
                </div>
              </div>
              <div class="col-md-12 inp-container">
                <div class="label">
                  <label for="techLiteracy">Tech Literacy:</label>
                </div>
                <div class="input-wrapper">
                  <!-- <awe-input label="Tech Literacy:"  [(ngModel)]="editData.techLiteracy"
                    placeholder="Enter tech literacy" class="w-100"></awe-input> -->
                  <ava-textbox [(ngModel)]="editData.techLiteracy" id="techLiter
                    acy" name="Tech Literacy" placeholder="Enter tech literacy" icon [required]="true" size="md"
                    type="text">
                  </ava-textbox>
                </div>
              </div>
              <div class="col-12 inp-container">
                <div class="label">
                  <label for="quote">Quote:</label>
                </div>
                <div class="input-wrapper">
                  <!-- <awe-input label="Quote:"  [(ngModel)]="editData.quote" placeholder="Enter quote"
                    class="w-100"></awe-input> -->
                  <ava-textbox [(ngModel)]="editData.quote" id="quote" name="
                    Quote" placeholder="Enter quote" icon [required]="true" size="md" type="text">
                  </ava-textbox>
                </div>
              </div>
            </div>
          </div>

          <!-- Array Data (Pain Points, Goals, etc.) -->
          <div *ngIf="
            isArrayData(editData) &&
            selectedCardForEdit?.type !== 'skills' &&
            selectedCardForEdit?.type !== 'devices'
          ">
            <div *ngFor="let item of editData; let i = index; trackBy: trackByIndex" class="mb-3">
              <div class="gap-2">
                <!-- <awe-input  [(ngModel)]="editData[i]" type="text" [icons]="['awe_trash']" [placeholder]="
                  'Enter ' + selectedCardForEdit?.title?.toLowerCase() + ' item'
                " class="flex-grow-1" (iconClickEvent)="removeArrayItem(i)">
                </awe-input> -->
                <ava-textbox [(ngModel)]="editData[i]" id="item" name="
                Item" placeholder="Enter item" icon [required]="true" size="lg" type="text">
                  <aava-icon slot="icon-end" iconName="trash-2" [iconSize]="16" [cursor]="true"
                    (click)="removeArrayItem(i)" [disabled]="false" />
                </ava-textbox>
              </div>
            </div>
            <button (click)="addArrayItem()" class="add-new">Add new +</button>
          </div>

          <!-- Skills Data -->
          <div *ngIf="selectedCardForEdit?.type === 'skills'">
            <div *ngFor="let skill of editData; let i = index; trackBy: trackByIndex" class="mb-3">
              <label for="skillName">Skill Name:</label>
              <!-- <awe-input  [icons]="['awe_trash']" [required]="true" errorMessage="This field is required"
                [(ngModel)]="skill.name" placeholder="Skill name" (iconClickEvent)="removeArrayItem(i)" class="w-100">
              </awe-input> -->
              <ava-textbox [(ngModel)]="skill.name" id="skillName" name="
              Skill Name" placeholder="Skill name" icon [required]="true" size="md" type="text">
              </ava-textbox>
              <div class="input-wrapper">
                <awe-slider label="Level" [(ngModel)]="skill.level" mobileSize="small" tabletSize="medium"
                  desktopSize="large" touchTargetSize="44px" [showTicks]="true"
                  [customTickValues]="[0, 25, 50, 75, 100]" variant="primary" (dragStart)="onDragStart(i)"
                  (dragEnd)="onDragEnd(i)" [min]="0" [max]="100"></awe-slider>
              </div>
            </div>
            <button (click)="addArrayItem()" class="mb-3 add-new">
              Add Skills+
            </button>
          </div>

          <!-- Devices Data -->
          <div *ngIf="selectedCardForEdit?.type === 'devices'">
            <div class="mb-3">
              <label class="form-label">Available Devices</label>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" [checked]="editData.includes('mobile')"
                  (change)="toggleDevice('mobile')" id="deviceMobile" />
                <label class="form-check-label" for="deviceMobile">
                  Mobile
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" [checked]="editData.includes('laptop')"
                  (change)="toggleDevice('laptop')" id="deviceLaptop" />
                <label class="form-check-label" for="deviceLaptop">
                  Laptop
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" [checked]="editData.includes('tablet')"
                  (change)="toggleDevice('tablet')" id="deviceTablet" />
                <label class="form-check-label" for="deviceTablet">
                  Tablet
                </label>
              </div>
            </div>
          </div>

          <!-- Regenerate Section -->
          <!-- <div class="regenerate-section mt-3">
          <awe-heading variant="h6" type="bold">Regenerate with AI</awe-heading>

          <awe-input
            label="Prompt:"
            [expand]="true"
            [(ngModel)]="regeneratePrompt"
            [icons]="['awe_send']"
            
            placeholder="Enter prompt to regenerate content..."
            (iconClickEvent)="onRegenerate()"
            class="mb-2"
          >
          </awe-input>

        </div> -->
        </div>
      </div>

      <div awe-modal-footer>
        <div class="footer-action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
          <!-- <button type="button" class="btn-cancel px-5" (click)="closeEditModal()">
            Cancel
          </button> -->
          <aava-button label="Cancel" variant="secondary" gradient="none" [pill]="true" shapes="rounded"
            (click)="closeEditModal()">
          </aava-button>
          <!-- <button type="button" class="btn-delete px-5" (click)="saveCardData()">
            Update
          </button> -->
          <aava-button label="Update" variant="primary" gradient="#7c3aed" [pill]="true"  shapes="rounded"
            (click)="saveCardData()">
          </aava-button>
        </div>
      </div>
    </awe-modal>
  </div>