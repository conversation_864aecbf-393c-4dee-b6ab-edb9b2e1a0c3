<!-- eslint-disable @angular-eslint/template/click-events-have-key-events -->
<div class="swot-container" (click)="closeAllDropdowns()">
  <div class="swot-grid">
    <div *ngFor="let section of swotDataService.getSections()" class="swot-quadrant">
      <div class="section-header">
        <div class="section-title">
          <span>{{ section.subtitle }}</span>
        </div>
        <button class="section-action" (click)="addNewFeature(section.id)"
          [attr.aria-label]="'Add more ' + section.subtitle.toLowerCase()" title="Add more">
          <span class="plus-icon">+</span>
        </button>
      </div>

      <!-- Drop Zone with Accordion Cards -->
      <div class="accordion-container">
        <!-- Accordion Cards -->
        <div *ngFor="let feature of section.features" class="accordion-card"
          [class.blur-inactive]="activeAccordion !== null && activeAccordion !== feature.id"
          [class.active]="activeAccordion === feature.id">
          <!-- Accordion Header -->
          <button class="accordion-header" (click)="toggleAccordion(feature.id)">
            <div class="accordion-title-section">
              <h3 class="accordion-title" [title]="feature.title">
                {{ truncateTitle(feature.title) }}
              </h3>
            </div>
            <div class="accordion-controls">
              <!-- Three dots only visible when expanded -->
              <div *ngIf="isAccordionActive(feature.id)" class="three-dots-container">
                <awe-icons (click)="toggleDropdown(feature.id, $event)" [iconName]="'three-dot-vertical'"
                  [iconColor]="'blue'" class="three-dot-icon"></awe-icons>
                <div class="dropdown-arrow position-relative">
                  <div class="dropdown-menu dropdown-menu-end" [class.show]="isDropdownOpen(feature.id)">
                    <button class="dropdown-item border-buttom" (click)="openEditModal(feature)" type="button">
                      Edit
                    </button>
                    <button class="dropdown-item text-danger" type="button"
                      (click)="deleteFeature(feature.id); closeAllDropdowns()">
                      Delete
                    </button>
                  </div>
                </div>
              </div>
              <div class="chevron-icon">
                <svg *ngIf="!isAccordionActive(feature.id)" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <polyline points="6,9 12,15 18,9"></polyline>
                </svg>
                <svg *ngIf="isAccordionActive(feature.id)" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2">
                  <polyline points="18,15 12,9 6,15"></polyline>
                </svg>
              </div>
            </div>
          </button>

          <!-- Accordion Content -->
          <div class="accordion-content" [class.expanded]="isAccordionActive(feature.id)">
            <div class="accordion-content-inner">
              <div class="content-divider"></div>

              <!-- Feature Description -->
              <p class="feature-description">
                {{ feature.description }}
              </p>

              <!-- Tags Display -->
              <div class="tags-container" *ngIf="feature.tags && feature.tags.length > 0">
                <div class="tag-pill" *ngFor="let tag of feature.tags">
                  {{ formatTag(tag) }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Empty State -->
        <div *ngIf="section.features.length === 0" class="empty-state">
          Drag and drop SWOT items here
        </div>


      </div>
    </div>
  </div>

  <!-- Clear Selection Button -->
  <div *ngIf="activeAccordion !== null" class="clear-selection-container">
    <button class="clear-selection-btn" (click)="clearAccordionSelection()">
      Clear Selection
    </button>
  </div>
</div>

<!-- Edit Feature Modal -->
<awe-modal [isOpen]="isEditModalOpen" (closed)="closeEditModal()" [showHeader]="true" [showFooter]="true" width="34vw"
  height="auto" position="center" animation="fade" [showCloseButton]="false" modalClass="edit-feature-modal">
  <!-- Projected Modal Header -->
  <div awe-modal-header class="edit-modal-header">
    <awe-heading variant="s1" type="bold" class="modal-title mb-0">
      {{ isAddingNewFeature ? "Add " + getSectionName() : "Edit " + getSectionName() }}
    </awe-heading>
  </div>

  <!-- Projected Modal Body -->
  <div awe-modal-body class="edit-modal-body">
    <!-- Feature Title -->
    <div class="row">
      <div class="col-md-12 inp-container">
        <div class="label">
          <label for="featureTitle">Title:</label>
        </div>
        <div class="input-wrapper">
          <awe-input variant="fluid" id="featureTitle" [(ngModel)]="editableFeatureTitle"
          [icons]="['awe_trash']"
            placeholder="Enter SWOT item title" class="w-100"></awe-input>
        </div>
      </div>
      <!-- Feature Description -->
      <div class="col-md-12 mt-2">
        <div>
          <label for="featureDescription">Description:</label>
        </div>
        <div class="input-wrapper">
          <awe-input variant="fluid" [expand]="true" id="featureDescription" [(ngModel)]="editableFeatureDescription"
            placeholder="Enter SWOT item description" class="w-100"></awe-input>
        </div>
      </div>
    </div>



    <!-- Regenerate Section -->
    <!-- <div class="regenerate-section mt-3">
      <awe-heading variant="s2" type="bold">Regenerate with AI</awe-heading>
      <awe-input
        label="Prompt:"
        [expand]="true"
        [(ngModel)]="regeneratePrompt"
        id="regeneratePrompt"
        [icons]="['awe_send']"
        variant="fluid"
        placeholder="Enter prompt to regenerate content..."
        (iconClickEvent)="updateFeature()"
      >
      </awe-input>
    </div> -->

    <!-- Validation Message -->
    <div *ngIf="!isValidSwotData()" class="validation-message mt-3">
      <div class="alert alert-warning">
        Please provide a title and either a description or at least one tag.
      </div>
    </div>
  </div>

  <!-- Projected Modal Footer -->
  <div awe-modal-footer class="edit-modal-footer">
    <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
      <button type="button" class="btn-cancel px-5" (click)="closeEditModal()">
        Cancel
      </button>
      <button
        type="button"
        class="btn-delete px-5"
        [disabled]="!isValidSwotData()"
        [class.disabled]="!isValidSwotData()"
        (click)="updateFeature()">
        {{ isAddingNewFeature ? "Add SWOT Item" : "Save Changes" }}
      </button>
    </div>
  </div>
</awe-modal>