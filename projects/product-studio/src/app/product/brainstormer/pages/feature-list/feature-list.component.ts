/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-alert */
/* eslint-disable complexity */
/* eslint-disable no-empty */
/* eslint-disable no-console */
import {
  CdkDragDrop,
  DragDropModule,
  CdkDragMove,
} from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  OnDestroy,
  ChangeDetectorRef,
  ViewChild,
  ElementRef,
  AfterViewInit,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { InputComponent } from '@awe/play-comp-library';
import { Subscription } from 'rxjs';

import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import {
  FeatureDataService,
  FeatureCard,
  FeatureSection,
} from '../../services/export-service/feature-data.service';
import { ProductPipelineService } from '../../services/product-pipeline.service';

@Component({
  selector: 'app-feature-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DragDropModule,
    AweModalComponent,
    InputComponent,
  ],
  templateUrl: './feature-list.component.html',
  styleUrls: ['./feature-list.component.scss'], // Corrected property name
})
export class FeatureListComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('scrollContainer', { static: false }) scrollContainer!: ElementRef;

  // Subscription management
  private subscription = new Subscription();

  // Horizontal scroll state
  canScrollLeft = false;
  canScrollRight = true;

  roboBallIcon: string = 'icons/robo_ball.svg';
  threeDotsIcon: string = 'icons/three-dot.svg';

  // Modal State
  isEditModalOpen = false;
  selectedFeatureForEdit: FeatureCard | null = null;
  isAddingNewFeature = false; // Track if we're adding new or editing existing
  currentSectionId: string = ''; // Track which section we're adding to
  // Create a working copy for editing to avoid direct mutation until save
  editableFeatureTitle: string = '';
  editableFeatureDescription: string = '';
  editableFeatureTags: string[] = [];
  regeneratePrompt: string = '';

  // Expansion Modal State
  isExpansionModalOpen = false;
  expandedFeature: FeatureCard | null = null;

  openDropdownId: string | null = null;

  sections: FeatureSection[] = [];

  constructor(
    private cdRef: ChangeDetectorRef,
    private featureDataService: FeatureDataService,
    private pipelineService: ProductPipelineService
  ) {}

  ngOnInit(): void {
    // Subscribe to data changes from service FIRST to ensure we catch all updates
    this.subscription.add(
      this.featureDataService.sections$.subscribe(sections => {
        // Sections data processed

        this.sections = sections;
        this.cdRef.detectChanges(); // Force change detection

        console.log(
          '✅ FeatureListComponent: Component sections updated, total features:',
          sections.reduce(
            (total, section) => total + section.features.length,
            0
          )
        );

        // Check scroll buttons after sections update
        setTimeout(() => this.checkScrollButtons(), 100);
      })
    );

    // Subscribe to pipeline state changes to load feature data from API
    this.subscription.add(
      this.pipelineService.pipelineState$.subscribe(state => {
        if (state?.data?.features) {
          // Log detailed feature data structure
          const features = state.data.features;
          if (Array.isArray(features)) {
            // Features processed by rank
          } else if (typeof features === 'object') {
            // Features processed by category
          }

          this.featureDataService.updateFromApiResponse(state.data.features);
          // Check scroll buttons after data loads
          setTimeout(() => this.checkScrollButtons(), 200);
        } else {
        }
      })
    );

    document.addEventListener('click', event => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-menu-container')) {
        this.closeAllDropdowns();
      }
    });

    // TEMPORARY: Load sample data for testing if no data is available after 2 seconds
    setTimeout(() => {
      if (
        this.sections.length === 0 ||
        this.sections.every(section => section.features.length === 0)
      ) {
        this.featureDataService.loadSampleData();
      }
    }, 2000);
  }

  ngAfterViewInit(): void {
    // Set up scroll event listener
    if (this.scrollContainer?.nativeElement) {
      this.scrollContainer.nativeElement.addEventListener('scroll', () => {
        this.checkScrollButtons();
      });
      // Initial check with multiple attempts to ensure content is loaded
      setTimeout(() => this.checkScrollButtons(), 100);
      setTimeout(() => this.checkScrollButtons(), 500);
      setTimeout(() => this.checkScrollButtons(), 1000);
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    this.stopAutoScroll();
  }

  // Horizontal scroll methods
  scrollLeft(): void {
    if (this.scrollContainer?.nativeElement) {
      this.scrollContainer.nativeElement.scrollBy({
        left: -300,
        behavior: 'smooth',
      });
      // Update button states after scroll
      setTimeout(() => this.checkScrollButtons(), 100);
    }
  }

  scrollRight(): void {
    if (this.scrollContainer?.nativeElement) {
      this.scrollContainer.nativeElement.scrollBy({
        left: 300,
        behavior: 'smooth',
      });
      // Update button states after scroll
      setTimeout(() => this.checkScrollButtons(), 100);
    }
  }

  checkScrollButtons(): void {
    if (this.scrollContainer?.nativeElement) {
      const element = this.scrollContainer.nativeElement;
      const { scrollLeft, scrollWidth, clientWidth } = element;

      this.canScrollLeft = scrollLeft > 5; // Small threshold to avoid precision issues
      this.canScrollRight = scrollLeft < scrollWidth - clientWidth - 5;

      console.log('Feature List scroll check:', {
        scrollLeft,
        scrollWidth,
        clientWidth,
        canScrollLeft: this.canScrollLeft,
        canScrollRight: this.canScrollRight,
      });
    }
  }

  // Public method to manually trigger scroll button check
  updateScrollButtons(): void {
    this.checkScrollButtons();
  }

  getSectionIds(): string[] {
    return this.featureDataService.getSectionIds();
  }

  getTotalFeatureCount(): number {
    return this.sections.reduce(
      (total, section) => total + section.features.length,
      0
    );
  }

  /**
   * Manual method to refresh feature data from pipeline state
   */
  refreshFeatureData(): void {
    // Check current sections state completed

    const currentState = this.pipelineService.currentState;

    if (currentState?.data?.features) {
      const features = currentState.data.features;

      // Feature analysis completed

      this.featureDataService.updateFromApiResponse(features);
    } else {
      this.featureDataService.loadSampleData();
    }
  }

  onDrop(event: CdkDragDrop<FeatureCard[]>) {
    if (event.previousContainer === event.container) {
      // Reorder within same section
      const sectionId = event.container.id;
      this.featureDataService.reorderFeatures(
        sectionId,
        event.previousIndex,
        event.currentIndex
      );
    } else {
      // Move between sections
      const fromSectionId = event.previousContainer.id;
      const toSectionId = event.container.id;
      const featureId = event.previousContainer.data[event.previousIndex].id;

      this.featureDataService.moveFeature(
        featureId,
        fromSectionId,
        toSectionId,
        event.currentIndex
      );
    }
  }

  addNewFeature(sectionId: string): void {
    // Open modal for adding new feature
    this.isAddingNewFeature = true;
    this.currentSectionId = sectionId;
    this.selectedFeatureForEdit = null;
    // Clear editable data for new feature
    this.editableFeatureTitle = '';
    this.editableFeatureDescription = '';
    this.editableFeatureTags = [''];
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;
  }

  deleteFeature(_sectionId: string, featureId: string): void {
    this.featureDataService.deleteFeature(featureId);
    this.closeAllDropdowns();
  }

  toggleDropdown(featureId: string, event: Event): void {
    event.stopPropagation();
    this.openDropdownId = this.openDropdownId === featureId ? null : featureId;
  }

  isDropdownOpen(featureId: string): boolean {
    return this.openDropdownId === featureId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  openEditModal(feature: FeatureCard): void {
    this.isAddingNewFeature = false; // We're editing, not adding
    this.selectedFeatureForEdit = { ...feature }; // Edit a copy
    this.currentSectionId = ''; // Not needed for editing
    // Set up editable data
    this.editableFeatureTitle = feature.title;
    this.editableFeatureDescription = feature.description;
    this.editableFeatureTags = [...feature.tags]; // Copy array
    this.regeneratePrompt = '';
    this.isEditModalOpen = true;
    this.closeAllDropdowns();
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedFeatureForEdit = null;
    this.isAddingNewFeature = false;
    this.currentSectionId = '';
    this.editableFeatureTitle = '';
    this.editableFeatureDescription = '';
    this.editableFeatureTags = [];
    this.regeneratePrompt = '';
  }

  // Card Click Handler
  onCardClick(featureId: string, event: Event): void {
    // Check if the click target is the three-dot menu or dropdown
    const target = event.target as HTMLElement;
    const isThreeDotMenu = target.closest('.three-dot-menu');
    const isDropdown = target.closest('.dropdown-arrow');

    // Only expand if not clicking on three-dot menu or dropdown
    if (!isThreeDotMenu && !isDropdown) {
      // Small delay to distinguish between click and drag start
      setTimeout(() => {
        if (!this.isDragging) {
          this.expandCard(featureId);
        }
      }, 50);
    }
  }

  // Track dragging state
  private isDragging = false;

  // Auto-scroll properties
  private autoScrollInterval: any = null;
  private readonly SCROLL_TRIGGER_ZONE = 80; // 80px trigger zone for earlier activation
  private readonly BASE_SCROLL_SPEED = 12; // Base speed for very responsive scrolling
  private readonly MAX_SCROLL_SPEED = 35; // Maximum speed when very close to edge
  private readonly SCROLL_INTERVAL = 6; // ~165fps (6ms interval) for ultra-responsive scrolling
  private currentScrollSpeed = 0; // Current adaptive scroll speed

  // Drag event handlers
  onDragStarted(): void {
    this.isDragging = true;
  }

  onDragEnded(): void {
    // Reset dragging state after a short delay
    setTimeout(() => {
      this.isDragging = false;
    }, 100);
    // Stop auto-scroll when drag ends
    this.stopAutoScroll();
  }

  // Handle drag move events for auto-scroll
  onDragMoved(event: CdkDragMove): void {
    if (!this.scrollContainer?.nativeElement) return;

    const containerElement = this.scrollContainer.nativeElement;
    const containerRect = containerElement.getBoundingClientRect();
    const pointerPosition = event.pointerPosition;

    // Calculate distances from edges
    const distanceFromLeft = pointerPosition.x - containerRect.left;
    const distanceFromRight = containerRect.right - pointerPosition.x;

    // Determine scroll direction and target element
    let scrollDirection: 'left' | 'right' | 'up' | 'down' | null = null;
    let targetScrollElement = containerElement;
    let distanceFromEdge = 0;

    // Check horizontal scrolling (main container)
    if (
      distanceFromLeft < this.SCROLL_TRIGGER_ZONE &&
      containerElement.scrollLeft > 0
    ) {
      scrollDirection = 'left';
      targetScrollElement = containerElement;
      distanceFromEdge = distanceFromLeft;
    } else if (
      distanceFromRight < this.SCROLL_TRIGGER_ZONE &&
      containerElement.scrollLeft <
        containerElement.scrollWidth - containerElement.clientWidth
    ) {
      scrollDirection = 'right';
      targetScrollElement = containerElement;
      distanceFromEdge = distanceFromRight;
    } else {
      // Check for vertical scrolling within individual drop zones
      const dropZones = containerElement.querySelectorAll(
        '.feature-list-dropzone'
      );
      for (const dropZone of Array.from(dropZones)) {
        const dropZoneElement = dropZone as HTMLElement;
        const dropZoneRect = dropZoneElement.getBoundingClientRect();

        // Check if pointer is within this drop zone
        if (
          pointerPosition.x >= dropZoneRect.left &&
          pointerPosition.x <= dropZoneRect.right &&
          pointerPosition.y >= dropZoneRect.top &&
          pointerPosition.y <= dropZoneRect.bottom
        ) {
          const distanceFromDropZoneTop = pointerPosition.y - dropZoneRect.top;
          const distanceFromDropZoneBottom =
            dropZoneRect.bottom - pointerPosition.y;

          // Check vertical scrolling within this drop zone
          if (
            distanceFromDropZoneTop < this.SCROLL_TRIGGER_ZONE &&
            dropZoneElement.scrollTop > 0
          ) {
            scrollDirection = 'up';
            targetScrollElement = dropZoneElement;
            distanceFromEdge = distanceFromDropZoneTop;
            break;
          } else if (
            distanceFromDropZoneBottom < this.SCROLL_TRIGGER_ZONE &&
            dropZoneElement.scrollTop <
              dropZoneElement.scrollHeight - dropZoneElement.clientHeight
          ) {
            scrollDirection = 'down';
            targetScrollElement = dropZoneElement;
            distanceFromEdge = distanceFromDropZoneBottom;
            break;
          }
        }
      }
    }

    // Calculate adaptive scroll speed based on distance from edge
    if (scrollDirection) {
      // Calculate speed: closer to edge = faster scrolling
      // Speed ranges from BASE_SCROLL_SPEED to MAX_SCROLL_SPEED
      const speedMultiplier = 1 - distanceFromEdge / this.SCROLL_TRIGGER_ZONE;
      this.currentScrollSpeed =
        this.BASE_SCROLL_SPEED +
        speedMultiplier * (this.MAX_SCROLL_SPEED - this.BASE_SCROLL_SPEED);

      this.startAutoScroll(scrollDirection, targetScrollElement);
    } else {
      this.stopAutoScroll();
    }
  }

  // Start auto-scroll in the specified direction
  private startAutoScroll(
    direction: 'left' | 'right' | 'up' | 'down',
    targetElement?: HTMLElement
  ): void {
    // Stop existing auto-scroll
    this.stopAutoScroll();

    // Use provided target element or default to main scroll container
    const element = targetElement || this.scrollContainer?.nativeElement;

    if (!element) {
      return;
    }

    // Immediate first scroll for instant responsiveness
    this.performScroll(element, direction);

    // Start new auto-scroll interval for continuous scrolling
    this.autoScrollInterval = setInterval(() => {
      if (!element) {
        this.stopAutoScroll();
        return;
      }

      this.performScroll(element, direction);

      // Update scroll button states if scrolling the main container
      if (element === this.scrollContainer?.nativeElement) {
        this.checkScrollButtons();
      }
    }, this.SCROLL_INTERVAL);
  }

  // Perform a single scroll step
  private performScroll(
    element: HTMLElement,
    direction: 'left' | 'right' | 'up' | 'down'
  ): void {
    switch (direction) {
      case 'left':
        if (element.scrollLeft > 0) {
          element.scrollLeft -= this.currentScrollSpeed;
        } else {
          this.stopAutoScroll();
        }
        break;
      case 'right':
        if (element.scrollLeft < element.scrollWidth - element.clientWidth) {
          element.scrollLeft += this.currentScrollSpeed;
        } else {
          this.stopAutoScroll();
        }
        break;
      case 'up':
        if (element.scrollTop > 0) {
          element.scrollTop -= this.currentScrollSpeed;
        } else {
          this.stopAutoScroll();
        }
        break;
      case 'down':
        if (element.scrollTop < element.scrollHeight - element.clientHeight) {
          element.scrollTop += this.currentScrollSpeed;
        } else {
          this.stopAutoScroll();
        }
        break;
    }
  }

  // Stop auto-scroll
  private stopAutoScroll(): void {
    if (this.autoScrollInterval) {
      clearInterval(this.autoScrollInterval);
      this.autoScrollInterval = null;
    }
  }

  // Expansion Modal Methods
  expandCard(featureId: string): void {
    // Find the feature across all sections
    for (const section of this.sections) {
      const feature = section.features.find(f => f.id === featureId);
      if (feature) {
        this.expandedFeature = feature;
        this.isExpansionModalOpen = true;
        // Close any open dropdowns when expanding
        this.closeAllDropdowns();
        break;
      }
    }
  }

  closeExpansionModal(): void {
    this.isExpansionModalOpen = false;
    this.expandedFeature = null;
  }

  // Tag formatting method
  formatTag(tag: string): string {
    return tag
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  // Keyboard event handler
  onKeyDown(event: KeyboardEvent, action: () => void): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      action();
    }
  }

  updateFeature(): void {
    // Validate required fields
    if (!this.editableFeatureTitle.trim()) {
      alert('Please enter a feature title');
      return;
    }

    if (this.isAddingNewFeature) {
      // Adding new feature via service
      this.featureDataService.addFeature(this.currentSectionId, {
        title: this.editableFeatureTitle.trim(),
        description:
          this.editableFeatureDescription.trim() || 'No description provided',
        tags: this.editableFeatureTags
          .filter(tag => tag.trim() !== '')
          .map(tag => tag.trim()),
      });
    } else {
      // Updating existing feature via service
      if (!this.selectedFeatureForEdit) return;

      this.featureDataService.updateFeature(this.selectedFeatureForEdit.id, {
        title: this.editableFeatureTitle.trim(),
        description:
          this.editableFeatureDescription.trim() || 'No description provided',
        tags: this.editableFeatureTags
          .filter(tag => tag.trim() !== '')
          .map(tag => tag.trim()),
      });
    }

    this.closeEditModal();
  }

  // Methods for managing editable tags in the modal
  addEditableTag(): void {
    this.editableFeatureTags.push(''); // Add a new empty string to edit
    this.cdRef.detectChanges(); // Ensure ngFor updates
    setTimeout(() => {
      const inputs = document.querySelectorAll('.edit-tag-input');
      const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
      if (lastInput) {
        lastInput.focus();
      }
    });
  }

  removeEditableTag(index: number): void {
    this.editableFeatureTags.splice(index, 1);
  }

  // Required for ngFor with [(ngModel)] on primitive types like string
  trackByFn(index: number, _item: any): any {
    return index;
  }

  // This method is for direct delete from card, called by (click) on delete button
  handleDeleteFeatureFromCard(sectionId: string, featureId: string) {
    this.deleteFeature(sectionId, featureId);
    this.closeAllDropdowns();
  }
}
