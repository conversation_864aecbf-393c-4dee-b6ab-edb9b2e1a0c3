<!-- eslint-disable @angular-eslint/template/click-events-have-key-events -->
<div class="container-fluid mt-3" (click)="closeAllDropdowns()">

  <!-- Horizontal scrolling wrapper -->
  <div class="feature-list-horizontal-wrapper">
    <div class="scroll-container-wrapper">
      <!-- Left Arrow -->
      <button
        (click)="scrollLeft()"
        [disabled]="!canScrollLeft"
        class="scroll-arrow scroll-arrow-left"
        [class.disabled]="!canScrollLeft"
        aria-label="Scroll left"
      >
        <svg class="arrow-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="15,18 9,12 15,6"></polyline>
        </svg>
      </button>

      <!-- Right Arrow -->
      <button
        (click)="scrollRight()"
        [disabled]="!canScrollRight"
        class="scroll-arrow scroll-arrow-right"
        [class.disabled]="!canScrollRight"
        aria-label="Scroll right"
      >
        <svg class="arrow-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="9,18 15,12 9,6"></polyline>
        </svg>
      </button>

    <div #scrollContainer class="feature-list-horizontal-container">
      <!-- Feature sections with fixed width for horizontal scrolling -->
      <div
        *ngFor="let section of sections"
        class="feature-section-column d-flex flex-column"
      >
      <div class="feature-card-main">
        <!-- Section Main Header (Mo, S, Co, W) -->
        <div class="section-header p-2">
          <!-- Child 1: The title text -->
          <div class="section-title">
            <span>{{ section.subtitle }}</span>
          </div>
          <!-- Child 2: The action button with initials -->
          <div class="section-action">{{ section.title }}</div>
        </div>
        <!-- Drop Zone for Features -->
        <div
          cdkDropList
          [id]="section.id"
          [cdkDropListData]="section.features"
          [cdkDropListConnectedTo]="getSectionIds()"
          (cdkDropListDropped)="onDrop($event)"
          class="feature-list-dropzone border border-top-0 d-flex flex-column flex-grow-1 gap-2"
        >
          <!-- Feature Cards using custom implementation -->
          <div
            *ngFor="let feature of section.features"
            class="feature-item-card"
            cdkDrag
            (click)="onCardClick(feature.id, $event)"
            (keydown.enter)="expandCard(feature.id)"
            (keydown.space)="expandCard(feature.id)"
            tabindex="0"
            role="button"
            [attr.aria-label]="'Expand feature: ' + feature.title"
            (cdkDragStarted)="
              onDragStarted();
              $event.source.element.nativeElement.style.cursor = 'grabbing'
            "
            (cdkDragMoved)="onDragMoved($event)"
            (cdkDragEnded)="
              onDragEnded();
              $event.source.element.nativeElement.style.cursor = 'pointer'
            "
          >
            <!-- Custom Card Header -->
            <div class="feature-card-header d-flex justify-content-between align-items-center">
              <h3
                class="feature-title mb-0 flex-grow-1 pe-2"
                [title]="feature.title"
              >
                {{ feature.title }}
              </h3>

              <!-- Custom three-dot menu icon -->
              <div class="three-dot-menu" (click)="toggleDropdown(feature.id, $event)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="5" r="2" fill="currentColor"/>
                  <circle cx="12" cy="12" r="2" fill="currentColor"/>
                  <circle cx="12" cy="19" r="2" fill="currentColor"/>
                </svg>
              </div>

              <div class="dropdown-arrow position-relative">
                <div
                  class="dropdown-menu dropdown-menu-end"
                  [class.show]="isDropdownOpen(feature.id)"
                >
                  <button
                    class="dropdown-item border-buttom"
                    (click)="openEditModal(feature)"
                    type="button"
                  >
                    Edit
                  </button>
                  <button
                    class="dropdown-item text-danger"
                    type="button"
                    (click)="
                      deleteFeature(section.id, feature.id); closeAllDropdowns()
                    "
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>

            <!-- Custom Card Body -->
            <div class="feature-card-body p-3 pt-0 d-flex flex-column">
              <p
                class="feature-description text-muted small mb-3 flex-grow-1"
                [title]="feature.description"
              >
                {{ feature.description }}
              </p>

              <!-- Feature Tags with Horizontal Scrolling -->
              <div class="feature-tags-section mt-auto">
                <div class="feature-tags-scroll-container">
                  <div class="feature-tags-horizontal">
                    <span
                      *ngFor="let tag of feature.tags"
                      class="feature-tag"
                    >
                      {{ formatTag(tag) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State for Drop Zone -->
          <div
            *ngIf="section.features.length === 0"
            class="text-center text-muted fst-italic py-4"
          >
            Drag and drop features here
          </div>
        </div>

        <!-- Add More Button -->
        <div class="add-more-section mt-3 text-center p-3">
          <button
            class="add-more-btn w-100"
            (click)="addNewFeature(section.id)"
          >
            Add more
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="ms-2">
              <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
    </div>
  </div>
</div>

<!-- Edit Feature Modal -->
<awe-modal
  [isOpen]="isEditModalOpen"
  (closed)="closeEditModal()"
  [showHeader]="true"
  [showFooter]="true"
  width="600px"
  height="auto"
  position="center"
  animation="fade"
  [showCloseButton]="true"
  modalClass="edit-feature-modal"
>
  <!-- Projected Modal Header -->
  <div awe-modal-header class="edit-modal-header">
    <h2 class="modal-title mb-0">
      {{ isAddingNewFeature ? "Add New Feature" : "Edit Feature" }}
    </h2>
  </div>

  <!-- Projected Modal Body -->
  <div awe-modal-body class="edit-modal-body">
    <h6
      class="feature-title mb-3"
      *ngIf="!isAddingNewFeature && selectedFeatureForEdit"
    >
      {{ selectedFeatureForEdit.title }}
    </h6>
    <h6 class="feature-title mb-3" *ngIf="isAddingNewFeature">
      Create a new feature for this section
    </h6>

    <!-- Feature Title -->
    <div class="row">
      <div class="col-md-12 inp-container mt-2">
        <div class="label">
          <label for="name">Title:</label>
        </div>
        <div class="input-wrapper">
          <awe-input
            variant="fluid"
            id="featureTitle"
            [(ngModel)]="editableFeatureTitle"
            placeholder="Enter feature title"
            class="w-100"
          ></awe-input>
        </div>
      </div>
      <!-- Feature Description -->
      <div class="col-md-12 mt-2">
        <div>
          <label for="name">Description:</label>
        </div>
        <div class="input-wrapper">
          <awe-input
            variant="fluid"
            [expand]="true"
            id="featureDescription"
            [(ngModel)]="editableFeatureDescription"
            placeholder="Enter feature description"
            class="w-100"
          ></awe-input>
        </div>
      </div>
    </div>

    <!-- Editable Tags -->
    <div class="mt-3">
      <label class="form-label fw-medium">Tags</label>
      <div
        *ngFor="
          let tag of editableFeatureTags;
          let i = index;
          trackBy: trackByFn
        "
        class="editable-tag-item d-flex align-items-center mb-2"
      >
        <awe-input
          variant="fluid"
          [icons]="['awe_trash']"
          [required]="true"
          errorMessage="This field is required"
          [(ngModel)]="editableFeatureTags[i]"
          placeholder="Enter tag"
          (iconClickEvent)="removeEditableTag(i)"
          class="w-100"
        >
        </awe-input>
      </div>

      <!-- Add New Tag Button -->
      <button type="button" class="add-new w-100" (click)="addEditableTag()">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-1">
          <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        Add Tag
      </button>
    </div>

    <!-- Regenerate Section -->
    <!-- <div class="regenerate-section mt-3">
      <h3 class="regenerate-title">Regenerate with AI</h3>
      <awe-input
        label="Prompt:"
        [expand]="true"
        [(ngModel)]="regeneratePrompt"
        id="regeneratePrompt"
        [icons]="['awe_send']"
        variant="fluid"
        placeholder="Enter prompt to regenerate content..."
        (iconClickEvent)="updateFeature()"
      >
      </awe-input>
    </div> -->
  </div>

  <!-- Projected Modal Footer -->
  <div awe-modal-footer class="edit-modal-footer">
    <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
      <button type="button" class="btn-cancel px-5" (click)="closeEditModal()">
        Cancel
      </button>
      <button type="button" class="btn-delete px-5" (click)="updateFeature()">
        {{ isAddingNewFeature ? "Add Feature" : "Update" }}
      </button>
    </div>
  </div>
</awe-modal>

<!-- Feature Expansion Modal -->
<awe-modal
  [isOpen]="isExpansionModalOpen"
  (closed)="closeExpansionModal()"
  [showHeader]="true"
  [showFooter]="false"
  width="34vw"
  maxWidth="70vw"
  height="auto"
  position="center"
  animation="fade"
  [showCloseButton]="true"
  modalClass="feature-expansion-modal"
  [attr.aria-labelledby]="'expansion-modal-title'"
  [attr.aria-describedby]="'expansion-modal-description'"
>
  <!-- Projected Modal Header -->
  <div awe-modal-header class="expansion-modal-header">
    <h2 id="expansion-modal-title" class="expansion-modal-title mb-0">
      {{ expandedFeature?.title }}
    </h2>
  </div>

  <!-- Projected Modal Body -->
  <div awe-modal-body class="expansion-modal-body" *ngIf="expandedFeature">
    <!-- Full Description -->
    <div class="expanded-description">
      <h4 class="modal-section-title">Description</h4>
      <p id="expansion-modal-description" class="modal-description-text">{{ expandedFeature.description }}</p>
    </div>

    <!-- All Tags -->
    <div class="expanded-tags-section" *ngIf="expandedFeature.tags && expandedFeature.tags.length > 0">
      <h4 class="modal-section-title">Tags</h4>
      <div class="modal-tags-container">
        <span
          *ngFor="let tag of expandedFeature.tags"
          class="feature-tag"
        >
          {{ formatTag(tag) }}
        </span>
      </div>
    </div>
  </div>
</awe-modal>
