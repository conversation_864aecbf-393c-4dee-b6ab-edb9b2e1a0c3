<!-- eslint-disable @angular-eslint/template/click-events-have-key-events -->
<div class="container-fluid" (click)="clearAccordionSelection()">
  <!-- Floating Add Button -->
  <button class="floating-add-btn" (click)="openAddModal()" aria-label="Add new persona">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
      stroke-linecap="round" stroke-linejoin="round">
      <line x1="12" y1="5" x2="12" y2="19"></line>
      <line x1="5" y1="12" x2="19" y2="12"></line>
    </svg>
  </button>

  <!-- Horizontal Scroll Container -->
  <div class="horizontal-scroll-wrapper" *ngIf="personas.length > 0; else noPersonas">
    <div class="scroll-container-wrapper">
      <!-- Left Arrow -->
      <button (click)="scrollLeft()" [disabled]="!canScrollLeft" class="scroll-arrow scroll-arrow-left"
        [class.disabled]="!canScrollLeft" aria-label="Scroll left">
        <svg class="arrow-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor"
          stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="15,18 9,12 15,6"></polyline>
        </svg>
      </button>

      <!-- Right Arrow -->
      <button (click)="scrollRight()" [disabled]="!canScrollRight" class="scroll-arrow scroll-arrow-right"
        [class.disabled]="!canScrollRight" aria-label="Scroll right">
        <svg class="arrow-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor"
          stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="9,18 15,12 9,6"></polyline>
        </svg>
      </button>

      <!-- Scrollable Container -->
      <div #scrollContainer class="horizontal-scroll-container mt-2">
        <div *ngFor="let persona of personas; trackBy: trackByPersona" class="persona-card-item"
          (click)="onPersonaClick(persona)">
          <awe-card [applyBodyPadding]="false" class="persona-card h-100">
            <!-- Card Body -->
            <div class="p-4 d-flex flex-column h-100">
              <!-- Profile Section -->
              <div class="profile-section texcenter">
                <div class="card-actions">
                  <div class="three-dots-btn p-2" (click)="$event.stopPropagation(); openDeleteModal(persona)"
                    aria-label="Delete Persona">
                    <awe-icons iconName="awe_trash" iconSize="20px"></awe-icons>
                  </div>
                </div>
                <div class="avatar-wrapper mx-auto">
                  <img [src]="persona.avatar" [alt]="persona.name || persona.role" class="avatar" />
                </div>
                <h2 class="persona-name d-flex align-items-center justify-content-center">
                  {{ persona.name || persona.role }}
                </h2>
              </div>
              <!-- Info Section -->
              <!-- <div class="info-section">
              <div class="info-row">
                <span>Age</span><span>{{ persona.age }}</span>
              </div>
              <div class="info-row">
                <span>Education</span><span>{{ persona.education }}</span>
              </div>
              <div class="info-row">
                <span>Status</span><span>{{ persona.status }}</span>
              </div>
              <div class="info-row">
                <span>Location</span><span>{{ persona.location }}</span>
              </div>
              <div class="info-row">
                <span>Tech Literacy</span><span>{{ persona.techLiteracy }}</span>
              </div>
            </div> -->

              <!-- Accordion Sections -->
              <div class="accordion-sections mt-3">
                <!-- Pain Points Accordion -->
                <div class="accordion-card"
                  [class.blur-inactive]="activeAccordion !== null && activeAccordion !== persona.id + '-painPoints'"
                  [class.active]="activeAccordion === persona.id + '-painPoints'">
                  <button class="accordion-header"
                    (click)="$event.stopPropagation(); toggleAccordion(persona.id + '-painPoints')">
                    <div class="accordion-title-section">
                      <h4 class="accordion-title">Pain Points</h4>
                    </div>
                    <div class="accordion-controls">
                      <div class="chevron-icon">
                        <svg *ngIf="!isAccordionActive(persona.id + '-painPoints')" width="16" height="16"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                        <svg *ngIf="isAccordionActive(persona.id + '-painPoints')" width="16" height="16"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline points="18,15 12,9 6,15"></polyline>
                        </svg>
                      </div>
                    </div>
                  </button>
                  <div class="accordion-content" [class.expanded]="isAccordionActive(persona.id + '-painPoints')">
                    <div class="accordion-content-inner">
                      <div class="content-divider"></div>
                      <p class="accordion-description">
                        {{ getFirstItem(persona.painPoints) }}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Motivation Accordion -->
                <div class="accordion-card"
                  [class.blur-inactive]="activeAccordion !== null && activeAccordion !== persona.id + '-motivation'"
                  [class.active]="activeAccordion === persona.id + '-motivation'">
                  <button class="accordion-header"
                    (click)="$event.stopPropagation(); toggleAccordion(persona.id + '-motivation')">
                    <div class="accordion-title-section">
                      <h4 class="accordion-title">Motivation</h4>
                    </div>
                    <div class="accordion-controls">
                      <div class="chevron-icon">
                        <svg *ngIf="!isAccordionActive(persona.id + '-motivation')" width="16" height="16"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                        <svg *ngIf="isAccordionActive(persona.id + '-motivation')" width="16" height="16"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline points="18,15 12,9 6,15"></polyline>
                        </svg>
                      </div>
                    </div>
                  </button>
                  <div class="accordion-content" [class.expanded]="isAccordionActive(persona.id + '-motivation')">
                    <div class="accordion-content-inner">
                      <div class="content-divider"></div>
                      <p class="accordion-description">
                        {{ getFirstItem(persona.motivation) }}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Goals Accordion -->
                <div class="accordion-card"
                  [class.blur-inactive]="activeAccordion !== null && activeAccordion !== persona.id + '-goals'"
                  [class.active]="activeAccordion === persona.id + '-goals'">
                  <button class="accordion-header"
                    (click)="$event.stopPropagation(); toggleAccordion(persona.id + '-goals')">
                    <div class="accordion-title-section">
                      <h4 class="accordion-title">Goals</h4>
                    </div>
                    <div class="accordion-controls">
                      <div class="chevron-icon">
                        <svg *ngIf="!isAccordionActive(persona.id + '-goals')" width="16" height="16"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                        <svg *ngIf="isAccordionActive(persona.id + '-goals')" width="16" height="16" viewBox="0 0 24 24"
                          fill="none" stroke="currentColor" stroke-width="2">
                          <polyline points="18,15 12,9 6,15"></polyline>
                        </svg>
                      </div>
                    </div>
                  </button>
                  <div class="accordion-content" [class.expanded]="isAccordionActive(persona.id + '-goals')">
                    <div class="accordion-content-inner">
                      <div class="content-divider"></div>
                      <p class="accordion-description">
                        {{ getFirstItem(persona.goals) }}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Expectations Accordion -->
                <div class="accordion-card"
                  [class.blur-inactive]="activeAccordion !== null && activeAccordion !== persona.id + '-expectations'"
                  [class.active]="activeAccordion === persona.id + '-expectations'">
                  <button class="accordion-header"
                    (click)="$event.stopPropagation(); toggleAccordion(persona.id + '-expectations')">
                    <div class="accordion-title-section">
                      <h4 class="accordion-title">Expectations</h4>
                    </div>
                    <div class="accordion-controls">
                      <div class="chevron-icon">
                        <svg *ngIf="!isAccordionActive(persona.id + '-expectations')" width="16" height="16"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                        <svg *ngIf="isAccordionActive(persona.id + '-expectations')" width="16" height="16"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline points="18,15 12,9 6,15"></polyline>
                        </svg>
                      </div>
                    </div>
                  </button>
                  <div class="accordion-content" [class.expanded]="isAccordionActive(persona.id + '-expectations')">
                    <div class="accordion-content-inner">
                      <div class="content-divider"></div>
                      <p class="accordion-description">
                        {{ getFirstItem(persona.expectations) }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Quote Section -->
              <div class="quote-section mt-3">
                <img [src]="colonIcon" alt="Quote" class="quote-icon" />
                <p class="quote-text">{{ persona.quote }}</p>
              </div>

              <!-- Personality Section with Horizontal Scroll -->
              <div class="personality-section mt-3">
                <h3 class="personality-title">Personality</h3>
                <div class="personality-scroll-container">
                  <div class="personality-tags-horizontal">
                    <span *ngFor="let trait of persona.personality; let i = index" class="personality-tag"
                      [class]="'personality-tag-' + (i % 3 + 1)">
                      {{ trait }}
                    </span>
                  </div>
                </div>
              </div>

            </div>
          </awe-card>
        </div>
      </div>
    </div>
  </div>

  <ng-template #noPersonas>
    <div class="text-center p-5 text-muted">
      No user personas found. Click 'Add new' to start.
    </div>
  </ng-template>

</div>

<!-- MODALS (Add/Delete) -->
<!-- New Add/Edit Modal -->
<awe-modal [isOpen]="isModalOpen" [showHeader]="true" [showFooter]="true" [width]="'600px'" [maxWidth]="'90vw'"
  (closed)="closeModal()">
  <div awe-modal-header class="mb-2">
    <awe-heading variant="s1" type="bold">{{
      modalMode === "edit" ? "Edit Persona" : "Add New Persona"
      }}</awe-heading>
  </div>
  <div awe-modal-body>
    <div class="modal-form" *ngIf="editData">
      <div class="profile-data row g-3">
        <div class="col-md-12 inp-container">
          <div class="label"><label>Name:</label></div>
          <div class="input-wrapper">
            <awe-input variant="fluid" [(ngModel)]="editData.name" placeholder="Enter name"></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Role:</label></div>
          <div class="input-wrapper">
            <awe-input variant="fluid" [(ngModel)]="editData.role" placeholder="Enter role"></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Age:</label></div>
          <div class="input-wrapper">
            <awe-input variant="fluid" [(ngModel)]="editData.age" type="number" placeholder="Enter age"></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Education:</label></div>
          <div class="input-wrapper">
            <awe-input variant="fluid" [(ngModel)]="editData.education" placeholder="Enter education"></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Status:</label></div>
          <div class="input-wrapper">
            <awe-input variant="fluid" [(ngModel)]="editData.status" placeholder="Enter status"></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Location:</label></div>
          <div class="input-wrapper">
            <awe-input variant="fluid" [(ngModel)]="editData.location" placeholder="Enter location"></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Tech Literacy:</label></div>
          <div class="input-wrapper">
            <awe-input variant="fluid" [(ngModel)]="editData.techLiteracy"
              placeholder="Enter tech literacy"></awe-input>
          </div>
        </div>
        <div class="col-md-12 inp-container">
          <div class="label"><label>Quote:</label></div>
          <div class="input-wrapper">
            <awe-input variant="fluid" [(ngModel)]="editData.quote" placeholder="Enter quote"></awe-input>
          </div>
        </div>
      </div>

      <!-- Validation Message -->
      <div *ngIf="modalMode === 'add' && !isValidPersonaData()" class="validation-message mt-3">
        <div class="alert alert-warning">
          Please fill in at least the Name and Role fields, plus one additional field or personality trait.
        </div>
      </div>
    </div>
  </div>
  <div awe-modal-footer class="delete-modal">
    <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
      <button type="button" class="btn-cancel px-5" (click)="closeModal()">
        Cancel
      </button>
      <button type="button" class="btn-delete px-5" [disabled]="modalMode === 'add' && !isValidPersonaData()"
        [class.disabled]="modalMode === 'add' && !isValidPersonaData()" (click)="saveCardData()">
        {{ modalMode === "edit" ? "Update" : "Add Persona" }}
      </button>
    </div>
  </div>
</awe-modal>

<!-- Delete Confirmation Modal -->
<awe-modal class="delete-modal" [isOpen]="isDeleteModalOpen" modalClass="delete-modal-custom">
  <div awe-modal-body class="d-flex gap-3 mb-3 flex-column align-items-center justify-content-center py-2">
    <div class="delete-icon-wrapper d-flex justify-content-center align-items-center mb-3">
      <img [src]="awe_delete" alt="Delete" class="delete-icon" />
    </div>
    <awe-heading variant="s2" type="regular" class="text-center mb-4">Are you sure you want to delete
      this?</awe-heading>
    <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
      <button type="button" class="btn-cancel px-5" (click)="closeDeleteModal()">
        Cancel
      </button>
      <button type="button" class="btn-delete px-5" (click)="confirmDeletePersona()">
        Delete
      </button>
    </div>
  </div>
</awe-modal>