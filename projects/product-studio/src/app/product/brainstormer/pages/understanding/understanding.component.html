<!-- eslint-disable @angular-eslint/template/click-events-have-key-events -->
<!-- <PERSON><PERSON><PERSON> Container with Responsive Grid Layout -->
<div class="container-fluid px-3 py-4">
  <!-- Row 1: Problem and Solution (2 columns) -->
  <div class="row g-3 mb-4 d-flex  align-items-stretch">
    <!-- Problem Cards -->
    <div class="col-12 col-md-6 p-1">
      <div class="d-flex flex-column gap-3">
        <!-- Cards with data -->
        <div *ngFor="let item of firstRowProblemCardWithData; trackBy: trackByItemId">
          <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true"
            cardClass="understanding-card d-flex flex-column align-items-stretch h-100">
            <!-- Header Content -->
            <div awe-card-header-content class="d-flex align-items-center justify-content-between">
              <div class="d-flex align-items-center gap-2">
                <div class="card-icon d-flex align-items-center justify-content-center"
                  [style.background-color]="item.iconBg">
                  <img [src]="item.icon" [alt]="item.title + ' Icon'" class="icon-img">
                </div>
                <h3 class="card-title mb-0">{{item.title}}</h3>
              </div>
              <button class="btn btn-sm btn-link p-1" (click)="openEditModal(item)"
                [attr.aria-label]="'Edit ' + item.title">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M3.3335 12.6654H4.1745L10.9988 5.84103L10.1578 5.00003L3.3335 11.8244V12.6654ZM2.93616 13.6654C2.76539 13.6654 2.62227 13.6076 2.50683 13.492C2.39127 13.3766 2.3335 13.2335 2.3335 13.0627V11.9077C2.3335 11.7451 2.36472 11.5902 2.42716 11.4429C2.4895 11.2955 2.57539 11.1671 2.68483 11.0577L11.1272 2.6192C11.2279 2.52764 11.3392 2.45692 11.461 2.40703C11.5829 2.35703 11.7107 2.33203 11.8443 2.33203C11.978 2.33203 12.1074 2.35575 12.2327 2.4032C12.358 2.45064 12.4689 2.52609 12.5655 2.62953L13.3797 3.45386C13.4831 3.55042 13.5568 3.66153 13.6008 3.7872C13.6448 3.91286 13.6668 4.03853 13.6668 4.1642C13.6668 4.29831 13.6439 4.42625 13.5982 4.54803C13.5524 4.66992 13.4796 4.78125 13.3797 4.88203L4.94116 13.314C4.83172 13.4235 4.70333 13.5094 4.556 13.5717C4.40866 13.6341 4.25372 13.6654 4.09116 13.6654H2.93616ZM10.571 5.42786L10.1578 5.00003L10.9988 5.84103L10.571 5.42786Z"
                    fill="#1C1B1F" />
                </svg>
              </button>
            </div>

            <!-- Body Content -->
            <div class="card-body-content" (click)="openViewModal(item)">
              <div class="line-list">
                <div *ngFor="let dataItem of item.data; let i = index; let last = last; trackBy: trackByDataIndex"
                  class="line-item py-1" [class.border-bottom]="!last" [class.pb-2]="!last">
                  {{ dataItem }}
                </div>
              </div>
            </div>
          </awe-card>
        </div>

        <!-- Empty cards with re-add option -->
        <div *ngFor="let item of getEmptyCards(firstRowProblemCard); trackBy: trackByItemId">
          <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true"
            cardClass="understanding-card empty-card h-100">
            <!-- Header Content -->
            <div awe-card-header-content class="d-flex align-items-center gap-2">
              <div class="card-icon d-flex align-items-center justify-content-center"
                [style.background-color]="item.iconBg">
                <img [src]="item.icon" [alt]="item.title + ' Icon'" class="icon-img">
              </div>
              <h3 class="card-title mb-0">{{item.title}}</h3>
            </div>

            <!-- Body Content -->
            <div class="d-flex align-items-center justify-content-center py-4">
              <button class="btn btn-outline-secondary d-flex align-items-center gap-2" (click)="openEditModal(item)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                Add Content
              </button>
            </div>
          </awe-card>
        </div>
      </div>
    </div>

    <!-- Solution Cards -->
    <div class="col-12 col-md-6 p-1">
      <div class="d-flex flex-column gap-3">
        <!-- Cards with data -->
        <div *ngFor="let item of firstRowSolutionCardWithData; trackBy: trackByItemId">
          <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-card h-100">
            <!-- Header Content -->
            <div awe-card-header-content class="d-flex align-items-center justify-content-between">
              <div class="d-flex align-items-center gap-2">
                <div class="card-icon d-flex align-items-center justify-content-center"
                  [style.background-color]="item.iconBg">
                  <img [src]="item.icon" [alt]="item.title + ' Icon'" class="icon-img">
                </div>
                <h3 class="card-title mb-0">{{item.title}}</h3>
              </div>
              <button class="btn btn-sm btn-link p-1" (click)="openEditModal(item)"
                [attr.aria-label]="'Edit ' + item.title">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M3.3335 12.6654H4.1745L10.9988 5.84103L10.1578 5.00003L3.3335 11.8244V12.6654ZM2.93616 13.6654C2.76539 13.6654 2.62227 13.6076 2.50683 13.492C2.39127 13.3766 2.3335 13.2335 2.3335 13.0627V11.9077C2.3335 11.7451 2.36472 11.5902 2.42716 11.4429C2.4895 11.2955 2.57539 11.1671 2.68483 11.0577L11.1272 2.6192C11.2279 2.52764 11.3392 2.45692 11.461 2.40703C11.5829 2.35703 11.7107 2.33203 11.8443 2.33203C11.978 2.33203 12.1074 2.35575 12.2327 2.4032C12.358 2.45064 12.4689 2.52609 12.5655 2.62953L13.3797 3.45386C13.4831 3.55042 13.5568 3.66153 13.6008 3.7872C13.6448 3.91286 13.6668 4.03853 13.6668 4.1642C13.6668 4.29831 13.6439 4.42625 13.5982 4.54803C13.5524 4.66992 13.4796 4.78125 13.3797 4.88203L4.94116 13.314C4.83172 13.4235 4.70333 13.5094 4.556 13.5717C4.40866 13.6341 4.25372 13.6654 4.09116 13.6654H2.93616ZM10.571 5.42786L10.1578 5.00003L10.9988 5.84103L10.571 5.42786Z"
                    fill="#1C1B1F" />
                </svg>
              </button>
            </div>

            <!-- Body Content -->
            <div class="card-body-content" (click)="openViewModal(item)">
              <div class="line-list">
                <div *ngFor="let dataItem of item.data; let i = index; let last = last; trackBy: trackByDataIndex"
                  class="line-item py-1" [class.border-bottom]="!last" [class.pb-2]="!last">
                  {{ dataItem }}
                </div>
              </div>
            </div>
          </awe-card>
        </div>

        <!-- Empty cards with re-add option -->
        <div *ngFor="let item of getEmptyCards(firstRowSolutionCard); trackBy: trackByItemId">
          <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true"
            cardClass="understanding-card empty-card h-100">
            <!-- Header Content -->
            <div awe-card-header-content class="d-flex align-items-center gap-2">
              <div class="card-icon d-flex align-items-center justify-content-center"
                [style.background-color]="item.iconBg">
                <img [src]="item.icon" [alt]="item.title + ' Icon'" class="icon-img">
              </div>
              <h3 class="card-title mb-0">{{item.title}}</h3>
            </div>

            <!-- Body Content -->
            <div class="d-flex align-items-center justify-content-center py-4">
              <button class="btn btn-outline-secondary d-flex align-items-center gap-2" (click)="openEditModal(item)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                Add Content
              </button>
            </div>
          </awe-card>
        </div>
      </div>
    </div>
  </div>

  <!-- Row 2: Value Proposition, Key Metrics, Alternatives (3 columns) -->
  <div class="row g-3 mb-4">
    <!-- Value Proposition -->
    <div class="col-12 col-md-6 col-lg-4">
      <div *ngFor="let item of valuePropositionDataWithData; trackBy: trackByItemId">
        <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-card h-100">
          <!-- Header Content -->
          <div awe-card-header-content class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center gap-2">
              <div class="card-icon d-flex align-items-center justify-content-center"
                [style.background-color]="item.iconBg">
                <img [src]="item.icon" [alt]="item.title + ' Icon'" class="icon-img">
              </div>
              <h3 class="card-title mb-0">{{item.title}}</h3>
            </div>
            <button class="btn btn-sm btn-link p-1" (click)="openEditModal(item)"
              [attr.aria-label]="'Edit ' + item.title">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path
                  d="M3.3335 12.6654H4.1745L10.9988 5.84103L10.1578 5.00003L3.3335 11.8244V12.6654ZM2.93616 13.6654C2.76539 13.6654 2.62227 13.6076 2.50683 13.492C2.39127 13.3766 2.3335 13.2335 2.3335 13.0627V11.9077C2.3335 11.7451 2.36472 11.5902 2.42716 11.4429C2.4895 11.2955 2.57539 11.1671 2.68483 11.0577L11.1272 2.6192C11.2279 2.52764 11.3392 2.45692 11.461 2.40703C11.5829 2.35703 11.7107 2.33203 11.8443 2.33203C11.978 2.33203 12.1074 2.35575 12.2327 2.4032C12.358 2.45064 12.4689 2.52609 12.5655 2.62953L13.3797 3.45386C13.4831 3.55042 13.5568 3.66153 13.6008 3.7872C13.6448 3.91286 13.6668 4.03853 13.6668 4.1642C13.6668 4.29831 13.6439 4.42625 13.5982 4.54803C13.5524 4.66992 13.4796 4.78125 13.3797 4.88203L4.94116 13.314C4.83172 13.4235 4.70333 13.5094 4.556 13.5717C4.40866 13.6341 4.25372 13.6654 4.09116 13.6654H2.93616ZM10.571 5.42786L10.1578 5.00003L10.9988 5.84103L10.571 5.42786Z"
                  fill="#1C1B1F" />
              </svg>
            </button>
          </div>

          <!-- Body Content -->
          <div class="card-body-content" (click)="openViewModal(item)">
            <div class="line-list">
              <div *ngFor="let dataItem of item.data; let i = index; let last = last; trackBy: trackByDataIndex"
                class="line-item py-1" [class.border-bottom]="!last" [class.pb-2]="!last">
                {{ dataItem }}
              </div>
            </div>
          </div>
        </awe-card>
      </div>
    </div>

    <!-- Key Metrics -->
    <div class="col-12 col-md-6 col-lg-4">
      <div *ngFor="let item of keyMetricsDataWithData; trackBy: trackByItemId">
        <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-card h-100">
          <!-- Header Content -->
          <div awe-card-header-content class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center gap-2">
              <div class="card-icon d-flex align-items-center justify-content-center"
                [style.background-color]="item.iconBg">
                <img [src]="item.icon" [alt]="item.title + ' Icon'" class="icon-img">
              </div>
              <h3 class="card-title mb-0">{{item.title}}</h3>
            </div>
            <button class="btn btn-sm btn-link p-1" (click)="openEditModal(item)"
              [attr.aria-label]="'Edit ' + item.title">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path
                  d="M3.3335 12.6654H4.1745L10.9988 5.84103L10.1578 5.00003L3.3335 11.8244V12.6654ZM2.93616 13.6654C2.76539 13.6654 2.62227 13.6076 2.50683 13.492C2.39127 13.3766 2.3335 13.2335 2.3335 13.0627V11.9077C2.3335 11.7451 2.36472 11.5902 2.42716 11.4429C2.4895 11.2955 2.57539 11.1671 2.68483 11.0577L11.1272 2.6192C11.2279 2.52764 11.3392 2.45692 11.461 2.40703C11.5829 2.35703 11.7107 2.33203 11.8443 2.33203C11.978 2.33203 12.1074 2.35575 12.2327 2.4032C12.358 2.45064 12.4689 2.52609 12.5655 2.62953L13.3797 3.45386C13.4831 3.55042 13.5568 3.66153 13.6008 3.7872C13.6448 3.91286 13.6668 4.03853 13.6668 4.1642C13.6668 4.29831 13.6439 4.42625 13.5982 4.54803C13.5524 4.66992 13.4796 4.78125 13.3797 4.88203L4.94116 13.314C4.83172 13.4235 4.70333 13.5094 4.556 13.5717C4.40866 13.6341 4.25372 13.6654 4.09116 13.6654H2.93616ZM10.571 5.42786L10.1578 5.00003L10.9988 5.84103L10.571 5.42786Z"
                  fill="#1C1B1F" />
              </svg>
            </button>
          </div>

          <!-- Body Content -->
          <div class="card-body-content" (click)="openViewModal(item)">
            <div class="line-list">
              <div *ngFor="let dataItem of item.data; let i = index; let last = last; trackBy: trackByDataIndex"
                class="line-item py-1" [class.border-bottom]="!last" [class.pb-2]="!last">
                {{ dataItem }}
              </div>
            </div>
          </div>
        </awe-card>
      </div>
    </div>

    <!-- Alternatives -->
    <div class="col-12 col-md-6 col-lg-4">
      <div *ngFor="let item of alternativesDataWithData; trackBy: trackByItemId">
        <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-card h-100">
          <!-- Header Content -->
          <div awe-card-header-content class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center gap-2">
              <div class="card-icon d-flex align-items-center justify-content-center"
                [style.background-color]="item.iconBg">
                <img [src]="item.icon" [alt]="item.title + ' Icon'" class="icon-img">
              </div>
              <h3 class="card-title mb-0">{{item.title}}</h3>
            </div>
            <button class="btn btn-sm btn-link p-1" (click)="openEditModal(item)"
              [attr.aria-label]="'Edit ' + item.title">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path
                  d="M3.3335 12.6654H4.1745L10.9988 5.84103L10.1578 5.00003L3.3335 11.8244V12.6654ZM2.93616 13.6654C2.76539 13.6654 2.62227 13.6076 2.50683 13.492C2.39127 13.3766 2.3335 13.2335 2.3335 13.0627V11.9077C2.3335 11.7451 2.36472 11.5902 2.42716 11.4429C2.4895 11.2955 2.57539 11.1671 2.68483 11.0577L11.1272 2.6192C11.2279 2.52764 11.3392 2.45692 11.461 2.40703C11.5829 2.35703 11.7107 2.33203 11.8443 2.33203C11.978 2.33203 12.1074 2.35575 12.2327 2.4032C12.358 2.45064 12.4689 2.52609 12.5655 2.62953L13.3797 3.45386C13.4831 3.55042 13.5568 3.66153 13.6008 3.7872C13.6448 3.91286 13.6668 4.03853 13.6668 4.1642C13.6668 4.29831 13.6439 4.42625 13.5982 4.54803C13.5524 4.66992 13.4796 4.78125 13.3797 4.88203L4.94116 13.314C4.83172 13.4235 4.70333 13.5094 4.556 13.5717C4.40866 13.6341 4.25372 13.6654 4.09116 13.6654H2.93616ZM10.571 5.42786L10.1578 5.00003L10.9988 5.84103L10.571 5.42786Z"
                  fill="#1C1B1F" />
              </svg>
            </button>
          </div>

          <!-- Body Content -->
          <div class="card-body-content" (click)="openViewModal(item)">
            <div class="line-list">
              <div *ngFor="let dataItem of item.data; let i = index; let last = last; trackBy: trackByDataIndex"
                class="line-item py-1" [class.border-bottom]="!last" [class.pb-2]="!last">
                {{ dataItem }}
              </div>
            </div>
          </div>
        </awe-card>
      </div>
    </div>
  </div>

  <!-- Row 3: Solution Tenants, Customer Segments, Key Partners (3 columns) -->
  <div class="row g-3 mb-4">
    <!-- Solution Tenants -->
    <div class="col-12 col-md-6 col-lg-4">
      <div *ngFor="let item of solutionTenantsDataWithData; trackBy: trackByItemId">
        <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-card h-100">
          <!-- Header Content -->
          <div awe-card-header-content class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center gap-2">
              <div class="card-icon d-flex align-items-center justify-content-center"
                [style.background-color]="item.iconBg">
                <img [src]="item.icon" [alt]="item.title + ' Icon'" class="icon-img">
              </div>
              <h3 class="card-title mb-0">{{item.title}}</h3>
            </div>
            <button class="btn btn-sm btn-link p-1" (click)="openEditModal(item)"
              [attr.aria-label]="'Edit ' + item.title">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path
                  d="M3.3335 12.6654H4.1745L10.9988 5.84103L10.1578 5.00003L3.3335 11.8244V12.6654ZM2.93616 13.6654C2.76539 13.6654 2.62227 13.6076 2.50683 13.492C2.39127 13.3766 2.3335 13.2335 2.3335 13.0627V11.9077C2.3335 11.7451 2.36472 11.5902 2.42716 11.4429C2.4895 11.2955 2.57539 11.1671 2.68483 11.0577L11.1272 2.6192C11.2279 2.52764 11.3392 2.45692 11.461 2.40703C11.5829 2.35703 11.7107 2.33203 11.8443 2.33203C11.978 2.33203 12.1074 2.35575 12.2327 2.4032C12.358 2.45064 12.4689 2.52609 12.5655 2.62953L13.3797 3.45386C13.4831 3.55042 13.5568 3.66153 13.6008 3.7872C13.6448 3.91286 13.6668 4.03853 13.6668 4.1642C13.6668 4.29831 13.6439 4.42625 13.5982 4.54803C13.5524 4.66992 13.4796 4.78125 13.3797 4.88203L4.94116 13.314C4.83172 13.4235 4.70333 13.5094 4.556 13.5717C4.40866 13.6341 4.25372 13.6654 4.09116 13.6654H2.93616ZM10.571 5.42786L10.1578 5.00003L10.9988 5.84103L10.571 5.42786Z"
                  fill="#1C1B1F" />
              </svg>
            </button>
          </div>

          <!-- Body Content -->
          <div class="card-body-content" (click)="openViewModal(item)">
            <div class="line-list">
              <div *ngFor="let dataItem of item.data; let i = index; let last = last; trackBy: trackByDataIndex"
                class="line-item py-1" [class.border-bottom]="!last" [class.pb-2]="!last">
                {{ dataItem }}
              </div>
            </div>
          </div>
        </awe-card>
      </div>
    </div>

    <!-- Customer Segments -->
    <div class="col-12 col-md-6 col-lg-4">
      <div *ngFor="let item of customerSegmentItemsWithData; trackBy: trackByItemId">
        <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-card h-100">
          <!-- Header Content -->
          <div awe-card-header-content class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center gap-2">
              <div class="card-icon d-flex align-items-center justify-content-center"
                [style.background-color]="item.iconBg">
                <img [src]="item.icon" [alt]="item.title + ' Icon'" class="icon-img">
              </div>
              <h3 class="card-title mb-0">{{item.title}}</h3>
            </div>
            <button class="btn btn-sm btn-link p-1" (click)="openEditModal(item)"
              [attr.aria-label]="'Edit ' + item.title">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path
                  d="M3.3335 12.6654H4.1745L10.9988 5.84103L10.1578 5.00003L3.3335 11.8244V12.6654ZM2.93616 13.6654C2.76539 13.6654 2.62227 13.6076 2.50683 13.492C2.39127 13.3766 2.3335 13.2335 2.3335 13.0627V11.9077C2.3335 11.7451 2.36472 11.5902 2.42716 11.4429C2.4895 11.2955 2.57539 11.1671 2.68483 11.0577L11.1272 2.6192C11.2279 2.52764 11.3392 2.45692 11.461 2.40703C11.5829 2.35703 11.7107 2.33203 11.8443 2.33203C11.978 2.33203 12.1074 2.35575 12.2327 2.4032C12.358 2.45064 12.4689 2.52609 12.5655 2.62953L13.3797 3.45386C13.4831 3.55042 13.5568 3.66153 13.6008 3.7872C13.6448 3.91286 13.6668 4.03853 13.6668 4.1642C13.6668 4.29831 13.6439 4.42625 13.5982 4.54803C13.5524 4.66992 13.4796 4.78125 13.3797 4.88203L4.94116 13.314C4.83172 13.4235 4.70333 13.5094 4.556 13.5717C4.40866 13.6341 4.25372 13.6654 4.09116 13.6654H2.93616ZM10.571 5.42786L10.1578 5.00003L10.9988 5.84103L10.571 5.42786Z"
                  fill="#1C1B1F" />
              </svg>
            </button>
          </div>

          <!-- Body Content -->
          <div class="card-body-content" (click)="openViewModal(item)">
            <div class="line-list">
              <div *ngFor="let dataItem of item.data; let i = index; let last = last; trackBy: trackByDataIndex"
                class="line-item py-1" [class.border-bottom]="!last" [class.pb-2]="!last">
                {{ dataItem }}
              </div>
            </div>
          </div>
        </awe-card>
      </div>
    </div>

    <!-- Key Partners -->
    <div class="col-12 col-md-6 col-lg-4">
      <div *ngFor="let item of keyPartnersDataWithData; trackBy: trackByItemId">
        <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-card h-100">
          <!-- Header Content -->
          <div awe-card-header-content class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center gap-2">
              <div class="card-icon d-flex align-items-center justify-content-center"
                [style.background-color]="item.iconBg">
                <img [src]="item.icon" [alt]="item.title + ' Icon'" class="icon-img">
              </div>
              <h3 class="card-title mb-0">{{item.title}}</h3>
            </div>
            <button class="btn btn-sm btn-link p-1" (click)="openEditModal(item)"
              [attr.aria-label]="'Edit ' + item.title">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path
                  d="M3.3335 12.6654H4.1745L10.9988 5.84103L10.1578 5.00003L3.3335 11.8244V12.6654ZM2.93616 13.6654C2.76539 13.6654 2.62227 13.6076 2.50683 13.492C2.39127 13.3766 2.3335 13.2335 2.3335 13.0627V11.9077C2.3335 11.7451 2.36472 11.5902 2.42716 11.4429C2.4895 11.2955 2.57539 11.1671 2.68483 11.0577L11.1272 2.6192C11.2279 2.52764 11.3392 2.45692 11.461 2.40703C11.5829 2.35703 11.7107 2.33203 11.8443 2.33203C11.978 2.33203 12.1074 2.35575 12.2327 2.4032C12.358 2.45064 12.4689 2.52609 12.5655 2.62953L13.3797 3.45386C13.4831 3.55042 13.5568 3.66153 13.6008 3.7872C13.6448 3.91286 13.6668 4.03853 13.6668 4.1642C13.6668 4.29831 13.6439 4.42625 13.5982 4.54803C13.5524 4.66992 13.4796 4.78125 13.3797 4.88203L4.94116 13.314C4.83172 13.4235 4.70333 13.5094 4.556 13.5717C4.40866 13.6341 4.25372 13.6654 4.09116 13.6654H2.93616ZM10.571 5.42786L10.1578 5.00003L10.9988 5.84103L10.571 5.42786Z"
                  fill="#1C1B1F" />
              </svg>
            </button>
          </div>

          <!-- Body Content -->
          <div class="card-body-content" (click)="openViewModal(item)">
            <div class="line-list">
              <div *ngFor="let dataItem of item.data; let i = index; let last = last; trackBy: trackByDataIndex"
                class="line-item py-1" [class.border-bottom]="!last" [class.pb-2]="!last">
                {{ dataItem }}
              </div>
            </div>
          </div>
        </awe-card>
      </div>
    </div>
  </div>

  <!-- Row 4: Cost Structure and Revenue Streams (2 columns) -->
  <div class="row g-3 mb-4">
    <div class="col-12 col-md-6" *ngFor="let item of costRevenueItemsWithData; trackBy: trackByItemId">
      <awe-card [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-card h-100">
        <!-- Header Content -->
        <div awe-card-header-content class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center gap-2">
            <div class="card-icon d-flex align-items-center justify-content-center"
              [style.background-color]="item.iconBg">
              <img [src]="item.icon" [alt]="item.title + ' Icon'" class="icon-img">
            </div>
            <h3 class="card-title mb-0">{{item.title}}</h3>
          </div>
          <button class="btn btn-sm btn-link p-1" (click)="openEditModal(item)"
            [attr.aria-label]="'Edit ' + item.title">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path
                d="M3.3335 12.6654H4.1745L10.9988 5.84103L10.1578 5.00003L3.3335 11.8244V12.6654ZM2.93616 13.6654C2.76539 13.6654 2.62227 13.6076 2.50683 13.492C2.39127 13.3766 2.3335 13.2335 2.3335 13.0627V11.9077C2.3335 11.7451 2.36472 11.5902 2.42716 11.4429C2.4895 11.2955 2.57539 11.1671 2.68483 11.0577L11.1272 2.6192C11.2279 2.52764 11.3392 2.45692 11.461 2.40703C11.5829 2.35703 11.7107 2.33203 11.8443 2.33203C11.978 2.33203 12.1074 2.35575 12.2327 2.4032C12.358 2.45064 12.4689 2.52609 12.5655 2.62953L13.3797 3.45386C13.4831 3.55042 13.5568 3.66153 13.6008 3.7872C13.6448 3.91286 13.6668 4.03853 13.6668 4.1642C13.6668 4.29831 13.6439 4.42625 13.5982 4.54803C13.5524 4.66992 13.4796 4.78125 13.3797 4.88203L4.94116 13.314C4.83172 13.4235 4.70333 13.5094 4.556 13.5717C4.40866 13.6341 4.25372 13.6654 4.09116 13.6654H2.93616ZM10.571 5.42786L10.1578 5.00003L10.9988 5.84103L10.571 5.42786Z"
                fill="#1C1B1F" />
            </svg>
          </button>
        </div>

        <!-- Body Content -->
        <div class="card-body-content" (click)="openViewModal(item)">
          <div class="line-list">
            <div *ngFor="let dataItem of item.data; let i = index; let last = last; trackBy: trackByDataIndex"
              class="line-item py-1" [class.border-bottom]="!last" [class.pb-2]="!last">
              {{ dataItem }}
            </div>
          </div>
        </div>
      </awe-card>
    </div>
  </div>
</div>




<!-- View Modal for Full Content -->
<div *ngIf="isViewModalOpen" class="modal-overlay" (click)="closeViewModal()">
  <div class="modal-container view-modal-container" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h4 class="modal-title">{{ selectedItemForView?.title }}</h4>
      <button class="close-btn" (click)="closeViewModal()" [attr.aria-label]="'Close modal'">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>

    <div *ngIf="selectedItemForView" class="modal-body">
      <div class="view-content">
        <!-- Full Content Display -->
        <div class="content-section">
          <div class="content-items">
            <div *ngFor="let dataItem of selectedItemForView.data; let i = index; let last = last" class="content-item"
              [class.last-item]="last">
              {{ dataItem }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<!-- Enhanced Edit Modal -->
<awe-modal [isOpen]="isEditModalOpen" (closed)="closeEditModal()" [showHeader]="true" [showFooter]="true" width="50%"
  height="auto" position="center" animation="fade" [showCloseButton]="true" modalClass="edit-understanding-modal">
  <!-- Projected Modal Header -->
  <div awe-modal-header class="edit-modal-header">
    <awe-heading variant="s1" type="regular">Edit Understanding</awe-heading>
  </div>

  <!-- Projected Modal Body -->
  <div awe-modal-body *ngIf="selectedItemForEdit" class="edit-modal-body mt-2">
    <awe-body-text type="body-test">{{
      selectedItemForEdit.title
      }}</awe-body-text>

    <!-- Editable Data Items -->
    <div *ngFor="
        let dataPoint of editableItemData;
        let i = index;
        trackBy: trackByFn
      " class="editable-data-item d-flex align-items-center mb-2 p-1">
      <input type="text" class="form-control-sm flex-grow-1" [(ngModel)]="editableItemData[i]"
        placeholder="Enter data" />
      <awe-icons iconName="awe_trash" iconColor="danger" (click)="removeEditableDataItem(i)"></awe-icons>
    </div>

    <!-- Add New Data Item -->
    <div (click)="addEditableDataItem()" class="editable-data-item w-100 add-new-data-btn">
      <span class="d-flex align-items-center justify-content-start">Add New</span>
      <span class="d-flex align-items-center justify-content-end">
        <awe-icons class="d-flex align-items-center justify-content-end" iconName="awe_plus"
          iconColor="danger"></awe-icons>
      </span>
    </div>

    <!-- Regenerate Section -->
    <div class="regenerate-section mt-4">
      <div class="input-group">
        <awe-input id="regeneratePrompt" label="Regenerate" variant="fluid" placeholder="This field is required"
          class="form-control form-control-sm" [required]="false" errorMessage="This field is required"
          [icons]="['awe_send']" iconColor="action" [(ngModel)]="regeneratePrompt"
          placeholder="Type your prompt here...">
        </awe-input>
      </div>
    </div>
  </div>

  <!-- Projected Modal Footer -->
  <div awe-modal-footer class="edit-modal-footer">
    <div awe-modal-footer>
      <div class="action-btn gap-4 mt-4 d-flex w-100 justify-content-center">
        <button type="button" class="btn-cancel px-5" (click)="closeEditModal()">
          Cancel
        </button>
        <button type="button" class="btn-delete px-5" (click)="updateUnderstandingItem($event)">
          Update
        </button>
      </div>
    </div>
  </div>
</awe-modal>