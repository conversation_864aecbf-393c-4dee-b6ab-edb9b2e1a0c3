import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CardsComponent } from '@awe/play-comp-library';

import { HeroSectionHeaderComponent } from '../shared/components/hero-section-header/hero-section-header.component';
import { RecentProjectsComponent } from '../shared/components/recent-projects/recent-projects.component';

interface CardData {
  id: number;
  frontTitle: string;
  frontDescription: string;
  frontImage: string;
  redirectPath: string;
  isDisabled: boolean;
}

@Component({
  selector: 'app-main-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    HeroSectionHeaderComponent,
    CardsComponent,
    RecentProjectsComponent,
  ],
  templateUrl: './main-dashboard.component.html',
  styleUrl: './main-dashboard.component.scss',
})
export class MainDashboardComponent {
  card_1: string = 'cards-images/card_1.png';
  card_2: string = 'cards-images/card_2.png';
  card_3: string = 'cards-images/card_3.png';
  // person_icon: string = 'charts-images/daily-active-chart.png';
  // projects_chart: string = 'charts-images/projects-brainstorm-chart.png';
  // daily_chart: string = 'charts-images/daily-active-chart.png';
  // studio_chart: string = 'charts-images/studio-chart.png'
  // ellipse_image: string = 'charts-images/Ellipse-image.png'
  // ellipse_background_image: string = 'charts-images/Ellipse-background-image.png'

  cardData: CardData[] = [
    {
      id: 1,
      frontTitle: 'User Story Creation',
      frontDescription: 'Create and generate User Stories effortlessly with AI',
      frontImage: this.card_1,
      redirectPath: 'user-stories',
      isDisabled: true,
    },
    {
      id: 2,
      frontTitle: 'Brainstormer',
      frontDescription: 'From understanding your product to the roadmap',
      frontImage: this.card_3,
      redirectPath: 'brainstormer',
      isDisabled: false,
    },
    {
      id: 3,
      frontTitle: 'Create Workflow',
      frontDescription: 'Brainstorm your product by creating your own flow',
      frontImage: this.card_2,
      redirectPath: 'create-workflow',
      isDisabled: true,
    },
  ];

  constructor(private router: Router) {}

  navigateToFeature(card: CardData): void {
    if (card.isDisabled) {
      return;
    }
    this.router.navigate(['', card.redirectPath]);
  }
}
