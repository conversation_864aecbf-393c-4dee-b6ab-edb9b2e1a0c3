
// import { productStudioHeaderConfig } from './config/header.config';
// import { SharedAppHeaderComponent } from '@shared/components/app-header/app-header.component';
import {
  Component,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';

import { environment } from '../environments/environment';
import { ThemeServiceService } from './product/shared/services/auth-config-service/theme-service.service';
import { OrgConfigService } from './shared/services/org-config.service';
import { AuthConfig, AuthService, CentralizedRedirectService, ThemeInitService, TokenStorageService } from '@shared';
// import { OrgConfigService } from './shared/services/org-config/org-config.service';


@Component({
  selector: 'app-root',
  standalone: true,
  providers: [OrgConfigService],
  imports: [
    RouterOutlet,
    CommonModule,
    // SharedAppHeaderComponent
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  @ViewChild('dialogHost', { read: ViewContainerRef })
  dialogHost!: ViewContainerRef;
  showHeaderAndNav: boolean = true;
  redirectUrl = '';

  // Header configuration
  // headerConfig: HeaderConfig = productStudioHeaderConfig;

  constructor(
    // private authTokenService: AuthTokenService,
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
    private router: Router,
    public themeService: ThemeServiceService,
    private centralizedRedirectService: CentralizedRedirectService,
    public orgConfigService: OrgConfigService, // Inject OrgConfigService for realm functionality
    private themeInitService: ThemeInitService,
  ) { }

  ngOnInit(): void {
    // Initialize theme system early
    this.themeInitService.initialize();
    const authConfigs = this.authService.getAuthConfig();
    this.redirectUrl = authConfigs?.redirectUrl || window.location.origin;
    const authConfig: AuthConfig = {
      apiAuthUrl: environment.productStudioApiAuthUrl,
      redirectUrl: environment.productStudioRedirectUrl,
      postLoginRedirectUrl: '/',
      appName: 'product-studio',
    };

    this.authService.setAuthConfig(authConfig);

    // Check authentication status and redirect if needed
    if (!this.checkAuthenticationAndRedirect()) {
      return; // Don't continue if not authenticated
    }

    // this.authTokenService.handleAuthCodeAndToken();
    // this.authTokenService.startTokenCheck();

    // org_path is now set during login, no need to check here
  }

  ngOnDestroy() {
    // this.authTokenService.stopTokenCheck();
  }

  // Simple authentication check
  private checkAuthenticationAndRedirect(): boolean {
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();

    if (!accessToken && !refreshToken) {
      // Store current URL and redirect to marketing login
      // this.centralizedRedirectService.storeIntendedDestination(window.location.href);
      this.centralizedRedirectService.redirectToMarketingLogin();
      return false;
    }
    return true;
  }

  // Header event handlers
  onNavigation(route: string): void {
  }

  onLogout() {
    if (this.tokenStorage.getLoginType() === 'basic') {
      this.authService.basicLogout().subscribe({
        next: () => {
          this.tokenStorage.clearTokens();
          this.centralizedRedirectService.redirectToMarketingLogin();
          this.tokenStorage.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('Basic logout failed:', error);
          this.tokenStorage.clearTokens();
          this.centralizedRedirectService.redirectToMarketingLogin();
        },
      });
    } else {
      // For SSO logout, redirect to marketing login
      this.centralizedRedirectService.redirectToMarketingLogin();
    }
  }

  onProfileAction(action: string): void {
  }

  onThemeToggle(theme: 'light' | 'dark'): void {
    // Theme service will handle the actual theme change
  }
  onOrgConfigChange(orgConfig: any): void {
    // The org_path cookie is already set by the shared header component
    // Additional logic can be added here if needed for Product Studio specific handling
  }
}
