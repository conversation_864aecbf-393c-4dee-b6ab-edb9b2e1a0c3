/* eslint-disable prettier/prettier */
/* eslint-disable import/order */
import { authInterceptor, errorInterceptor } from '@shared';
import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptors,
} from '@angular/common/http';
import {
  ApplicationConfig,
  importProvidersFrom,
  provideZoneChangeDetection,
} from '@angular/core';
import { provideRouter } from '@angular/router';
import {
  LucideAngularModule,
  Hammer,
  User,
  Settings,
  Info,
  ChevronLeft,
  ChevronRight,
  ShieldAlert,
  Hourglass,
  CircleCheck,
  XCircle,
  AlignVerticalDistributeStart,
  CircleCheckBig,
  MoveLeft,
  Play,
  CalendarDays,
  EllipsisVertical,
  SquarePen,
  Wifi,
  Search,
  AlertCircle,
  EyeOff,
  Mail,
  Phone,
  Check,
  X,
  Edit,
  Trash,
  Plus,
  Minus,
  ChevronDown,
  ChevronUp,
  Eye,
  Home,
  Layout,
  Bell,
  Grid,
  Star,
  Leaf,
  CheckCircle,
  AlertTriangle,
  XOctagon,
  Sparkles,
  Slash,
  Feather,
  Globe,
  Send,
  Box,
  <PERSON>clip,
  <PERSON>t,
  Archive,
  Copy,
  Trash2,
  Users,
  Wrench,
  TrendingUp,
  PanelLeft,
  BookOpen,
  NotebookText,
  Redo,
  RotateCcw,
  Swords,
  Undo,
  Pencil,
  RotateCw,
  SendHorizontal,
  WandSparkles,
  MousePointer2,
  Hand,
  ZoomIn,
  ZoomOut,
  Clock,
  CircleX,
  FileText,
  Download,
  Save,
  LayoutGrid,
} from 'lucide-angular';
import { MarkdownModule } from 'ngx-markdown';

import { routes } from './app.routes';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
  provideHttpClient(withInterceptors([authInterceptor, errorInterceptor])),
   
    importProvidersFrom(
      LucideAngularModule.pick({
        Hammer,
        User,
        Settings,
        Info,
        ChevronLeft,
        ChevronRight,
        ShieldAlert,
        Hourglass,
        CircleCheck,
        XCircle,
        AlignVerticalDistributeStart,
        CircleCheckBig,
        MoveLeft,
        Play,
        CalendarDays,
        EllipsisVertical,
        SquarePen,
        Wifi,
        Search,
        AlertCircle,
        EyeOff,
        Mail,
        Phone,
        Check,
        X,
        Edit,
        Trash,
        Plus,
        Minus,
        ChevronDown,
        ChevronUp,
        Eye,
        Home,
        Layout,
        Bell,
        Grid,
        Star,
        Leaf,
        CheckCircle,
        AlertTriangle,
        XOctagon,
        Sparkles,
        Slash,
        Feather,
        Globe,
        Send,
        Box,
        Paperclip,
        Bot,
        Archive,
        Copy,
        Trash2,
        Users,
        Wrench,
        TrendingUp,
        PanelLeft,
        BookOpen,
        NotebookText,
        Redo,
        RotateCcw,
        Swords,
        Undo,
        Pencil,
        RotateCw,
        SendHorizontal,
        WandSparkles,
        MousePointer2,
        Hand,
        ZoomIn,
        ZoomOut,
        Clock,
        CircleX,
        FileText,
        Download,
        Save,
        LayoutGrid,
      }),
      MarkdownModule.forRoot()
    ),
  ],
};
