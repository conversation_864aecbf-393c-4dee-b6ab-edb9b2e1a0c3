/* eslint-disable prettier/prettier */
// Helper function to safely get environment variables from window.env
const getRequiredEnv = (key: string): string => {
  interface EnvWindow extends Window { env?: Record<string, string>; }
  const envWindow = window as EnvWindow;
  const value = envWindow.env?.[key];
  if (value === undefined || value === null) {
    throw new Error(`Environment variable '${key}' is not defined in window.env.`);
  }
  return String(value);
};

export const environment = {
  production: false,

  // API Configuration
  // API_BASE_URL will be injected from Docker environment, fallback to production default
  // apiBaseUrl: (window as any).__env?.API_BASE_URL || 'https://aava-dev.avateam.io/server/product',
  // apiUrl: (window as any).__env?.API_BASE_URL || 'https://ava-plus-product-studio-api-dev.azurewebsites.net',

  // Pipeline API Configuration
  
  // // Authentication Configuration
  // productStudioApiAuthUrl: 'https://aava-dev.avateam.io/api/auth',
  // productStudioRedirectUrl: 'https://aava-dev.avateam.io/product/',

//  pipelineApiBaseUrl: "https://aava-dev.avateam.io/server/product/api/v1",
  
  // Helper function to get API endpoint with base URL
  apiBaseUrl: getRequiredEnv('baseUrl'),
  apiUrl: getRequiredEnv('baseUrl'),
  pipelineApiBaseUrl: getRequiredEnv('pipelineApiBaseUrl'),
  productStudioApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),
  productStudioRedirectUrl: getRequiredEnv('productStudioUrl'),
  // Studio app URLs
  elderWandUrl: getRequiredEnv('elderWandUrl'),
  experienceStudioUrl: getRequiredEnv('experienceStudioUrl'),
  consoleUrl: getRequiredEnv('consoleUrl'),
  getApiUrl: (endpoint: string) => {
    const baseUrl = getRequiredEnv('baseUrl');
    return `${baseUrl}${endpoint}`;
  }
};
