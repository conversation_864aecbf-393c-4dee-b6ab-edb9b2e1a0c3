// Environment configuration for Product Studio Application - Production
export const environment = {
  production: true,

  // API Configuration
  // API_BASE_URL will be injected from Docker environment, fallback to production default
  apiBaseUrl:
    (window as any).__env?.API_BASE_URL ||
    'https://aava-dev.avateam.io/server/product',
  apiUrl:
    (window as any).__env?.API_BASE_URL ||
    'https://ava-plus-product-studio-api-dev.azurewebsites.net',

  // Pipeline API Configuration
  pipelineApiBaseUrl: 'https://aava-dev.avateam.io/server/product/api/v1',

  // Authentication Configuration
  productStudioApiAuthUrl: 'https://aava-dev.avateam.io/api/auth',
  productStudioRedirectUrl: 'https://aava-dev.avateam.io/product/',

  // Studio app URLs
  elderWandUrl:
    (window as any).__env?.elderWandUrl ||
    'https://aava-dev.avateam.io/launchpad/',
  experienceStudioUrl:
    (window as any).__env?.experienceStudioUrl ||
    'https://aava-dev.avateam.io/experience-studio',
  consoleUrl:
    (window as any).__env?.consoleUrl || 'https://aava-dev.avateam.io/console/',

  // Helper function to get API endpoint with base URL
  getApiUrl: (endpoint: string) => {
    const baseUrl =
      (window as any).__env?.API_BASE_URL ||
      'https://ava-plus-product-studio-api-dev.azurewebsites.net';
    return `${baseUrl}${endpoint}`;
  },
};
