<svg width="360" height="68" viewBox="0 0 360 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_5395_35210)">
<mask id="path-1-inside-1_5395_35210" fill="white">
<path d="M13 34C13 20.7452 23.7452 10 37 10H304C317.255 10 328 20.7452 328 34V34C328 47.2548 317.255 58 304 58H37C23.7452 58 13 47.2548 13 34V34Z"/>
</mask>
<g clip-path="url(#paint0_angular_5395_35210_clip_path)" data-figma-skip-parse="true" mask="url(#path-1-inside-1_5395_35210)"><g transform="matrix(0 0.024 -0.199183 0 170.5 34)"><foreignObject x="-3333.33" y="-3333.33" width="6666.67" height="6666.67"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(195, 133, 251, 1) 0deg,rgba(188, 130, 243, 1) 60.2401deg,rgba(244, 185, 234, 1) 85.1532deg,rgba(141, 152, 255, 1) 126.663deg,rgba(170, 110, 238, 1) 209.329deg,rgba(255, 103, 119, 1) 250.911deg,rgba(255, 186, 113, 1) 291.415deg,rgba(198, 134, 255, 1) 332.659deg,rgba(195, 133, 251, 1) 360deg);height:100%;width:100%;opacity:0.4"></div></foreignObject></g></g><path d="M37 10V66H304V10V-46H37V10ZM304 58V2H37V58V114H304V58ZM37 58V2C54.6731 2 69 16.3269 69 34H13H-43C-43 78.1828 -7.18279 114 37 114V58ZM328 34H272C272 16.3269 286.327 2 304 2V58V114C348.183 114 384 78.1828 384 34H328ZM304 10V66C286.327 66 272 51.6731 272 34H328H384C384 -10.1828 348.183 -46 304 -46V10ZM37 10V-46C-7.18278 -46 -43 -10.1828 -43 34H13H69C69 51.6731 54.6731 66 37 66V10Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;transform&#34;:{&#34;m00&#34;:1.9288188735544672e-14,&#34;m01&#34;:-398.36529541015625,&#34;m02&#34;:369.68264770507812,&#34;m10&#34;:48.0,&#34;m11&#34;:-1.6120662230716880e-15,&#34;m12&#34;:10.0},&#34;opacity&#34;:0.40000000596046448,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-1-inside-1_5395_35210)"/>
</g>
<g filter="url(#filter1_f_5395_35210)">
<mask id="path-3-inside-2_5395_35210" fill="white">
<path d="M13 34C13 20.7452 23.7452 10 37 10H304C317.255 10 328 20.7452 328 34V34C328 47.2548 317.255 58 304 58H37C23.7452 58 13 47.2548 13 34V34Z"/>
</mask>
<g clip-path="url(#paint1_angular_5395_35210_clip_path)" data-figma-skip-parse="true" mask="url(#path-3-inside-2_5395_35210)"><g transform="matrix(0 0.024 -0.199183 0 170.5 34)"><foreignObject x="-3333.33" y="-3333.33" width="6666.67" height="6666.67"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(195, 133, 251, 1) 0deg,rgba(188, 130, 243, 1) 60.2401deg,rgba(244, 185, 234, 1) 85.1532deg,rgba(141, 152, 255, 1) 126.663deg,rgba(170, 110, 238, 1) 209.329deg,rgba(255, 103, 119, 1) 250.911deg,rgba(255, 186, 113, 1) 291.415deg,rgba(198, 134, 255, 1) 332.659deg,rgba(195, 133, 251, 1) 360deg);height:100%;width:100%;opacity:0.4"></div></foreignObject></g></g><path d="M37 10V66H304V10V-46H37V10ZM304 58V2H37V58V114H304V58ZM37 58V2C54.6731 2 69 16.3269 69 34H13H-43C-43 78.1828 -7.18279 114 37 114V58ZM328 34H272C272 16.3269 286.327 2 304 2V58V114C348.183 114 384 78.1828 384 34H328ZM304 10V66C286.327 66 272 51.6731 272 34H328H384C384 -10.1828 348.183 -46 304 -46V10ZM37 10V-46C-7.18278 -46 -43 -10.1828 -43 34H13H69C69 51.6731 54.6731 66 37 66V10Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;transform&#34;:{&#34;m00&#34;:1.9288188735544672e-14,&#34;m01&#34;:-398.36529541015625,&#34;m02&#34;:369.68264770507812,&#34;m10&#34;:48.0,&#34;m11&#34;:-1.6120662230716880e-15,&#34;m12&#34;:10.0},&#34;opacity&#34;:0.40000000596046448,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-3-inside-2_5395_35210)"/>
</g>
<g filter="url(#filter2_f_5395_35210)">
<mask id="path-5-inside-3_5395_35210" fill="white">
<path d="M13 34C13 20.7452 23.7452 10 37 10H304C317.255 10 328 20.7452 328 34V34C328 47.2548 317.255 58 304 58H37C23.7452 58 13 47.2548 13 34V34Z"/>
</mask>
<g clip-path="url(#paint2_angular_5395_35210_clip_path)" data-figma-skip-parse="true" mask="url(#path-5-inside-3_5395_35210)"><g transform="matrix(0 0.024 -0.199183 0 170.5 34)"><foreignObject x="-3333.33" y="-3333.33" width="6666.67" height="6666.67"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(195, 133, 251, 1) 0deg,rgba(188, 130, 243, 1) 60.2401deg,rgba(244, 185, 234, 1) 85.1532deg,rgba(141, 152, 255, 1) 126.663deg,rgba(170, 110, 238, 1) 209.329deg,rgba(255, 103, 119, 1) 250.911deg,rgba(255, 186, 113, 1) 291.415deg,rgba(198, 134, 255, 1) 332.659deg,rgba(195, 133, 251, 1) 360deg);height:100%;width:100%;opacity:0.4"></div></foreignObject></g></g><path d="M37 10V66H304V10V-46H37V10ZM304 58V2H37V58V114H304V58ZM37 58V2C54.6731 2 69 16.3269 69 34H13H-43C-43 78.1828 -7.18279 114 37 114V58ZM328 34H272C272 16.3269 286.327 2 304 2V58V114C348.183 114 384 78.1828 384 34H328ZM304 10V66C286.327 66 272 51.6731 272 34H328H384C384 -10.1828 348.183 -46 304 -46V10ZM37 10V-46C-7.18278 -46 -43 -10.1828 -43 34H13H69C69 51.6731 54.6731 66 37 66V10Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;transform&#34;:{&#34;m00&#34;:1.9288188735544672e-14,&#34;m01&#34;:-398.36529541015625,&#34;m02&#34;:369.68264770507812,&#34;m10&#34;:48.0,&#34;m11&#34;:-1.6120662230716880e-15,&#34;m12&#34;:10.0},&#34;opacity&#34;:0.40000000596046448,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-5-inside-3_5395_35210)"/>
</g>
<g style="mix-blend-mode:hard-light" filter="url(#filter3_f_5395_35210)">
<mask id="path-7-inside-4_5395_35210" fill="white">
<path d="M13 34C13 20.7452 23.7452 10 37 10H304C317.255 10 328 20.7452 328 34V34C328 47.2548 317.255 58 304 58H37C23.7452 58 13 47.2548 13 34V34Z"/>
</mask>
<g clip-path="url(#paint3_angular_5395_35210_clip_path)" data-figma-skip-parse="true" mask="url(#path-7-inside-4_5395_35210)"><g transform="matrix(0 0.024 -0.199183 0 170.5 34)"><foreignObject x="-3333.33" y="-3333.33" width="6666.67" height="6666.67"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(195, 133, 251, 1) 0deg,rgba(188, 130, 243, 1) 60.2401deg,rgba(244, 185, 234, 1) 85.1532deg,rgba(141, 152, 255, 1) 126.663deg,rgba(170, 110, 238, 1) 209.329deg,rgba(255, 103, 119, 1) 250.911deg,rgba(255, 186, 113, 1) 291.415deg,rgba(198, 134, 255, 1) 332.659deg,rgba(195, 133, 251, 1) 360deg);height:100%;width:100%;opacity:0.4"></div></foreignObject></g></g><path d="M37 10V66H304V10V-46H37V10ZM304 58V2H37V58V114H304V58ZM37 58V2C54.6731 2 69 16.3269 69 34H13H-43C-43 78.1828 -7.18279 114 37 114V58ZM328 34H272C272 16.3269 286.327 2 304 2V58V114C348.183 114 384 78.1828 384 34H328ZM304 10V66C286.327 66 272 51.6731 272 34H328H384C384 -10.1828 348.183 -46 304 -46V10ZM37 10V-46C-7.18278 -46 -43 -10.1828 -43 34H13H69C69 51.6731 54.6731 66 37 66V10Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;transform&#34;:{&#34;m00&#34;:1.9288188735544672e-14,&#34;m01&#34;:-398.36529541015625,&#34;m02&#34;:369.68264770507812,&#34;m10&#34;:48.0,&#34;m11&#34;:-1.6120662230716880e-15,&#34;m12&#34;:10.0},&#34;opacity&#34;:0.40000000596046448,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-7-inside-4_5395_35210)"/>
</g>
<foreignObject x="-100" y="-96" width="560" height="260"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(50px);clip-path:url(#bgblur_0_5395_35210_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter4_iiii_5395_35210)" data-figma-bg-blur-radius="100">
<path d="M0 34C0 17.4315 13.4315 4 30 4H330C346.569 4 360 17.4315 360 34V34C360 50.5685 346.569 64 330 64H30C13.4314 64 0 50.5685 0 34V34Z" fill="#1E1E1E" fill-opacity="0.5"/>
<path d="M30 4.5H330C346.292 4.5 359.5 17.7076 359.5 34C359.5 50.2924 346.292 63.5 330 63.5H30C13.7076 63.5 0.5 50.2924 0.5 34C0.5 17.7076 13.7076 4.5 30 4.5Z" stroke="#616874"/>
</g>
<defs>
<filter id="filter0_f_5395_35210" x="3" y="0" width="335" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_5395_35210"/>
</filter>
<clipPath id="paint0_angular_5395_35210_clip_path"><path d="M37 10V66H304V10V-46H37V10ZM304 58V2H37V58V114H304V58ZM37 58V2C54.6731 2 69 16.3269 69 34H13H-43C-43 78.1828 -7.18279 114 37 114V58ZM328 34H272C272 16.3269 286.327 2 304 2V58V114C348.183 114 384 78.1828 384 34H328ZM304 10V66C286.327 66 272 51.6731 272 34H328H384C384 -10.1828 348.183 -46 304 -46V10ZM37 10V-46C-7.18278 -46 -43 -10.1828 -43 34H13H69C69 51.6731 54.6731 66 37 66V10Z" mask="url(#path-1-inside-1_5395_35210)"/></clipPath><filter id="filter1_f_5395_35210" x="3" y="0" width="335" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_5395_35210"/>
</filter>
<clipPath id="paint1_angular_5395_35210_clip_path"><path d="M37 10V66H304V10V-46H37V10ZM304 58V2H37V58V114H304V58ZM37 58V2C54.6731 2 69 16.3269 69 34H13H-43C-43 78.1828 -7.18279 114 37 114V58ZM328 34H272C272 16.3269 286.327 2 304 2V58V114C348.183 114 384 78.1828 384 34H328ZM304 10V66C286.327 66 272 51.6731 272 34H328H384C384 -10.1828 348.183 -46 304 -46V10ZM37 10V-46C-7.18278 -46 -43 -10.1828 -43 34H13H69C69 51.6731 54.6731 66 37 66V10Z" mask="url(#path-3-inside-2_5395_35210)"/></clipPath><filter id="filter2_f_5395_35210" x="3" y="0" width="335" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_5395_35210"/>
</filter>
<clipPath id="paint2_angular_5395_35210_clip_path"><path d="M37 10V66H304V10V-46H37V10ZM304 58V2H37V58V114H304V58ZM37 58V2C54.6731 2 69 16.3269 69 34H13H-43C-43 78.1828 -7.18279 114 37 114V58ZM328 34H272C272 16.3269 286.327 2 304 2V58V114C348.183 114 384 78.1828 384 34H328ZM304 10V66C286.327 66 272 51.6731 272 34H328H384C384 -10.1828 348.183 -46 304 -46V10ZM37 10V-46C-7.18278 -46 -43 -10.1828 -43 34H13H69C69 51.6731 54.6731 66 37 66V10Z" mask="url(#path-5-inside-3_5395_35210)"/></clipPath><filter id="filter3_f_5395_35210" x="3" y="0" width="335" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_5395_35210"/>
</filter>
<clipPath id="paint3_angular_5395_35210_clip_path"><path d="M37 10V66H304V10V-46H37V10ZM304 58V2H37V58V114H304V58ZM37 58V2C54.6731 2 69 16.3269 69 34H13H-43C-43 78.1828 -7.18279 114 37 114V58ZM328 34H272C272 16.3269 286.327 2 304 2V58V114C348.183 114 384 78.1828 384 34H328ZM304 10V66C286.327 66 272 51.6731 272 34H328H384C384 -10.1828 348.183 -46 304 -46V10ZM37 10V-46C-7.18278 -46 -43 -10.1828 -43 34H13H69C69 51.6731 54.6731 66 37 66V10Z" mask="url(#path-7-inside-4_5395_35210)"/></clipPath><filter id="filter4_iiii_5395_35210" x="-100" y="-96" width="560" height="260" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-6"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00784314 0 0 0 0 0.00784314 0 0 0 0 0.00784314 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5395_35210"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00784314 0 0 0 0 0.00784314 0 0 0 0 0.00784314 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_5395_35210" result="effect2_innerShadow_5395_35210"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-6"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00784314 0 0 0 0 0.00784314 0 0 0 0 0.00784314 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_5395_35210" result="effect3_innerShadow_5395_35210"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="6"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00769982 0 0 0 0 0.00769982 0 0 0 0 0.00769982 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_5395_35210" result="effect4_innerShadow_5395_35210"/>
</filter>
<clipPath id="bgblur_0_5395_35210_clip_path" transform="translate(100 96)"><path d="M0 34C0 17.4315 13.4315 4 30 4H330C346.569 4 360 17.4315 360 34V34C360 50.5685 346.569 64 330 64H30C13.4314 64 0 50.5685 0 34V34Z"/>
</clipPath></defs>
</svg>
