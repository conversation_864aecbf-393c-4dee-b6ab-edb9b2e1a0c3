<svg width="368" height="68" viewBox="0 0 368 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_5395_35182)" filter="url(#filter0_d_5395_35182)">
<g filter="url(#filter1_f_5395_35182)">
<mask id="path-1-inside-1_5395_35182" fill="white">
<path d="M17 32C17 18.7452 27.7452 8 41 8H308C321.255 8 332 18.7452 332 32V32C332 45.2548 321.255 56 308 56H41C27.7452 56 17 45.2548 17 32V32Z"/>
</mask>
<g clip-path="url(#paint0_angular_5395_35182_clip_path)" data-figma-skip-parse="true" mask="url(#path-1-inside-1_5395_35182)"><g transform="matrix(0 0.024 -0.199183 0 174.5 32)"><foreignObject x="-3333.33" y="-3333.33" width="6666.67" height="6666.67"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(195, 133, 251, 1) 0deg,rgba(188, 130, 243, 1) 60.2401deg,rgba(244, 185, 234, 1) 85.1532deg,rgba(141, 152, 255, 1) 126.663deg,rgba(170, 110, 238, 1) 209.329deg,rgba(255, 103, 119, 1) 250.911deg,rgba(255, 186, 113, 1) 291.415deg,rgba(198, 134, 255, 1) 332.659deg,rgba(195, 133, 251, 1) 360deg);height:100%;width:100%;opacity:0.4"></div></foreignObject></g></g><path d="M41 8V64H308V8V-48H41V8ZM308 56V0H41V56V112H308V56ZM41 56V0C58.6731 0 73 14.3269 73 32H17H-39C-39 76.1828 -3.18279 112 41 112V56ZM332 32H276C276 14.3269 290.327 0 308 0V56V112C352.183 112 388 76.1828 388 32H332ZM308 8V64C290.327 64 276 49.6731 276 32H332H388C388 -12.1828 352.183 -48 308 -48V8ZM41 8V-48C-3.18278 -48 -39 -12.1828 -39 32H17H73C73 49.6731 58.6731 64 41 64V8Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;transform&#34;:{&#34;m00&#34;:1.9288188735544672e-14,&#34;m01&#34;:-398.36529541015625,&#34;m02&#34;:373.68264770507812,&#34;m10&#34;:48.0,&#34;m11&#34;:-1.6120662230716880e-15,&#34;m12&#34;:8.0},&#34;opacity&#34;:0.40000000596046448,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-1-inside-1_5395_35182)"/>
</g>
<g filter="url(#filter2_f_5395_35182)">
<mask id="path-3-inside-2_5395_35182" fill="white">
<path d="M17 32C17 18.7452 27.7452 8 41 8H308C321.255 8 332 18.7452 332 32V32C332 45.2548 321.255 56 308 56H41C27.7452 56 17 45.2548 17 32V32Z"/>
</mask>
<g clip-path="url(#paint1_angular_5395_35182_clip_path)" data-figma-skip-parse="true" mask="url(#path-3-inside-2_5395_35182)"><g transform="matrix(0 0.024 -0.199183 0 174.5 32)"><foreignObject x="-3333.33" y="-3333.33" width="6666.67" height="6666.67"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(195, 133, 251, 1) 0deg,rgba(188, 130, 243, 1) 60.2401deg,rgba(244, 185, 234, 1) 85.1532deg,rgba(141, 152, 255, 1) 126.663deg,rgba(170, 110, 238, 1) 209.329deg,rgba(255, 103, 119, 1) 250.911deg,rgba(255, 186, 113, 1) 291.415deg,rgba(198, 134, 255, 1) 332.659deg,rgba(195, 133, 251, 1) 360deg);height:100%;width:100%;opacity:0.4"></div></foreignObject></g></g><path d="M41 8V64H308V8V-48H41V8ZM308 56V0H41V56V112H308V56ZM41 56V0C58.6731 0 73 14.3269 73 32H17H-39C-39 76.1828 -3.18279 112 41 112V56ZM332 32H276C276 14.3269 290.327 0 308 0V56V112C352.183 112 388 76.1828 388 32H332ZM308 8V64C290.327 64 276 49.6731 276 32H332H388C388 -12.1828 352.183 -48 308 -48V8ZM41 8V-48C-3.18278 -48 -39 -12.1828 -39 32H17H73C73 49.6731 58.6731 64 41 64V8Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;transform&#34;:{&#34;m00&#34;:1.9288188735544672e-14,&#34;m01&#34;:-398.36529541015625,&#34;m02&#34;:373.68264770507812,&#34;m10&#34;:48.0,&#34;m11&#34;:-1.6120662230716880e-15,&#34;m12&#34;:8.0},&#34;opacity&#34;:0.40000000596046448,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-3-inside-2_5395_35182)"/>
</g>
<g filter="url(#filter3_f_5395_35182)">
<mask id="path-5-inside-3_5395_35182" fill="white">
<path d="M17 32C17 18.7452 27.7452 8 41 8H308C321.255 8 332 18.7452 332 32V32C332 45.2548 321.255 56 308 56H41C27.7452 56 17 45.2548 17 32V32Z"/>
</mask>
<g clip-path="url(#paint2_angular_5395_35182_clip_path)" data-figma-skip-parse="true" mask="url(#path-5-inside-3_5395_35182)"><g transform="matrix(0 0.024 -0.199183 0 174.5 32)"><foreignObject x="-3333.33" y="-3333.33" width="6666.67" height="6666.67"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(195, 133, 251, 1) 0deg,rgba(188, 130, 243, 1) 60.2401deg,rgba(244, 185, 234, 1) 85.1532deg,rgba(141, 152, 255, 1) 126.663deg,rgba(170, 110, 238, 1) 209.329deg,rgba(255, 103, 119, 1) 250.911deg,rgba(255, 186, 113, 1) 291.415deg,rgba(198, 134, 255, 1) 332.659deg,rgba(195, 133, 251, 1) 360deg);height:100%;width:100%;opacity:0.4"></div></foreignObject></g></g><path d="M41 8V64H308V8V-48H41V8ZM308 56V0H41V56V112H308V56ZM41 56V0C58.6731 0 73 14.3269 73 32H17H-39C-39 76.1828 -3.18279 112 41 112V56ZM332 32H276C276 14.3269 290.327 0 308 0V56V112C352.183 112 388 76.1828 388 32H332ZM308 8V64C290.327 64 276 49.6731 276 32H332H388C388 -12.1828 352.183 -48 308 -48V8ZM41 8V-48C-3.18278 -48 -39 -12.1828 -39 32H17H73C73 49.6731 58.6731 64 41 64V8Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;transform&#34;:{&#34;m00&#34;:1.9288188735544672e-14,&#34;m01&#34;:-398.36529541015625,&#34;m02&#34;:373.68264770507812,&#34;m10&#34;:48.0,&#34;m11&#34;:-1.6120662230716880e-15,&#34;m12&#34;:8.0},&#34;opacity&#34;:0.40000000596046448,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-5-inside-3_5395_35182)"/>
</g>
<g style="mix-blend-mode:hard-light" filter="url(#filter4_f_5395_35182)">
<mask id="path-7-inside-4_5395_35182" fill="white">
<path d="M17 32C17 18.7452 27.7452 8 41 8H308C321.255 8 332 18.7452 332 32V32C332 45.2548 321.255 56 308 56H41C27.7452 56 17 45.2548 17 32V32Z"/>
</mask>
<g clip-path="url(#paint3_angular_5395_35182_clip_path)" data-figma-skip-parse="true" mask="url(#path-7-inside-4_5395_35182)"><g transform="matrix(0 0.024 -0.199183 0 174.5 32)"><foreignObject x="-3333.33" y="-3333.33" width="6666.67" height="6666.67"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(195, 133, 251, 1) 0deg,rgba(188, 130, 243, 1) 60.2401deg,rgba(244, 185, 234, 1) 85.1532deg,rgba(141, 152, 255, 1) 126.663deg,rgba(170, 110, 238, 1) 209.329deg,rgba(255, 103, 119, 1) 250.911deg,rgba(255, 186, 113, 1) 291.415deg,rgba(198, 134, 255, 1) 332.659deg,rgba(195, 133, 251, 1) 360deg);height:100%;width:100%;opacity:0.4"></div></foreignObject></g></g><path d="M41 8V64H308V8V-48H41V8ZM308 56V0H41V56V112H308V56ZM41 56V0C58.6731 0 73 14.3269 73 32H17H-39C-39 76.1828 -3.18279 112 41 112V56ZM332 32H276C276 14.3269 290.327 0 308 0V56V112C352.183 112 388 76.1828 388 32H332ZM308 8V64C290.327 64 276 49.6731 276 32H332H388C388 -12.1828 352.183 -48 308 -48V8ZM41 8V-48C-3.18278 -48 -39 -12.1828 -39 32H17H73C73 49.6731 58.6731 64 41 64V8Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.73725491762161255,&#34;g&#34;:0.50980395078659058,&#34;b&#34;:0.95294117927551270,&#34;a&#34;:1.0},&#34;position&#34;:0.16733372211456299},{&#34;color&#34;:{&#34;r&#34;:0.95904946327209473,&#34;g&#34;:0.72734165191650391,&#34;b&#34;:0.91948962211608887,&#34;a&#34;:1.0},&#34;position&#34;:0.23653654754161835},{&#34;color&#34;:{&#34;r&#34;:0.55441081523895264,&#34;g&#34;:0.59937852621078491,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.35184183716773987},{&#34;color&#34;:{&#34;r&#34;:0.66666668653488159,&#34;g&#34;:0.43137255311012268,&#34;b&#34;:0.93333333730697632,&#34;a&#34;:1.0},&#34;position&#34;:0.58146977424621582},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.40445962548255920,&#34;b&#34;:0.46972432732582092,&#34;a&#34;:1.0},&#34;position&#34;:0.69697558879852295},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.73015475273132324,&#34;b&#34;:0.44365233182907104,&#34;a&#34;:1.0},&#34;position&#34;:0.80948722362518311},{&#34;color&#34;:{&#34;r&#34;:0.77719807624816895,&#34;g&#34;:0.52703452110290527,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.92405146360397339}],&#34;transform&#34;:{&#34;m00&#34;:1.9288188735544672e-14,&#34;m01&#34;:-398.36529541015625,&#34;m02&#34;:373.68264770507812,&#34;m10&#34;:48.0,&#34;m11&#34;:-1.6120662230716880e-15,&#34;m12&#34;:8.0},&#34;opacity&#34;:0.40000000596046448,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-7-inside-4_5395_35182)"/>
</g>
<foreignObject x="-96" y="-98" width="560" height="260"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(50px);clip-path:url(#bgblur_1_5395_35182_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter5_iiii_5395_35182)" data-figma-bg-blur-radius="100">
<path d="M4 32C4 15.4315 17.4315 2 34 2H334C350.569 2 364 15.4315 364 32V32C364 48.5685 350.569 62 334 62H34C17.4314 62 4 48.5685 4 32V32Z" fill="white" fill-opacity="0.6"/>
<path d="M34 2.5H334C350.292 2.5 363.5 15.7076 363.5 32C363.5 48.2924 350.292 61.5 334 61.5H34C17.7076 61.5 4.5 48.2924 4.5 32C4.5 15.7076 17.7076 2.5 34 2.5Z" stroke="#BBBEC5"/>
</g>
</g>
<defs>
<filter id="filter0_d_5395_35182" x="0" y="0" width="368" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.819608 0 0 0 0 0.827451 0 0 0 0 0.847059 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5395_35182"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5395_35182" result="shape"/>
</filter>
<filter id="filter1_f_5395_35182" x="7" y="-2" width="335" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_5395_35182"/>
</filter>
<clipPath id="paint0_angular_5395_35182_clip_path"><path d="M41 8V64H308V8V-48H41V8ZM308 56V0H41V56V112H308V56ZM41 56V0C58.6731 0 73 14.3269 73 32H17H-39C-39 76.1828 -3.18279 112 41 112V56ZM332 32H276C276 14.3269 290.327 0 308 0V56V112C352.183 112 388 76.1828 388 32H332ZM308 8V64C290.327 64 276 49.6731 276 32H332H388C388 -12.1828 352.183 -48 308 -48V8ZM41 8V-48C-3.18278 -48 -39 -12.1828 -39 32H17H73C73 49.6731 58.6731 64 41 64V8Z" mask="url(#path-1-inside-1_5395_35182)"/></clipPath><filter id="filter2_f_5395_35182" x="7" y="-2" width="335" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_5395_35182"/>
</filter>
<clipPath id="paint1_angular_5395_35182_clip_path"><path d="M41 8V64H308V8V-48H41V8ZM308 56V0H41V56V112H308V56ZM41 56V0C58.6731 0 73 14.3269 73 32H17H-39C-39 76.1828 -3.18279 112 41 112V56ZM332 32H276C276 14.3269 290.327 0 308 0V56V112C352.183 112 388 76.1828 388 32H332ZM308 8V64C290.327 64 276 49.6731 276 32H332H388C388 -12.1828 352.183 -48 308 -48V8ZM41 8V-48C-3.18278 -48 -39 -12.1828 -39 32H17H73C73 49.6731 58.6731 64 41 64V8Z" mask="url(#path-3-inside-2_5395_35182)"/></clipPath><filter id="filter3_f_5395_35182" x="7" y="-2" width="335" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_5395_35182"/>
</filter>
<clipPath id="paint2_angular_5395_35182_clip_path"><path d="M41 8V64H308V8V-48H41V8ZM308 56V0H41V56V112H308V56ZM41 56V0C58.6731 0 73 14.3269 73 32H17H-39C-39 76.1828 -3.18279 112 41 112V56ZM332 32H276C276 14.3269 290.327 0 308 0V56V112C352.183 112 388 76.1828 388 32H332ZM308 8V64C290.327 64 276 49.6731 276 32H332H388C388 -12.1828 352.183 -48 308 -48V8ZM41 8V-48C-3.18278 -48 -39 -12.1828 -39 32H17H73C73 49.6731 58.6731 64 41 64V8Z" mask="url(#path-5-inside-3_5395_35182)"/></clipPath><filter id="filter4_f_5395_35182" x="7" y="-2" width="335" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_5395_35182"/>
</filter>
<clipPath id="paint3_angular_5395_35182_clip_path"><path d="M41 8V64H308V8V-48H41V8ZM308 56V0H41V56V112H308V56ZM41 56V0C58.6731 0 73 14.3269 73 32H17H-39C-39 76.1828 -3.18279 112 41 112V56ZM332 32H276C276 14.3269 290.327 0 308 0V56V112C352.183 112 388 76.1828 388 32H332ZM308 8V64C290.327 64 276 49.6731 276 32H332H388C388 -12.1828 352.183 -48 308 -48V8ZM41 8V-48C-3.18278 -48 -39 -12.1828 -39 32H17H73C73 49.6731 58.6731 64 41 64V8Z" mask="url(#path-7-inside-4_5395_35182)"/></clipPath><filter id="filter5_iiii_5395_35182" x="-96" y="-98" width="560" height="260" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-6"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.94902 0 0 0 0 0.94902 0 0 0 0 0.94902 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5395_35182"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.94902 0 0 0 0 0.94902 0 0 0 0 0.94902 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_5395_35182" result="effect2_innerShadow_5395_35182"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-6"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.94902 0 0 0 0 0.94902 0 0 0 0 0.94902 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_5395_35182" result="effect3_innerShadow_5395_35182"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="6"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.94902 0 0 0 0 0.94902 0 0 0 0 0.94902 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_5395_35182" result="effect4_innerShadow_5395_35182"/>
</filter>
<clipPath id="bgblur_1_5395_35182_clip_path" transform="translate(96 98)"><path d="M4 32C4 15.4315 17.4315 2 34 2H334C350.569 2 364 15.4315 364 32V32C364 48.5685 350.569 62 334 62H34C17.4314 62 4 48.5685 4 32V32Z"/>
</clipPath><clipPath id="clip0_5395_35182">
<rect width="360" height="60" fill="white" transform="translate(4 2)"/>
</clipPath>
</defs>
</svg>
