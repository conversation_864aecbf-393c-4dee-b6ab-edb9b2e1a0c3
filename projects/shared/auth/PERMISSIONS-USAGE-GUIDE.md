# Role-Based Access Control (RBAC) Usage Guide

## 🚀 Quick Start

### 1. Set User Role
```typescript
// In your app component or after login
this.authService.setUserRole(8); // roleId = 8
```

### 2. Use in Templates
```html
<!-- Show button if user can create workflows -->
<button *appHasPermission="'Workflows:create'">Create Workflow</button>

<!-- Hide element if user has permission -->
<div *appNoPermission="'Settings:delete'">Cannot delete settings</div>
```

### 3. Use in Components
```typescript
// Check permissions
const canCreate = this.permissionsStore.hasPermission('Workflows', 'create');
const canApprove = this.permissionsStore.hasPermission('Collaborative Agents', 'approve');
```

## 🎯 Template Directives

### Basic Permission Check
```html
<!-- Single permission -->
<button *appHasPermission="'Workflows:create'">Create</button>

<!-- Alternative syntax -->
<button *appHasPermission 
        appPermissionPage="Workflows" 
        appPermissionAction="create">
  Create
</button>
```

### Multiple Permissions
```html
<!-- Any permission -->
<div *appHasAnyPermission="[
  {page: 'Workflows', action: 'create'},
  {page: 'Tools', action: 'create'}
]">
  Can create content
</div>

<!-- All permissions -->
<div *appHasAllPermissions="[
  {page: 'Collaborative Agents', action: 'approve'},
  {page: 'Collaborative Agents', action: 'reject'}
]">
  Can approve/reject agents
</div>
```

## 🛡️ Route Protection

```typescript
const routes: Routes = [
  {
    path: 'workflows',
    component: WorkflowsComponent,
    canActivate: [PermissionGuard],
    data: {
      permission: {
        page: 'Workflows',
        action: 'read'
      }
    }
  }
];
```

## 🔧 Service Usage

```typescript
// Check permissions
const hasPermission = this.permissionsStore.hasPermission('Workflows', 'create');

// Get page actions
const actions = this.permissionsStore.getPageActions('Workflows');

// Check if can access page
const canAccess = this.permissionsStore.canAccessPage('Analytics');
```

## 📊 Available Pages & Actions

### Pages
- PlayGround, Prompts, Workflows, Tools, Security, Analytics
- GuardRails, Collaborative Agents, Individual Agents
- Models, ChatBots, Collections, Settings, Plugins

### Actions
- create, read, update, delete, import, export, download, approve, reject
