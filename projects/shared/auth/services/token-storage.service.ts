import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class TokenStorageService {
  private ACCESS_TOKEN_KEY = 'access_token';
  private REFRESH_TOKEN_KEY = 'refresh_token';
  private DA_NAME_KEY = 'da_name';
  private DA_USERNAME_KEY = 'da_username';
  private DA_ID_TOKEN = 'id_token';
  private LOGIN_TYPE_KEY = 'login_type';
  private ACCESS_TOKEN_EXPIRES_KEY = 'access_token_expires';

  private getCurrentDomain(): string {
    const hostname = window.location.hostname;
    const port = window.location.port;

    // For Docker setup: all apps run on localhost without ports
    if (
      hostname === 'localhost' &&
      (port === '' || port === '80' || port === '443')
    ) {
      return '';
    }

    // For traditional local development with ports
    if (
      hostname === 'localhost' &&
      ['4200', '4201', '4202', '4203', '4204'].includes(port)
    ) {
      return '';
    }

    // For production, use the hostname
    return hostname;
  }

  public storeLoginType(loginType: 'sso' | 'basic'): void {
    this.setCookie(this.LOGIN_TYPE_KEY, loginType);
  }

  public getLoginType(): 'sso' | 'basic' | null {
    const loginType = this.getCookie(this.LOGIN_TYPE_KEY);
    return loginType === 'sso' || loginType === 'basic' ? loginType : null;
  }

  public setCookie(cname: string, cvalue: string, exseconds?: number): void {
    const d = new Date();
    let expires = '';
    if (exseconds) {
      d.setTime(d.getTime() + exseconds * 1000);
      expires = `expires=${d.toUTCString()}`;
    }

    const secureFlag = window.location.protocol === 'https:' ? ';Secure' : '';
    const sameSiteFlag = ';SameSite=Lax'; // Changed from Strict to Lax for cross-app compatibility
    let domainFlag = '';

    const domain = this.getCurrentDomain();
    if (domain) {
      domainFlag = `;domain=${domain}`;
    }

    document.cookie = `${cname}=${cvalue};${expires};path=/;${secureFlag}${sameSiteFlag}${domainFlag}`;
  }

  public getCookie(name: string): string | null {
    const match = document.cookie.match(new RegExp(`(^| )${name}=([^;]+)`));
    const value = match ? decodeURIComponent(match[2]) : null;
    return value;
  }

  public deleteCookie(name: string): void {
    const domain = this.getCurrentDomain();
    let domainFlag = '';

    if (domain) {
      domainFlag = `;domain=${domain}`;
    }

    document.cookie = `${name}=; path=/; max-age=0; SameSite=Lax${domainFlag}`;

    // Also try to delete from the current hostname (without domain prefix)
    // in case cookies were set before the domain sharing was implemented
    const currentHostname = window.location.hostname;
    if (domain && domain !== currentHostname) {
      document.cookie = `${name}=; path=/; max-age=0; SameSite=Lax;domain=${currentHostname}`;
    }

    // If deleting access token, also clear the expiry time
    if (name === this.ACCESS_TOKEN_KEY) {
      this.deleteCookie(this.ACCESS_TOKEN_EXPIRES_KEY);
    }
  }

  storeTokens(
    accessToken: string,
    refreshToken: string,
    expiresInSeconds: number
  ): void {
    this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);
    this.setCookie(this.REFRESH_TOKEN_KEY, refreshToken);

    // Store expiry time in cookies for consistency
    const expiryTime = new Date();
    expiryTime.setSeconds(expiryTime.getSeconds() + expiresInSeconds);
    this.setCookie(this.ACCESS_TOKEN_EXPIRES_KEY, expiryTime.toISOString());
  }

  storeAccessToken(accessToken: string, expiresInSeconds?: number): void {
    this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);
  }

  storeDaInfo(daName: string, daUsername: string, idToken: string): void {
    this.setCookie(this.DA_NAME_KEY, daName);
    this.setCookie(this.DA_USERNAME_KEY, daUsername);
    this.setCookie(this.DA_ID_TOKEN, idToken);
  }

  getAccessToken(): string | null {
    return this.getCookie(this.ACCESS_TOKEN_KEY);
  }

  getRefreshToken(): string | null {
    return this.getCookie(this.REFRESH_TOKEN_KEY);
  }

  getDaName(): string | null {
    return this.getCookie(this.DA_NAME_KEY);
  }

  getDaUsername(): string | null {
    return this.getCookie(this.DA_USERNAME_KEY);
  }

  getIdToken(): string | null {
    return this.getCookie(this.DA_ID_TOKEN);
  }

  getTokenExpiryTime(): string | null {
    return this.getCookie(this.ACCESS_TOKEN_EXPIRES_KEY);
  }

  hasValidTokens(): boolean {
    const accessToken = this.getAccessToken();
    const refreshToken = this.getRefreshToken();
    return !!(accessToken || refreshToken);
  }

  isAccessTokenExpired(): boolean {
    const accessToken = this.getAccessToken();
    if (!accessToken) return true;

    try {
      // Validate JWT structure first
      const parts = accessToken.split('.');
      if (parts.length !== 3) return true;

      const payload = JSON.parse(atob(parts[1]));
      if (!payload.exp) return true;

      const expiryTime = payload.exp * 1000; // Convert to milliseconds
      const now = Date.now();

      // Add 30 second buffer to account for clock skew
      return now >= expiryTime - 30000;
    } catch {
      return true; // If can't parse, assume expired
    }
  }

  getTokenTimeRemaining(): number {
    const accessToken = this.getAccessToken();
    if (!accessToken) return 0;

    try {
      const parts = accessToken.split('.');
      if (parts.length !== 3) return 0;

      const payload = JSON.parse(atob(parts[1]));
      if (!payload.exp) return 0;

      const expiryTime = payload.exp * 1000;
      const now = Date.now();
      return Math.max(0, expiryTime - now);
    } catch {
      return 0;
    }
  }

  clearTokens(): void {
    this.deleteCookie(this.ACCESS_TOKEN_KEY);
    this.deleteCookie(this.REFRESH_TOKEN_KEY);
    this.deleteCookie(this.DA_NAME_KEY);
    this.deleteCookie(this.DA_USERNAME_KEY);
    this.deleteCookie(this.DA_ID_TOKEN);
    this.deleteCookie(this.LOGIN_TYPE_KEY);
    this.deleteCookie(this.ACCESS_TOKEN_EXPIRES_KEY);
  }
}
