import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable, inject, signal, computed } from '@angular/core';
import { Router } from '@angular/router';
import {
  CentralizedRedirectService,
  TokenStorageService,
  AuthConfig,
  ILoginResponse,
  ITokenResponse,
  ITokenPair,
} from '@shared';
import { Observable, tap, map, of, throwError, catchError } from 'rxjs';

import { UserDetailsService } from '../../services/user-details.service';
import type { UserDetails } from '../../services/user-details.service';
import { PermissionsStore } from '../stores/permissions.store';

// Simple auth state interface
interface AuthState {
  isAuthenticated: boolean;
  loginType: 'sso' | 'basic' | null;
  user: {
    name: string;
    email: string;
    idToken: string;
    roleId?: number; // Add role ID for permissions
  } | null;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private http = inject(HttpClient);
  private router = inject(Router);
  private tokenStorage = inject(TokenStorageService);
  private centralizedRedirectService = inject(CentralizedRedirectService);
  private permissionsStore = inject(PermissionsStore);
  private userDetailsService = inject(UserDetailsService);

  private authConfig: AuthConfig | null = null;

  // Simple signal-based state management
  private readonly _authState = signal<AuthState>({
    isAuthenticated: false,
    loginType: null,
    user: null,
  });

  // Public computed signals
  public readonly authState = this._authState.asReadonly();
  public readonly isAuthenticated = computed(
    () => this._authState().isAuthenticated
  );
  public readonly loginType = computed(() => this._authState().loginType);
  public readonly user = computed(() => this._authState().user);

  constructor() {
    // Initialize state from storage
    this.initializeState();

    // Start token monitoring
    this.startTokenMonitoring();
  }

  // Configuration methods
  public setAuthConfig(config: AuthConfig): void {
    this.authConfig = config;
  }

  public getAuthConfig(): AuthConfig | null {
    return this.authConfig;
  }

  public getPostLoginRedirectUrl(): string {
    return this.authConfig?.postLoginRedirectUrl || '/dashboard';
  }

  public getApiAuthUrl(): string {
    if (!this.authConfig?.apiAuthUrl) {
      throw new Error(
        'Auth configuration not set. Call setAuthConfig() first.'
      );
    }
    return this.authConfig.apiAuthUrl;
  }

  private getRedirectUrl(): string {
    if (!this.authConfig?.redirectUrl) {
      throw new Error(
        'Auth configuration not set. Call setAuthConfig() first.'
      );
    }
    return this.authConfig.redirectUrl;
  }

  private getAuthHeaders(): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Basic YWRtaW46YWRtaW4xMjM=`,
    });
  }

  // State management
  private initializeState(): void {
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();
    const loginType = this.tokenStorage.getLoginType();
    const userName = this.tokenStorage.getCookie('da_name');
    const userEmail = this.tokenStorage.getCookie('da_username');
    const idToken = this.tokenStorage.getIdToken();

    this._authState.set({
      isAuthenticated: !!(accessToken || refreshToken),
      loginType: loginType || null,
      user: accessToken
        ? {
            name: userName || '',
            email: userEmail || '',
            idToken: idToken || '',
          }
        : null,
    });
  }

  private updateState(isAuthenticated: boolean, user?: any): void {
    this._authState.update(state => ({
      ...state,
      isAuthenticated,
      user: isAuthenticated ? user : null,
    }));

    // Load permissions if user is authenticated and has a roleId
    if (isAuthenticated && user?.roleId) {
      this.loadUserPermissions(user.roleId);
    } else if (!isAuthenticated) {
      // Clear permissions when user logs out
      this.permissionsStore.clearPermissions();
    }
  }

  // Token monitoring
  private startTokenMonitoring(): void {
    // Check token every 5 minutes
    setInterval(
      () => {
        this.checkTokenStatus();
      },
      70 * 1000
    );
  }

  private checkTokenStatus(): void {
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();

    if (!accessToken && refreshToken) {
      // Try to refresh token based on login type
      const loginType = this.tokenStorage.getLoginType();
      if (loginType === 'basic') {
        this.basicRefreshToken().subscribe({
          error: () => {
            // Refresh failed, clear state
            this.clearAuthState();
          },
        });
      } else {
        this.refreshToken().subscribe({
          error: () => {
            // Refresh failed, clear state
            this.clearAuthState();
          },
        });
      }
    } else if (!accessToken && !refreshToken) {
      // No tokens, clear state
      this.clearAuthState();
    }
  }

  // Public methods
  public getLoginFlow(): 'sso' | 'basic' {
    return this.tokenStorage.getLoginType() || 'sso';
  }

  public isUserAuthenticated(): boolean {
    return this.isAuthenticated();
  }

  // SSO Authentication
  public loginSSO(redirectUrl?: string): Observable<ILoginResponse> {
    const finalRedirectUrl = redirectUrl || this.getRedirectUrl();
    const url = `${this.getApiAuthUrl()}/login-url?redirectUrl=${encodeURIComponent(finalRedirectUrl)}`;

    return this.http
      .get<ILoginResponse>(url, { headers: this.getAuthHeaders() })
      .pipe(
        tap(({ loginUrl }) => {
          window.location.href = loginUrl;
        }),
        catchError(error => {
          console.error('SSO login failed:', error);
          return throwError(() => error);
        })
      );
  }

  public exchangeCodeForToken(
    code: string,
    redirectUrl?: string
  ): Observable<ITokenPair> {
    const finalRedirectUrl = redirectUrl || this.getRedirectUrl();
    const encodedRedirectUrl = encodeURIComponent(finalRedirectUrl);
    const url = `${this.getApiAuthUrl()}/token?redirectUrl=${encodedRedirectUrl}`;

    return this.http
      .post<ITokenResponse>(url, { code }, { headers: this.getAuthHeaders() })
      .pipe(
        tap(response => {
          this.tokenStorage.storeLoginType('sso');
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: da_name,
            email: da_username,
            id_token: idToken,
          } = response;

          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);

          this.updateState(true, {
            name: da_name,
            email: da_username,
            idToken,
          });

          // Set default org_path if not exists
          if (!this.tokenStorage.getCookie('org_path')) {
            this.setDefaultOrgPath().subscribe();
          }
        }),
        map(response => ({
          accessToken: response.access_token,
          refreshToken: response.refresh_token,
        })),
        catchError(error => {
          console.error('Token exchange failed:', error);
          return throwError(() => error);
        })
      );
  }

  // Token refresh
  public refreshToken(refreshToken?: string): Observable<ITokenPair> {
    const refreshTokenFromStorage = this.tokenStorage.getRefreshToken();
    const url = `${this.getApiAuthUrl()}/refresh-token`;

    return this.http
      .post<ITokenResponse>(
        url,
        { refreshToken: refreshToken || refreshTokenFromStorage },
        { headers: this.getAuthHeaders() }
      )
      .pipe(
        tap(response => {
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: da_name,
            email: da_username,
            id_token: idToken,
          } = response;

          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);

          this.updateState(true, {
            name: da_name,
            email: da_username,
            idToken,
          });

          // Set default org_path if not exists
          if (!this.tokenStorage.getCookie('org_path')) {
            this.setDefaultOrgPath().subscribe();
          }
        }),
        map(response => ({
          accessToken: response.access_token,
          refreshToken: response.refresh_token,
        })),
        catchError(error => {
          console.error('Token refresh failed:', error);
          return throwError(() => error);
        })
      );
  }

  // Basic Authentication
  public basicLoginWithCredentials(
    username: string,
    password: string
  ): Observable<ITokenResponse> {
    const url = `${this.getApiAuthUrl()}/basic/login`;
    const loginBody = { userName: username, password: password };

    return this.http
      .post<ITokenResponse>(url, loginBody, { headers: this.getAuthHeaders() })
      .pipe(
        tap(response => {
          this.tokenStorage.storeLoginType('basic');
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: da_name,
            email: da_username,
            id_token: idToken,
          } = response;

          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);

          this.updateState(true, {
            name: da_name,
            email: da_username,
            idToken,
          });

          // Set default org_path if not exists
          if (!this.tokenStorage.getCookie('org_path')) {
            this.setDefaultOrgPath().subscribe();
          }
        }),
        catchError(error => {
          console.error('Basic login failed:', error);
          return throwError(() => error);
        })
      );
  }

  public basicRefreshToken(): Observable<ITokenResponse> {
    const refreshToken = this.tokenStorage.getRefreshToken();
    const url = `${this.getApiAuthUrl()}/basic/refresh/token`;

    return this.http
      .post<ITokenResponse>(
        url,
        { refreshToken },
        { headers: this.getAuthHeaders() }
      )
      .pipe(
        tap(response => {
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: da_name,
            email: da_username,
            id_token: idToken,
          } = response;

          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);

          this.updateState(true, {
            name: da_name,
            email: da_username,
            idToken,
          });

          // Set default org_path if not exists
          if (!this.tokenStorage.getCookie('org_path')) {
            this.setDefaultOrgPath().subscribe();
          }
        }),
        catchError(error => {
          console.error('Basic token refresh failed:', error);
          return throwError(() => error);
        })
      );
  }

  // Logout
  public logout(redirectUrl?: string): Observable<{ logoutUrl: string }> {
    const idToken = this.tokenStorage.getIdToken();
    const finalRedirectUrl = redirectUrl || `${window.location.origin}/`;
    const url = `${this.getApiAuthUrl()}/logout-url?redirectUrl=${encodeURIComponent(finalRedirectUrl)}`;
    const headers = this.getAuthHeaders().set('X-ID-TOKEN', idToken || '');

    return this.http.get<{ logoutUrl: string }>(url, { headers }).pipe(
      tap(({ logoutUrl }) => {
        this.clearAuthState();
        window.location.href = logoutUrl;
      }),
      catchError(error => {
        console.error('SSO logout failed:', error);
        // Fallback: clear state and redirect
        this.clearAuthState();
        this.centralizedRedirectService.redirectToMarketingLogin();
        return throwError(() => error);
      })
    );
  }

  public basicLogout(): Observable<void> {
    const refreshToken = this.tokenStorage.getRefreshToken();

    if (!refreshToken) {
      this.clearAuthState();
      return of(void 0);
    }

    const url = `${this.getApiAuthUrl()}/basic/logout`;
    const headers = this.getAuthHeaders().set('X-REFRESH-TOKEN', refreshToken);

    return this.http.post(url, null, { headers, responseType: 'text' }).pipe(
      map(() => {
        this.clearAuthState();
        return void 0;
      }),
      catchError(error => {
        console.error('Basic logout failed:', error);
        // Fallback: clear state anyway
        this.clearAuthState();
        return of(void 0);
      })
    );
  }

  // Auth code handling (from AuthTokenService)
  public handleAuthCodeAndToken(): void {
    const authCode = new URLSearchParams(window.location.search).get('code');
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();
    const loginType = this.tokenStorage.getLoginType();

    // If we have an authorization code, process it directly
    if (authCode) {
      this.exchangeCodeForToken(authCode).subscribe({
        next: () => {
          this.centralizedRedirectService.handlePostLoginRedirect(
            this.getApiAuthUrl()
          );
        },
        error: () => {
          this.centralizedRedirectService.redirectToMarketingLogin();
        },
      });
      return;
    }

    // Check for encoded URLs
    if (
      this.router.url.includes('code=') ||
      this.router.url.includes('token=') ||
      this.router.url.includes('%2F%3Fcode%3D') ||
      this.router.url.includes('%2F%3Ftoken%3D')
    ) {
      return;
    }

    if (loginType === 'basic') {
      this.handleBasicLoginFlow(accessToken, refreshToken);
    } else {
      this.handleSSOFlow(accessToken, refreshToken);
    }
  }

  private handleBasicLoginFlow(
    accessToken: string | null,
    refreshToken: string | null
  ): void {
    if (!accessToken && refreshToken) {
      this.refreshToken().subscribe({
        error: () => {
          this.redirectToMarketingLogin();
        },
      });
    } else if (!accessToken && !refreshToken) {
      this.redirectToMarketingLogin();
    }
  }

  private handleSSOFlow(
    accessToken: string | null,
    refreshToken: string | null
  ): void {
    if (!accessToken && refreshToken) {
      this.refreshToken().subscribe({
        error: () => {
          this.redirectToMarketingLogin();
        },
      });
    } else if (!accessToken && !refreshToken) {
      this.redirectToMarketingLogin();
    }
  }

  private redirectToMarketingLogin(): void {
    this.centralizedRedirectService.redirectToMarketingLogin();
  }

  // State management
  public clearAuthState(): void {
    this.tokenStorage.clearTokens();
    this.updateState(false);
  }

  // Permission management
  /**
   * Load permissions for a specific role
   * @param roleId - The role ID to load permissions for
   */
  private loadUserPermissions(roleId: number): void {
    this.permissionsStore.loadPermissions(roleId).subscribe({
      next: () => {},
      error: (error: any) => {
        console.error('Failed to load permissions:', error);
      },
    });
  }

  /**
   * Set user role and load permissions
   * @param roleId - The role ID to set
   */
  public setUserRole(roleId: number): void {
    const currentUser = this._authState().user;
    if (currentUser) {
      const updatedUser = { ...currentUser, roleId };
      this._authState.update(state => ({
        ...state,
        user: updatedUser,
      }));
      // Store role ID in localStorage for persistence
      localStorage.setItem('userRoleId', roleId.toString());
      // Load permissions for the role
      this.loadUserPermissions(roleId);
    }
  }

  /**
   * Get current user role ID
   * @returns The current role ID or null
   */
  public getCurrentRoleId(): number | null {
    const user = this._authState().user;
    if (user && 'roleId' in user) {
      return (user as any).roleId;
    }

    // Fallback: try to get from localStorage
    const storedRoleId = localStorage.getItem('userRoleId');
    return storedRoleId ? parseInt(storedRoleId, 10) : null;
  }

  /**
   * Check if user has permission for a specific page and action
   * @param page - The page to check
   * @param action - The action to check
   * @returns True if user has permission
   */
  public hasPermission(page: string, action: string): boolean {
    const roleId = this.getCurrentRoleId();
    if (!roleId) return false;

    return this.permissionsStore.hasPermission(page as any, action as any);
  }

  /**
   * Check if user can access a specific page
   * @param page - The page to check
   * @returns True if user can access the page
   */
  public canAccessPage(page: string): boolean {
    const roleId = this.getCurrentRoleId();
    if (!roleId) return false;

    return this.permissionsStore.canAccessPage(page as any);
  }

  /**
   * Clear permissions cache
   * @param roleId - Optional role ID to clear specific cache
   */
  public clearPermissionsCache(roleId?: number): void {
    this.permissionsStore.clearCache(roleId);
  }

  // Organization path management
  public setDefaultOrgPath(): Observable<string | null> {
    return this.userDetailsService.getUserDetails(this.getApiAuthUrl()).pipe(
      map(userDetails => {
        const defaultPath = this.findDefaultOrgPath(userDetails);
        if (defaultPath) {
          this.tokenStorage.setCookie('org_path', defaultPath);
          return defaultPath;
        }
        return null;
      })
    );
  }

  private findDefaultOrgPath(userDetails: UserDetails): string | null {
    // Use the first realm from user details to build org_path
    if (userDetails.realms && userDetails.realms.length > 0) {
      const realm = userDetails.realms[0]; // Take the first realm as default
      const orgName = realm.orgName;
      const domainName = realm.domainName;
      const projectName = realm.projectName;
      const teamName = realm.teamName;
      const orgId = realm.orgId;
      const domainId = realm.domainId;
      const projectId = realm.projectId;
      const teamId = realm.teamId;

      return `${orgName}@${domainName}@${projectName}@${teamName}::${orgId}@${domainId}@${projectId}@${teamId}`;
    }
    return null;
  }
}
