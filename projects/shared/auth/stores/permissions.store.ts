import { Injectable, inject, signal, computed, effect } from '@angular/core';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { tap, map } from 'rxjs/operators';

import {
  IPermissionState,
  IAccessControl,
  PermissionPage,
  PermissionAction,
} from '../interfaces/permission.interface';
import { PermissionsService } from '../services/permissions.service';

@Injectable({
  providedIn: 'root',
})
export class PermissionsStore {
  private permissionsService = inject(PermissionsService);

  // Private signals for state management
  private readonly _permissions = signal<IAccessControl[]>([]);
  private readonly _roleId = signal<number | null>(null);
  private readonly _isLoading = signal<boolean>(false);
  private readonly _error = signal<string | null>(null);
  private readonly _lastUpdated = signal<Date | null>(null);

  // Public readonly signals
  public readonly permissions = this._permissions.asReadonly();
  public readonly roleId = this._roleId.asReadonly();
  public readonly isLoading = this._isLoading.asReadonly();
  public readonly error = this._error.asReadonly();
  public readonly lastUpdated = this._lastUpdated.asReadonly();

  // Computed signals
  public readonly hasPermissions = computed(
    () => this._permissions().length > 0
  );
  public readonly accessiblePages = computed(() =>
    this._permissions().map(p => p.page)
  );

  // BehaviorSubjects for RxJS compatibility
  private readonly _permissionsSubject = new BehaviorSubject<IAccessControl[]>(
    []
  );
  public readonly permissions$ = this._permissionsSubject.asObservable();

  private readonly _roleIdSubject = new BehaviorSubject<number | null>(null);
  public readonly roleId$ = this._roleIdSubject.asObservable();

  private readonly _isLoadingSubject = new BehaviorSubject<boolean>(false);
  public readonly isLoading$ = this._isLoadingSubject.asObservable();

  private readonly _errorSubject = new BehaviorSubject<string | null>(null);
  public readonly error$ = this._errorSubject.asObservable();

  constructor() {
    // Sync signals with subjects for backward compatibility
    effect(() => {
      this._permissionsSubject.next(this._permissions());
    });

    effect(() => {
      this._roleIdSubject.next(this._roleId());
    });

    effect(() => {
      this._isLoadingSubject.next(this._isLoading());
    });

    effect(() => {
      this._errorSubject.next(this._error());
    });
  }

  // ==================== STATE MANAGEMENT ====================

  /**
   * Set loading state
   */
  private setLoading(loading: boolean): void {
    this._isLoading.set(loading);
  }

  /**
   * Set error state
   */
  private setError(error: string | null): void {
    this._error.set(error);
  }

  /**
   * Clear error state
   */
  private clearError(): void {
    this._error.set(null);
  }

  /**
   * Update permissions
   */
  private updatePermissions(permissions: IAccessControl[]): void {
    this._permissions.set(permissions);
    this._lastUpdated.set(new Date());
  }

  /**
   * Update role ID
   */
  private updateRoleId(roleId: number | null): void {
    this._roleId.set(roleId);
  }

  // ==================== PUBLIC METHODS ====================

  /**
   * Load permissions for a specific role
   * @param roleId - The role ID to load permissions for
   * @param forceRefresh - Force refresh from server
   */
  loadPermissions(
    roleId: number,
    forceRefresh: boolean = false
  ): Observable<IAccessControl[]> {
    this.setLoading(true);
    this.clearError();

    return this.permissionsService.getPermissions(roleId, forceRefresh).pipe(
      tap({
        next: permissions => {
          this.updatePermissions(permissions);
          this.updateRoleId(roleId);
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to load permissions');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Check if current role has permission for a specific page and action
   * @param page - The page to check
   * @param action - The action to check
   */
  hasPermission(page: PermissionPage, action: PermissionAction): boolean {
    const currentRoleId = this._roleId();
    if (!currentRoleId) return false;

    const pagePermissions = this._permissions().find(p => p.page === page);
    return pagePermissions ? pagePermissions.actions.includes(action) : false;
  }

  /**
   * Check if current role has permission for a specific page and action (synchronous)
   * @param page - The page to check
   * @param action - The action to check
   */
  hasPermissionSync(page: PermissionPage, action: PermissionAction): boolean {
    return this.hasPermission(page, action);
  }

  /**
   * Check if current role has permission for a specific page and action (Observable)
   * @param page - The page to check
   * @param action - The action to check
   */
  hasPermission$(
    page: PermissionPage,
    action: PermissionAction
  ): Observable<boolean> {
    const currentRoleId = this._roleId();
    if (!currentRoleId) return of(false);

    return this.permissionsService
      .hasPermission(currentRoleId, page, action)
      .pipe(map(result => result.hasPermission));
  }

  /**
   * Get all actions available for a specific page
   * @param page - The page to get actions for
   */
  getPageActions(page: PermissionPage): PermissionAction[] {
    const pagePermissions = this._permissions().find(p => p.page === page);
    return pagePermissions ? pagePermissions.actions : [];
  }

  /**
   * Get all actions available for a specific page (Observable)
   * @param page - The page to get actions for
   */
  getPageActions$(page: PermissionPage): Observable<PermissionAction[]> {
    const currentRoleId = this._roleId();
    if (!currentRoleId) return of([]);

    return this.permissionsService.getPageActions(currentRoleId, page);
  }

  /**
   * Get all accessible pages for current role
   */
  getAccessiblePages(): PermissionPage[] {
    return this._permissions().map(p => p.page);
  }

  /**
   * Get all accessible pages for current role (Observable)
   */
  getAccessiblePages$(): Observable<PermissionPage[]> {
    const currentRoleId = this._roleId();
    if (!currentRoleId) return of([]);

    return this.permissionsService.getAccessiblePages(currentRoleId);
  }

  /**
   * Check multiple permissions at once
   * @param checks - Array of permission checks
   */
  checkMultiplePermissions(
    checks: Array<{ page: PermissionPage; action: PermissionAction }>
  ): boolean[] {
    return checks.map(check => this.hasPermission(check.page, check.action));
  }

  /**
   * Check multiple permissions at once (Observable)
   * @param checks - Array of permission checks
   */
  checkMultiplePermissions$(
    checks: Array<{ page: PermissionPage; action: PermissionAction }>
  ): Observable<boolean[]> {
    const currentRoleId = this._roleId();
    if (!currentRoleId) return of(checks.map(() => false));

    return this.permissionsService
      .hasMultiplePermissions(currentRoleId, checks)
      .pipe(map(results => results.map(result => result.hasPermission)));
  }

  /**
   * Check if user can perform any action on a page
   * @param page - The page to check
   */
  canAccessPage(page: PermissionPage): boolean {
    const pagePermissions = this._permissions().find(p => p.page === page);
    return pagePermissions ? pagePermissions.actions.length > 0 : false;
  }

  /**
   * Check if user can perform any action on a page (Observable)
   * @param page - The page to check
   */
  canAccessPage$(page: PermissionPage): Observable<boolean> {
    const currentRoleId = this._roleId();
    if (!currentRoleId) return of(false);

    return this.getPageActions$(page).pipe(map(actions => actions.length > 0));
  }

  /**
   * Get permission details for a specific page
   * @param page - The page to get details for
   */
  getPagePermissionDetails(page: PermissionPage): IAccessControl | null {
    return this._permissions().find(p => p.page === page) || null;
  }

  /**
   * Refresh permissions for current role
   */
  refreshPermissions(): Observable<IAccessControl[]> {
    const currentRoleId = this._roleId();
    if (!currentRoleId) {
      this.setError('No role ID set');
      return of([]);
    }

    return this.loadPermissions(currentRoleId, true);
  }

  /**
   * Clear all permissions and reset state
   */
  clearPermissions(): void {
    this._permissions.set([]);
    this._roleId.set(null);
    this._error.set(null);
    this._lastUpdated.set(null);
    this.permissionsService.clearCache();
  }

  /**
   * Set role ID without loading permissions
   * @param roleId - The role ID to set
   */
  setRoleId(roleId: number): void {
    this.updateRoleId(roleId);
  }

  /**
   * Get current permission state
   */
  getState(): IPermissionState {
    return {
      permissions: this._permissions(),
      roleId: this._roleId(),
      isLoading: this._isLoading(),
      error: this._error(),
      lastUpdated: this._lastUpdated(),
    };
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Check if permissions are loaded for a specific role
   * @param roleId - The role ID to check
   */
  isLoadedForRole(roleId: number): boolean {
    return this._roleId() === roleId && this._permissions().length > 0;
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    entries: Array<{ roleId: number; age: number }>;
  } {
    return this.permissionsService.getCacheStats();
  }

  /**
   * Clear cache
   * @param roleId - Optional role ID to clear specific cache
   */
  clearCache(roleId?: number): void {
    this.permissionsService.clearCache(roleId);
  }
}
