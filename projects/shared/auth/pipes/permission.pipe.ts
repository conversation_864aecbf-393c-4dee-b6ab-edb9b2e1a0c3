import { Pipe, PipeTransform, inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';

import {
  PermissionPage,
  PermissionAction,
} from '../interfaces/permission.interface';
import { AuthService } from '../services/auth.service';
import { PermissionsStore } from '../stores/permissions.store';

@Pipe({
  name: 'hasPermission',
  pure: false, // Make it impure to react to permission changes
  standalone: true,
})
export class HasPermissionPipe implements PipeTransform, OnDestroy {
  private permissionsStore = inject(PermissionsStore);
  private authService = inject(AuthService);
  private subscription: Subscription = new Subscription();

  transform(
    page: PermissionPage,
    action: PermissionAction = 'read',
    roleId?: number
  ): boolean {
    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      return false;
    }

    // Get role ID
    const currentRoleId = roleId || this.getRoleId();
    if (!currentRoleId) {
      return false;
    }

    // Check if permissions are loaded
    if (this.permissionsStore.isLoadedForRole(currentRoleId)) {
      return this.permissionsStore.hasPermission(page, action);
    }

    // If permissions not loaded, return false (will be updated when loaded)
    return false;
  }

  private getRoleId(): number | null {
    const user = this.authService.user();
    if (user && 'roleId' in user) {
      return (user as any).roleId;
    }

    const storedRoleId = localStorage.getItem('userRoleId');
    return storedRoleId ? parseInt(storedRoleId, 10) : null;
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}

@Pipe({
  name: 'hasAnyPermission',
  pure: false,
  standalone: true,
})
export class HasAnyPermissionPipe implements PipeTransform, OnDestroy {
  private permissionsStore = inject(PermissionsStore);
  private authService = inject(AuthService);
  private subscription: Subscription = new Subscription();

  transform(
    permissions: Array<{ page: PermissionPage; action: PermissionAction }>,
    roleId?: number
  ): boolean {
    if (!permissions || !permissions.length) {
      return false;
    }

    if (!this.authService.isAuthenticated()) {
      return false;
    }

    const currentRoleId = roleId || this.getRoleId();
    if (!currentRoleId) {
      return false;
    }

    if (this.permissionsStore.isLoadedForRole(currentRoleId)) {
      return permissions.some(permission =>
        this.permissionsStore.hasPermission(permission.page, permission.action)
      );
    }

    return false;
  }

  private getRoleId(): number | null {
    const user = this.authService.user();
    if (user && 'roleId' in user) {
      return (user as any).roleId;
    }

    const storedRoleId = localStorage.getItem('userRoleId');
    return storedRoleId ? parseInt(storedRoleId, 10) : null;
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}

@Pipe({
  name: 'hasAllPermissions',
  pure: false,
  standalone: true,
})
export class HasAllPermissionsPipe implements PipeTransform, OnDestroy {
  private permissionsStore = inject(PermissionsStore);
  private authService = inject(AuthService);
  private subscription: Subscription = new Subscription();

  transform(
    permissions: Array<{ page: PermissionPage; action: PermissionAction }>,
    roleId?: number
  ): boolean {
    if (!permissions || !permissions.length) {
      return false;
    }

    if (!this.authService.isAuthenticated()) {
      return false;
    }

    const currentRoleId = roleId || this.getRoleId();
    if (!currentRoleId) {
      return false;
    }

    if (this.permissionsStore.isLoadedForRole(currentRoleId)) {
      return permissions.every(permission =>
        this.permissionsStore.hasPermission(permission.page, permission.action)
      );
    }

    return false;
  }

  private getRoleId(): number | null {
    const user = this.authService.user();
    if (user && 'roleId' in user) {
      return (user as any).roleId;
    }

    const storedRoleId = localStorage.getItem('userRoleId');
    return storedRoleId ? parseInt(storedRoleId, 10) : null;
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}

@Pipe({
  name: 'canAccessPage',
  pure: false,
  standalone: true,
})
export class CanAccessPagePipe implements PipeTransform, OnDestroy {
  private permissionsStore = inject(PermissionsStore);
  private authService = inject(AuthService);
  private subscription: Subscription = new Subscription();

  transform(page: PermissionPage, roleId?: number): boolean {
    if (!this.authService.isAuthenticated()) {
      return false;
    }

    const currentRoleId = roleId || this.getRoleId();
    if (!currentRoleId) {
      return false;
    }

    if (this.permissionsStore.isLoadedForRole(currentRoleId)) {
      return this.permissionsStore.canAccessPage(page);
    }

    return false;
  }

  private getRoleId(): number | null {
    const user = this.authService.user();
    if (user && 'roleId' in user) {
      return (user as any).roleId;
    }

    const storedRoleId = localStorage.getItem('userRoleId');
    return storedRoleId ? parseInt(storedRoleId, 10) : null;
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}

@Pipe({
  name: 'getPageActions',
  pure: false,
  standalone: true,
})
export class GetPageActionsPipe implements PipeTransform, OnDestroy {
  private permissionsStore = inject(PermissionsStore);
  private authService = inject(AuthService);
  private subscription: Subscription = new Subscription();

  transform(page: PermissionPage, roleId?: number): PermissionAction[] {
    if (!this.authService.isAuthenticated()) {
      return [];
    }

    const currentRoleId = roleId || this.getRoleId();
    if (!currentRoleId) {
      return [];
    }

    if (this.permissionsStore.isLoadedForRole(currentRoleId)) {
      return this.permissionsStore.getPageActions(page);
    }

    return [];
  }

  private getRoleId(): number | null {
    const user = this.authService.user();
    if (user && 'roleId' in user) {
      return (user as any).roleId;
    }

    const storedRoleId = localStorage.getItem('userRoleId');
    return storedRoleId ? parseInt(storedRoleId, 10) : null;
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
