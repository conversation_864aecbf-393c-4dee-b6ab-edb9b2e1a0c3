import {
  HttpInterceptorFn,
  HttpErrorResponse,
  HttpRequest,
  HttpHandlerFn,
} from '@angular/common/http';
import { inject } from '@angular/core';
import {
  catchError,
  switchMap,
  throwError,
  filter,
  take,
  Observable,
  timeout,
  TimeoutError,
  retry,
  timer,
  finalize,
} from 'rxjs';
import { BehaviorSubject } from 'rxjs';

import { AuthService } from '../services/auth.service';
import { TokenStorageService } from '../services/token-storage.service';

let isRefreshing = false;
const refreshTokenSubject = new BehaviorSubject<string | null>(null);

export const authInterceptor: HttpInterceptorFn = (
  req: HttpRequest<any>,
  next: HttpHandlerFn
) => {
  const authService = inject(AuthService);
  const tokenStorage = inject(TokenStorageService);
  const token = tokenStorage.getAccessToken();

  // Clone request and add access token only if no Authorization header exists
  let authReq = req;
  const hasExistingAuth = req.headers.has('Authorization');
  
  if (token && !hasExistingAuth) {
    authReq = req.clone({
      setHeaders: { Authorization: `Bearer ${token}` },
    });
  }

  return next(authReq).pipe(
    catchError((error: HttpErrorResponse) => {
      // Only handle 401 errors for requests that don't already have Authorization headers
      if (error.status === 401 && !req.headers.has('Authorization')) {
        if (!isRefreshing) {
          isRefreshing = true;
          refreshTokenSubject.next(null);

          let refreshObservable: Observable<any>;
          const loginType = tokenStorage.getLoginType();
          if (loginType === 'basic') {
            refreshObservable = authService.basicRefreshToken();
          } else {
            refreshObservable = authService.refreshToken();
          }

          return refreshObservable.pipe(
            timeout(10000),
            retry({
              count: 3,
              delay: (_error, retryCount) => {
                console.warn(`Refresh token attempt ${retryCount} failed, retrying...`);
                return timer(1000 * Math.pow(2, retryCount - 1)); // 1s, 2s, 4s
              },
            }),
            switchMap(() => {
              const updatedAccessToken = tokenStorage.getAccessToken();
              refreshTokenSubject.next(updatedAccessToken);

              // Retry the failed request with new token
              return next(
                req.clone({
                  setHeaders: { Authorization: `Bearer ${updatedAccessToken}` },
                })
              );
            }),
            catchError(err => {
              if (err instanceof TimeoutError) {
                console.error('Refresh token request timed out after retries');
              }
              authService.logout();
              return throwError(() => err);
            }),
            finalize(() => {
              isRefreshing = false;
            })
          );
        } else {
          // Wait until refresh is done
          return refreshTokenSubject.pipe(
            filter(token => token != null),
            take(1),
            switchMap(newToken =>
              next(
                req.clone({
                  setHeaders: { Authorization: `Bearer ${newToken}` },
                })
              )
            )
          );
        }
      }

      return throwError(() => error);
    })
  );
};
