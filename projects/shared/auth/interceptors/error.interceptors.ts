import {
  HttpErrorResponse,
  HttpHandlerFn,
  HttpInterceptorFn,
  HttpRequest,
} from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { catchError, Observable, throwError } from 'rxjs';

import { AuthService } from '../services/auth.service';
import { TokenStorageService } from '../services/token-storage.service';
import { AavaToastService } from '@aava/play-core';
import { CentralizedRedirectService } from '../../services/centralized-redirect.service';

export const errorInterceptor: HttpInterceptorFn = (
  req: HttpRequest<any>,
  next: HttpHandlerFn
): Observable<any> => {
  const router = inject(Router);
  const authService = inject(AuthService);
  const tokenStorage = inject(TokenStorageService);
  const toastService = inject(AavaToastService);
  const centralizedRedirectService = inject(CentralizedRedirectService);
  
  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      let userMsg = error?.error?.message || 'An unexpected error occurred.';
      let showToast = true;

      if (error.error instanceof ErrorEvent) {
        userMsg = `Network error: ${error.error.message}`;
      } else if (!error?.error?.message) {
        switch (error.status) {
          case 401:
            // Only show 401 error if we have tokens (actual auth error)
            // Don't show toast if no tokens exist (user just needs to login)
            if (tokenStorage.hasValidTokens()) {
              userMsg = 'Session expired. Please log in again.';
            } else {
              // Silent redirect to login without toast
              showToast = false;
              centralizedRedirectService.redirectToMarketingLogin();
            }
            break;
          case 403:
            userMsg = 'Forbidden. You do not have access.';
            break;
          case 404:
            userMsg = 'Resource not found.';
            break;
          case 409:
            // Handle duplicate name error with custom message from server
            userMsg = error.error?.message || 'Duplicate name found!';
            break;
          case 500:
            userMsg = 'Server error. Try again later.';
            break;
          default:
            if (error.status >= 500) {
              userMsg = 'Server error. Try again later.';
            } else if (error.status >= 400) {
              userMsg = error.error?.message || 'Request failed. Please check your input.';
            }
        }
      }

      // Only show toast when appropriate
      if (showToast && toastService) {
        toastService.error({
          title: 'Error',
          message: userMsg,
          duration: 4000,
          showCloseButton: false,
          design: 'modern',
          size: 'small',
        });
      }

      // Ensure error object has message
      if (error.error) {
        error.error.message = userMsg;
      }
      
      return throwError(() => error);
    })
  );
};
