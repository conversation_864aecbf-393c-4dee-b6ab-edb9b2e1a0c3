import { inject } from '@angular/core';
import { Router, UrlTree } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { AuthService } from '../services/auth.service';
import { TokenStorageService } from '../services/token-storage.service';
import { CentralizedRedirectService } from '../../services/centralized-redirect.service';

export const authGuard = (): Observable<boolean | UrlTree> | boolean | UrlTree => {
  const tokenStorage = inject(TokenStorageService);
  const authService = inject(AuthService);
  const router = inject(Router);
  const centralizedRedirectService = inject(CentralizedRedirectService);

  // If access token is valid, allow access
  if (tokenStorage.getAccessToken() && !tokenStorage.isAccessTokenExpired()) {
    if (console.debug) {
      console.debug('AuthGuard: Valid access token found.');
    }
    return true;
  }

  // If access token is expired, but a refresh token is available, try to refresh
  const refreshToken = tokenStorage.getRefreshToken();
  if (refreshToken) {
    if (console.debug) {
      console.debug('AuthGuard: Access token expired, attempting refresh...');
    }

    const loginType = tokenStorage.getLoginType();
    let refreshObservable: Observable<any>;

    if (loginType === 'basic') {
      refreshObservable = authService.basicRefreshToken();
    } else {
      // Default to SSO refresh
      refreshObservable = authService.refreshToken();
    }

    return refreshObservable.pipe(
      map((response) => {
        // The refresh token observable from auth.service returns different shapes.
        // refreshToken returns an object with `accessToken`
        // basicRefreshToken returns an object with `access_token`
        if (response && (response.accessToken || response.access_token)) {
            if (console.debug) {
                console.debug('AuthGuard: Token refresh successful.');
            }
            return true;
        }
        
        if (console.debug) {
            console.debug('AuthGuard: Token refresh failed, redirecting to login.');
        }
        authService.clearAuthState();
        centralizedRedirectService.redirectToMarketingLogin();
        return false;
      }),
      catchError(() => {
        if (console.debug) {
          console.debug('AuthGuard: Token refresh failed, redirecting to login.');
        }
        authService.clearAuthState(); // Clear out old state
        centralizedRedirectService.redirectToMarketingLogin();
        return of(false);
      })
    );
  }

  // No valid tokens at all
  if (console.debug) {
    console.debug('AuthGuard: No valid tokens, redirecting to login.');
  }
  // Use centralized redirect service to navigate to marketing login
  centralizedRedirectService.redirectToMarketingLogin();
  return false;
};
