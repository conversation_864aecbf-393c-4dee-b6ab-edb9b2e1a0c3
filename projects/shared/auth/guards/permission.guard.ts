import { Injectable, inject } from '@angular/core';
import {
  CanActivate,
  CanActivateChild,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import {
  PermissionPage,
  PermissionAction,
} from '../interfaces/permission.interface';
import { AuthService } from '../services/auth.service';
import { PermissionsStore } from '../stores/permissions.store';
import { CentralizedRedirectService } from '../../services/centralized-redirect.service';

export interface PermissionGuardData {
  page: PermissionPage;
  action: PermissionAction;
  redirectTo?: string;
  fallbackRoute?: string;
}

@Injectable({
  providedIn: 'root',
})
export class PermissionGuard implements CanActivate, CanActivateChild {
  private authService = inject(AuthService);
  private permissionsStore = inject(PermissionsStore);
  private router = inject(Router);
  private centralizedRedirectService = inject(CentralizedRedirectService);

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkPermission(route, state);
  }

  canActivateChild(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkPermission(route, state);
  }

  private checkPermission(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      this.redirectToLogin(state.url);
      return of(false);
    }

    // Get permission data from route
    const permissionData = this.getPermissionData(route);
    if (!permissionData) {
      // No permission required, allow access
      return of(true);
    }

    // Get role ID
    const roleId = this.getRoleId();
    if (!roleId) {
      console.warn('PermissionGuard: No role ID found');
      this.handlePermissionFailure(permissionData, state.url);
      return of(false);
    }

    // Check if permissions are already loaded
    if (this.permissionsStore.isLoadedForRole(roleId)) {
      const hasPermission = this.permissionsStore.hasPermission(
        permissionData.page,
        permissionData.action
      );

      if (hasPermission) {
        return of(true);
      } else {
        this.handlePermissionFailure(permissionData, state.url);
        return of(false);
      }
    }

    // Load permissions and check
    return this.permissionsStore.loadPermissions(roleId).pipe(
      map(() => {
        const hasPermission = this.permissionsStore.hasPermission(
          permissionData.page,
          permissionData.action
        );

        if (hasPermission) {
          return true;
        } else {
          this.handlePermissionFailure(permissionData, state.url);
          return false;
        }
      }),
      catchError(error => {
        console.error('PermissionGuard: Error loading permissions:', error);
        this.handlePermissionFailure(permissionData, state.url);
        return of(false);
      })
    );
  }

  private getPermissionData(
    route: ActivatedRouteSnapshot
  ): PermissionGuardData | null {
    // Check route data
    if (route.data && route.data['permission']) {
      return route.data['permission'] as PermissionGuardData;
    }

    // Check parent route data
    let parent = route.parent;
    while (parent) {
      if (parent.data && parent.data['permission']) {
        return parent.data['permission'] as PermissionGuardData;
      }
      parent = parent.parent;
    }

    return null;
  }

  private getRoleId(): number | null {
    const user = this.authService.user();
    if (user && 'roleId' in user) {
      return (user as any).roleId;
    }

    // Fallback: try to get from localStorage
    const storedRoleId = localStorage.getItem('userRoleId');
    return storedRoleId ? parseInt(storedRoleId, 10) : null;
  }

  private handlePermissionFailure(
    permissionData: PermissionGuardData,
    currentUrl: string
  ): void {
    console.warn(
      `PermissionGuard: Access denied for ${permissionData.page}:${permissionData.action}`
    );

    if (permissionData.redirectTo) {
      this.router.navigate([permissionData.redirectTo]);
    } else if (permissionData.fallbackRoute) {
      this.router.navigate([permissionData.fallbackRoute]);
    } else {
      // Default: redirect to dashboard or home
      this.router.navigate(['/dashboard']);
    }
  }

  private redirectToLogin(returnUrl: string): void {
    this.centralizedRedirectService.redirectToMarketingLogin();
  }
}

/**
 * Guard for checking multiple permissions (user must have ALL permissions)
 */
@Injectable({
  providedIn: 'root',
})
export class AllPermissionsGuard implements CanActivate, CanActivateChild {
  private authService = inject(AuthService);
  private permissionsStore = inject(PermissionsStore);
  private router = inject(Router);
  private centralizedRedirectService = inject(CentralizedRedirectService);

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAllPermissions(route, state);
  }

  canActivateChild(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAllPermissions(route, state);
  }

  private checkAllPermissions(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    if (!this.authService.isAuthenticated()) {
      this.centralizedRedirectService.redirectToMarketingLogin();
      return of(false);
    }

    const permissionsData = this.getPermissionsData(route);
    if (!permissionsData || !permissionsData.length) {
      return of(true);
    }

    const roleId = this.getRoleId();
    if (!roleId) {
      console.warn('AllPermissionsGuard: No role ID found');
      this.router.navigate(['/dashboard']);
      return of(false);
    }

    if (this.permissionsStore.isLoadedForRole(roleId)) {
      const hasAllPermissions = permissionsData.every(permission =>
        this.permissionsStore.hasPermission(permission.page, permission.action)
      );

      if (hasAllPermissions) {
        return of(true);
      } else {
        this.router.navigate(['/dashboard']);
        return of(false);
      }
    }

    return this.permissionsStore.loadPermissions(roleId).pipe(
      map(() => {
        const hasAllPermissions = permissionsData.every(permission =>
          this.permissionsStore.hasPermission(
            permission.page,
            permission.action
          )
        );

        if (hasAllPermissions) {
          return true;
        } else {
          this.router.navigate(['/dashboard']);
          return false;
        }
      }),
      catchError(error => {
        console.error('AllPermissionsGuard: Error loading permissions:', error);
        this.router.navigate(['/dashboard']);
        return of(false);
      })
    );
  }

  private getPermissionsData(
    route: ActivatedRouteSnapshot
  ): Array<{ page: PermissionPage; action: PermissionAction }> | null {
    if (route.data && route.data['permissions']) {
      return route.data['permissions'] as Array<{
        page: PermissionPage;
        action: PermissionAction;
      }>;
    }

    let parent = route.parent;
    while (parent) {
      if (parent.data && parent.data['permissions']) {
        return parent.data['permissions'] as Array<{
          page: PermissionPage;
          action: PermissionAction;
        }>;
      }
      parent = parent.parent;
    }

    return null;
  }

  private getRoleId(): number | null {
    const user = this.authService.user();
    if (user && 'roleId' in user) {
      return (user as any).roleId;
    }

    const storedRoleId = localStorage.getItem('userRoleId');
    return storedRoleId ? parseInt(storedRoleId, 10) : null;
  }
}

/**
 * Guard for checking any permission (user must have AT LEAST ONE permission)
 */
@Injectable({
  providedIn: 'root',
})
export class AnyPermissionGuard implements CanActivate, CanActivateChild {
  private authService = inject(AuthService);
  private permissionsStore = inject(PermissionsStore);
  private router = inject(Router);
  private centralizedRedirectService = inject(CentralizedRedirectService);

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAnyPermission(route, state);
  }

  canActivateChild(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAnyPermission(route, state);
  }

  private checkAnyPermission(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    if (!this.authService.isAuthenticated()) {
      this.centralizedRedirectService.redirectToMarketingLogin();
      return of(false);
    }

    const permissionsData = this.getPermissionsData(route);
    if (!permissionsData || !permissionsData.length) {
      return of(true);
    }

    const roleId = this.getRoleId();
    if (!roleId) {
      console.warn('AnyPermissionGuard: No role ID found');
      this.router.navigate(['/dashboard']);
      return of(false);
    }

    if (this.permissionsStore.isLoadedForRole(roleId)) {
      const hasAnyPermission = permissionsData.some(permission =>
        this.permissionsStore.hasPermission(permission.page, permission.action)
      );

      if (hasAnyPermission) {
        return of(true);
      } else {
        this.router.navigate(['/dashboard']);
        return of(false);
      }
    }

    return this.permissionsStore.loadPermissions(roleId).pipe(
      map(() => {
        const hasAnyPermission = permissionsData.some(permission =>
          this.permissionsStore.hasPermission(
            permission.page,
            permission.action
          )
        );

        if (hasAnyPermission) {
          return true;
        } else {
          this.router.navigate(['/dashboard']);
          return false;
        }
      }),
      catchError(error => {
        console.error('AnyPermissionGuard: Error loading permissions:', error);
        this.router.navigate(['/dashboard']);
        return of(false);
      })
    );
  }

  private getPermissionsData(
    route: ActivatedRouteSnapshot
  ): Array<{ page: PermissionPage; action: PermissionAction }> | null {
    if (route.data && route.data['anyPermissions']) {
      return route.data['anyPermissions'] as Array<{
        page: PermissionPage;
        action: PermissionAction;
      }>;
    }

    let parent = route.parent;
    while (parent) {
      if (parent.data && parent.data['anyPermissions']) {
        return parent.data['anyPermissions'] as Array<{
          page: PermissionPage;
          action: PermissionAction;
        }>;
      }
      parent = parent.parent;
    }

    return null;
  }

  private getRoleId(): number | null {
    const user = this.authService.user();
    if (user && 'roleId' in user) {
      return (user as any).roleId;
    }

    const storedRoleId = localStorage.getItem('userRoleId');
    return storedRoleId ? parseInt(storedRoleId, 10) : null;
  }
}
