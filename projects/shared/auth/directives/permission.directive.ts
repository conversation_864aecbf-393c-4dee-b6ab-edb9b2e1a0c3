import {
  Directive,
  Input,
  TemplateRef,
  ViewContainerRef,
  OnInit,
  OnDestroy,
  inject,
} from '@angular/core';
import { Subscription, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import {
  PermissionPage,
  PermissionAction,
} from '../interfaces/permission.interface';
import { AuthService } from '../services/auth.service';
import { PermissionsStore } from '../stores/permissions.store';

@Directive({
  selector: '[appPermission]',
  standalone: true,
})
export class PermissionDirective implements OnInit, OnDestroy {
  private templateRef = inject(TemplateRef<any>);
  private viewContainer = inject(ViewContainerRef);
  private permissionsStore = inject(PermissionsStore);
  private authService = inject(AuthService);

  private subscription: Subscription = new Subscription();
  private hasView = false;

  // Input properties
  @Input() appPermission: string = '';
  @Input() appPermissionPage: PermissionPage | null = null;
  @Input() appPermissionAction: PermissionAction = 'read';
  @Input() appPermissionFallback: boolean = false; // Show/hide on permission failure
  @Input() appPermissionRoleId: number | null = null; // Override role ID

  ngOnInit(): void {
    this.checkPermission();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private checkPermission(): void {
    // Parse permission string if provided
    if (this.appPermission) {
      const parsed = this.parsePermissionString(this.appPermission);
      this.appPermissionPage = parsed.page;
      this.appPermissionAction = parsed.action;
    }

    if (!this.appPermissionPage) {
      console.warn('PermissionDirective: No page specified');
      this.handlePermissionResult(false);
      return;
    }

    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      this.handlePermissionResult(false);
      return;
    }

    // Get role ID
    const roleId = this.getRoleId();
    if (!roleId) {
      console.warn('PermissionDirective: No role ID found');
      this.handlePermissionResult(false);
      return;
    }

    // Check if permissions are already loaded
    if (this.permissionsStore.isLoadedForRole(roleId)) {
      const hasPermission = this.permissionsStore.hasPermissionSync(
        this.appPermissionPage!,
        this.appPermissionAction
      );
      this.handlePermissionResult(hasPermission);
      return;
    }

    // Load permissions and check
    this.subscription.add(
      this.permissionsStore
        .loadPermissions(roleId)
        .pipe(
          map(() => {
            const hasPermission = this.permissionsStore.hasPermissionSync(
              this.appPermissionPage!,
              this.appPermissionAction
            );
            return hasPermission;
          }),
          catchError((error: any) => {
            console.error(
              'PermissionDirective: Error loading permissions:',
              error
            );
            return of(false);
          })
        )
        .subscribe((hasPermission: boolean) => {
          this.handlePermissionResult(hasPermission);
        })
    );
  }

  private handlePermissionResult(hasPermission: boolean): void {
    const shouldShow = this.appPermissionFallback
      ? !hasPermission
      : hasPermission;

    if (shouldShow && !this.hasView) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    } else if (!shouldShow && this.hasView) {
      this.viewContainer.clear();
      this.hasView = false;
    }
  }

  private parsePermissionString(permission: string): {
    page: PermissionPage;
    action: PermissionAction;
  } {
    const parts = permission.split(':');
    const page = parts[0] as PermissionPage;
    const action = (parts[1] || 'read') as PermissionAction;
    return { page, action };
  }

  private getRoleId(): number | null {
    // Use override if provided
    if (this.appPermissionRoleId !== null) {
      return this.appPermissionRoleId;
    }

    // Get from auth service
    const user = this.authService.user();
    if (user && 'roleId' in user) {
      return (user as any).roleId;
    }

    // Fallback: try to get from localStorage
    const storedRoleId = localStorage.getItem('userRoleId');
    return storedRoleId ? parseInt(storedRoleId, 10) : null;
  }
}

/**
 * Directive for showing elements when user has permission
 */
@Directive({
  selector: '[appHasPermission]',
  standalone: true,
})
export class HasPermissionDirective extends PermissionDirective {
  @Input() appHasPermission: string = '';
  @Input() appHasPermissionPage: PermissionPage | null = null;
  @Input() appHasPermissionAction: PermissionAction = 'read';
  @Input() appHasPermissionRoleId: number | null = null;

  override ngOnInit(): void {
    // Map inputs to parent directive
    this.appPermission = this.appHasPermission;
    this.appPermissionPage = this.appHasPermissionPage;
    this.appPermissionAction = this.appHasPermissionAction;
    this.appPermissionRoleId = this.appHasPermissionRoleId;
    this.appPermissionFallback = false; // Show when has permission

    super.ngOnInit();
  }
}

/**
 * Directive for hiding elements when user has permission
 */
@Directive({
  selector: '[appNoPermission]',
  standalone: true,
})
export class NoPermissionDirective extends PermissionDirective {
  @Input() appNoPermission: string = '';
  @Input() appNoPermissionPage: PermissionPage | null = null;
  @Input() appNoPermissionAction: PermissionAction = 'read';
  @Input() appNoPermissionRoleId: number | null = null;

  override ngOnInit(): void {
    // Map inputs to parent directive
    this.appPermission = this.appNoPermission;
    this.appPermissionPage = this.appNoPermissionPage;
    this.appPermissionAction = this.appNoPermissionAction;
    this.appPermissionRoleId = this.appNoPermissionRoleId;
    this.appPermissionFallback = true; // Show when no permission

    super.ngOnInit();
  }
}

/**
 * Directive for showing elements when user has any of the specified permissions
 */
@Directive({
  selector: '[appHasAnyPermission]',
  standalone: true,
})
export class HasAnyPermissionDirective implements OnInit, OnDestroy {
  private templateRef = inject(TemplateRef<any>);
  private viewContainer = inject(ViewContainerRef);
  private permissionsStore = inject(PermissionsStore);
  private authService = inject(AuthService);

  private subscription: Subscription = new Subscription();
  private hasView = false;

  @Input() appHasAnyPermission: Array<{
    page: PermissionPage;
    action: PermissionAction;
  }> = [];
  @Input() appHasAnyPermissionRoleId: number | null = null;

  ngOnInit(): void {
    this.checkAnyPermission();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private checkAnyPermission(): void {
    if (!this.appHasAnyPermission.length) {
      console.warn('HasAnyPermissionDirective: No permissions specified');
      this.handlePermissionResult(false);
      return;
    }

    if (!this.authService.isAuthenticated()) {
      this.handlePermissionResult(false);
      return;
    }

    const roleId = this.getRoleId();
    if (!roleId) {
      console.warn('HasAnyPermissionDirective: No role ID found');
      this.handlePermissionResult(false);
      return;
    }

    if (this.permissionsStore.isLoadedForRole(roleId)) {
      const hasAnyPermission = this.appHasAnyPermission.some(check =>
        this.permissionsStore.hasPermissionSync(check.page, check.action)
      );
      this.handlePermissionResult(hasAnyPermission);
      return;
    }

    this.subscription.add(
      this.permissionsStore
        .loadPermissions(roleId)
        .pipe(
          map(() => {
            const hasAnyPermission = this.appHasAnyPermission.some(check =>
              this.permissionsStore.hasPermissionSync(check.page, check.action)
            );
            return hasAnyPermission;
          }),
          catchError((error: any) => {
            console.error(
              'HasAnyPermissionDirective: Error loading permissions:',
              error
            );
            return of(false);
          })
        )
        .subscribe((hasAnyPermission: boolean) => {
          this.handlePermissionResult(hasAnyPermission);
        })
    );
  }

  private handlePermissionResult(hasPermission: boolean): void {
    if (hasPermission && !this.hasView) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    } else if (!hasPermission && this.hasView) {
      this.viewContainer.clear();
      this.hasView = false;
    }
  }

  private getRoleId(): number | null {
    if (this.appHasAnyPermissionRoleId !== null) {
      return this.appHasAnyPermissionRoleId;
    }

    const user = this.authService.user();
    if (user && 'roleId' in user) {
      return (user as any).roleId;
    }

    const storedRoleId = localStorage.getItem('userRoleId');
    return storedRoleId ? parseInt(storedRoleId, 10) : null;
  }
}

/**
 * Directive for showing elements when user has all of the specified permissions
 */
@Directive({
  selector: '[appHasAllPermissions]',
  standalone: true,
})
export class HasAllPermissionsDirective implements OnInit, OnDestroy {
  private templateRef = inject(TemplateRef<any>);
  private viewContainer = inject(ViewContainerRef);
  private permissionsStore = inject(PermissionsStore);
  private authService = inject(AuthService);

  private subscription: Subscription = new Subscription();
  private hasView = false;

  @Input() appHasAllPermissions: Array<{
    page: PermissionPage;
    action: PermissionAction;
  }> = [];
  @Input() appHasAllPermissionsRoleId: number | null = null;

  ngOnInit(): void {
    this.checkAllPermissions();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private checkAllPermissions(): void {
    if (!this.appHasAllPermissions.length) {
      console.warn('HasAllPermissionsDirective: No permissions specified');
      this.handlePermissionResult(false);
      return;
    }

    if (!this.authService.isAuthenticated()) {
      this.handlePermissionResult(false);
      return;
    }

    const roleId = this.getRoleId();
    if (!roleId) {
      console.warn('HasAllPermissionsDirective: No role ID found');
      this.handlePermissionResult(false);
      return;
    }

    if (this.permissionsStore.isLoadedForRole(roleId)) {
      const hasAllPermissions = this.appHasAllPermissions.every(check =>
        this.permissionsStore.hasPermissionSync(check.page, check.action)
      );
      this.handlePermissionResult(hasAllPermissions);
      return;
    }

    this.subscription.add(
      this.permissionsStore
        .loadPermissions(roleId)
        .pipe(
          map(() => {
            const hasAllPermissions = this.appHasAllPermissions.every(check =>
              this.permissionsStore.hasPermissionSync(check.page, check.action)
            );
            return hasAllPermissions;
          }),
          catchError((error: any) => {
            console.error(
              'HasAllPermissionsDirective: Error loading permissions:',
              error
            );
            return of(false);
          })
        )
        .subscribe((hasAllPermissions: boolean) => {
          this.handlePermissionResult(hasAllPermissions);
        })
    );
  }

  private handlePermissionResult(hasPermission: boolean): void {
    if (hasPermission && !this.hasView) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    } else if (!hasPermission && this.hasView) {
      this.viewContainer.clear();
      this.hasView = false;
    }
  }

  private getRoleId(): number | null {
    if (this.appHasAllPermissionsRoleId !== null) {
      return this.appHasAllPermissionsRoleId;
    }

    const user = this.authService.user();
    if (user && 'roleId' in user) {
      return (user as any).roleId;
    }

    const storedRoleId = localStorage.getItem('userRoleId');
    return storedRoleId ? parseInt(storedRoleId, 10) : null;
  }
}
