import { Component, OnInit, signal, inject, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { AavaSSOLoginComponent } from '@aava/play-core';

import { CentralizedRedirectService, TokenStorageService, IBasicCredentials, AuthService } from '@shared';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule, AavaSSOLoginComponent],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  @ViewChild(AavaSSOLoginComponent) ssoLoginComp?: AavaSSOLoginComponent;
  
  // Modern signal-based state
  isLoading = signal(false);
  errorMessage = signal<string | null>(null);
  
  // Services
  private authService = inject(AuthService);
  private tokenStorage = inject(TokenStorageService);
  private centralizedRedirectService = inject(CentralizedRedirectService);

  ngOnInit(): void {
    // Check if already authenticated
    if (this.authService.isUserAuthenticated()) {
      this.handlePostLoginRedirect();
      return;
    }
  }

  onBasicLogin(creds?: IBasicCredentials): void {
    const credentials = creds || this.readChildCredentials();
    
    if (!credentials?.username || !credentials?.password) {
      this.errorMessage.set('Username and password are required');
      return;
    }

    this.setLoadingState(true);
    this.clearError();

    this.authService.basicLoginWithCredentials(
      credentials.username,
      credentials.password
    ).subscribe({
      next: () => {
        this.tokenStorage.storeLoginType('basic');
        localStorage.removeItem('marketing_login_initiated');
        this.handlePostLoginRedirect();
      },
      error: (error) => {
        this.setLoadingState(false);
        this.errorMessage.set(error.error?.message || 'Login failed. Please try again.');
      }
    });
  }

  onLoginEvent(event: any): void {
    if (event?.type === 'login') {
      const creds = event?.credentials as IBasicCredentials | undefined;
      if (creds?.username && creds?.password) {
        this.onBasicLogin(creds);
      } else {
        const fallbackCreds = this.readChildCredentials();
        if (fallbackCreds) {
          this.onBasicLogin(fallbackCreds);
        }
      }
    }
  }

  onSSOContainerClick(evt: MouseEvent): void {
    const target = evt.target as HTMLElement | null;
    const buttonEl = target?.closest?.('aava-button') as HTMLElement | null;
    const label = buttonEl?.getAttribute?.('label') || '';
    const typeAttr = buttonEl?.getAttribute?.('type') || '';
    
    if (buttonEl && (/sign\s*in/i.test(label) || typeAttr === 'submit')) {
      try {
        this.ssoLoginComp?.onSubmit();
      } catch {
        // Error handling moved to centralized error handling
      }
    }
  }

  onCompanyLogin(): void {
    if (!this.authService.getAuthConfig()) {
      return;
    }
    try {
      localStorage.setItem('marketing_login_initiated', 'true');
    } catch {}
    this.authService.loginSSO().subscribe();
  }

  onForgotPassword(): void {}

  onTroubleSigningIn(): void {}

  clearError(): void {
    this.errorMessage.set(null);
  }

  private setLoadingState(loading: boolean): void {
    this.isLoading.set(loading);
  }

  private handlePostLoginRedirect(): void {
    this.centralizedRedirectService.handlePostLoginRedirect(this.authService.getApiAuthUrl());
  }

  private readChildCredentials(): IBasicCredentials | null {
    try {
      const username = (this.ssoLoginComp?.username as any)?.value?.trim?.();
      const password = (this.ssoLoginComp?.password as any)?.value?.trim?.();
      
      if (username && password) {
        return { username, password };
      }
    } catch (error) {
      console.warn('Failed to read child credentials:', error);
    }
    
    return null;
  }
}


