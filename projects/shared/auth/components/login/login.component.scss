.desktop-layout {
  .login-image-container {
    height: 100vh;
    background-image: url('../../public/login.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .login-section {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    min-height: 100vh;
  }
}

/* Mobile/Tablet Layout (below lg) */
.mobile-tablet-layout {
  display: none;
  height: 100vh;
  width: 100%;

  .login-image-container {
    position: relative;
    height: 100vh;
    width: 100%;
    background-image: url('../../public/login-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;

    .login-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      background: rgba(0, 0, 0, 0.3);

      aava-sso-login {
        width: 100%;
        max-width: 400px;
      }
    }
  }
}

/* Responsive breakpoints */
@media (max-width: 991.98px) {
  .desktop-layout {
    display: none !important;
  }

  .mobile-tablet-layout {
    display: block !important;
  }
}

/* Tablet Layout (max-width: 768px) */
@media (max-width: 768px) {
  .mobile-tablet-layout {
    .login-image-container {
      .login-overlay {
        padding: 1.5rem;

        aava-sso-login {
          max-width: 100%;
          margin: 0 1rem;
        }
      }
    }
  }
}

/* Mobile Layout (max-width: 480px) */
@media (max-width: 480px) {
  .mobile-tablet-layout {
    .login-image-container {
      .login-overlay {
        padding: 1rem;

        aava-sso-login {
          margin: 0 0.5rem;
          max-width: calc(100% - 1rem);
        }
      }
    }
  }
}

/* Small Mobile Layout (max-width: 320px) */
@media (max-width: 320px) {
  .mobile-tablet-layout {
    .login-image-container {
      .login-overlay {
        padding: 0.75rem;

        aava-sso-login {
          margin: 0 0.25rem;
          max-width: calc(100% - 0.5rem);
        }
      }
    }
  }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .mobile-tablet-layout {
    .login-image-container {
      .login-overlay {
        padding: 1rem;

        aava-sso-login {
          max-width: 80%;
        }
      }
    }
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .desktop-layout .login-image-container,
  .mobile-tablet-layout .login-image-container {
    background-size: cover;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
