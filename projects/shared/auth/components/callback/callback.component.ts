import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CentralizedRedirectService, AuthService } from '@shared';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-callback',
  template: `
    <div class="callback-container">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p>Processing authentication...</p>
      </div>
    </div>
  `,
  styles: [
    `
      .callback-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        flex-direction: column;
      }
      .loading-spinner {
        text-align: center;
      }
      .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    `,
  ],
})
export class CallbackComponent implements OnInit, OnDestroy {
  private subscription: Subscription = new Subscription();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private centralizedRedirectService: CentralizedRedirectService
  ) {}

  ngOnInit(): void {
    const refreshToken = this.route.snapshot.queryParams['refresh_token'];
    const code = this.route.snapshot.queryParams['code'];
    const error = this.route.snapshot.queryParams['error'];
    const errorDescription =
      this.route.snapshot.queryParams['error_description'];

    if (error) {
      this.handleAuthError(error, errorDescription);
      return;
    }

    if (refreshToken) {
      this.handleTokenRefresh(refreshToken);
    } else if (code) {
      this.handleCodeExchange(code);
    } else {
      this.handleAuthError(
        'no_code',
        'No authorization code or refresh token found'
      );
    }
  }

  private handleAuthError(error: string, description?: string): void {
    const errorMessage = description || `Authentication failed: ${error}`;

    // Use centralized redirect service to navigate to marketing login
    this.centralizedRedirectService.redirectToMarketingLogin();
  }

  private handleTokenRefresh(refreshToken: string): void {
    const refreshSub = this.authService.refreshToken(refreshToken).subscribe({
      next: () => {
        this.handlePostLoginRedirect();
      },
      error: () => {
        this.handleAuthError(
          'refresh_failed',
          'Token refresh failed. Please login again.'
        );
      },
    });
    this.subscription.add(refreshSub);
  }

  private handleCodeExchange(code: string): void {
    const exchangeSub = this.authService.exchangeCodeForToken(code).subscribe({
      next: () => {
        this.handlePostLoginRedirect();
      },
      error: () => {
        this.handleAuthError(
          'exchange_failed',
          'Failed to exchange authorization code for token. Please try again.'
        );
      },
    });
    this.subscription.add(exchangeSub);
  }

  private handlePostLoginRedirect(): void {
    try {
      // Use centralized redirect service to determine where to go
      this.centralizedRedirectService.handlePostLoginRedirect(
        this.authService.getApiAuthUrl()
      );
    } catch (error) {
      // Fallback to default redirect
      window.location.href = '/';
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
