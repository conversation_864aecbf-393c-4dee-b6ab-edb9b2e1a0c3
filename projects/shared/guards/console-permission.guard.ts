import { inject } from '@angular/core';
import { CanActivateFn, UrlTree } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { AuthService } from '../auth/services/auth.service';
import { CentralizedRedirectService } from '../services/centralized-redirect.service';
import {
  UserDetailsService,
  UserDetails,
} from '../services/user-details.service';

/**
 * Guard allowing access to Console only for users who are:
 * - Admin (role name contains 'admin') OR
 * - Have studioInfo with name 'console'
 *
 * On failure, user is redirected to Launchpad (or Realm if realms invalid).
 */
export const consolePermissionGuard: CanActivateFn = (): Observable<
  boolean | UrlTree
> => {
  const authService = inject(AuthService);
  const userDetailsService = inject(UserDetailsService);
  const centralizedRedirectService = inject(CentralizedRedirectService);
  // const router = inject(Router);

  // Must be authenticated first
  if (!authService.isUserAuthenticated()) {
    centralizedRedirectService.redirectToMarketingLogin();
    return of(false);
  }

  const apiAuthUrl = authService.getAuthConfig()?.apiAuthUrl;

  return userDetailsService.getUserDetails(apiAuthUrl).pipe(
    map((details: UserDetails) => {
      const isAllowed = hasConsoleAccess(details);
      if (isAllowed) {
        return true;
      }
      // If not allowed, redirect away from console
      centralizedRedirectService.performRedirect('/launchpad/');
      return false;
    }),
    catchError(err => {
      console.error(
        'consolePermissionGuard: failed to fetch user details',
        err
      );
      // Be safe: redirect to Launchpad
      centralizedRedirectService.performRedirect('/launchpad/');
      return of(false);
    })
  );
};

function hasConsoleAccess(details: UserDetails): boolean {
  if (!details) return false;

  // 1) studioInfo includes console
  const studios = Array.isArray(details.studioInfo) ? details.studioInfo : [];
  const hasConsoleStudio = studios.some(
    s => s?.name?.toLowerCase() === 'console'
  );
  if (hasConsoleStudio) return true;

  // 2) roles include admin
  const roles = Array.isArray(details.roles) ? details.roles : [];
  const isAdmin = roles.some(r => r?.name?.toLowerCase().includes('admin'));
  return isAdmin;
}
