:host {
  display: block;
  height: 80vh;
  overflow: hidden;
}

.create-agent-prompt-container {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  overflow: hidden;
  padding: 1rem;
  box-sizing: border-box;
  padding-top: 15vh;
  max-width: 85%;
  margin: 0 auto;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-container {
  display: flex;
  justify-content: center;
}

.logo-svg {
  width: 120px;
  height: 120px;
  animation: elegantSlideIn 1.5s ease-out both;
  filter: drop-shadow(0 8px 32px rgba(51, 157, 255, 0.3));
}

.prompt-section {
  animation: fadeInUp 0.8s ease-out 1.2s both;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  width: 100%;
}

.main-prompt {
  text-align: center;
  font-family: 'PP Neue Machina';
  background: var(--prompt-box-style);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.input-container {
  width: 100%;
  max-width: 775px;
  margin-bottom: 1rem;
  animation: pulsatingGlow 3s ease-in-out infinite;
  border-radius: 1rem !important;
  position: relative;

  ::ng-deep .ava-textbox__container {
    border-radius: 1rem !important;
  }
}

.prompt-input {
  width: 100%;
  ::ng-deep .send-icon {
    svg {
      stroke: var(--color-surface-subtle-hover);
    }
    &.send-icon-active {
      svg {
        stroke: var(--color-border-default);
      }
    }
  }
}

.suggestions-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 0.75rem;
  width: 100%;
  max-width: 775px;

  &.loading {
    opacity: 0.6;
    pointer-events: none;
  }
}

.suggestion-button:nth-child(3) {
  grid-column: 1 / -1;
  justify-self: center;
  max-width: 50%;
}

// Animations
@keyframes gradientBorder {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@media (max-width: 320px) {
  .content-wrapper.hidden {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
  }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding: 0.75rem 1rem;
  background-color: var(
    --Surface-Fill-Dark-Surface-Dark-1,
    rgba(30, 30, 30, 0.1)
  );
  border: 1px solid var(--error-400);
  border-radius: 0.5rem;
  color: var(--error-400);
  font-size: 0.875rem;
  font-weight: 500;
}

::ng-deep
  .ava-button.ava-button--outlined:not(.primary):not(.success):not(
    .warning
  ):not(.danger):not(.info) {
  color: var(--neutral-900) !important;
  border: 1px solid var(--color-surface-subtle-hover) !important;
  backdrop-filter: blur(15px) !important;
}
