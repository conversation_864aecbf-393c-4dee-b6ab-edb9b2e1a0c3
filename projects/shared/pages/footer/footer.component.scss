.footer {
  background-color: transparent;
  color: #616874;
  padding: 12px 5px;
  width: 100%;
  
  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    
    .copyright {
      font-size: 14px;
    }
    
    .footer-links {
      display: flex;
      gap: 30px;
      
      .footer-link {
        color: #616874;
        text-decoration: none;
        font-size: 14px;
        transition: color 0.2s ease;
        
        &:hover {
          color: #616874;
          text-decoration: none;
        }
      }
    }
  }
  
  // Responsive design
  @media (max-width: 768px) {
    .footer-content {
      flex-direction: column;
      gap: 10px;
      text-align: center;
      
      .footer-links {
        gap: 20px;
      }
    }
  }
}
