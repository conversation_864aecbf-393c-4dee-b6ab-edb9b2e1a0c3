import { AavaDialogService } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { CookiePolicyComponent } from 'projects/shared/components/agent-builder/cookie-policy/cookie-policy.component';

import { CookieService } from '../../services/cookie.service';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  standalone: true,
  imports: [CommonModule],
})
export class FooterComponent implements OnInit {
  constructor(
    private dialogService: AavaDialogService,
    private cookieService: CookieService
  ) {}

  ngOnInit(): void {
    // Check if user has consent on component initialization
    this.checkAndShowCookieConsent();
  }

  /**
   * Check if user has consent and show cookie dialog if needed
   */
  private checkAndShowCookieConsent(): void {
    // Check if user already has consent
    if (!this.cookieService.hasConsent()) {
      this.showCookieDialog();
    }
  }

  /**
   * Show cookie consent dialog
   */
  private showCookieDialog(): void {
    this.dialogService.openModal(
      CookiePolicyComponent,
      {
        position: 'right-bottom',
        width: '722px',
        overlay: false,
        showCloseButton: false,
        customDialogStyle: {
          'margin-bottom': '1.75rem',
        },
      },
      {
        title: 'Cookie Consent',
        message:
          'We use cookies and similar technologies to enhance your browsing experience, analyze site traffic, and personalize content. By clicking "Accept All", you agree to the storing of cookies on your device.',
      }
    );
  }

  /**
   * Handle manual cookie policy click (from footer link)
   */
  onCookieClick(event: Event): void {
    event.preventDefault();
    this.showCookieDialog();
  }
}
