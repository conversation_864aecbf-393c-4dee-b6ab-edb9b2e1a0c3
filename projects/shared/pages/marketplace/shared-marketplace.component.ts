import { AavaDialogService, AavaSkeletonComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import {
  ArtifactDetailsComponent,
  ArtifactAction,
  CreateArtifactComponent,
  CreateArtifactModalComponent,
  ArtifactDetailModalComponent,
  ArtifactMetadata,
  FilterSection,
  FilterSearchEvent,
  FilterSearchComponentComponent,
} from '@shared';

interface MarketplaceAgent {
  id: string;
  title: string;
  description: string;
  category: string;
  entityType: string;
  author: string;
  status: 'active' | 'inactive' | 'pending' | 'error';
  tags: string[];
  metadata: { [key: string]: string | number | boolean };
  createdOn?: string;
  avatarUrl?: string;
}

@Component({
  selector: 'app-shared-marketplace',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CreateArtifactComponent,
    ArtifactDetailsComponent,
    CreateArtifactModalComponent,
    FilterSearchComponentComponent,
    AavaSkeletonComponent,
  ],
  templateUrl: './shared-marketplace.component.html',
  styleUrl: './shared-marketplace.component.scss',
})
export class SharedMarketplaceComponent {
  @Input() artifactList!: MarketplaceAgent[];
  @Input() isLoading: boolean = false;
  @Output() filterApplied = new EventEmitter<{
    searchQuery: string;
    selectedFilters: { [sectionId: string]: string[] };
    selectedCapabilities: string[]; // Note: These will be mapped to 'tags' in the API
    selectedRealmCapabilities: string[]; // Adding realm capabilities
    selectedSort: string;
    selectedStatus: string;
    artifactType: string;
    practiceArea: string;
    sampleFile: string;
    pageNumber: number;
    pageSize: number;
  }>();

  // Router injection
  private router = inject(Router);

  searchQuery = '';
  isCreateModalVisible = false;
  isFilterModalVisible = false;
  isDetailModalVisible = false;
  selectedAgent: MarketplaceAgent | null = null;
  constructor(private dialogService: AavaDialogService) {}

  // Filter configuration
  filterSections: FilterSection[] = [];

  selectedFilters: { [sectionId: string]: string[] } = {};

  get selectedAgentMetadata(): ArtifactMetadata | null {
    if (!this.selectedAgent) {
      return null;
    }

    return {
      toolName: this.selectedAgent.title,
      description: this.selectedAgent.description,
      createdBy: this.selectedAgent.author,
      createdOn: this.selectedAgent.createdOn || 'N/A',
      realmAssigned: this.selectedAgent.category,
      avatarUrl: this.selectedAgent.avatarUrl,
    };
  }

  get filteredAgents(): MarketplaceAgent[] {
    let filtered = this.artifactList;

    // Apply search filter
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase().trim();
      filtered = filtered.filter(
        agent =>
          agent.title.toLowerCase().includes(query) ||
          agent.description.toLowerCase().includes(query) ||
          agent.category.toLowerCase().includes(query) ||
          agent.author.toLowerCase().includes(query) ||
          agent.tags.some(tag => tag.toLowerCase().includes(query)) ||
          agent.entityType.toLowerCase().includes(query)
      );
    }

    // Apply other filters
    Object.entries(this.selectedFilters).forEach(
      ([sectionId, filterValues]) => {
        if (filterValues.length > 0) {
          switch (sectionId) {
            case 'category':
              filtered = filtered.filter(agent =>
                filterValues.includes(agent.category)
              );
              break;
            case 'author':
              filtered = filtered.filter(agent =>
                filterValues.includes(agent.author)
              );
              break;
            case 'status':
              filtered = filtered.filter(agent =>
                filterValues.includes(agent.status)
              );
              break;
            case 'tags':
              filtered = filtered.filter(agent =>
                agent.tags.some(tag => filterValues.includes(tag))
              );
              break;
          }
        }
      }
    );

    return filtered;
  }

  defaultActions: ArtifactAction[] = [
    { id: 'view', label: 'View', variant: 'primary' },
    { id: 'install', label: 'Install', variant: 'secondary' },
  ];

  onCreateNew() {
    // Handle navigation to create new agent
    this.isCreateModalVisible = false;
    // TODO: Add navigation logic for creating new artifacts
  }

  onArtifactTypeSelected(artifactType: { id: string }) {
    this.isCreateModalVisible = false;

    // Navigate to the appropriate builder based on artifact type using absolute paths
    // Since marketplace is at root level (/marketplace) and build routes are at root level (/build)
    switch (artifactType.id) {
      case 'agents':
        this.router.navigate(['/build/agent']);
        break;
      case 'tools':
        this.router.navigate(['/build/tools']);
        break;
      case 'guardrails':
        this.router.navigate(['/build/guardrails/create']);
        break;
      case 'pipeline':
        this.router.navigate(['/build/pipelines']);
        break;
      case 'model':
        this.router.navigate(['/build/model/create']);
        break;
      case 'knowledgebases':
        this.router.navigate(['/build/knowledge/create']);
        break;
      default:
        console.warn('Unknown artifact type:', artifactType.id);
    }
  }

  onCreateArtifactClick(event: Event) {
    event.preventDefault();
    this.isCreateModalVisible = true;
  }

  onModalClose() {
    this.isCreateModalVisible = false;
  }

  onDetailModalClose() {
    this.isDetailModalVisible = false;
    this.selectedAgent = null;
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onAgentClick(agent: MarketplaceAgent, event?: MouseEvent) {
    // Handle agent selection or navigation
    this.selectedAgent = agent;

    // Open modal using dialogService and pass data
    const modalRef = this.dialogService.openModal(
      ArtifactDetailModalComponent,
      {},
      {
        data: agent,
      }
    );

    // If the modal reference is a promise with a component instance, set the data
    if (modalRef && typeof modalRef.then === 'function') {
      modalRef.then((instance: ArtifactDetailModalComponent) => {
        if (instance && instance.setDialogData) {
          instance.setDialogData(agent);
        }
      });
    }
  }

  onAgentAction(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    agent: MarketplaceAgent,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    actionEvent?: { action: ArtifactAction; event: MouseEvent }
  ) {
    // Handle specific agent actions
  }

  onTagClick(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    agent: MarketplaceAgent,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    tagEvent: { tag: string; event: MouseEvent }
  ) {
    // Handle tag filtering
  }

  onSearch(query: string) {
    this.searchQuery = query;
    // Implement search functionality
  }

  onFilterClick() {
    this.isFilterModalVisible = !this.isFilterModalVisible;
  }

  onFilterChange(event: FilterSearchEvent) {
    this.searchQuery = event.searchQuery;
    this.selectedFilters = { ...event.selectedFilters };

    // Prepare filter data for emission, with null checks
    const filterData = {
      searchQuery: event.searchQuery || '',
      selectedFilters: event.selectedFilters || {},
      selectedCapabilities: event.selectedCapabilities || [], // These will be passed as 'tags' in the API
      selectedRealmCapabilities: event.selectedRealmCapabilities || [], // Add realm capabilities
      selectedSort: event.selectedSort || '',
      selectedStatus: event.selectedStatus || '',
      artifactType: event.artifactType || 'all', // Default to 'all' instead of undefined
      practiceArea: event.practiceArea || '', // Default to empty string
      sampleFile: event.sampleFile || '', // Default to empty string
      pageNumber: 0,
      pageSize: 20,
    };

    // Emit an event to parent component to refresh artifacts with filters
    this.filterApplied.emit(filterData);
  }

  onFilterModalToggle(isVisible: boolean) {
    this.isFilterModalVisible = isVisible;
  }
  onPlayClick(agent: MarketplaceAgent, event: Event) {
    //route according to entityType
    const routeMap: Record<string, string> = {
      agent: 'agent',
      tool: 'tools',
      pipeline: 'pipelines',
      knowledgebase: 'knowledge',
      guardrail: 'guardrails',
    };

    console.log('Navigating to playground for agent:', routeMap);
    console.log('Agent entityType:', agent.entityType);

    const route = routeMap[agent.entityType];
    console.log('Navigating to playground for agent:', route);
    if (!route) return;

    this.router.navigate([`/build/${route}/playground`], {
      queryParams: { id: agent.id },
    });
  }
}
