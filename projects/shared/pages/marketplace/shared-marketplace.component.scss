/* Marketplace Component Styles */
.marketplace {
  backdrop-filter: blur(15px);
  border: 2px solid var(--Global-colors-White-White, #fdfdfd);
  border-radius: 12px;
  padding: 24px;
  margin: 0 1rem;
  min-height: 100vh;
  background: var(
    --Surface-Fill-Light-Surface-White-6,
    rgba(255, 255, 255, 0.6)
  );

  /* Header */
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32px;
    // gap: 24px;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      // gap: 16px;
    }
  }

  /* Filter Section */
  &__filters {
    margin-bottom: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // gap: 24px;

    @media (max-width: 768px) {
      margin-bottom: 24px;
      flex-direction: column;
      align-items: stretch;
      // gap: 16px;
    }

    app-filter-search-component {
      flex: 0 0 auto;
      max-width: 600px;

      @media (max-width: 768px) {
        max-width: none;
        order: 2;
      }
    }
  }

  &__search-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    max-width: 600px;

    @media (max-width: 768px) {
      flex-direction: column;
      max-width: none;
      width: 100%;
    }
  }

  /* Grid Layout */
  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
    align-items: start;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    @media (min-width: 769px) and (max-width: 1024px) {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
    }

    @media (min-width: 1025px) {
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }

    @media (min-width: 1400px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  /* Agent Card Styling */
  &__agent-card {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      z-index: 2;
    }

    /* Ensure consistent card heights */
    min-height: 232px;
    height: 232px;
    display: flex;
    flex-direction: column;

    /* Custom styling for agent cards */
    :deep(.artifact-details__content) {
      flex: 1;
    }

    :deep(.artifact-details__description) {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.4;
      max-height: calc(1.4em * 3);
    }

    :deep(.artifact-details__tags) {
      max-height: 60px;
      overflow: hidden;
      mask-image: linear-gradient(to bottom, black 70%, transparent 100%);
      -webkit-mask-image: linear-gradient(
        to bottom,
        black 70%,
        transparent 100%
      );
    }

    :deep(.artifact-details__metadata) {
      margin-top: auto;
      padding-top: 8px;
    }

    :deep(.artifact-details__actions) {
      margin-top: 12px;
      border-top: 1px solid
        var(--glass-border-color, rgba(255, 255, 255, 0.125));
      padding-top: 12px;
    }
  }

  /* Create Card Styling - Match agent card height */
  &__grid app-create-artifact {
    :deep(.create-card) {
      height: 100%;
      min-height: 232px;
    }
  }

  /* Skeleton Card Styling */
  &__skeleton-card {
    height: 232px;
    background: var(
      --Surface-Fill-Light-Surface-White-6,
      rgba(255, 255, 255, 0.6)
    );
    backdrop-filter: blur(8px);
    border-radius: 12px;
    border: 1px solid var(--glass-border-color, rgba(255, 255, 255, 0.125));
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
  }

  &__skeleton-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 12px;
  }

  &__skeleton-title {
    margin-bottom: 4px;
  }

  &__skeleton-entity {
    margin-bottom: 8px;
  }

  &__skeleton-description {
    flex-grow: 1;
  }

  &__skeleton-footer {
    margin-top: auto;
    padding-top: 12px;
    border-top: 1px solid var(--glass-border-color, rgba(255, 255, 255, 0.125));
    display: flex;
    justify-content: space-between;
  }

  /* Empty states */
  &__empty {
    grid-column: 1 / -1;
    text-align: center;
    padding: 48px 24px;
    color: var(--color-text-secondary, rgba(255, 255, 255, 0.8));

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 8px 0;
    }

    p {
      font-size: 14px;
      margin: 0;
      opacity: 0.8;
    }
  }

  /* Animation for grid items */
  &__grid > * {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
  }

  @for $i from 1 through 20 {
    &__grid > *:nth-child(#{$i}) {
      animation-delay: #{($i - 1) * 0.1}s;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive adjustments */
  @media (max-width: 480px) {
    padding: 16px;

    &__header {
      margin-bottom: 24px;
    }

    &__grid {
      gap: 16px;
    }

    &__agent-card {
      min-height: 240px;
      height: 240px;

      :deep(.artifact-details__description) {
        -webkit-line-clamp: 2;
        line-clamp: 2;
        max-height: calc(1.4em * 2);
      }
    }

    &__grid app-create-artifact {
      height: 240px;

      :deep(.create-card) {
        height: 100%;
        min-height: 240px;
      }
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    &__agent-card {
      transition: none;

      &:hover {
        transform: none;
      }
    }
  }
}
