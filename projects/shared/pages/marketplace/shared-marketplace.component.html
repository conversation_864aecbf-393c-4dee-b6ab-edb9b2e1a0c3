<!-- Marketplace Page -->
<div class="marketplace">
  <!-- Filter Section -->
  <div class="marketplace__filters">
    <div class="body-bold-default-200-inter">Marketplace</div>
    <app-filter-search-component
      [filterSections]="filterSections"
      [searchPlaceholder]="'Search agents...'"
      (searchChange)="onSearch($event)"
      (filterChange)="onFilterChange($event)"
      (filterModalToggle)="onFilterModalToggle($event)"
    ></app-filter-search-component>
  </div>

  <!-- Content Grid -->
  <div class="marketplace__grid">
    <!-- Create New Card -->
    <app-create-artifact
      (cardClicked)="onCreateArtifactClick($event)"
    ></app-create-artifact>

    <!-- Skeleton Loader Grid while loading -->
    <ng-container *ngIf="isLoading">
      <div
        class="marketplace__skeleton-card"
        *ngFor="let i of [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]"
      >
        <div class="marketplace__skeleton-content">
          <aava-skeleton
            [width]="'100%'"
            [height]="'2rem'"
            [shape]="'rectangle'"
            [animation]="'wave'"
            class="marketplace__skeleton-title"
          ></aava-skeleton>

          <aava-skeleton
            [width]="'30%'"
            [height]="'1.5rem'"
            [shape]="'rectangle'"
            [animation]="'wave'"
            class="marketplace__skeleton-entity"
          ></aava-skeleton>

          <aava-skeleton
            [width]="'100%'"
            [height]="'4rem'"
            [shape]="'rectangle'"
            [animation]="'wave'"
            class="marketplace__skeleton-description"
          ></aava-skeleton>

          <div class="marketplace__skeleton-footer">
            <aava-skeleton
              [width]="'40%'"
              [height]="'1.75rem'"
              [shape]="'rectangle'"
              [animation]="'wave'"
            ></aava-skeleton>

            <aava-skeleton
              [width]="'40%'"
              [height]="'1.75rem'"
              [shape]="'rectangle'"
              [animation]="'wave'"
            ></aava-skeleton>
          </div>
        </div>
      </div>
    </ng-container>

    <!-- Agent Cards -->
    <ng-container *ngIf="!isLoading">
      <div *ngFor="let agent of filteredAgents">
        <app-artifact-details
          [title]="agent.title"
          [entityType]="agent.entityType"
          [description]="agent.description"
          [status]="agent.status"
          [tags]="agent.tags"
          [metadata]="{ Author: agent.author }"
          [actions]="defaultActions"
          [customClass]="'marketplace__agent-card marketplace__card'"
          (cardClicked)="onAgentClick(agent, $event)"
          (actionClicked)="onAgentAction(agent, $event)"
          (tagClicked)="onTagClick(agent, $event)"
          (actionPlayClick)="onPlayClick(agent, $event)"
        ></app-artifact-details>
      </div>
    </ng-container>

    <!-- No Results Message -->
    <div
      *ngIf="!isLoading && filteredAgents.length === 0 && searchQuery.trim()"
      class="marketplace__empty"
    >
      <h3>No agents found</h3>
      <p>Try adjusting your search terms or browse all available agents.</p>
    </div>
  </div>
</div>

<!-- Create Artifact Modal -->
<app-create-artifact-modal
  [isVisible]="isCreateModalVisible"
  (modalClosed)="onModalClose()"
  (artifactTypeSelected)="onArtifactTypeSelected($event)"
></app-create-artifact-modal>
