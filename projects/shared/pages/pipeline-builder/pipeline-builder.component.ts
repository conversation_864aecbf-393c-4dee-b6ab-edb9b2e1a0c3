/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable complexity */
import { AavaDialogService } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  computed,
  effect,
  inject,
  Injectable,
  Injector,
  signal,
  untracked,
  OnInit,
  OnDestroy,
  OnChanges,
  SimpleChanges,
  Input,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Mutator } from '@foblex/mutator';
import { WorkflowStore } from 'projects/shared/stores';
import { Subject, takeUntil } from 'rxjs';

// Local components
import {
  AgentDetailsModalComponent,
  AgentDetailsData,
} from '../../components/agent-details-modal/agent-details-modal.component';
import { FilterChangeEvent } from '../../components/filter-component';
import { AgentCardNodeData } from '../../components/pipeline-builder/components/agent-card-node';
import {
  CanvasNode,
  CanvasConnection,
  CanvasViewport,
} from '../../components/pipeline-builder/components/main-canvas';
import { PipelineCanvasPanelComponent } from '../../components/pipeline-builder/components/pipeline-canvas-panel';
import { PipelineLibraryPanelComponent } from '../../components/pipeline-builder/components/pipeline-library-panel';
import { WorkflowDetailsData } from '../../components/pipeline-builder/components/workflow-details-accordion';
// Services
import { AgentService } from '../../services/agent.service';
import { GoodAtTagsService } from '../../services/good-at-tags.service';
import { PracticeAreasService } from '../../services/practice-areas.service';
import { TeamIdService } from '../../services/team-id.service';
import { WorkflowService } from '../../services/workflow.service';

// State interface for Mutator
interface IState {
  nodes: Record<string, CanvasNode>;
  connections: Record<string, CanvasConnection>;
  selection?: {
    nodes: string[];
    connections: string[];
  };
  transform?: {
    position: { x: number; y: number };
    scale: number;
  };
}

// Workflow payload interfaces
interface WorkflowAgent {
  serial: number;
  agentId: number;
}

interface WorkflowConfig {
  managerLlm: any[];
  topP: number;
  maxToken: number;
  temperature: number;
  enableAgenticMemory: boolean;
}

interface WorkflowPayload {
  name: string;
  description: string;
  workflowAgents: WorkflowAgent[];
  topP: number;
  maxToken: number;
  temperature: number;
  workflowConfig: WorkflowConfig;
  teamId: number;
  status: 'CREATED' | 'DRAFTED' | 'IN_REVIEW' | 'APPROVED' | 'REJECTED';
  createdBy: number;
  modifiedBy: number;
}

@Injectable()
class PipelineState extends Mutator<IState> {}

@Component({
  selector: 'app-pipeline-builder',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    PipelineLibraryPanelComponent,
    PipelineCanvasPanelComponent,
  ],
  providers: [PipelineState],
  templateUrl: './pipeline-builder.component.html',
  styleUrls: ['./pipeline-builder.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PipelineBuilderComponent implements OnInit, OnDestroy, OnChanges {
  // Input properties for external control
  @Input() executeMode: boolean = false;
  @Input() workflowAgents: any[] = [];
  @Input() editMode: boolean = false;
  @Input() pipelineId?: number;

  // Services
  private readonly agentService = inject(AgentService);
  private readonly goodAtTagsService = inject(GoodAtTagsService);
  private readonly practiceAreasService = inject(PracticeAreasService);
  private readonly workflowService = inject(WorkflowService);
  private readonly teamIdService = inject(TeamIdService);
  private readonly router = inject(Router);
  private readonly dialogService = inject(AavaDialogService);
  private readonly destroy$ = new Subject<void>();

  // Execute mode properties
  protected readonly isExecuteMode = signal<boolean>(false);
  protected readonly internalWorkflowAgents = signal<any[]>([]);

  // Computed properties for execute mode
  protected readonly hasWorkflowAgents = computed(() => {
    return this.internalWorkflowAgents().length > 0;
  });

  // Canvas panel configuration for execute mode
  protected readonly canvasPanelConfig = computed(() => {
    if (this.isExecuteMode()) {
      return {
        showTopbar: true,
        topbarConfig: {
          undoText: 'Undo',
          redoText: 'Redo',
          runText: 'Run',
          showUndoRedo: false, // Hide undo/redo in execute mode
          showRun: false, // Hide run button in execute mode
        },
        canvasHeight: '80%',
        canvasConfig: {
          enablePan: true,
          enableZoom: false, // Disable zoom in execute mode
          enableSelection: false, // Disable selection in execute mode
          enableConnections: false, // Disable connections in execute mode
          gridSize: 20,
          snapToGrid: false,
          minZoom: 0.1,
          maxZoom: 3,
          defaultZoom: 1,
        },
      };
    }

    // Default configuration for normal mode
    return {
      showTopbar: true,
      topbarConfig: {
        undoText: 'Undo',
        redoText: 'Redo',
        runText: 'Run',
        showUndoRedo: false,
        showRun: true,
      },
      canvasHeight: '100%',
      canvasConfig: {
        enablePan: true,
        enableZoom: true,
        enableSelection: true,
        enableConnections: true,
        gridSize: 20,
        snapToGrid: false,
        minZoom: 0.1,
        maxZoom: 3,
        defaultZoom: 1,
      },
    };
  });

  // Auto-save properties
  private autosaveTimeout: any;
  private autosaveCount = 0;
  private readonly baseDelay = 2000; // 2 seconds base delay
  private readonly maxDelay = 30000; // 30 seconds max delay

  // Workflow data
  protected readonly workflowData = signal<WorkflowDetailsData>({
    name: '',
    description: '',
    temperature: '',
    topP: '',
    maxRpm: '',
    maxToken: '',
    maxIteration: '',
    maxExecutionTime: '',
    selectedModel: '',
    enableManagerLlm: false,
  });

  searchValue = '';

  // Canvas state
  canvasViewport: CanvasViewport = { x: 0, y: 0, zoom: 1 };

  // Mutator state management
  protected readonly state = inject(PipelineState);
  private readonly _injector = inject(Injector);
  private readonly workflowStore = inject(WorkflowStore);

  protected readonly viewModel = signal<IState | undefined>(undefined);

  // Agents data
  protected readonly availableAgents = signal<AgentCardNodeData[]>([]);
  protected readonly isLoadingAgents = signal<boolean>(false);
  protected readonly agentsError = signal<string | null>(null);
  protected readonly isSavingWorkflow = signal<boolean>(false);

  // Pipeline status tracking
  protected readonly originalPipelineStatus = signal<string>('DRAFTED');

  // Filtered agents (excluding those used in canvas and applying search)
  protected readonly filteredAgents = computed(() => {
    const allAgents = this.availableAgents();
    const usedAgentIds = this.getUsedAgentIds();
    const searchValue = this.searchValue.toLowerCase().trim();
    // Force recomputation by accessing the viewModel
    this.viewModel();

    // First filter out used agents
    let filtered = allAgents.filter(agent => !usedAgentIds.has(agent.id));

    // Then apply search filter if there's a search value
    if (searchValue) {
      filtered = filtered.filter(
        agent =>
          agent.name.toLowerCase().includes(searchValue) ||
          agent.tags?.some(tag => tag.toLowerCase().includes(searchValue))
      );
    }

    return filtered;
  });

  // Cache for dropdown data
  private goodAtTagsCache: Map<number, string> = new Map();
  private practiceAreasCache: Map<number, string> = new Map();

  // Computed values from state
  protected readonly canvasNodes = computed(() => {
    return Object.values(this.viewModel()?.nodes || {});
  });

  protected readonly canvasConnections = computed(() => {
    return Object.values(this.viewModel()?.connections || {});
  });

  // Validation computed values
  protected readonly hasMinimumAgents = computed(() => {
    const nodes = this.canvasNodes();
    return nodes.length >= 1;
  });

  protected readonly hasWorkflowDetails = computed(() => {
    const data = this.workflowData();
    return !!(data.name?.trim() && data.description?.trim());
  });

  protected readonly canRunWorkflow = computed(() => {
    return this.hasMinimumAgents() && this.hasWorkflowDetails();
  });

  protected readonly canUndo = computed(() => this.state.canUndo());
  protected readonly canRedo = computed(() => this.state.canRedo());

  private _listenStateChanges(): void {
    effect(
      () => {
        this.state.changes();
        untracked(() => this._applyChanges());
      },
      { injector: this._injector }
    );
  }

  private _applyChanges(): void {
    this.viewModel.set(this.state.getSnapshot());
  }

  // Canvas state is now managed by Mutator

  constructor() {
    this._listenStateChanges();
    // Initialize state with empty canvas
    this.state.initialize({
      nodes: {},
      connections: {},
      transform: {
        position: { x: 0, y: 0 },
        scale: 1,
      },
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Check if workflowAgents input has changed
    if (changes['workflowAgents'] || changes['executeMode']) {
      this.initializeExecuteMode();
    }
  }

  ngOnInit(): void {
    this.loadAgents();
    this.loadPipelineForEdit();
    // Don't call initializeExecuteMode here - it's called in ngOnChanges
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    // Clear auto-save timeout
    if (this.autosaveTimeout) {
      clearTimeout(this.autosaveTimeout);
    }
  }

  /**
   * Load dropdown data for mapping
   */
  private loadDropdownData(): void {
    // Load good at tags
    this.goodAtTagsService
      .getGoodAtTags()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: tags => {
          tags.forEach(tag => {
            this.goodAtTagsCache.set(tag.id, tag.name);
          });
        },
        error: error => {
          console.error('Error loading good at tags:', error);
        },
      });

    // Load practice areas
    this.practiceAreasService
      .getPracticeAreas()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: areas => {
          areas.forEach(area => {
            this.practiceAreasCache.set(area.id, area.name);
          });
        },
        error: error => {
          console.error('Error loading practice areas:', error);
        },
      });
  }

  /**
   * Initialize execute mode if workflowAgents are provided
   */
  private initializeExecuteMode(): void {
    // Set execute mode based on input
    this.isExecuteMode.set(this.executeMode);
    this.internalWorkflowAgents.set(this.workflowAgents);

    // Render nodes from workflowAgents only if we have agents
    if (this.executeMode && this.workflowAgents.length > 0) {
      this.renderWorkflowNodes(this.workflowAgents);
    }
  }

  /**
   * Render nodes from workflowAgents data
   */
  private renderWorkflowNodes(workflowAgents: any[]): void {
    const nodes: Record<string, CanvasNode> = {};
    const connections: Record<string, CanvasConnection> = {};

    // Create nodes for each agent
    workflowAgents.forEach((agent, index) => {
      const nodeId = `agent-${agent.agentId}`;
      const node: CanvasNode = {
        id: nodeId,
        type: 'agent',
        position: { x: 50 + index * 250, y: 100 }, // Position nodes horizontally, more towards left
        data: {
          id: agent.agentId.toString(),
          name: agent.name,
          description: agent.description,
          agentDetails: agent.agentDetails,
          modelName: agent.modelName,
          modelDeploymentName: agent.modelDeploymentName,
          serial: agent.serial,
          workflowId: agent.workflowId,
        },
      };

      nodes[nodeId] = node;

      // Create connections between sequential agents
      if (index > 0) {
        const prevNodeId = `agent-${workflowAgents[index - 1].agentId}`;
        const connectionId = `conn-${prevNodeId}-${nodeId}`;
        const connection: CanvasConnection = {
          id: connectionId,
          source: prevNodeId,
          target: nodeId,
          outputId: `output-${prevNodeId}`,
          inputId: `input-${nodeId}`,
          type: 'auto-sequential',
          animated: false,
        };

        connections[connectionId] = connection;
      }
    });

    // Initialize state with the rendered nodes
    this.state.initialize({
      nodes,
      connections,
      transform: {
        position: { x: 0, y: 0 },
        scale: 1,
      },
    });
  }

  /**
   * Load pipeline data for edit mode
   */
  private loadPipelineForEdit(): void {
    if (this.editMode && this.pipelineId) {
      this.workflowService
        .getById(this.pipelineId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: any) => {
            const pipeline = response?.workFlowDetail;
            if (pipeline) {
              // Set original status for edit mode
              this.originalPipelineStatus.set(pipeline.status || 'DRAFTED');

              // Load workflow data
              this.workflowData.set({
                name: pipeline.name || '',
                description: pipeline.description || '',
                temperature: pipeline.temperature?.toString() || '',
                topP: pipeline.topP?.toString() || '',
                maxRpm: pipeline.maxRpm?.toString() || '',
                maxToken: pipeline.maxToken?.toString() || '',
                maxIteration: pipeline.maxIteration?.toString() || '',
                maxExecutionTime: pipeline.maxExecutionTime?.toString() || '',
                selectedModel: pipeline.selectedModel || '',
                enableManagerLlm:
                  pipeline.workflowConfig?.enableAgenticMemory || false,
              });

              // Load workflow agents and render nodes
              if (
                pipeline.workflowAgents &&
                pipeline.workflowAgents.length > 0
              ) {
                this.renderWorkflowNodes(pipeline.workflowAgents);
              }
            }
          },
          error: error => {
            console.error('Error loading pipeline for edit:', error);
          },
        });
    }
  }

  /**
   * Load agents from API
   */
  private loadAgents(): void {
    this.isLoadingAgents.set(true);
    this.agentsError.set(null);

    this.agentService
      .getAll({
        isDeleted: false,
        type: 'workflow',
      })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (agents: unknown) => {
          // Handle the API response structure
          const agentDetails =
            (agents as any)?.agentDetails || (agents as any[]) || [];
          const mappedAgents = this.mapAgentsToCardData(agentDetails);
          this.availableAgents.set(mappedAgents);
          this.isLoadingAgents.set(false);
        },
        error: error => {
          console.error('Error loading agents:', error);
          this.agentsError.set('Failed to load agents');
          this.isLoadingAgents.set(false);
        },
      });
  }

  /**
   * Get IDs of agents currently used in the canvas
   */
  private getUsedAgentIds(): Set<string> {
    const usedIds = new Set<string>();
    const currentState = this.state.getSnapshot();

    Object.values(currentState.nodes).forEach(node => {
      // Extract agent ID from node data
      if (node.data && node.data.id) {
        usedIds.add(node.data.id);
      }
    });

    return usedIds;
  }

  /**
   * Map API agents to AgentCardNodeData format
   */
  private mapAgentsToCardData(agents: any[]): AgentCardNodeData[] {
    return agents.map(agent => {
      // Map tags from agent.tags (good at tags)
      const tags: string[] = [];

      if (agent.tags && Array.isArray(agent.tags)) {
        agent.tags.forEach((tagId: number) => {
          const tagName = this.goodAtTagsCache.get(tagId);
          if (tagName) {
            tags.push(tagName);
          }
        });
      }

      // Map practice area as type
      let practiceAreaName = 'Agent';
      if (agent.practiceArea) {
        const practiceArea = this.practiceAreasCache.get(agent.practiceArea);
        if (practiceArea) {
          practiceAreaName = practiceArea;
        }
      }

      return {
        id: agent.id.toString(),
        name: agent.name,
        tags: tags.length > 0 ? tags : ['AI Agent'],
        icon: 'bot',
        type: practiceAreaName,
        disabled: false,
        selected: false,
        data: agent, // Complete agent data
      };
    });
  }

  onSearchChange(value: string): void {
    this.searchValue = value;
  }

  onFilterChange(event: FilterChangeEvent): void {
    // The filteredAgents computed property automatically handles:
    // 1. Hiding agents that are currently used in the canvas
    // 2. Applying search and other filters on top of that
    // This ensures used agents are always hidden regardless of other filters
    console.log('Filter changed:', event);
    // TODO: Implement additional filtering logic if needed
    // The base filtering (hiding used agents) is handled by filteredAgents computed property
  }

  onCreateAgentClick(): void {
    // TODO: Implement create agent functionality
  }

  onUndoClick(): void {
    this.state.undo();
  }

  onRedoClick(): void {
    this.state.redo();
  }

  async onRunClick(): Promise<void> {
    try {
      // Save workflow first with CREATED status, then navigate to playground
      const workflow = await this.onSaveWorkflow(true);

      // Navigate to playground after successful save with workflow ID
      this.router.navigate(['/build/pipelines/playground'], {
        queryParams: { id: workflow.id },
      });
    } catch (error) {
      console.error('Failed to save workflow:', error);
      // TODO: Show error message to user
    }
  }

  /**
   * Save workflow to backend
   */
  onSaveWorkflow(isRunClick: boolean = false): Promise<any> {
    const payload = this.buildWorkflowPayload(isRunClick);
    this.isSavingWorkflow.set(true);

    return new Promise((resolve, reject) => {
      this.workflowService
        .create(payload)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: workflow => {
            console.log('Workflow saved successfully:', workflow);
            this.isSavingWorkflow.set(false);
            resolve(workflow);
          },
          error: error => {
            console.error('Error saving workflow:', error);
            this.isSavingWorkflow.set(false);
            reject(error);
          },
        });
    });
  }

  /**
   * Build workflow payload from current state
   */
  private buildWorkflowPayload(isRunClick: boolean = false): WorkflowPayload {
    const workflowData = this.workflowData();
    const workflowAgents = this.buildWorkflowAgents();
    const teamId = this.teamIdService.getTeamIdFromCookies() || 216; // Fallback to 216 if not found

    // Determine status based on context
    let status: 'CREATED' | 'DRAFTED' | 'IN_REVIEW' | 'APPROVED' | 'REJECTED';
    if (isRunClick) {
      // Run button click: set to CREATED
      status = 'CREATED';
    } else if (this.editMode) {
      // Edit mode: keep original status
      status = this.originalPipelineStatus() as
        | 'CREATED'
        | 'DRAFTED'
        | 'IN_REVIEW'
        | 'APPROVED'
        | 'REJECTED';
    } else {
      // Autosave: use DRAFTED
      status = 'DRAFTED';
    }

    const payload: WorkflowPayload = {
      name: workflowData.name || 'Untitled Workflow',
      description: workflowData.description || '',
      workflowAgents,
      topP: parseFloat(workflowData.topP || '0.95') || 0.95,
      maxToken: parseInt(workflowData.maxToken || '4000') || 4000,
      temperature: parseFloat(workflowData.temperature || '0.3') || 0.3,
      teamId,
      status,
      createdBy: 101, // Default user ID
      modifiedBy: 101, // Default user ID
      workflowConfig: {
        managerLlm: workflowData.enableManagerLlm ? [] : [],
        topP: parseFloat(workflowData.topP || '0.95') || 0.95,
        maxToken: parseInt(workflowData.maxToken || '4000') || 4000,
        temperature: parseFloat(workflowData.temperature || '0.3') || 0.3,
        enableAgenticMemory: workflowData.enableManagerLlm || false,
      },
    };

    return payload;
  }

  /**
   * Build workflow agents array from canvas nodes in linear order
   */
  private buildWorkflowAgents(): WorkflowAgent[] {
    const currentState = this.state.getSnapshot();
    const nodes = Object.values(currentState.nodes);

    if (nodes.length === 0) {
      return [];
    }

    // Sort nodes by their position to get linear order
    const sortedNodes = nodes.sort((a, b) => {
      // Sort by x position (left to right)
      return a.position.x - b.position.x;
    });

    return sortedNodes.map((node, index) => ({
      serial: index + 1,
      agentId: parseInt(node.data?.id) || 0,
    }));
  }

  // Workflow details event handlers
  onPipelineNameChange(value: string): void {
    this.workflowData.update(data => ({ ...data, name: value }));
    this.triggerAutosave();
  }

  onDescriptionChange(value: string): void {
    this.workflowData.update(data => ({ ...data, description: value }));
    this.triggerAutosave();
  }

  onTemperatureChange(value: string): void {
    this.workflowData.update(data => ({ ...data, temperature: value }));
    this.triggerAutosave();
  }

  onTopPChange(value: string): void {
    this.workflowData.update(data => ({ ...data, topP: value }));
    this.triggerAutosave();
  }

  onMaxRpmChange(value: string): void {
    this.workflowData.update(data => ({ ...data, maxRpm: value }));
    this.triggerAutosave();
  }

  onMaxTokenChange(value: string): void {
    this.workflowData.update(data => ({ ...data, maxToken: value }));
    this.triggerAutosave();
  }

  onMaxIterationChange(value: string): void {
    this.workflowData.update(data => ({ ...data, maxIteration: value }));
    this.triggerAutosave();
  }

  onMaxExecutionTimeChange(value: string): void {
    this.workflowData.update(data => ({ ...data, maxExecutionTime: value }));
    this.triggerAutosave();
  }

  onModelChange(value: string): void {
    this.workflowData.update(data => ({ ...data, selectedModel: value }));
    this.triggerAutosave();
  }

  onManagerLlmToggle(enabled: boolean): void {
    this.workflowData.update(data => ({ ...data, enableManagerLlm: enabled }));
    this.triggerAutosave();
  }

  onAgentClick(agent: AgentCardNodeData): void {
    // Transform agent card data to AgentDetailsData format with correct mapping
    const agentDetailsData = this.transformAgentCardDataToAgentDetails(agent);

    // Open the agent details modal with correct data and increased width
    const modalRef = this.dialogService.openModal(
      AgentDetailsModalComponent,
      {
        width: '60vw',
        maxWidth: '1200px',
        maxHeight: '80vh',
      },
      {
        data: agentDetailsData,
      }
    );

    // Set the data on the modal instance
    if (modalRef && typeof modalRef.then === 'function') {
      modalRef.then((instance: AgentDetailsModalComponent) => {
        if (instance && instance.setDialogData) {
          instance.setDialogData(agentDetailsData);
        }
      });
    }
  }

  onAgentDragStart(_event: DragEvent): void {
    // The drag data is already set by the agent-card-node component
  }

  /**
   * Transform agent card data to AgentDetailsData format with correct mapping
   */
  private transformAgentCardDataToAgentDetails(
    agentCard: AgentCardNodeData
  ): AgentDetailsData {
    // Get the complete agent data from the card's data property
    const agentData = agentCard.data || {};

    return {
      id: agentCard.id || '',
      name: agentCard.name || '',
      role: agentData.role || '',
      practiceArea: agentCard.type || '',
      goodAt: agentCard.tags || [],
      model: agentData.modelName || '',
      temperature: agentData.temperature || '',
      maxRpm: agentData.maxRpm || '100',
      maxToken: agentData.maxToken || '',
      topP: agentData.topP || '',
      maxIteration: agentData.maxIteration || '',
      maxExecutionTime: agentData.maxExecutionTime || '',
      knowledgeBase: agentData.knowledgeBase || [],
      guardrails: agentData.guardrails || [],
      tools: agentData.tools || [],
      goal: agentData.goal || '',
      backStory: agentData.backStory || '',
      description: agentData.description || 'No description available',
    };
  }

  onNodeDragStart(event: DragEvent, node: unknown): void {
    if (event.dataTransfer) {
      event.dataTransfer.setData('text/plain', JSON.stringify(node));
    }
  }

  onCanvasDrop(event: DragEvent): void {
    event.preventDefault();
    // Node will be handled by the canvas component via onCreateNode
  }

  onCanvasDragOver(event: DragEvent): void {
    event.preventDefault();
  }

  // Canvas event handlers

  onNodeSelected(_nodeId: string): void {
    // Node selection is handled by Foblex Flow internally
  }

  onNodeDoubleClicked(_nodeId: string): void {
    // TODO: Implement node double-click functionality
  }

  onConnectionCreated(event: { source: string; target: string }): void {
    const newConnection: CanvasConnection = {
      id: `conn-${Date.now()}`,
      source: event.source,
      target: event.target,
      outputId: `output-${event.source}`,
      inputId: `input-${event.target}`,
      type: 'auto-sequential',
      animated: false,
    };

    this.state.create({
      connections: {
        [newConnection.id]: newConnection,
      },
    });
  }

  onConnectionDeleted(connectionId: string): void {
    // Remove connection using Object.fromEntries
    const currentState = this.state.getSnapshot();
    const updatedConnections = Object.fromEntries(
      Object.entries(currentState.connections).filter(
        ([key]) => key !== connectionId
      )
    );

    this.state.create({
      nodes: currentState.nodes,
      connections: updatedConnections,
    });
  }

  onViewportChanged(event: CanvasViewport): void {
    this.canvasViewport = event;
  }

  onCanvasClicked(_event: { x: number; y: number }): void {
    // TODO: Implement canvas click functionality
  }

  onNodeCreated(node: CanvasNode): void {
    this.state.create({
      nodes: {
        [node.id]: node,
      },
    });
    this.triggerAutosave();
  }

  onNodeMoved(event: {
    nodeId: string;
    position: { x: number; y: number };
  }): void {
    this.state.update({
      nodes: {
        [event.nodeId]: { position: event.position },
      },
    });
  }

  onNodeRemoved(nodeId: string): void {
    // Get current state
    const currentState = this.state.getSnapshot();

    // Find all connections that involve this node
    const connectionsToRemove: string[] = [];
    Object.values(currentState.connections).forEach(connection => {
      if (connection.source === nodeId || connection.target === nodeId) {
        connectionsToRemove.push(connection.id);
      }
    });

    // Remove the node
    const filteredNodes = Object.fromEntries(
      Object.entries(currentState.nodes).filter(([key]) => key !== nodeId)
    );

    // Remove all connections involving this node
    Object.fromEntries(
      Object.entries(currentState.connections).filter(
        ([key]) => !connectionsToRemove.includes(key)
      )
    );

    // Reconnect remaining nodes in linear sequence
    const remainingNodes = Object.values(filteredNodes);
    const newConnections: Record<string, CanvasConnection> = {};

    // Create linear connections between remaining nodes
    for (let i = 0; i < remainingNodes.length - 1; i++) {
      const currentNode = remainingNodes[i];
      const nextNode = remainingNodes[i + 1];

      const connectionId = `conn-${Date.now()}-${i}`;
      const newConnection: CanvasConnection = {
        id: connectionId,
        source: currentNode.id,
        target: nextNode.id,
        outputId: `output-${currentNode.id}`,
        inputId: `input-${nextNode.id}`,
        type: 'auto-sequential',
        animated: false,
      };

      newConnections[connectionId] = newConnection;
    }

    // Initialize with filtered nodes and new connections
    // Note: The filteredAgents computed property will automatically
    // make the removed agent available again in the left panel
    this.state.initialize({
      nodes: filteredNodes,
      connections: newConnections,
    });
    this.triggerAutosave();
  }

  // ==================== AUTOSAVE FUNCTIONALITY ====================

  /**
   * Trigger autosave when workflow data changes
   */
  private triggerAutosave(): void {
    const hasRequiredFields = this.canRunWorkflow();

    if (hasRequiredFields) {
      // Clear any existing timeout
      clearTimeout(this.autosaveTimeout);

      // Calculate delay with exponential backoff
      const delay = this.calculateAutosaveDelay();

      this.autosaveTimeout = setTimeout(() => {
        const payload = this.buildWorkflowPayload(false); // false = not run click, so DRAFTED status

        // Auto-save workflow (silent save, no navigation)
        this.workflowService
          .create(payload)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: workflow => {
              console.log('Workflow auto-saved:', workflow);
              this.autosaveCount++;
            },
            error: error => {
              console.error('Failed to auto-save workflow:', error);
            },
          });
      }, delay);
    }
  }

  /**
   * Calculate autosave delay with exponential backoff
   */
  private calculateAutosaveDelay(): number {
    if (this.autosaveCount === 0) {
      // First save: immediate
      return 0;
    }

    // Subsequent saves: exponential backoff
    const delay = Math.min(
      this.baseDelay * Math.pow(2, this.autosaveCount - 1),
      this.maxDelay
    );

    return delay;
  }
}
