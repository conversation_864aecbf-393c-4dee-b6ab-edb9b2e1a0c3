<div class="pipeline-builder-container" [class.execute-mode]="isExecuteMode()">
  <!-- Left Sidebar - Hidden in execute mode -->
  @if (!isExecuteMode()) {
    <app-pipeline-library-panel
      [agents]="filteredAgents()"
      [searchValue]="searchValue"
      [disabled]="isLoadingAgents()"
      [isLoading]="isLoadingAgents()"
      (searchChanged)="onSearchChange($event)"
      (filterChanged)="onFilterChange($event)"
      (createAgentClicked)="onCreateAgentClick()"
      (agentClicked)="onAgentClick($event)"
      (agentDragStarted)="onAgentDragStart($event)"
    >
    </app-pipeline-library-panel>
  }

  <!-- Right Main Area - Full width in execute mode -->
  <app-pipeline-canvas-panel
    [workflowData]="workflowData()"
    [canUndo]="canUndo()"
    [canRedo]="canRedo()"
    [disabled]="isExecuteMode()"
    [isLoading]="isSavingWorkflow()"
    [canRunWorkflow]="canRunWorkflow()"
    [hasMinimumAgents]="hasMinimumAgents()"
    [hasWorkflowDetails]="hasWorkflowDetails()"
    [canvasNodes]="canvasNodes()"
    [canvasConnections]="canvasConnections()"
    [canvasViewport]="canvasViewport"
    [config]="canvasPanelConfig()"
    (pipelineNameChanged)="onPipelineNameChange($event)"
    (descriptionChanged)="onDescriptionChange($event)"
    (temperatureChanged)="onTemperatureChange($event)"
    (topPChanged)="onTopPChange($event)"
    (maxRpmChanged)="onMaxRpmChange($event)"
    (maxTokenChanged)="onMaxTokenChange($event)"
    (maxIterationChanged)="onMaxIterationChange($event)"
    (maxExecutionTimeChanged)="onMaxExecutionTimeChange($event)"
    (modelChanged)="onModelChange($event)"
    (managerLlmToggled)="onManagerLlmToggle($event)"
    (undoClicked)="onUndoClick()"
    (redoClicked)="onRedoClick()"
    (runClicked)="onRunClick()"
    (nodeCreated)="onNodeCreated($event)"
    (nodeSelected)="onNodeSelected($event)"
    (nodeDoubleClicked)="onNodeDoubleClicked($event)"
    (nodeRemoved)="onNodeRemoved($event)"
    (nodeMoved)="onNodeMoved($event)"
    (connectionCreated)="onConnectionCreated($event)"
    (connectionDeleted)="onConnectionDeleted($event)"
    (viewportChanged)="onViewportChanged($event)"
    (canvasClicked)="onCanvasClicked($event)"
  >
  </app-pipeline-canvas-panel>

  <!-- Agent Details Modal -->
</div>
