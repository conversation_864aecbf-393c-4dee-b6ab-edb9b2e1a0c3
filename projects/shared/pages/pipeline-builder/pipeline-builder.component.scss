.pipeline-builder-container {
  display: flex;
  height: 100vh;
  gap: 1rem;
  margin: 1rem;

  // Execute mode - full width canvas
  &.execute-mode {
    app-pipeline-canvas-panel {
      width: 100%;
      flex: 1;
    }
  }
}

// Left Sidebar - Now using pipeline-library-panel component
app-pipeline-library-panel {
  width: 320px;
  flex-shrink: 0;
}

// Right Main Area - Canvas Panel
app-pipeline-canvas-panel {
  flex: 1;
  min-width: 0; // Allow flex child to shrink
}

// Global fix for Aava select dropdowns positioning
::ng-deep .aava-select-panel {
  position: fixed !important;
  z-index: 100000 !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
}

// Note: Canvas styling is now handled by the main-canvas-foblex component

// Responsive Design
@media (max-width: 1024px) {
  app-pipeline-library-panel {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .pipeline-builder-container {
    flex-direction: column;
    height: auto;
  }

  app-pipeline-library-panel {
    width: 100%;
    height: 200px;
    overflow-y: auto;
  }

  app-pipeline-canvas-panel {
    height: calc(100vh - 200px);
  }
}
