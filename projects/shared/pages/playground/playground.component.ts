/* eslint-disable complexity */
import { AavaDialogService, AavaSkeletonComponent } from '@aava/play-core';
import { CommonModule, Location } from '@angular/common';
import { Component, OnInit, signal, computed, OnDestroy } from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TokenStorageService } from '@shared';
import { ApprovalModalComponent } from 'projects/shared/components/approve-modal/approval-approval-modal.component';
import { LoadingOverlayComponent } from 'projects/shared/components/loading-overlay/loading-overlay.component';
import {
  ArtifactInputsComponent,
  InputField,
} from 'projects/shared/components/playground-widgets/artifact-inputs/artifact-inputs.component';
import { ArtifactMetaInfoComponent } from 'projects/shared/components/playground-widgets/artifact-meta-info/artifact-meta-info.component';
import { BlueprintWidgetComponent } from 'projects/shared/components/playground-widgets/blueprint-widget/blueprint-widget.component';
import {
  LogViewerComponent,
  ColoredLogEntry,
} from 'projects/shared/components/playground-widgets/log-viewer/log-viewer.component';
import { OutputWidgetComponent } from 'projects/shared/components/playground-widgets/output-widget/output-widget.component';
import {
  HeaderTab,
  HeaderAction,
  PlaygroundHeaderComponent,
} from 'projects/shared/components/playground-widgets/playground-header/playground-header.component';
import { SendbackArtifactModalComponent } from 'projects/shared/components/sendback-modal/sendback-modal.component';
import { AgentService, WorkflowService, GuardrailService } from 'projects/shared/services';
import { ApprovalService } from 'projects/shared/services/approval.service';
import { PlaygroundService } from 'projects/shared/services/playground.service';
import { ToastWrapperService } from 'projects/shared/services/toast-wrapper.service';
import { catchError, firstValueFrom, from, map, of, switchMap } from 'rxjs';
import { Observable, Subject, takeUntil } from 'rxjs';
import { ToolService } from '../../services/tool.service';
import { DropdownDataStore } from '../../stores/dropdown-data.store';

@Component({
  selector: 'app-playground',
  standalone: true,
  imports: [
    CommonModule,
    ArtifactMetaInfoComponent,
    ArtifactInputsComponent,
    PlaygroundHeaderComponent,
    BlueprintWidgetComponent,
    OutputWidgetComponent,
    LogViewerComponent,
    ReactiveFormsModule,
    AavaSkeletonComponent,
  ],
  templateUrl: './playground.component.html',
  styleUrls: ['./playground.component.scss'],
})
export class PlaygroundComponent implements OnInit, OnDestroy {
  activeTab: string = 'blueprint';
  isLeftPanelCollapsed: boolean = false;
  playgroundType: string = ''; // Remove default, let ngOnInit set it
  agentData: any = null;
  agentId: number | null = null; // Store agent ID from query params
  hasTriedExecution: boolean = false;
  isReviewMode: boolean = false; // Set to true when inReview=true in query params
  executionLogs: any[] = [];
  workflowLogs: ColoredLogEntry[] = []; // For real-time WebSocket logs
  isExecutionLoading = false;
  agentDetails: any;
  executionId: string = '';
  finalOutput: string | any = '';
  pipelineDetails: any = [];
  pipelineId!: number;
  toolId!: any; // Keep for non-signal usage (approval, etc.)
  currentDetails: any = null;
  currentInputs: InputField[] = [];
  allowedFormats: string[] = ['zip'];
  isDataLoading: boolean = false;
  guardrailId!: number;
  // WebSocket and execution management
  private destroy$ = new Subject<void>();
  // API data
  apiData = {
    agentName: 'Autonomous Systems Insight and Console',
    description:
      'AI agents are software systems that use AI to pursue goals and complete tasks on behalf of users.',
  };

  // Signal-based tool management
  signalToolId = signal<string | null>(null); // Signal for tool ID (used for reactive operations)
  isLoadingTool = signal<boolean>(false);
  isExtractingParameters = signal<boolean>(false);
  toolParameters = signal<{ [key: string]: string }>({});
  dynamicForm = signal<FormGroup | null>(null);
  toolData = signal<any>(null);
  guardrailData = signal<any>(null);
  toolTestResults = signal<any>(null);
  guardRailsTestResults = signal<any>(null);

  // Artifact status tracking
  artifactStatus = signal<string>('DRAFTED'); // Default status
  isStatusLoading = signal<boolean>(true); // Track if status is still loading

  // Blueprint loading states
  isBlueprintLoading = signal<boolean>(false); // Track blueprint data loading
  isApprovalLoading = signal<boolean>(false); // Track approval action loading

  protected readonly dynamicToolInputs = computed(() => {
    const params = this.toolParameters();
    return Object.entries(params).map(
      ([paramName, paramType]) =>
        ({
          label: paramName,
          placeholder: this.getPlaceholderForType(paramType),
          type: this.getInputType(paramType) as 'text' | 'file',
          required: true,
          value: this.getDefaultValue(paramType)?.toString() || '',
        }) as InputField
    );
  });

  // Tool-specific metadata
  toolMetadata = {
    fields: [
      { label: 'Realm Assigned', value: 'Ascendion' },
      { label: 'Business domain', value: 'Marketing' },
      { label: 'Created by', value: 'Michael Scott' },
      { label: 'Created on', value: '12 May 2024' },
    ],
    tags: [
      { text: 'Software Development' },
      { text: 'Management' },
      { text: 'Marketing' },
    ],
  };

  // Pipeline inputs (7 inputs with file upload)
  pipelineInputs: InputField[] = [
    {
      label: 'Upload File',
      placeholder: 'File.zip',
      allowedFormats: this.allowedFormats,
      type: 'file',
    },
  ];

  // Tool inputs (2 inputs for tools)
  toolInputs: InputField[] = []
  //   { label: 'Input 1', placeholder: 'Sample Input KB', type: 'text' },
  //   {
  //     label: 'Input 2',
  //     placeholder: 'A KB that has sample input',
  //     type: 'text',
  //   },
  // ];
  //agent inputs
  agentInputs: InputField[] = [
    {
      label: 'Upload File',
      placeholder: 'Sample Input KB',
      value: '',
      type: 'file',
    },
  ];
  skeletonConfigs: any[] = [
    {
      width: '100%',
      height: '20px',
      shape: 'rectangle' as const,
      animation: 'wave' as const,
      description: 'Default skeleton for text content',
    },
    {
      width: '80%',
      height: '16px',
      shape: 'rectangle' as const,
      animation: 'wave' as const,
      description: 'Shorter text line',
    },
    {
      width: '60%',
      height: '16px',
      shape: 'rectangle' as const,
      animation: 'wave' as const,
      description: 'Even shorter text line',
    },
  ];

  // Guardrails specific metadata
  guardrailsMetadata = {
    fields: [
      { label: 'Realm Assigned', value: 'Ascendion' },
      { label: 'Business domain', value: 'Marketing' },
      { label: 'Created by', value: 'Michael Scott' },
      { label: 'Created on', value: '12 May 2024' },
    ],
    tags: [
      { text: 'Software Development' },
      { text: 'Management' },
      { text: 'Marketing' },
    ],
  };

  guardrailInputs: InputField[] = [
    {
      label: 'Enter Prompt',
      placeholder: 'Sample Input Text',
      value: '',
      type: 'text',
    },
  ];

  get isPipelinePlayground(): boolean {
    return this.playgroundType === 'pipeline';
  }

  get isToolPlayground(): boolean {
    return this.playgroundType === 'tool';
  }

  get isAgentPlayground(): boolean {
    return this.playgroundType === 'agent';
  }

  get isGuardrailPlayground(): boolean {
    return this.playgroundType === 'guardrail';
  }

  private getInputType(paramType: string): string {
    switch (paramType) {
      case 'number':
        return 'text'; // Keep as text since InputField only supports 'text' | 'file'
      case 'boolean':
        return 'text'; // Keep as text since InputField only supports 'text' | 'file'
      default:
        return 'text';
    }
  }

  private getPlaceholderForType(paramType: string): string {
    switch (paramType) {
      case 'string':
        return 'Enter text value';
      case 'number':
        return 'Enter numeric value (e.g., 123)';
      case 'boolean':
        return 'Enter true or false';
      case 'array':
        return 'Enter array values (JSON format)';
      case 'object':
        return 'Enter object (JSON format)';
      default:
        return `Enter ${paramType} value`;
    }
  }

  pipelineLoaderText = signal<string>('');
  pipelineLogsLoader = signal<boolean>(false);
  pipelineResultLoader = signal<boolean>(false);
  showPipelineLoader = computed(() => 
  this.pipelineLogsLoader() || this.pipelineResultLoader()
);

  // get currentInputs(): InputField[] {
  //   if (this.isPipelinePlayground) return this.pipelineInputs;
  //   if (this.isToolPlayground) return this.toolInputs;
  //   if (this.isAgentPlayground) return this.agentInputs;
  //   return [];
  // }
  // get currentDetails() {
  //   switch (this.playgroundType) {
  //     case 'agent':
  //       return this.agentDetails;
  //     case 'pipeline':
  //       return this.pipelineDetails;
  //       // case 'tool':
  //       //   return this.toolDetails;
  //       // default:
  //       return null;
  //   }
  // }

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private playgroundService: PlaygroundService,
    private tokenStorage: TokenStorageService,
    private agentService: AgentService,
    private workflowService: WorkflowService,
    private guardrailService: GuardrailService,
    private dialogService: AavaDialogService,
    private approvalService: ApprovalService,
    private location: Location,
    private toolService: ToolService,
    private dropdownStore: DropdownDataStore,
    private fb: FormBuilder,
    // private toastService: AavaToastService,
    private toastWrapper: ToastWrapperService
  ) {
    // Remove the navigation state handling from constructor
    // It's now handled in ngOnInit()
  }

  ngOnInit() {
    this.isDataLoading = true;
    this.executionId = crypto.randomUUID();
    this.detectPlaygroundType();
    this.loadNavigationState();
    this.route.queryParams.subscribe(params => {
      this.loadQueryParams(params);
      this.loadDetailsIfIdPresent();
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();

    // Disconnect WebSocket if connected
    if (this.isPipelinePlayground) {
      this.workflowService.workflowLogDisconnect();
    }
  }
  private loadDetailsIfIdPresent(): void {
    const hasId =
      this.agentId !== null ||
      this.pipelineId !== null ||
      this.signalToolId() !== null || this.guardrailId !== null;
    if (hasId) {
      this.loadDetails();
    } else {
      // No ID present, set status loading to false immediately
      this.isDataLoading = false;
    }
  }
  private detectPlaygroundType() {
    const path = window.location.href.split('?')[0];
    const parts = path.split('/').filter(Boolean);

    if (parts.includes('agent')) this.playgroundType = 'agent';
    else if (parts.includes('tools')) this.playgroundType = 'tool';
    else if (parts.includes('pipelines')) this.playgroundType = 'pipeline';
    else if (parts.includes('guardrails')) this.playgroundType = 'guardrail';
    else this.playgroundType = 'agent'; // fallback default
  }
  private loadQueryParams(params: any) {
    const id = params['id'] ? Number(params['id']) : null;
    const inReview = params['inReview'] === 'true';

    // Set review mode
    this.isReviewMode = inReview;

    if (!id) return;

    switch (this.playgroundType) {
      case 'agent':
        this.agentId = id;
        break;
      case 'pipeline':
        this.pipelineId = id;
        break;
      case 'tool':
        this.signalToolId.set(id?.toString() || null);
        this.toolId = id; // Also set regular toolId for approval operations
        break;
      case 'guardrail':
        this.guardrailId = id;
        break;
    }
  }
  private loadNavigationState() {
    const navigation = this.router.getCurrentNavigation();
    if (!navigation?.extras.state) return;

    if (navigation.extras.state['toolData']) {
      this.playgroundType = 'tool';
      const toolData = navigation.extras.state['toolData'];

      // Set both signal and regular toolId if available
      if (toolData.id) {
        this.signalToolId.set(toolData.id.toString());
        this.toolId = toolData.id;
      }

      this.apiData = {
        agentName: toolData.name || this.apiData.agentName,
        description: toolData.description || this.apiData.description,
      };

      this.toolMetadata = {
        fields: [
          { label: 'Tool Name', value: toolData.name || 'N/A' },
          { label: 'Tool Class', value: toolData.tool_class_name || 'N/A' },
          { label: 'Created by', value: 'Current User' },
          { label: 'Created on', value: new Date().toLocaleDateString() },
        ],
        tags: [
          { text: 'Software Development' },
          { text: 'Automation' },
          { text: 'Custom Tool' },
        ],
      };
    }

    if (navigation.extras.state['agentData']) {
      this.playgroundType = 'agent';
      this.agentData = navigation.extras.state['agentData'];

      this.apiData = {
        agentName: this.agentData.agent.name || this.apiData.agentName,
        description:
          this.agentData.behaviour.description || this.apiData.description,
      };
    }
  }
  private loadGuardrailData(id: number): Observable<any> {
    if(!id) return of(null);

    return this.guardrailService.getGuardrailDetailsById(id).pipe(map((res) => {
      console.log("reshere==>", res);
      this.guardrailsMetadata = this.buildGuardrailMetadata(res?.guardrail);
      this.artifactStatus.set(res?.guardrail?.status || 'DRAFTED');
      this.guardrailData.set(res?.guardrail);
      this.currentDetails = res?.guardrail;
      this.apiData = {
        agentName: res?.guardrail?.name,
        description: res?.guardrail?.description,
      };
      return res;
    }),
    catchError(err => {
      console.error('Failed to guardrail tool:', err);

      // wrap the toast Promise as an Observable
      return from(
        this.toastWrapper.error('Failed to load guardrail data. Please try again.')
      ).pipe(
        switchMap(() => of(null)) // continue with null so the stream doesn't break
      );
    }))
  }
  
  private loadDetails() {
    const apiMap: Record<string, (id: number) => Observable<any>> = {
      agent: id => this.agentService.getById(id),
      pipeline: id => this.workflowService.getById(id),
      tool: id => this.loadToolData(id).pipe(
        map(tool => ({ userToolDetail: tool })) 
      ),
      guardrail: id => this.loadGuardrailData(id),
    };

    const idMap: Record<string, number | null> = {
      agent: this.agentId,
      pipeline: this.pipelineId,
      tool: this.toolId,
      guardrail: this.guardrailId,
    };

    const responseKeyMap: Record<string, string> = {
      agent: 'agentDetail',
      pipeline: 'workFlowDetail',
      tool: 'userToolDetail',
      guardrail: 'guardrail',
    };

    const apiFn = apiMap[this.playgroundType];
    const id = idMap[this.playgroundType];
    const key = responseKeyMap[this.playgroundType];

    if (!apiFn || !id || !key) return;

    // Show blueprint loading for agents and pipelines
    if (this.isAgentPlayground || this.isPipelinePlayground || this.isGuardrailPlayground) {
      this.isBlueprintLoading.set(true);
    }

    apiFn(id).subscribe({
      next: (response: any) => {
        const details = response?.[key] ?? null;
        this.currentDetails = details;
        console.log("current details: ", this.currentDetails);

        // Extract and set artifact status
        if (this.isAgentPlayground) {
          this.artifactStatus.set(details?.status || 'DRAFTED');
          // single description extraction
          this.currentInputs = this.extractInputs(details, this.playgroundType);
        } else if (this.isPipelinePlayground) {
          this.artifactStatus.set(details?.status || 'DRAFTED');
          // multiple agent descriptions
          const agents = details?.workflowAgents || [];
          this.currentInputs = this.extractInputs(agents, this.playgroundType);
          console.log('this.currentInputs==>', this.currentInputs);
        } else if (this.isToolPlayground) {
          this.artifactStatus.set(details?.status || 'DRAFTED');
          // For tools, use dynamicToolInputs when available, otherwise use default toolInputs
          this.currentInputs =
            this.dynamicToolInputs().length > 0
              ? this.dynamicToolInputs()
              : this.toolInputs;
        } else if (this.isGuardrailPlayground) {
          this.artifactStatus.set(details?.status || 'DRAFTED');
          this.currentInputs = this.guardrailInputs;
        }

        // Status is now loaded

        this.isDataLoading = false;

        // Hide blueprint loading for agents and pipelines
        if (this.isAgentPlayground || this.isPipelinePlayground || this.isGuardrailPlayground) {
          this.isBlueprintLoading.set(false);
        }
      },
      error: err => {
        console.error('Error while fetching details:', err);
        this.isDataLoading = false;

        // Hide blueprint loading even on error
        if (this.isAgentPlayground || this.isPipelinePlayground || this.isGuardrailPlayground) {
          this.isBlueprintLoading.set(false);
        }
        // optionally show a toast / snackbar
      },
      complete: () => {
        console.log('API call completed.');
      },
    });
  }

  private extractInputs(data: any, type: string): InputField[] {
    const PLACEHOLDER_PATTERNS = /(%\d+\$s)|\{\{([a-zA-Z0-9-_]+)\}\}/g;

    // Map placeholder -> Set of agent names
    const placeholderMap: { [placeholder: string]: Set<string> } = {};

    // Normalize to array (works for single object or array of objects)
    const agentsArray = Array.isArray(data) ? data : [data];

    agentsArray.forEach(agentItem => {
      const agentName = agentItem?.agent?.name || agentItem?.name || '';
      const agentDescription =
        agentItem?.agent?.task?.description || agentItem?.description;

      if (!agentDescription) return;

      const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS);
      for (const match of matches) {
        const placeholder = match[1] || match[2];
        if (!placeholderMap[placeholder]) {
          placeholderMap[placeholder] = new Set();
        }
        if (agentName) placeholderMap[placeholder].add(agentName);
      }
    });

    // Convert map to InputField[]
    let extractedInputs: InputField[] = Object.entries(placeholderMap).map(
      ([placeholder, agentSet]) => {
        const cleanPlaceholder = placeholder.replace(/[{}]/g, '').trim();
        const agentList = [...agentSet].join(', ');
        return {
          label: `Input for {{${cleanPlaceholder}}} in ${agentList || 'Unknown Agent'}`,
          placeholder: cleanPlaceholder,
          value: '',
          type: 'text', // extracted ones are always text unless we detect otherwise
        };
      }
    );

    // Fallback to defaults if no placeholders found
    if (extractedInputs.length === 0) {
      if (type === 'agent') extractedInputs = this.agentInputs;
      if (type === 'pipeline') extractedInputs = this.pipelineInputs;
      if (type === 'guardrail') extractedInputs = this.guardrailInputs;
    }

    // Ensure at least one file input at the top
    const hasFile = extractedInputs.some(input => input.type === 'file');
    if (!hasFile) {
      extractedInputs.unshift({
        label: 'Input 1',
        placeholder: 'File.zip',
        value: '',
        type: 'file',
      });
    }

    // Reorder so that all file inputs are on top
    const fileInputs = extractedInputs.filter(input => input.type === 'file');
    const otherInputs = extractedInputs.filter(input => input.type !== 'file');
    return [...fileInputs, ...otherInputs];
  }

  // Format placeholder into readable string
  private formatInputName(placeholder: string, index: number): string {
    if (!placeholder) return `Input ${index}`;
    let name = placeholder.replace(/[{}%$\d+s]/g, '').trim();
    if (!name) return `Input ${index}`;
    name = name.replace(/([a-z])([A-Z])/g, '$1 $2');
    return name.charAt(0).toUpperCase() + name.slice(1);
  }

  onFileUploaded(event: { index: number; file: File }): void {
    //console.log('File uploaded:', event.file.name, 'at index:', event.index);
  }

  onTryClicked(formInputs: InputField[]): void {
    this.hasTriedExecution = true;
    this.isExecutionLoading = true;
    this.pipelineLogsLoader.set(true);
    this.pipelineResultLoader.set(true);
    this.setActiveTab('output');
    if (this.isToolPlayground && this.dynamicForm()) {
      this.validateAndTestTool();
      return;
    }

    if (this.isAgentPlayground) {
      this.executeAgent(formInputs);
    } else if (this.isPipelinePlayground) {
      // this.executePipeline(formInputs);
      this.onPipelineExecute(formInputs);
    } else if (this.isGuardrailPlayground) {
      this.executeGuardrail(formInputs);
    }
    // else if (this.isToolPlayground) {
    //   this.executeTool(formInputs);
    // }
  }
  
  onPipelineExecute(formInputs: InputField[] ) {
    console.log(formInputs)
    const files: File[] = [];
    formInputs.forEach(input => {
      if (input.type === 'file' && input.uploadedFiles?.length) {
        files.push(...input.uploadedFiles);
      }
    });

    const payload = {
      pipeLineId: this.pipelineId,
      userInputs: this.mapInputs(formInputs),
      user: this.tokenStorage.getCookie('da_username'),
    };

    // // Clear previous logs and start WebSocket connection
    this.workflowLogs = [];

    this.playgroundService
      .pipelineExecution(payload, files)
      .subscribe({
        next: (res: any) => {
          this.executionId = res.workflowExecutionId;
          this.getPipelineStatus(this.executionId);
        },
        error: (err) => {
          console.error('Pipeline execution failed:', err);
          this.finalOutput = 'Pipeline execution failed. Please try again';

          // Add error log
          this.workflowLogs.push({
            content: `Pipeline execution failed`,
            color: 'red',
            timestamp: new Date(),
          });
          this.pipelineLogsLoader.set(true);
          this.pipelineResultLoader.set(true);
        },
      });
  }

  getPipelineStatus(id: string) {
    this.playgroundService.fetchPipelineExecutionStatus(id).subscribe({
      next: (res: any) => {
        const status = res?.workflowExecutionResponseList[0]?.status;
        switch(status) {
          case 'QUEUED':
          this.pipelineLoaderText.set('Your pipeline execution is queued and will start shortly.');
          break;
          case 'IN_PROGRESS':
          this.pipelineLoaderText.set('Your pipeline execution is now in progress');
          // this.pipelineLogsLoader.set(false); //will enable the code after implemnet ASCII expreseeion parse
          this.getPipelineLogs(id);
          break;
          case 'SUCCESS':
          this.pipelineLoaderText.set('');
          this.getPipelineLogs(id);
          this.getPipelineResult(id);
          break;
          case 'FAILED':
          this.pipelineResultLoader.set(false);
          this.pipelineLogsLoader.set(false)
          this.pipelineLoaderText.set('');
          this.finalOutput = 'Pipeline execution failed. Please try again';

          // Add error log
          this.workflowLogs.push({
            content: `Pipeline execution failed`,
            color: 'red',
            timestamp: new Date(),
          });
          break;
        }
      },
      error: (e) => console.error(e)
    })
  }

  getPipelineLogs(id: string) {
    this.playgroundService.fetchPipelineExecutionLogs(id).subscribe({
      next: ({workflowExecutionLogs}) => {
        workflowExecutionLogs.forEach((logs: any) => {
          try {
            const { content, color } = JSON.parse(logs?.logs);
            if(color) {
              this.pipelineLogsLoader.set(false);
              this.workflowLogs.push({content, color});
            }
          } catch(error) {
            console.error("unable to parse the logs", error)
            return;
          }
        })
      },
      error: (e) => console.error(e),
      complete: () => this.pipelineLogsLoader.set(false)
    })
  }

  getPipelineResult(id: string) {
    this.playgroundService.fetchPipelineExecutionResult(id).subscribe({
      next: ({result}) => {
        try {
          const { pipeLineAgents, output, tasksOutputs } = JSON.parse(result.response);
          this.finalOutput = {
            pipeline: {
              pipeLineAgents: {
                output,
                tasksOutputs,
                pipeLineAgents
              }
            }
          }
        } catch(error) {
          console.error("unable to parse the logs", error)
          return;
        }
      },
      error: (e) => console.error(e),
      complete: () => this.pipelineResultLoader.set(false)
    })
  }

  
  private executeGuardrail(formInputs: InputField[]): void {
    const prompt = formInputs[0].value;
    console.log('Prompt:', prompt);
    console.log('Guardrails:', this.currentDetails);
    const payload = {
      prompt: prompt,
      mode: "DEFAULT",
      colangContent: this.currentDetails?.content || '',
      yamlContent: this.currentDetails?.yamlContent || '',
      promptOverride: true,
      userSignature: this.tokenStorage.getCookie('da_username'),
    };
    console.log("Payload: ", payload);
    this.playgroundService.submitGuardrailExecution(payload).subscribe({
      next: (res) => {
        console.log("res==>", res);
        this.guardRailsTestResults.set(res?.response);
        this.isExecutionLoading = false;
      },
      error: (err) => {
        console.error('Failed to execute guardrail:', err);
        this.isExecutionLoading = false;
      }
    });
  }

  private async validateAndTestTool(): Promise<void> {
    const form = this.dynamicForm();
    const toolId = this.signalToolId();

    if (!form) {
      await this.toastWrapper.error('No input parameters available for testing.');

      this.isExecutionLoading = false;
      return;
    }

    if (!toolId) {
      await this.toastWrapper.error('Tool ID not found. Cannot test tool.');

      this.isExecutionLoading = false;
      return;
    }

    // Get current input values and validate them
    const currentInputs = this.currentInputs;
    const inputValues: { [key: string]: any } = {};
    const validationErrors: string[] = [];

    currentInputs.forEach((input, index) => {
      const paramName = input.label;
      const paramType = Object.entries(this.toolParameters())[index]?.[1];
      const inputValue = input.value?.trim();

      if (!inputValue) {
        validationErrors.push(`${paramName} is required`);
        return;
      }

      // Validate and convert based on parameter type
      try {
        let processedValue: any = inputValue;

        switch (paramType) {
          case 'number':
            processedValue = Number(inputValue);
            if (isNaN(processedValue)) {
              validationErrors.push(`${paramName} must be a valid number`);
              return;
            }
            break;

          case 'boolean':
            const lowerValue = inputValue.toLowerCase();
            if (!['true', 'false', '1', '0'].includes(lowerValue)) {
              validationErrors.push(
                `${paramName} must be true, false, 1, or 0`
              );
              return;
            }
            processedValue = lowerValue === 'true' || lowerValue === '1';
            break;

          case 'array':
            try {
              processedValue = JSON.parse(inputValue);
              if (!Array.isArray(processedValue)) {
                validationErrors.push(
                  `${paramName} must be a valid JSON array`
                );
                return;
              }
            } catch {
              validationErrors.push(`${paramName} must be a valid JSON array`);
              return;
            }
            break;

          case 'object':
            try {
              processedValue = JSON.parse(inputValue);
              if (
                typeof processedValue !== 'object' ||
                Array.isArray(processedValue)
              ) {
                validationErrors.push(
                  `${paramName} must be a valid JSON object`
                );
                return;
              }
            } catch {
              validationErrors.push(`${paramName} must be a valid JSON object`);
              return;
            }
            break;

          case 'string':
          default:
            // String values are used as-is
            break;
        }

        inputValues[paramName] = processedValue;
      } catch (error) {
        validationErrors.push(
          `${paramName} has invalid format for type ${paramType}`
        );
      }
    });

    // Show validation errors if any
    if (validationErrors.length > 0) {
      await this.toastWrapper.error(`Please fix the following errors:\n\n${validationErrors.join('\n')}`, 'Input Validation Error',);


      this.isExecutionLoading = false;
      return;
    }

    // Update form with validated input values
    Object.entries(inputValues).forEach(([key, value]) => {
      const control = form.get(key);
      if (control) {
        control.setValue(value);
        control.markAsTouched();
      }
    });

    try {
      console.log('Testing tool with validated inputs:', inputValues);
      console.log('Tool ID:', toolId);

      // Get tool data for execute API
      const tool = this.toolData();
      const customConfig = tool?.toolConfigs || tool?.toolConfig;

      if (!customConfig?.tool_class_def || !customConfig?.tool_class_name) {
        throw new Error('Tool class definition or name not found');
      }

      // Call the new execute API
      console.log('Executing tool with inputs format:', {
        class_definition: customConfig.tool_class_def,
        class_name: customConfig.tool_class_name,
        inputs: inputValues,
      });

      const testResponse = await firstValueFrom(
        this.toolService.execute(
          customConfig.tool_class_def,
          customConfig.tool_class_name,
          inputValues
        )
      );

      console.log('Tool execute response:', testResponse);

      // Store test results for display in output tab
      this.toolTestResults.set(testResponse);

      // Reset execution loading state
      this.isExecutionLoading = false;

      // Switch to output tab to show results
    } catch (error: any) {
      console.error('Tool execution failed:', error);

      // Always extract and format error to ensure details are shown
      const errorResponse = {
        error: true,
        status: 'INTERNAL_SERVER_ERROR',
        data: {
          status: error?.error?.data?.status || error?.data?.status || error?.status || 500,
          code: error?.error?.data?.code || error?.data?.code || error?.error?.code || error?.code || 'ERR-5000',
          message: error?.error?.data?.message || error?.data?.message || error?.error?.message || error?.message || 'Tool execution failed',
          details: error?.error?.data?.details || error?.data?.details || error?.error?.details || error?.details || JSON.stringify(error, null, 2),
          requestId: error?.error?.data?.requestId || error?.data?.requestId || error?.error?.requestId || error?.requestId || 'Not available',
          traceId: error?.error?.data?.traceId || error?.data?.traceId || error?.error?.traceId || error?.traceId || 'Not available',
          timestamp: error?.error?.data?.timestamp || error?.data?.timestamp || error?.timestamp || new Date().toISOString(),
        },
      };

      this.toolTestResults.set(errorResponse);
      // Reset execution loading state
      this.isExecutionLoading = false;

      // Switch to output tab to show error
      this.setActiveTab('output');
    }
  }

  private executeAgent(formInputs: InputField[]): void {
    const files: File[] = [];
    formInputs.forEach(input => {
      if (input.type === 'file' && input.uploadedFiles?.length) {
        files.push(...input.uploadedFiles);
      }
    });

    const hasFiles = files.length > 0;

    const payload = {
      agentId: this.agentId,
      userInputs: this.mapInputs(formInputs),
      executionId: this.executionId,
      user: this.tokenStorage.getCookie('da_username'),
    };

    this.playgroundService
      .submitAgentExecution(payload, hasFiles, files)
      .subscribe({
        next: response => {
          //console.log('Agent execution response:', response);
          this.isExecutionLoading = false;
          this.finalOutput = response?.agentResponse?.agent?.output;
        },
        error: err => {
          console.error('Agent execution failed:', err);
          this.finalOutput = 'Agent execution failed. Please try again';
          this.isExecutionLoading = false;
        },
      });
  }
  private executePipeline(formInputs: InputField[]): void {
    const files: File[] = [];
    formInputs.forEach(input => {
      if (input.type === 'file' && input.uploadedFiles?.length) {
        files.push(...input.uploadedFiles);
      }
    });

    const hasFiles = files.length > 0;

    const payload = {
      pipeLineId: this.pipelineId,
      userInputs: this.mapInputs(formInputs),
      executionId: this.executionId,
      user: this.tokenStorage.getCookie('da_username'),
    };

    // Clear previous logs and start WebSocket connection
    this.workflowLogs = [];
    this.getWorkflowLogs(this.executionId);

    this.playgroundService
      .submitPipelineExecution(payload, hasFiles, files)
      .subscribe({
        next: res => {
          //console.log('Pipeline execution response:', res);
          this.isExecutionLoading = false;
          this.finalOutput = res?.workflowResponse;

          // Add completion log
          this.workflowLogs.push({
            content: 'Pipeline execution completed successfully',
            color: '#0F8251',
            timestamp: new Date(),
          });
        },
        error: err => {
          console.error('Pipeline execution failed:', err);
          this.finalOutput = 'Pipeline execution failed. Please try again';

          // Add error log
          this.workflowLogs.push({
            content: `Pipeline execution failed: ${err?.error?.detail || err.message}`,
            color: 'red',
            timestamp: new Date(),
          });

          this.isExecutionLoading = false;
        },
      });
  }

  private executeTool(formInputs: InputField[]) {
    //Create the submitToolExecution service
  }

  /**
   * WebSocket logging method similar to your previous version
   */
  public getWorkflowLogs(executionId: string) {
    console.log(
      'Attempting to connect to WebSocket for executionId:',
      executionId
    );

    try {
      this.workflowService
        .workflowLogConnect(executionId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: message => {
            console.log('WebSocket message received:', message);
            const { content, color } = message;
            if (color) {
              this.workflowLogs.push({ content, color, timestamp: new Date() });
            } else {
              // Default color for messages without color
              this.workflowLogs.push({
                content,
                color: '#6B7280',
                timestamp: new Date(),
              });
            }
          },
          error: err => {
            console.error('WebSocket connection error:', err);
            this.workflowLogs.push({
              content: 'WebSocket connection failed. Using demo logs instead.',
              color: 'red',
              timestamp: new Date(),
            });
          },
          complete: () => {
            console.log('WebSocket connection closed');
            this.workflowLogs.push({
              content: 'Log streaming completed',
              color: '#6B7280',
              timestamp: new Date(),
            });
          },
        });
    } catch (error) {
      console.error('Failed to establish WebSocket connection:', error);
      this.workflowLogs.push({
        content: 'Failed to connect to log streaming service.',
        color: 'red',
        timestamp: new Date(),
      });
    }
  }

  private mapInputs(inputs: InputField[]): any {
    const userInputs: any = {};
    console.log(inputs)
    const parameterPattern = /^%\d+\$s$/;
    inputs.forEach(input => {
      if (input.type !== 'file') {
        const key = parameterPattern.test(input.placeholder) ? input.placeholder : `{{${input.placeholder}}}`;
        userInputs[key] = input.value || '';
      }
    });

    return userInputs;
  }

  setActiveTab(tabId: string): void {
    // Prevent setting execution-logs tab for tools
    if (this.isToolPlayground && tabId === 'execution-logs') {
      //console.log('Execution logs tab not available for tools');
      return;
    }

    console.log('Setting active tab to:', tabId);
    this.activeTab = tabId;
  }

  toggleLeftPanel(): void {
    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;
  }

  getEmptyPanelMessage(): string {
    switch (this.activeTab) {
      case 'blueprint':
        return this.isToolPlayground
          ? 'Tool blueprint content will appear here'
          : 'Blueprint content will appear here';
      case 'execution-logs':
        return this.hasTriedExecution
          ? this.executionLogs.length > 0
            ? 'Execution logs will appear here'
            : 'No execution logs available'
          : "Click on 'Try' to proceed";
      case 'output':
        return this.isToolPlayground
          ? 'Tool output will appear here'
          : 'Output will appear here';
      default:
        return 'Select a tab to view content';
    }
  }

  onSendForApproval(): void {
    let id: string | null = null;

    // Pick the correct ID based on playgroundType
    if (this.isAgentPlayground) {
      id = this.agentId?.toString() ?? null;
    } else if (this.isPipelinePlayground) {
      id = this.pipelineId?.toString() ?? null;
    } else if (this.isToolPlayground) {
      id = this.toolId?.toString() ?? null;
    }
    else if(this.isGuardrailPlayground){
      id = this.guardrailId?.toString() ?? null;
    }

    if (!id) {
      console.error('No valid ID found for approval');
      return;
    }

    // Show approval loading
    this.isApprovalLoading.set(true);

    this.playgroundService.sendForApproval(this.playgroundType, id).subscribe({
      next: async response => {
        // Hide approval loading
        this.isApprovalLoading.set(false);

        this.router.navigate(['/marketplace']);
        if (this.isAgentPlayground && response?.agentId) {
          // Show success toast
          await this.toastWrapper.success('Sent for Approval Successfully!');
        } else if (this.isPipelinePlayground && response?.id) {
          await this.toastWrapper.success('Sent for Approval Successfully!');
        } else if (this.isToolPlayground) {
          await this.toastWrapper.success('Sent for Approval Successfully!');
        } else if(this.isGuardrailPlayground){
          await this.toastWrapper.success(response?.message || 'Sent for Approval Successfully!');
        }
        else if (
          response?.message &&
          !response?.id &&
          !response?.agentId &&
          !this.isToolPlayground
        ) {
          await this.toastWrapper.success('Approval already sent.');
        } else {
          await this.toastWrapper.success('Approval already sent.');
        }
      },
      error: async error => {
        // Hide approval loading
        this.isApprovalLoading.set(false);

        // console.error('Error saving model:', error);
        await this.toastWrapper.error('Failed to send for approval.');
      },
    });
  }

  onExport(): void {
    console.log('Export clicked');
    // Implement export logic
  }

  onShare(): void {
    console.log('Share clicked');
    // Implement share logic
  }

  onSendBack(): void {
    if (
      this.isAgentPlayground ||
      this.isPipelinePlayground ||
      this.isToolPlayground ||
      this.isGuardrailPlayground
    ) {
      this.dialogService
        .openModal(SendbackArtifactModalComponent, {
          width: '440px',
        })
        .then(res => {
          if (res == null || res?.action === 'close') {
            return;
          }

          // Show approval loading
          this.isApprovalLoading.set(true);

          const whatWentGood = res?.whatWentGood;
          const whatWentWrong = res?.whatWentWrong;
          const improvements = res?.improvements;

          if (this.isAgentPlayground) {
            this.approvalService
              .rejectAgents(
                this.agentId,
                whatWentGood,
                whatWentWrong,
                improvements
              )
              .subscribe({
                next: async (res: any) => {
                  // Hide approval loading
                  this.isApprovalLoading.set(false);

                  this.router.navigate(['/approvals']);
                  await this.toastWrapper.success(
                    'Approval Rejected Successfully!'
                  );
                },
                error: async (err: any) => {
                  // Hide approval loading
                  this.isApprovalLoading.set(false);

                  console.error(err);
                  await this.toastWrapper.error('Approval Rejection Failed!');
                },
              });
          } else if (this.isPipelinePlayground) {
            this.approvalService
              .rejectWorkflows(
                this.pipelineId,
                whatWentGood,
                whatWentWrong,
                improvements
              )
              .subscribe({
                next: async (res: any) => {
                  // Hide approval loading
                  this.isApprovalLoading.set(false);

                  this.router.navigate(['/approvals']);
                  await this.toastWrapper.success(
                    'Approval Rejected Successfully!'
                  );
                },
                error: async (err: any) => {
                  // Hide approval loading
                  this.isApprovalLoading.set(false);

                  await this.toastWrapper.error('Approval Rejection Failed!');
                },
              });
          } else if (this.isToolPlayground) {
            this.approvalService
              .rejectTools(
                this.toolId,
                whatWentGood,
                whatWentWrong,
                improvements
              )
              .subscribe({
                next: async (res: any) => {
                  // Hide approval loading
                  this.isApprovalLoading.set(false);

                  this.router.navigate(['/approvals']);
                  await this.toastWrapper.success(
                    'Approval Rejected Successfully!'
                  );
                },
                error: async (err: any) => {
                  // Hide approval loading
                  this.isApprovalLoading.set(false);

                  await this.toastWrapper.error('Approval Rejection Failed!');
                },
              });
          }
          else if (this.isGuardrailPlayground) {
            this.approvalService.rejectGuardrails(this.guardrailId, whatWentGood, whatWentWrong, improvements).subscribe({
              next: async (res: any) => {
                // Hide approval loading
                this.isApprovalLoading.set(false);

                this.router.navigate(['/approvals']);
                await this.toastWrapper.success('Approval Rejected Successfully!');
              },
              error: async (err: any) => {
                // Hide approval loading
                this.isApprovalLoading.set(false);

                await this.toastWrapper.error('Approval Rejection Failed!');
              },
            });
          }
        });
    }
  }

  onApprove(): void {
    this.dialogService.open(ApprovalModalComponent).then(res => {
      console.log(res);

      if (res == null || res?.action === 'close') {
        return;
      }

      // Show approval loading
      this.isApprovalLoading.set(true);

      if (this.isAgentPlayground) {
        this.approvalService.approveAgents(this.agentId, res).subscribe({
          next: async (res: any) => {
            // Hide approval loading
            this.isApprovalLoading.set(false);

            this.router.navigate(['/approvals']);
            await this.toastWrapper.success('Approved Successfully.');
          },
          error: async (err: any) => {
            // Hide approval loading
            this.isApprovalLoading.set(false);

            await this.toastWrapper.error('Approval Failed.');
          },
        });
      } else if (this.isPipelinePlayground) {
        this.approvalService.approveWorkflows(this.pipelineId, res).subscribe({
          next: async (res: any) => {
            // Hide approval loading
            this.isApprovalLoading.set(false);

            this.router.navigate(['/approvals']);
            await this.toastWrapper.success('Approved Successfully.');
          },
          error: async (err: any) => {
            // Hide approval loading
            this.isApprovalLoading.set(false);

            await this.toastWrapper.error('Approval Failed.');
          },
        });
      } else if (this.isToolPlayground) {
        this.approvalService.approveTools(this.toolId, res).subscribe({
          next: async (res: any) => {
            // Hide approval loading
            this.isApprovalLoading.set(false);

            this.router.navigate(['/approvals']);
            await this.toastWrapper.success('Approved Successfully.');
          },
          error: async (err: any) => {
            // Hide approval loading
            this.isApprovalLoading.set(false);

            await this.toastWrapper.error('Approval Failed.');
          },
        });
      }
      else if (this.isGuardrailPlayground) {
        this.approvalService.approveGuardrails(this.guardrailId, res).subscribe({
          next: async (res: any) => {
            // Hide approval loading
            this.isApprovalLoading.set(false);

            this.router.navigate(['/approvals']);
            await this.toastWrapper.success('Approved Successfully.');
          },
          error: async (err: any) => {
            // Hide approval loading
            this.isApprovalLoading.set(false);

            await this.toastWrapper.error('Approval Failed.');
          },
        });
      }
    });
  }

  protected openhandleErrorDialog(title: string, message: string) {
    this.dialogService
      .error({
        title: title,
        message: message,
        size: 'lg',
      })
      .then(() => {
        // redirecting back to approvals logic
      });
  }

  onBackClick(): void {
    this.location.back();
  }

  // Add header configuration
  get leftPanelTitle(): string {
    return '';
  }

  rightPanelTabs: HeaderTab[] = [
    { id: 'blueprint', label: 'Blueprint' },
    { id: 'execution-logs', label: 'Execution Logs' },
    { id: 'output', label: 'Output' },
  ];

  get filteredRightPanelTabs(): HeaderTab[] {
    // console.log(
    //   'Playground type:',
    //   this.playgroundType,
    //   'Is tool playground:',
    //   this.isToolPlayground
    // );

    if (this.isToolPlayground || this.isGuardrailPlayground) {
      // For tools, only show blueprint and output tabs
      return this.rightPanelTabs.filter(
        tab => tab.id === 'blueprint' || tab.id === 'output'
      );
    }
    return this.rightPanelTabs;
  }

  get rightPanelActions(): HeaderAction[] {
    const actions: HeaderAction[] = [];

    // Don't show any buttons while status is loading
    if (this.isDataLoading) {
      return actions;
    }

    // Only show approval buttons if status is NOT APPROVED
    if (this.artifactStatus() !== 'APPROVED') {
      // Review mode - show Send Back and Approve buttons
      if (this.isReviewMode) {
        actions.push(
          {
            id: 'send-back',
            label: 'Send Back',
            iconName: 'arrow-left-from-line',
            iconPosition: 'right',
            clear: true,
          },
          {
            id: 'approve',
            label: 'Approve',
            iconName: 'circle-check',
            iconPosition: 'right',
          }
        );
      } else {
        // Normal mode - show Send for Approval button
        actions.push({
          id: 'send-for-approval',
          label: 'Send for Approval',
          iconName: 'circle-check',
          iconPosition: 'right',
        });
      }
    }

    // Always show Share/Export buttons regardless of status
    if (this.isPipelinePlayground || this.isAgentPlayground) {
      if (this.isPipelinePlayground) {
        actions.push({
          id: 'refresh',
          label: 'Refresh',
          iconName: 'refresh-ccw',
          iconPosition: this.isAgentPlayground ? 'left' : 'right',
          clear: true,
          disabled: !this.showPipelineLoader(),
        });
      }
      actions.push({
        id: 'export',
        label: 'Export',
        iconName: 'upload',
        iconPosition: this.isAgentPlayground ? 'left' : 'right',
        clear: true,
        disabled: true,
      });
    } else if (this.isToolPlayground) {
      actions.push({
        id: 'share',
        label: 'Share',
        iconName: 'upload',
        iconPosition: 'right',
        clear: true,
        disabled: true,
      });
    }

    return actions;
  }

  onHeaderActionClick(actionId: string): void {
    switch (actionId) {
      case 'send-for-approval':
        this.onSendForApproval();
        break;
      case 'export':
        this.onExport();
        break;
      case 'share':
        this.onShare();
        break;
      case 'send-back':
        this.onSendBack();
        break;
      case 'approve':
        this.onApprove();
        break;
      case 'refresh':
        this.getPipelineStatus(this.executionId);
        break;
    }
  }

  /**
   * Load tool data by ID and extract parameters
   */

  private loadToolData(id: number): Observable<any> {
    if (!id) {
      return of(null);
    }

    return this.toolService.getById(id).pipe(
      map((response: any) => {
        const tool = response?.userToolDetail ?? null;

        if (tool) {
          // enrich tool data before returning
          this.toolData.set(tool);
          this.artifactStatus.set(tool?.status || 'DRAFTED');
          this.currentDetails = tool;
          this.apiData = {
            agentName: tool.name,
            description: tool.description,
          };

          // build metadata
          this.toolMetadata = this.buildToolMetadata(tool);

          // optionally extract params
          const customConfig = tool.toolConfigs || tool.toolConfig;
          if (customConfig?.tool_class_def && customConfig?.tool_class_name) {
            this.extractToolParameters(customConfig.tool_class_def);
          }
        }

        return tool;
      }),
      catchError(err => {
        console.error('Failed to load tool:', err);

        // wrap the toast Promise as an Observable
        return from(
          this.toastWrapper.error('Failed to load tool data. Please try again.')
        ).pipe(
          switchMap(() => of(null)) // continue with null so the stream doesn't break
        );
      })
    );
  }

  private buildToolMetadata(tool: any): { fields: { label: string, value: string }[], tags: { text: string }[] } {
    const teamInfo = tool.teamInfo || {};

    // Resolve tag names from IDs
    const tagNames = tool.tags
      ? tool.tags.map((tagId: number) => {
        const tag = this.dropdownStore.getGoodAtTagById(tagId);
        return { text: tag?.name || `Tag ${tagId}` };
      })
      : [];

    // Resolve practice area name
    const practiceAreaName = tool.practiceArea
      ? this.dropdownStore.getPracticeAreaById(tool.practiceArea)?.name || 'Unknown'
      : 'Unknown';

    return {
      fields: [
        { label: 'Org', value: teamInfo.org || 'Unknown' },
        { label: 'Domain', value: teamInfo.domain || 'Unknown' },
        { label: 'Created by', value: tool.createdBy || 'Unknown' },
        { label: 'Practice Area', value: practiceAreaName },
      ],
      tags: tagNames,
    };
  }

  private buildGuardrailMetadata(guardrail: any): { fields: { label: string, value: string }[], tags: { text: string }[] } {
    console.log("guardrail==>", guardrail);
    const teamInfo = guardrail.teamInfo || {};

    // Resolve tag names from IDs
    const tagNames = guardrail.tags
      ? guardrail.tags.map((tagId: number) => {
        const tag = this.dropdownStore.getGoodAtTagById(tagId);
        return { text: tag?.name || `Tag ${tagId}` };
      })
      : [];

    // Resolve practice area name
    const practiceAreaName = guardrail.practiceArea
      ? this.dropdownStore.getPracticeAreaById(guardrail.practiceArea)?.name || 'Unknown'
      : 'Unknown';

    return {
      fields: [
        { label: 'Realm Assigned', value: teamInfo.org || 'Unknown' },
        { label: 'Business domain', value: teamInfo.domain || 'Unknown' },
        { label: 'Created by', value: guardrail.createdBy || 'Unknown' },
        { label: 'Practice Area', value: practiceAreaName },
      ],
      tags: tagNames,
    };
  }

  /**
   * Extract parameters from tool class definition using API
   */
  private async extractToolParameters(codeContent: string): Promise<void> {
    this.isExtractingParameters.set(true);

    try {
      const userSignature = this.getUserSignature();
      const toolData = this.toolData();
      const toolClassName =
        (toolData?.toolConfig as any)?.tool_class_name || 'UNKNOWN_TOOL';

      const compilePayload = {
        prompt: codeContent,
        mode: 'PARAMETER_EXTRACTOR',
        promptOverride: false,
        executionId: this.executionId,
        useCaseIdentifier: 'PARAMETER_EXTRACTOR@ADD@GOPAL@TEST_GOPAL@GOPALTEST',
        userSignature: userSignature,
      };

      console.log('Extracting parameters with payload:', compilePayload);
      const response = await firstValueFrom(
        this.toolService.compile(compilePayload)
      );
      console.log('Parameter extraction response:', response);

      if (response?.response?.choices?.[0]?.text) {
        try {
          const parametersText = response.response.choices[0].text.trim();
          const parameters = JSON.parse(parametersText);
          console.log('Extracted parameters:', parameters);

          this.toolParameters.set(parameters);
          this.createDynamicForm(parameters);

          // Update currentInputs for tools when parameters are extracted
          if (this.isToolPlayground) {
            this.currentInputs = this.dynamicToolInputs();
          }
        } catch (parseError) {
          console.error('Failed to parse parameters JSON:', parseError);
          await this.toastWrapper.error('Failed to parse extracted parameters. The tool definition may be invalid.', 'Parameter Extraction Error');


        }
      } else {
        console.warn('No parameter extraction response received');
        await this.toastWrapper.error('No parameters could be extracted from the tool definition.', 'Parameter Extraction Failed');

      }
    } catch (error) {
      console.error('Failed to extract parameters:', error);
      await this.toastWrapper.error(`Failed to extract parameters: ${error instanceof Error ? error.message : 'Unknown error'}`, 'Parameter Extraction Error');

    } finally {
      this.isExtractingParameters.set(false);
    }
  }

  /**
   * Create dynamic form based on extracted parameters
   */
  private createDynamicForm(parameters: { [key: string]: string }): void {
    const formControls: { [key: string]: any } = {};

    Object.entries(parameters).forEach(([paramName, paramType]) => {
      const validators = [Validators.required];

      // Add type-specific validators
      switch (paramType) {
        case 'number':
          validators.push(this.numberValidator);
          break;
        case 'boolean':
          validators.push(this.booleanValidator);
          break;
        case 'array':
          validators.push(this.arrayValidator);
          break;
        case 'object':
          validators.push(this.objectValidator);
          break;
      }

      formControls[paramName] = [this.getDefaultValue(paramType), validators];
    });

    const form = this.fb.group(formControls);
    this.dynamicForm.set(form);
    console.log('Created dynamic form for tool parameters:', form);
  }

  /**
   * Get default value for parameter type
   */
  private getDefaultValue(type: string): any {
    switch (type) {
      case 'number':
        return ''; // Return empty string for input field
      case 'boolean':
        return ''; // Return empty string for input field
      case 'array':
        return ''; // Return empty string for input field (user will enter JSON)
      case 'object':
        return ''; // Return empty string for input field (user will enter JSON)
      case 'string':
      default:
        return '';
    }
  }

  /**
   * Custom validator for number fields
   */
  private numberValidator(control: any) {
    const value = control.value;
    if (value === null || value === undefined || value === '') {
      return null; // Let required validator handle empty values
    }
    return isNaN(Number(value)) ? { invalidNumber: true } : null;
  }

  /**
   * Custom validator for boolean fields
   */
  private booleanValidator(control: any) {
    const value = control.value;
    if (value === null || value === undefined || value === '') {
      return null;
    }
    const lowerValue = String(value).toLowerCase();
    return ['true', 'false', '1', '0'].includes(lowerValue)
      ? null
      : { invalidBoolean: true };
  }

  /**
   * Custom validator for array fields
   */
  private arrayValidator(control: any) {
    const value = control.value;
    if (value === null || value === undefined || value === '') {
      return null;
    }
    try {
      const parsed = JSON.parse(String(value));
      return Array.isArray(parsed) ? null : { invalidArray: true };
    } catch {
      return { invalidArray: true };
    }
  }

  private objectValidator(control: any) {
    const value = control.value;
    if (value === null || value === undefined || value === '') {
      return null;
    }
    try {
      const parsed = JSON.parse(String(value));
      return typeof parsed === 'object' && !Array.isArray(parsed)
        ? null
        : { invalidObject: true };
    } catch {
      return { invalidObject: true };
    }
  }
  /**
   * Get user signature for API calls
   */
  private getUserSignature(): string {
    const email =
      this.tokenStorage.getCookie('da_username') || '<EMAIL>';
    return email;
  }

  /**
   * Get form input error message
   */
  private getInputError(paramName: string): string {
    const form = this.dynamicForm();
    if (!form) return '';

    const control = form.get(paramName);
    if (!control || !control.errors) return '';

    if (control.errors['required']) {
      return `${paramName} is required`;
    }
    if (control.errors['invalidNumber']) {
      return `${paramName} must be a valid number`;
    }
    if (control.errors['invalidBoolean']) {
      return `${paramName} must be true, false, 1, or 0`;
    }
    if (control.errors['invalidArray']) {
      return `${paramName} must be a valid JSON array`;
    }
    if (control.errors['invalidObject']) {
      return `${paramName} must be a valid JSON object`;
    }

    return 'Invalid input';
  }
}
