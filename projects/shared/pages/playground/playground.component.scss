.playground-layout {
  height: 100vh;
  background: #f0f4f8;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden;
}

.playground-content {
  display: grid;
  grid-template-columns: 350px 1fr;
  height: 100%;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  transition: grid-template-columns 0.3s ease;

  &:has(.left-panel.collapsed) {
    grid-template-columns: 48px 1fr;
  }
}

.left-panel {
  background: white;
  border-right: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;
  display: grid;
  grid-template-rows: auto 1fr;

  &.collapsed {
    .left-panel-content {
      display: none;
    }

    .left-panel-header {
      .panel-title-section {
        display: none;
      }

      justify-content: center;
    }
  }
}

.left-panel-header,
.right-panel-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  min-height: 56px;
  height: 56px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.left-panel-header {
  justify-content: space-between;
  border-right: 1px solid #e2e8f0;

  .panel-title-section {
    display: flex;
    align-items: center;
    gap: 8px;

    .panel-title {
      font-size: 14px;
      font-weight: 600;
      color: #1e293b;
      margin: 0 4px;
    }
  }

  .panel-toggle-btn {
    background: none;
    border: none;
    padding: 6px;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
    color: #64748b;

    &:hover {
      background: #e2e8f0;
      color: #1e293b;
    }
  }
}

.left-panel-content {
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.right-panel {
  background: #f8fafc;
  display: grid;
  grid-template-rows: auto 1fr;
  overflow: hidden;
}

.right-panel-header {
  justify-content: space-between;
  padding: 12px 20px;

  .header-tabs {
    display: flex;
    gap: 4px;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.right-panel-content {
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.empty-panel {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #64748b;
  font-size: 16px;
}

.skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

// Responsive breakpoints
@media (max-width: 1200px) {
  .playground-content {
    grid-template-columns: 320px 1fr;

    &:has(.left-panel.collapsed) {
      grid-template-columns: 48px 1fr;
    }
  }
}

@media (max-width: 768px) {
  .playground-layout {
    padding: 8px;
  }

  .playground-content {
    grid-template-columns: 280px 1fr;

    &:has(.left-panel.collapsed) {
      grid-template-columns: 40px 1fr;
    }
  }

  .right-panel-header {
    .header-tabs {
      gap: 2px;
    }

    .header-actions {
      gap: 4px;
    }
  }
}

@media (max-width: 640px) {
  .playground-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;

    &:has(.left-panel.collapsed) {
      grid-template-rows: 40px 1fr;
    }
  }

  .left-panel {
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
    max-height: 300px;

    &.collapsed {
      max-height: 40px;
    }
  }
}
