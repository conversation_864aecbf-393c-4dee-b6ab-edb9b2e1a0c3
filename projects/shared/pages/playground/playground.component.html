<div class="playground-layout">
  <!--  -->
  <!-- Main Content with integrated headers -->
  <div class="playground-content">
    <!-- Left Panel -->
    <aside class="left-panel" [class.collapsed]="isLeftPanelCollapsed">
      <!-- Left Panel Header -->
      <app-playground-header
        [title]="leftPanelTitle"
        [showBackIcon]="true"
        [showToggleIcon]="true"
        toggleIconName="panel-left-close"
        (backClicked)="onBackClick()"
        (toggleClicked)="toggleLeftPanel()"
      >
      </app-playground-header>
 
      <!-- Left Panel Content -->
      <div class="left-panel-content">
        @if (isBlueprintLoading() || isApprovalLoading()) {
          <div class="skeleton-container">
            <aava-skeleton height="120px" width="100%"></aava-skeleton>
            <aava-skeleton height="200px" width="100%"></aava-skeleton>
          </div>
        } @else if(isGuardrailPlayground){
          <app-artifact-meta-info [isLoadingData]="isDataLoading" [agentName]="currentDetails?.name"
            [description]="currentDetails?.description" [showMetadata]="isGuardrailPlayground" [metadataFields]="guardrailsMetadata.fields"
            [skillTags]="guardrailsMetadata.tags">
          </app-artifact-meta-info>
          <app-artifact-inputs [inputs]="currentInputs" [isLoadingInputs]="isExtractingParameters()"
            (tryClicked)="onTryClicked($event)">
          </app-artifact-inputs>
        } @else {
            <app-artifact-meta-info
              [isLoadingData]="isDataLoading"
              [agentName]="currentDetails?.name"
              [description]="currentDetails?.description"
              [showMetadata]="isToolPlayground"
              [metadataFields]="toolMetadata.fields"
              [skillTags]="toolMetadata.tags"
            >
            </app-artifact-meta-info>
            <app-artifact-inputs
              [inputs]="currentInputs"
              [isLoadingInputs]="isExtractingParameters()"
              (tryClicked)="onTryClicked($event)"
            >
            </app-artifact-inputs>
        }
      </div>
    </aside>
 
    <!-- Right Panel -->
    <main class="right-panel">
      <!-- Right Panel Header -->
      <app-playground-header
        [playgroundType]="playgroundType"
        [tabs]="filteredRightPanelTabs"
        [actions]="rightPanelActions"
        [activeTab]="activeTab"
        (tabClicked)="setActiveTab($event)"
        (actionClicked)="onHeaderActionClick($event)"
      >
      </app-playground-header>
 
      <!-- Right Panel Content -->
      <div class="right-panel-content">
        @if (isBlueprintLoading() || isApprovalLoading()) {
          <div class="skeleton-container">
            <aava-skeleton height="400px" width="100%"></aava-skeleton>
          </div>
        } @else {
          <app-blueprint-widget
            *ngIf="activeTab === 'blueprint'"
            [playgroundType]="playgroundType"
            [agentId]="agentId?.toString()"
            [executeMode]="isAgentPlayground"
            [toolData]="toolData()"
            [guardrailData]="guardrailData()"
            [workflowAgents]="currentDetails?.workflowAgents || []"
          >
          </app-blueprint-widget>
 
          <app-output-widget
            *ngIf="activeTab === 'output'"
            [playgroundType]="playgroundType"
            [outputData]="finalOutput"
            [toolTestResults]="toolTestResults()"
            [guardrailsTestResults]="guardRailsTestResults()"
            [isDataLoading]="isPipelinePlayground ? pipelineResultLoader() : isExecutionLoading"
            [loaderText]="pipelineLoaderText()"
          ></app-output-widget>
 
          <app-log-viewer
            *ngIf="activeTab === 'execution-logs'"
            [playgroundType]="playgroundType"
            [logs]="executionLogs"
            [coloredLogs]="workflowLogs"
            [isLoading]="isPipelinePlayground ? pipelineLogsLoader() : isExecutionLoading"
            [loaderText]="pipelineLoaderText()"
          >
          </app-log-viewer>
        }
      </div>
    </main>
  </div>
</div>