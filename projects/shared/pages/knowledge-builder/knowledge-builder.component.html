<!-- Original form-based layout - commented out -->
<!--
<app-libraries-main-layout [isThirdColumnVisible]="false">
    <app-agent-builder-header
        [isApprovalValid]="true"
        [isFormValid]="!isSubmitDisabled()"
        [isPreviewValid]="!isPreviewDisabled()"
        (saveClicked)="onConfirmSave()"
        (previewClicked)="onPreviewChunk()"
    >
    </app-agent-builder-header>

    <main>
        <form [formGroup]="kbForm" class="kb__form d-flex p-4 g-4 col align-items-md-start">
            <app-matadata-widget class="metadata__widget"
                (fileTypeValueChange)="onDropdownValueChange($event)"
                (goodAtValueChange)="onGoodAtValueChange($event)"
                [kbForm]="kbForm"
            ></app-matadata-widget>

            <div class="content-area">
                <section class="configuration">
                    <app-configuration-widget [kbForm]="kbForm" [selectedConfigType]="selectedConfigType()" [selectedFiles]="selectedFiles()" [isDisabled]="isDisabled()" [isLoading]="isConfigurationLoading()" (fileChange)="fileChange($event)" class="metadata-widget"></app-configuration-widget>
                </section>
                <app-file-retriver-widget [kbForm]="kbForm" [isDisabled]="isDisabled()" [isLoading]="isFileRetrieverLoading()"></app-file-retriver-widget>
            </div>
        </form>
    </main>
</app-libraries-main-layout>
-->

<!-- New layout following guard rails pattern -->
<app-libraries-main-layout [isThirdColumnVisible]="false">
  <app-agent-builder-header
    header
    [isFormValid]="!isSubmitDisabled()"
    [isPreviewValid]="!isPreviewDisabled()"
    [isApprovalValid]="isDisabled()"
    [isDisabled]="isDisabled()"
    [isReviewMode]="isReviewMode()"
    (saveClicked)="onSave()"
    (previewClicked)="onPreviewChunk()"
    (approveClicked)="onApprove()"
    (sendBackClicked)="onSendBack()"
    (reviewApproveClicked)="onReviewApprove()"
  >
  </app-agent-builder-header>

  <app-knowledge-meta
    firstRow
    [data]="metadataData()"
    [kbForm]="kbForm"
    [isDisabled]="isDisabled()"
    [loading]="isMetadataLoading()"
    (fileTypeValueChange)="onDropdownValueChange($event)"
    (goodAtValueChange)="onGoodAtValueChange($event)"
  >
  </app-knowledge-meta>

  <div secondRow class="knowledge-content-area">
    <app-configuration-widget
      [kbForm]="kbForm"
      [isDisabled]="isDisabled()"
      [isLoading]="isConfigurationLoading()"
      [selectedConfigType]="selectedConfigType()"
      [selectedFiles]="selectedFiles()"
      (fileChange)="fileChange($event)"
      class="configuration-section"
    ></app-configuration-widget>

    <app-file-retriver-widget
      [kbForm]="kbForm"
      [isDisabled]="isDisabled()"
      [isLoading]="isFileRetrieverLoading()"
      class="file-retriever-section"
    ></app-file-retriver-widget>
  </div>
</app-libraries-main-layout>
