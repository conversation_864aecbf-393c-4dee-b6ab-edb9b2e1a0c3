import {
  AavaDefaultCardComponent,
  AavaDialogService,
  AavaIconComponent,
  AavaSelectComponent,
  AavaSelectOptionComponent,
  AavaSkeletonComponent,
  AavaTagComponent,
  AavaTextboxComponent,
} from '@aava/play-core';
import { Component, inject, OnInit, signal, computed } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
// import { MetadataWidgetComponent } from './widgets/metadata-widget/metadata-widget.component'; // Commented out - using new KnowledgeMetaComponent
import { KnowledgeBuilderHeaderComponent } from './components/knowledge-builder-header/knowledge-builder-header.component';
import { ConfigurationWidgetComponent } from './widgets/configuration-widget/configuration-widget.component';
import { FileRetriverWidgetComponent } from './widgets/file-retriver-widget/file-retriver-widget.component';
import {
  KnowledgeMetaComponent,
  KnowledgeMetaData,
} from './widgets/knowledge-meta/knowledge-meta.component';
import { configInputFieldConfig } from './constant/knowledge-form-config';
import { Router, ActivatedRoute } from '@angular/router';
import { LibrariesMainLayoutComponent } from 'projects/shared/components/libraries-main-layout/libraries-main-layout.component';
import { KnowledgeService } from 'projects/shared/services/knowledge.service';
import { KnowledgeBaseService } from 'projects/shared/services/knowledge-base.service';
import { ApprovalService } from 'projects/shared/services/approval.service';
import { TeamIdService } from 'projects/shared/services/team-id.service';
import { PreviewChunkRendererComponent } from './renderer/preview-chunk-renderer/preview-chunk-renderer.component';
import { ToastWrapperService } from 'projects/shared/services/toast-wrapper.service';
import { ApprovalModalComponent } from 'projects/shared/components/approve-modal/approval-approval-modal.component';
import { SendbackModalComponent } from 'projects/shared/components/simple-sendback-modal/approval-sendback-modal.component';
// import { TextboxComponent } from "@aava/play-comp-library";

@Component({
  selector: 'app-knowledge-builder',
  standalone: true,
  templateUrl: './knowledge-builder.component.html',
  styleUrls: ['./knowledge-builder.component.scss'],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    KnowledgeBuilderHeaderComponent,
    KnowledgeMetaComponent,
    ConfigurationWidgetComponent,
    FileRetriverWidgetComponent,
    LibrariesMainLayoutComponent,
  ],
})
export class KnowledgeBuilderComponent implements OnInit {
  kbForm!: FormGroup;
  selectedConfigType = signal<string>('Upload Files');
  uploadedFiles = signal<File[]>([]);
  selectedFiles = signal<any[]>([]); // For displaying files in review mode
  isDisabled = signal<boolean>(false);
  selecteedGoodAtIds = signal<number[]>([]);
  knowledgeId = signal<string>('');
  isReviewMode = signal<boolean>(false); // Set to true when inReview=true in query params

  // Loading states for skeleton loaders
  isConfigurationLoading = signal<boolean>(false);
  isFileRetrieverLoading = signal<boolean>(false);
  isMetadataLoading = signal<boolean>(false);

  // Computed property for metadata data
  readonly metadataData = computed<KnowledgeMetaData>(() => {
    if (!this.kbForm) {
      return {
        name: '',
        description: '',
        retriever: 'Default',
        uploadType: 'Upload Files',
        practiceArea: '',
        goodAtTags: [],
      };
    }
    return {
      name: this.kbForm.get('name')?.value || '',
      description: this.kbForm.get('description')?.value || '',
      retriever: this.kbForm.get('retriever')?.value || 'Default',
      uploadType: this.kbForm.get('uploadType')?.value || 'Upload Files',
      practiceArea: this.kbForm.get('practiceArea')?.value || '',
      goodAtTags: this.kbForm.get('goodAtTags')?.value || [],
    };
  });

  // API path suffix map based on upload source
  private pathSuffix: Record<string, string> = {
    'Azure Blob': '/blob',
    GitHub: '/github',
    'Share Point': '/sharepoint',
    'Confluence Wiki': '/confluence',
    Database: '/database',
  };

  private formBuilder = inject(FormBuilder);
  private dialogService = inject(AavaDialogService);
  private knowledgeService = inject(KnowledgeService);
  private knowledgeBaseService = inject(KnowledgeBaseService);
  private approvalService = inject(ApprovalService);
  private teamIdService = inject(TeamIdService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private toastWrapper = inject(ToastWrapperService);

  ngOnInit(): void {
    // Initialize the form first
    this.kbForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', Validators.required],
      retriever: ['Default'],
      splitSize: [5000],
      parentSplitSize: [5000],
      childSplitSize: [2000],
      embeddingModel: [null, Validators.required],
      uploadType: ['Upload Files', Validators.required],
      containerName: [''],
      accountName: [''],
      accountKey: [''],
      githubKey: [''],
      githubAccount: [''],
      githubRepo: [''],
      githubBranch: [''],
      tenantId: [''],
      sharepointSiteName: [''],
      sharepointFolderPath: [''],
      email: [''],
      clientId: [''],
      clientSecret: [''],
      apiToken: [''],
      baseUrl: [''],
      spaceKey: [''],
      overlap: [''],
      scheme: [''],
      host: [''],
      port: [''],
      user: [''],
      password: [''],
      dbname: [''],
      query: [''],
      goodAtTags: [[], Validators.required],
      practiceArea: ['', Validators.required],
    });

    // Check for review mode and ID in query params
    this.route.queryParams.subscribe(params => {
      const inReview = params['inReview'] === 'true';
      const kbId = params['id'];

      this.isReviewMode.set(inReview);

      // If in review mode and has ID, load the knowledge base data
      if (inReview && kbId) {
        this.loadKnowledgeBaseForReview(parseInt(kbId));
      } else {
        // Reset to create mode when no review parameters are present
        this.isReviewMode.set(false);
        this.isDisabled.set(false);
        this.selectedFiles.set([]);
        this.knowledgeId.set('');
        
        // Re-enable the form if it was previously disabled
        if (this.kbForm && this.kbForm.disabled) {
          this.kbForm.enable();
        }
      }
    });
  }

  /**
   * Load knowledge base data for review mode
   */
  private loadKnowledgeBaseForReview(kbId: number): void {
    this.isMetadataLoading.set(true);
    this.isConfigurationLoading.set(true); // Show skeleton loader for configuration widget
    this.isFileRetrieverLoading.set(true); // Show skeleton loader for file retriever widget

    // Set the knowledge ID for approval operations
    this.knowledgeId.set(kbId.toString());

    // Use the new collection details API to get complete information
    this.knowledgeService.getCollectionDetails(kbId).subscribe({
      next: (response) => {
        console.log('Collection details response:', response);

        if (response && response.collection) {
          const collection = response.collection;

          // Map all the fields from the API response to the form
          this.kbForm.patchValue({
            name: collection.name || '',
            description: collection.description || '',
            splitSize: parseInt(collection.splitSize) || 5000,
            parentSplitSize: collection.parentSplitSize !== 'null' ? parseInt(collection.parentSplitSize) : null,
            embeddingModel: collection.modelId || '',
            practiceArea: collection.practiceAreaId || '',
            goodAtTags: collection.goodAt ? collection.goodAt.map((item: any) => item.name) : []
          });

          // Set the selected files if available
          if (collection.files && collection.files.length > 0) {
            this.selectedFiles.set(collection.files);
          }

          // Set the goodAt IDs for the parent component
          if (collection.goodAt && collection.goodAt.length > 0) {
            this.selecteedGoodAtIds.set(collection.goodAt.map((item: any) => item.id));
          }

          // Set retriever type based on parentSplitSize
          const retrieverType = collection.parentSplitSize !== 'null' ? 'Parent Doc' : 'Default';
          this.kbForm.patchValue({ retriever: retrieverType });

          // Disable form in review mode
          if (this.isReviewMode()) {
            this.isDisabled.set(true);
            this.kbForm.disable(); // Disable the entire form
          }

          this.isMetadataLoading.set(false);
          this.isConfigurationLoading.set(false); // Hide skeleton loader for configuration widget
          this.isFileRetrieverLoading.set(false); // Hide skeleton loader for file retriever widget
        } else {
          // Fallback to old method if response structure is different
          this.loadKnowledgeBaseFiles(kbId);
        }
      },
      error: (error) => {
        console.error('Error loading collection details:', error);
        this.isConfigurationLoading.set(false); // Hide skeleton loader on error
        this.isFileRetrieverLoading.set(false); // Hide skeleton loader on error
        // Fallback to old method
        this.loadKnowledgeBaseFiles(kbId);
      }
    });
  }

  /**
   * Fallback method to load knowledge base files when metadata is not available
   */
  private loadKnowledgeBaseFiles(kbId: number): void {
    this.knowledgeService.getCollectionDetails(kbId).subscribe({
      next: (response: any) => {
        console.log('Knowledge base files response:', response);

        // Set default values for review mode since files endpoint doesn't have metadata
        this.kbForm.patchValue({
          name: `Knowledge Base ${kbId}`,
          description: 'Knowledge base under review'
        });

        // Disable form in review mode
        if (this.isReviewMode()) {
          this.isDisabled.set(true);
          this.kbForm.disable(); // Disable the entire form
        }

        this.isMetadataLoading.set(false);
        this.isConfigurationLoading.set(false); // Hide skeleton loader for configuration widget
        this.isFileRetrieverLoading.set(false); // Hide skeleton loader for file retriever widget
      },
      error: (error: any) => {
        console.error('Error loading knowledge base files:', error);
        this.isMetadataLoading.set(false);
        this.isConfigurationLoading.set(false); // Hide skeleton loader for configuration widget
        this.isFileRetrieverLoading.set(false); // Hide skeleton loader for file retriever widget
        this.toastWrapper.error(
          'Failed to load knowledge base data',
          'Error'
        );
      }
    });
  }

  onDropdownValueChange(event: string) {
    this.selectedConfigType.set(event);
  }

  fileChange(files: File[]) {
    this.uploadedFiles.set(files);
  }

  onGoodAtValueChange(selectedId: number[]) {
    this.selecteedGoodAtIds.set(selectedId);
  }

  onPreviewChunk() {
    if (this.uploadedFiles().length === 0) return;

    // Open modal immediately with loading state
    const modalRef = this.dialogService.openModal(
      PreviewChunkRendererComponent,
      {
        width: '64vw',
        maxHeight: '94vh',
      },
      {
        chunksList: [],
        isLoading: true,
      }
    );

    const retrieverType = this.kbForm.value.retriever;
    const splitSize = this.kbForm.value.splitSize;
    const parentSplitSize = this.kbForm.value.parentSplitSize;
    const childSplitSize = this.kbForm.value.childSplitSize;

    const submissionData = new FormData();
    this.uploadedFiles().forEach((file: File) => {
      submissionData.append('files', file);
    });

    const params: Record<string, any> = {};
    if (retrieverType === 'Parent Doc') {
      params['chunkSize'] = parentSplitSize;
      params['childChunkSize'] = childSplitSize;
      params['chunkType'] = 'parent-child';
    } else {
      params['chunkSize'] = splitSize;
      params['chunkType'] = 'default';
    }

    this.knowledgeService.submitPreview(submissionData, params).subscribe({
      next: (res: any) => {
        // Update modal with new data - simplified approach
        this.dialogService.close();
        this.dialogService.openModal(
          PreviewChunkRendererComponent,
          {
            width: '64vw',
            maxHeight: '94vh',
          },
          {
            chunksList: res.previewResponse,
            isLoading: false,
          }
        );
      },
      error: async (e: any) => {
        console.error(e);
        this.dialogService.close();
        await this.toastWrapper.error(
          e?.error?.message || e?.message || 'Failed to generate preview.'
        );
      },
    });
  }


  onSave() {
    if (this.kbForm.invalid) return;

    const type = this.kbForm.value.uploadType;
    const suffix = this.pathSuffix[type] || '';
    const retrieverType = this.getControl('retriever').value;
    const orgRetrieverType =
      retrieverType === 'Default' ? 'normal' : 'parent_doc_retriever';
    const knowledgeBase = this.getControl('name').value;
    const description = this.getControl('description').value;
    const splitSize = this.getControl('splitSize').value;
    const parentSplitSize = this.getControl('parentSplitSize')?.value;
    const childSplitSize = this.getControl('childSplitSize')?.value;
    const selectedModelId = this.getControl('embeddingModel')?.value;
    const selectedPracticeAreaId = this.getControl('practiceArea')?.value;
    const teamId = this.teamIdService.getTeamIdFromCookies();

    // Construct base payload
    const payload: Record<string, any> = {};

    // Add selected controls for non-file uploads to payload
    const isFileUpload = type === 'Upload Files';

    // Prepare data for submission
    let submissionData: any;
    if (isFileUpload) {
      if (!this.uploadedFiles() || this.uploadedFiles().length === 0) return;

      submissionData = new FormData();
      this.uploadedFiles().forEach((file: File) => {
        submissionData.append('files', file);
      });
      submissionData.append('knowledgeBase', knowledgeBase);
      submissionData.append('description', description);
      submissionData.append('model-ref', selectedModelId);
      submissionData.append('type', orgRetrieverType);
      if (retrieverType === 'Parent Doc') {
        submissionData.append('parentSplitSize', parentSplitSize);
        submissionData.append('splitSize', childSplitSize);
      } else {
        submissionData.append('splitSize', splitSize);
      }
      submissionData.append('practiceArea', selectedPracticeAreaId);
      submissionData.append('teamId', teamId);
      submissionData.append('status', 'CREATED');
      this.selecteedGoodAtIds().forEach(id => {
        submissionData.append('goodAt', id);
      });
    } else {
      submissionData = {};
      configInputFieldConfig[type]?.forEach(key => {
        const value = this.getControl(key.formControlName)?.value;
        submissionData[key.formControlName] = value;
      });

      payload['knowledgeBase'] = knowledgeBase;
      payload['description'] = description;
      payload['model-ref'] = selectedModelId;
      payload['type'] = orgRetrieverType;
      if (retrieverType === 'Parent Doc') {
        payload['parentSplitSize'] = parentSplitSize;
        payload['splitSize'] = childSplitSize;
      } else {
        payload['splitSize'] = splitSize;
      }
      payload['practiceArea'] = selectedPracticeAreaId;
      payload['teamId'] = teamId;
      payload['status'] = 'CREATED';
      payload['goodAt'] = this.selecteedGoodAtIds().join(',');
    }

    // Show skeleton loaders
    this.isConfigurationLoading.set(true);
    this.isFileRetrieverLoading.set(true);
    this.isMetadataLoading.set(true);

    // Single API call with unified error handling
    this.knowledgeService
      .submitUpload(submissionData, payload, suffix)
      .subscribe({
        next: async (info: any) => {
          // Hide skeleton loaders
          this.isConfigurationLoading.set(false);
          this.isFileRetrieverLoading.set(false);
          this.isMetadataLoading.set(false);
          
          this.isDisabled.set(true);
          this.knowledgeId.set(info.id);
          await this.toastWrapper.success(
            info?.info?.message ||
            info.message ||
            'Knowledge Base processed successfully.'
          );
        },
        error: async (err: any) => {
          // Hide skeleton loaders
          this.isConfigurationLoading.set(false);
          this.isFileRetrieverLoading.set(false);
          this.isMetadataLoading.set(false);
          
          // await this.toastWrapper.error(
          //   err?.error?.data?.code ||
          //   err?.error?.message ||
          //   err.message ||
          //   'Knowledge Base creation or update failed. Please try again.'
          // );
        },
      });
  }

  async onApprove() {
    // Show skeleton loaders and disable form
    this.isConfigurationLoading.set(true);
    this.isFileRetrieverLoading.set(true);
    this.isMetadataLoading.set(true);
    this.isDisabled.set(true);
    
    this.knowledgeService.submitApproval(this.knowledgeId()).subscribe({
      next: async (res: any) => {
        // Hide skeleton loaders
        this.isConfigurationLoading.set(false);
        this.isFileRetrieverLoading.set(false);
        this.isMetadataLoading.set(false);
        
        await this.toastWrapper.success(
          res.message || 'Knowledgebase status updated to IN_REVIEW.'
        );
        this.router.navigate(['/marketplace']);
      },
      error: async (e: any) => {
        // Hide skeleton loaders
        this.isConfigurationLoading.set(false);
        this.isFileRetrieverLoading.set(false);
        this.isMetadataLoading.set(false);
        
        console.error(e);
        // await this.toastWrapper.error(
        //   e?.error?.message || e?.message || 'Failed to approve knowledge base.'
        // );
      },
    });
  }

  async onSendBack() {
    if (!this.knowledgeId()) {
      await this.toastWrapper.error('No knowledge base ID found for rejection');
      return;
    }

    // Ensure form is disabled during send back process
    this.isDisabled.set(true);

    // Open sendback modal for feedback
    this.dialogService.openModal(SendbackModalComponent).then(result => {
      if (result !== null && result?.action !== 'close') {
        this.performSendBack(result);
      }
    });
  }

  private async performSendBack(feedback: any) {
    // Show loading state
    this.isConfigurationLoading.set(true);
    this.isFileRetrieverLoading.set(true);
    this.isMetadataLoading.set(true);

    const masterId = parseInt(this.knowledgeId());
    const comment = feedback || 'Knowledge base sent back for revision';

    this.approvalService.rejectKnowledge(masterId, comment).subscribe({
      next: async (res: any) => {
        // Hide loading state
        this.isConfigurationLoading.set(false);
        this.isFileRetrieverLoading.set(false);
        this.isMetadataLoading.set(false);

        await this.toastWrapper.success(
          res?.message || 'Knowledge Base sent back successfully'
        );

        // Navigate back to approvals page
        this.router.navigate(['/approvals']);
      },
      error: async (error: any) => {
        // Hide loading state
        this.isConfigurationLoading.set(false);
        this.isFileRetrieverLoading.set(false);
        this.isMetadataLoading.set(false);

        console.error('Error sending back knowledge base:', error);
        await this.toastWrapper.error(
          error?.error?.message || error?.message || 'Failed to send back knowledge base'
        );
      }
    });
  }

  async onReviewApprove() {
    if (!this.knowledgeId()) {
      await this.toastWrapper.error('No knowledge base ID found for approval');
      return;
    }

    // Ensure form is disabled during approval process
    this.isDisabled.set(true);

    // Open approval modal for feedback
    this.dialogService.openModal(ApprovalModalComponent).then(result => {
      if (result !== null && result?.action !== 'close') {
        this.performApproval(result);
      }
    });
  }

  private async performApproval(feedback: string) {
    // Show loading state
    this.isConfigurationLoading.set(true);
    this.isFileRetrieverLoading.set(true);
    this.isMetadataLoading.set(true);

    const masterId = parseInt(this.knowledgeId());
    const comment = feedback || 'Knowledge base approved';

    this.approvalService.approveKnowledge(masterId, comment).subscribe({
      next: async (res: any) => {
        // Hide loading state
        this.isConfigurationLoading.set(false);
        this.isFileRetrieverLoading.set(false);
        this.isMetadataLoading.set(false);

        await this.toastWrapper.success(
          res?.message || 'Knowledge Base approved successfully'
        );

        // Navigate back to approvals page
        this.router.navigate(['/approvals']);
      },
      error: async (error: any) => {
        // Hide loading state
        this.isConfigurationLoading.set(false);
        this.isFileRetrieverLoading.set(false);
        this.isMetadataLoading.set(false);

        console.error('Error approving knowledge base:', error);
        await this.toastWrapper.error(
          error?.error?.message || error?.message || 'Failed to approve knowledge base'
        );
      }
    });
  }

  isSubmitDisabled(): boolean {
    const isFormInvalid = this.kbForm.invalid;
    return isFormInvalid || this.isSplitSizeValid();
  }

  isSplitSizeValid() {
    const isUploadTypeFile = this.kbForm.value['uploadType'] === 'Upload Files';
    const isFileEmpty = this.uploadedFiles().length === 0;

    const isParentDoc = this.kbForm.value['retriever'] === 'Parent Doc';
    const splitSize = Number(this.kbForm.get('splitSize')?.value);
    const parentSplitSize = Number(this.kbForm.get('parentSplitSize')?.value);
    const childSplitSize = Number(this.kbForm.get('childSplitSize')?.value);

    //  Normal condition: splitSize must be at least 100
    const isNormalSplitSizeInvalid = !isParentDoc && splitSize < 100;

    //  Parent-child condition:
    // 1. parent must be > child
    // 2. both must be >= 100
    const isParentChildInvalid =
      isParentDoc &&
      (parentSplitSize <= childSplitSize ||
        parentSplitSize < 100 ||
        childSplitSize < 100);

    return (
      (isUploadTypeFile && isFileEmpty) ||
      isParentChildInvalid ||
      isNormalSplitSizeInvalid
    );
  }

  isPreviewDisabled() {
    const isUploadTypeFile = this.kbForm.value['uploadType'] === 'Upload Files';
    return this.isSplitSizeValid() || !isUploadTypeFile;
  }

  // Get a typed form control
  getControl(name: string): FormControl {
    return this.kbForm.get(name) as FormControl;
  }
}
