export interface FormFieldConfig {
  name: string;
  placeholder: string;
  formControlName: string;
  required: boolean;
  type: 'dropdown' | 'text' | 'textarea';
  options?: any[];
  dataMethod?: string; // Add this for dynamic dropdown data
  loading?: boolean; // Add loading state
}

export const configInputFieldConfig: Record<string, FormFieldConfig[]> = {
  'Azure Blob': [
    {
      name: 'Container Name',
      placeholder: 'Enter Container Name',
      formControlName: 'containerName',
      required: true,
      type: 'text',
    },
    {
      name: 'Account Name',
      placeholder: 'Enter Account Name',
      formControlName: 'accountName',
      required: true,
      type: 'text',
    },
    {
      name: 'Account Key',
      placeholder: 'Enter Account Key',
      formControlName: 'accountKey',
      required: true,
      type: 'text',
    },
  ],
  GitHub: [
    {
      name: 'GitHub Key',
      placeholder: 'Enter GitHub Key',
      formControlName: 'githubKey',
      required: true,
      type: 'text',
    },
    {
      name: 'GitHub Account',
      placeholder: 'Enter GitHub Account',
      formControlName: 'githubAccount',
      required: true,
      type: 'text',
    },
    {
      name: 'GitHub Repository',
      placeholder: 'Enter GitHub Repository',
      formControlName: 'githubRepo',
      required: true,
      type: 'text',
    },
    {
      name: 'GitHub Branch',
      placeholder: 'Enter GitHub Branch',
      formControlName: 'githubBranch',
      required: true,
      type: 'text',
    },
  ],
  'Share Point': [
    {
      name: 'Client ID',
      placeholder: 'Enter Client ID',
      formControlName: 'clientId',
      required: true,
      type: 'text',
    },
    {
      name: 'Client Secret',
      placeholder: 'Enter Client Secret',
      formControlName: 'clientSecret',
      required: true,
      type: 'text',
    },
    {
      name: 'Tenant ID',
      placeholder: 'Enter Tenant ID',
      formControlName: 'tenantId',
      required: true,
      type: 'text',
    },
    {
      name: 'SharePoint Site Name',
      placeholder: 'Enter SharePoint Site Name',
      formControlName: 'sharepointSiteName',
      required: true,
      type: 'text',
    },
    {
      name: 'SharePoint Folder Path',
      placeholder: 'Enter SharePoint Folder Path',
      formControlName: 'sharepointFolderPath',
      required: true,
      type: 'text',
    },
  ],
  'Confluence Wiki': [
    {
      name: 'Email ID',
      placeholder: 'Enter Email ID',
      formControlName: 'email',
      required: true,
      type: 'text',
    },
    {
      name: 'Client ID',
      placeholder: 'Enter Client ID',
      formControlName: 'clientId',
      required: true,
      type: 'text',
    },
    {
      name: 'Client Secret',
      placeholder: 'Enter Client Secret',
      formControlName: 'clientSecret',
      required: true,
      type: 'text',
    },
    {
      name: 'API Token',
      placeholder: 'Enter API Token',
      formControlName: 'apiToken',
      required: true,
      type: 'text',
    },
    {
      name: 'Base URL',
      placeholder: 'Enter Base URL',
      formControlName: 'baseUrl',
      required: true,
      type: 'text',
    },
    {
      name: 'Space Key',
      placeholder: 'Enter Space Key',
      formControlName: 'spaceKey',
      required: true,
      type: 'text',
    },
    {
      name: 'Overlap',
      placeholder: 'Enter Overlap',
      formControlName: 'overlap',
      required: true,
      type: 'text',
    },
  ],
  Database: [
    {
      name: 'Select Scheme',
      placeholder: 'Select Scheme',
      formControlName: 'scheme',
      required: true,
      type: 'dropdown',
      dataMethod: 'getScheme',
    },
    {
      name: 'DB Host',
      placeholder: 'Enter DB Host',
      formControlName: 'host',
      required: true,
      type: 'text',
    },
    {
      name: 'DB Port',
      placeholder: 'Enter DB Port',
      formControlName: 'port',
      required: true,
      type: 'text',
    },
    {
      name: 'DB User',
      placeholder: 'Enter DB User',
      formControlName: 'user',
      required: true,
      type: 'text',
    },
    {
      name: 'DB Password',
      placeholder: 'Enter DB Password',
      formControlName: 'password',
      required: true,
      type: 'text',
    },
    {
      name: 'DB Name',
      placeholder: 'Enter DB Name',
      formControlName: 'dbname',
      required: true,
      type: 'text',
    },
    {
      name: 'DB Query',
      placeholder: 'Enter DB Query',
      formControlName: 'query',
      required: true,
      type: 'text',
    },
    {
      name: 'Overlap',
      placeholder: 'Enter Overlap',
      formControlName: 'overlap',
      required: true,
      type: 'text',
    },
  ],
};
