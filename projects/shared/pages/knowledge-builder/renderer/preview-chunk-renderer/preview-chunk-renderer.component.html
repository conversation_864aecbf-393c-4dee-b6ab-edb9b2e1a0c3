<div class="p-4">
    <h4 class="card-title mb-3">File Preview</h4>

    @if (isLoading) {
    <!-- Skeleton loader -->
    <aava-default-card>
        <div class="file__container d-flex flex-sm-column gap-4">
            <aava-skeleton [shape]="'rectangle'" [width]="'60%'" [height]="'40px'"></aava-skeleton>

            <aava-skeleton [shape]="'rectangle'" [width]="'100%'" [height]="'60px'"></aava-skeleton>
        </div>
    </aava-default-card>
    } @else {
    <div class="file__container d-flex flex-sm-column gap-4">
        @for(file of chunksList; track file) {
        <div class="file__row">
            <div class="mb-1">{{file.fileName}}</div>
            <aava-default-card>
                @for(chunk of file.chunks; track chunk) {
                <div class="chunk__header text-12 chunk-number d-flex align-items-center gap-2">
                    <span># {{ chunk.chunkNumber }} :</span>
                    <span>{{ chunk.characterCount }} characters</span>
                </div>

                <div class="chunk__info text-12 mb-3">
                    @if(hasChildChunks(chunk)) {
                    @for(childChunk of getChildChunks(chunk); track childChunk ;let last = $last) {
                    <span>
                        <span class="child-chunk-number">{{ childChunk.childChunkNumber }}</span>
                        <span class="me-2">{{ childChunk.contentPreview }}</span>
                        <span *ngIf="!last" class="separator"> </span>
                    </span>
                    }
                    } @else() {
                    <span>{{ getContentPreview(chunk) }}</span>
                    }
                </div>
                }
            </aava-default-card>
        </div>
        }
    </div>
    }
</div>