import { AavaDefaultCardComponent, AavaSkeletonComponent, AavaDialogService } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

// Simplified interfaces
interface ChildChunk {
  childChunkNumber: string;
  contentPreview: string;
}

interface SimpleChunk {
  chunkNumber: string;
  characterCount: number;
  contentPreview: string;
}

interface ComplexChunk {
  chunkNumber: string;
  characterCount: number;
  childChunks: ChildChunk[];
}

interface ChunksData {
  fileName: string;
  chunks: (SimpleChunk | ComplexChunk)[];
}

@Component({
  selector: 'app-preview-chunk-renderer',
  standalone: true,
  templateUrl: './preview-chunk-renderer.component.html',
  styleUrls: ['./preview-chunk-renderer.component.scss'],
  imports: [AavaDefaultCardComponent, AavaSkeletonComponent, CommonModule],
})
export class PreviewChunkRendererComponent implements OnInit {
  @Input() data: any = {};
  
  chunksList: ChunksData[] = [];
  isLoading: boolean = true;

  constructor(private dialogService: AavaDialogService) {}

  ngOnInit() {
    // Get data from input or dialog service
    if (this.data.chunksList) {
      this.chunksList = this.data.chunksList;
    }
    if (this.data.isLoading !== undefined) {
      this.isLoading = this.data.isLoading;
    }
  }

  // Type guard to check if chunk has childChunks
  hasChildChunks(chunk: SimpleChunk | ComplexChunk): chunk is ComplexChunk {
    return (
      'childChunks' in chunk &&
      Array.isArray((chunk as ComplexChunk).childChunks)
    );
  }

  // Get child chunks safely
  getChildChunks(chunk: SimpleChunk | ComplexChunk): ChildChunk[] {
    if (this.hasChildChunks(chunk)) {
      return chunk.childChunks;
    }
    return [];
  }

  // Get content preview for simple chunks
  getContentPreview(chunk: SimpleChunk | ComplexChunk): string {
    if (!this.hasChildChunks(chunk)) {
      return (chunk as SimpleChunk).contentPreview;
    }
    return '';
  }
}
