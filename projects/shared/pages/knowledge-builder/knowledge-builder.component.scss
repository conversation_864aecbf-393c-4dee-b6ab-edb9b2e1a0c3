/* Original styles - commented out */
/*
.kb__container {
  border-radius: 0.75rem 0.75rem 0.75rem 0.75rem;
  box-shadow: var(--Elevation-01-X, 0) var(--Elevation-01-Y, 2px)
    var(--Elevation-01-Blur, 4px) var(--Elevation-01-Spread, 0)
    var(--Brand-Neutral-n-100, #d1d3d8);
  margin: 1rem;
}

.metadata__widget {
  height: -webkit-fill-available;
}

.kb__header {
    font: var(--Global-v1-Size-16, 1rem);
    color: var(--Colors-Text-primary);
}

.kb__form {
    padding: 24px;
    display: flex;
    gap: 24px;
}

.kb__form > *:first-child {
  width: 22vw;
}

.content-area {
    flex: 1;
    gap: 24px;
    display: flex;
    flex-direction: column;
}
*/

/* New layout styles following guard rails pattern */
:host {
  height: 100%;
  display: block;
}

.knowledge-content-area {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: calc(100vh - 200px); // Adjust based on header height
  overflow: auto;
  box-sizing: border-box;
}

.configuration-section {
  flex: 1 1 45%; // Use flex-grow and flexible percentage
  // min-height: 300px; // Minimum height for usability
  // max-height: 50%; // Maximum height to prevent overflow
  display: flex;
  flex-direction: column;
}

.file-retriever-section {
  flex: 1 1 45%; // Use flex-grow and flexible percentage  
  // min-height: 250px; // Minimum height for usability
  // max-height: 45%; // Maximum height to prevent overflow
  // overflow: auto; // Allow scrolling when content overflows
  display: flex;
  flex-direction: column;
}