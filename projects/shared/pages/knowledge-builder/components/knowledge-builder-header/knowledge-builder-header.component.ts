import { AavaButtonComponent, AavaDialogService } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
  inject,
} from '@angular/core';

@Component({
  selector: 'app-agent-builder-header',
  standalone: true,
  imports: [CommonModule, AavaButtonComponent],
  templateUrl: './knowledge-builder-header.component.html',
  styleUrls: ['./knowledge-builder-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class KnowledgeBuilderHeaderComponent {
  // Signal inputs
  readonly isPreviewValid = input.required<boolean>();
  readonly isFormValid = input.required<boolean>();
  readonly isApprovalValid = input.required<boolean>();
  readonly isDisabled = input.required<boolean>();
  readonly isReviewMode = input<boolean>(false);

  // Signal outputs
  readonly previewClicked = output<void>();
  readonly saveClicked = output<void>();
  readonly approveClicked = output<void>();
  readonly sendBackClicked = output<void>();
  readonly reviewApproveClicked = output<void>();

  private dialogService = inject(AavaDialogService);

  // Computed values
  protected runButtonAriaLabel = computed(() =>
    this.isFormValid()
      ? 'Save knowledge base'
      : 'Complete required fields to save knowledge base'
  );

  protected onSaveClick(): void {
    this.saveClicked.emit();
  }

  protected onApprovalClick(): void {
    this.approveClicked.emit();
  }

  protected onSendBackClick(): void {
    this.sendBackClicked.emit();
  }

  protected onReviewApproveClick(): void {
    this.reviewApproveClicked.emit();
  }

  onPreiewChunkClick() {
    this.previewClicked.emit();
  }
}
