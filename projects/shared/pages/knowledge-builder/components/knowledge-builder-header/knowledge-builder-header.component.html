<!-- <header class="kb__header d-flex">
  <h1 class="page-title">Knowledgebase Editor</h1>

  <div class="kb__actions d-flex">
    <aava-button
      label="Preview Chunk"
      variant="secondary"
      size="xs"
      [disabled]="!isPreviewValid()"
      iconName="eye"
      iconPosition="right"
      (userClick)="onPreiewChunkClick()"
    ></aava-button>
    <aava-button
      label="Save"
      variant="secondary"
      size="xs"
      iconName="save"
      iconPosition="right"
      [disabled]="!isFormValid()"
      (userClick)="onSaveClick()"
      [attr.aria-label]="runButtonAriaLabel()"
    ></aava-button>
    <aava-button
      label="Send for Approval"
      variant="primary"
      size="xs"
      iconName="send"
      iconPosition="right"
      [disabled]="!isApprovalValid()"
      (userClick)="onApprovalClick()"
    ></aava-button>
  </div>
</header> -->
<header class="tool-builder-header surface--fill9--blur10">
  <div class="header-spacer"></div>
  <h1 class="page-title">Knowledgebase Editor</h1>
  <div class="header-actions">
    <aava-button
      label="Preview Chunk"
      variant="primary"
      [clear]="true"
      size="xs"
      [disabled]="!isPreviewValid()"
      iconName="eye"
      iconPosition="right"
      (userClick)="onPreiewChunkClick()"
    ></aava-button>

    @if (!isReviewMode()) {
      <aava-button
        label="Save"
        variant="secondary"
        size="xs"
        iconName="save"
        iconPosition="right"
        [disabled]="!isFormValid() || isDisabled()"
        (userClick)="onSaveClick()"
        [attr.aria-label]="runButtonAriaLabel()"
      ></aava-button>
      <aava-button
        label="Send for Approval"
        variant="primary"
        size="xs"
        iconName="circle-check"
        iconPosition="right"
        [disabled]="!isApprovalValid()"
        (userClick)="onApprovalClick()"
      ></aava-button>
    }

    @if (isReviewMode()) {
      <aava-button
        label="Send Back"
        variant="secondary"
        size="xs"
        iconName="arrow-left-from-line"
        iconPosition="right"
        [clear]="true"
        (userClick)="onSendBackClick()"
      ></aava-button>
      <aava-button
        label="Approve"
        variant="primary"
        size="xs"
        iconName="circle-check"
        iconPosition="right"
        (userClick)="onReviewApproveClick()"
      ></aava-button>
    }
  </div>
</header>
