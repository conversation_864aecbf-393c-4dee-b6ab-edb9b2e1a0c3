import {
  AavaCheckboxComponent,
  AavaTextboxComponent,
  AavaSelectComponent,
  AavaSelectOptionComponent,
  AavaTagComponent,
  AavaSkeletonComponent,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DropdownDataStore } from 'projects/shared/stores/dropdown-data.store';

export interface KnowledgeMetaData {
  readonly name: string;
  readonly description: string;
  readonly retriever: string;
  readonly uploadType: string;
  readonly practiceArea: string;
  readonly goodAtTags: string[];
}

@Component({
  selector: 'app-knowledge-meta',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    AavaTextboxComponent,
    AavaSelectComponent,
    AavaSelectOptionComponent,
    AavaTagComponent,
    AavaCheckboxComponent,
    AavaSkeletonComponent
  ],
  templateUrl: './knowledge-meta.component.html',
  styleUrls: ['./knowledge-meta.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class KnowledgeMetaComponent implements OnInit {
  // Signal inputs
  readonly data = input.required<KnowledgeMetaData>();
  readonly kbForm = input.required<FormGroup>();
  readonly isDisabled = input.required<boolean>();

  // Signal outputs
  readonly dataChanged = output<Partial<KnowledgeMetaData>>();
  readonly fileTypeValueChange = output<string>();
  readonly goodAtValueChange = output<number[]>();
  readonly loading = input<boolean>(false);


  uploadOptions = signal<{ name: string; value: string }[]>([
    { name: 'Upload Files', value: 'Upload Files' },
    { name: 'Azure Blob', value: 'Azure Blob' },
    { name: 'GitHub', value: 'GitHub' },
    { name: 'Share Point', value: 'Share Point' },
    { name: 'Confluence Wiki', value: 'Confluence Wiki' },
    { name: 'Database', value: 'Database' },
  ]);

  private dropdownStore = inject(DropdownDataStore);

  readonly goodAtOptions = this.dropdownStore.goodAtOptions;
  readonly practiceAreaOptions = this.dropdownStore.practiceAreaOptions;

  ngOnInit(): void {
    this.loadDropdownData();
  }

    private async loadDropdownData(): Promise<void> {
    try {
      await Promise.all([
        this.dropdownStore.loadPracticeAreas(),
        this.dropdownStore.loadGoodAtTags(),
      ]);
    } catch (error) {
      console.error('Failed to load dropdown data:', error);
    }
  }
  // Form changes are handled by reactive forms automatically
  // We can emit changes when needed for parent component updates

  protected onUploadTypeChange(event: any): void {
    this.fileTypeValueChange.emit(event);
    this.dataChanged.emit({ uploadType: event });
  }

  protected onPracticeAreaChange(value: string): void {
    this.dataChanged.emit({ practiceArea: value });
  }

  protected onGoodAtSelectionChange(selectedValues: string[]): void {
    const normalizedValues = this.normalizeToArray(selectedValues);
    this.kbForm().patchValue({ goodAtTags: normalizedValues });
    const selectedIds = this.goodAtOptions()
      .filter(item => normalizedValues.includes(item.label))
      .map(item => item.id);
    this.goodAtValueChange.emit(selectedIds);
    this.dataChanged.emit({ goodAtTags: normalizedValues });
  }

  protected isOptionSelected(optionValue: string): boolean {
    return this.getGoodAtTagsArray().includes(optionValue);
  }

  protected onGoodAtTagRemove(tag: string): void {
    const updatedTags = this.getGoodAtTagsArray().filter(t => t !== tag);
    this.kbForm().patchValue({ goodAtTags: updatedTags });
    this.onGoodAtSelectionChange(updatedTags);
  }

  protected getGoodAtTagsArray(): string[] {
    return this.normalizeToArray(this.kbForm().get('goodAtTags')?.value);
  }

  private normalizeToArray(value: any): string[] {
    return Array.isArray(value) ? value : [];
  }
}
