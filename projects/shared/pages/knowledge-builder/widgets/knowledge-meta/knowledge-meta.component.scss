:host {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.knowledge-metadata-card {
  display: flex;
  flex-direction: column;
  gap: var(--Global-V1-Spacing-Space8, 16px);
  padding: 16px;
  width: 100%;
  height: 100%;
  border-radius: var(--Global-V1-Radius-Rad7, 14px);
  border: 2px solid var(--Brand-White-White, #FDFDFD);
  background: var(--Surface-Fill-Light-Surface-White-6, rgba(255, 255, 255, 0.6));
  box-shadow: 0 2px 4px 0 var(--Brand-Neutral-n-100, #D1D3D8);
  box-sizing: border-box;

  .card-title {
    color: #000;
    font-family: var(--Global-v1-Family-Body, Inter);
    font-size: var(--Global-v1-Size-18, 18px);
    font-style: normal;
    font-weight: 500;
    flex-shrink: 0;
    margin: 0;
  }
}

// Skeleton Loader Styles
.skeleton-fields {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.skeleton-field {
  display: flex;
  flex-direction: column;
  gap: .5rem;
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
}

// Form Fields
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
  overflow-y: auto;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

// Tag styling
.tag-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #f3f4f6;
  border-radius: 16px;
  font-size: 14px;
}

.tag-text {
  color: #374151;
}

.tag-remove {
  width: 16px;
  height: 16px;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 12px;

  &:hover:not(:disabled) {
    background-color: #e5e7eb;
    color: #374151;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// Form field styling
.form-field {
  aava-textbox,
  aava-select {
    width: 100%;
  }
}

// Responsive design
@media (max-width: 768px) {
  .knowledge-metadata-card {
    padding: 12px;
    gap: 12px;
  }
  
  .metadata-content {
    gap: 12px;
  }
}
