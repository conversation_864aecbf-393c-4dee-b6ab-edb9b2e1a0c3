<div class="knowledge-metadata-card">

      @if (loading()) {
      <!-- Skeleton Loaders -->
      <div class="skeleton-fields">
        @for (field of [1, 2, 3, 4, 5]; track field) {
          <div class="skeleton-field">
            <aava-skeleton
              [width]="'55%'"
              [height]="'20px'"
              [shape]="'rectangle'"
              [animation]="'wave'"
            ></aava-skeleton>
            <aava-skeleton
              [width]="'100%'"
              [height]="'40px'"
              [shape]="'rectangle'"
              [animation]="'wave'"
            ></aava-skeleton>
          </div>
        }
      </div>
    } @else {
  <h3 class="card-title">Knowledge Base Metadata</h3>
  
  <div class="form-fields" [formGroup]="kbForm()">
    <div class="form-field">
      <aava-textbox
        label="Knowledge Base Name"
        placeholder="Enter Name"
        size="sm"
        formControlName="name"
        [required]="true"
        [disabled]="isDisabled()"
      >
      </aava-textbox>
    </div>

    <div class="form-field">
      <aava-textbox
        label="Knowledge Base Description"
        placeholder="Enter Description"
        size="sm"
        formControlName="description"
        [required]="true"
        [disabled]="isDisabled()"
      >
      </aava-textbox>
    </div>

    <div class="form-field">
      <aava-select 
        label="Practice Area" 
        formControlName="practiceArea" 
        placeholder="Practice Area" 
        size="sm"
        (selectionChange)="onPracticeAreaChange($event)"
        [required]="true"
        [disabled]="isDisabled()"
      >
        @for(option of practiceAreaOptions(); track option) {
          <aava-select-option [value]="option.id">{{option.label}}</aava-select-option>
        }
      </aava-select>
    </div>

    <div class="form-field">
      <aava-select
        size="sm"
        [multiple]="true"
        label="Good At"
        placeholder="Select skills and technologies"
        formControlName="goodAtTags"
        (selectionChange)="onGoodAtSelectionChange($event)"
        [required]="true"
        [disabled]="isDisabled()"
      >
        <aava-select-option
          *ngFor="let option of goodAtOptions()"
          [value]="option.value"
        >
          <aava-checkbox
            size="sm"
            [isChecked]="isOptionSelected(option.value)"
          ></aava-checkbox>
          {{ option.label }}
        </aava-select-option>
      </aava-select>

      @if(getGoodAtTagsArray().length > 0) {
        <div class="tags-container">
          @for (tag of getGoodAtTagsArray(); track tag) {
            <aava-tag
              [label]="tag"
              size="sm"
              variant="outlined"
              [removable]="true"
              [disabled]="isDisabled()"
              (removed)="onGoodAtTagRemove(tag)"
            ></aava-tag>
          }
        </div>
      }
    </div>

    <div class="form-field">
      <aava-select 
        label="Select your File Type" 
        formControlName="uploadType"
        (selectionChange)="onUploadTypeChange($event)" 
        placeholder="Select" 
        size="sm"
        [required]="true"
        [disabled]="isDisabled()"
      >
        @for(option of uploadOptions(); track option.value) {
          <aava-select-option [value]="option.value">{{option.name}}</aava-select-option>
        }
      </aava-select>
    </div>
  </div>
}
</div>
