:host {
  height: 100%;
  display: block;
}

.kb__metadata--container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--Global-V1-Spacing-Space8, 16px);
  width: 100%;
  border-radius: var(--card-border-radius);
  border: var(--card-default-border);
  background: var(--card-background);
  box-shadow: var(--card-shadow);
  padding: var(--card-padding);
  box-sizing: border-box;
}

.metadata-content {
  width: 100%;
}

.tags-container {
  column-gap: 12px;
  flex-wrap: wrap;
  row-gap: 6px;
  margin: 1rem 0 0 0;
}