import { AavaCheckboxComponent, AavaTextboxComponent } from '@aava/play-core';
import { AavaSelectComponent } from '@aava/play-core';
import { AavaSelectOptionComponent } from '@aava/play-core';
import { AavaTagComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  inject,
  input,
  OnInit,
  output,
  signal,
} from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DropdownDataStore, DropdownOption } from 'projects/shared/stores/dropdown-data.store';

@Component({
  selector: 'app-matadata-widget',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AavaTextboxComponent,
    AavaSelectComponent,
    AavaSelectOptionComponent,
    AavaTagComponent,
    ReactiveFormsModule,
    AavaCheckboxComponent,
  ],
  templateUrl: './metadata-widget.component.html',
  styleUrls: ['./metadata-widget.component.scss'],
})
export class MetadataWidgetComponent implements OnInit {
  kbForm = input.required<FormGroup>();
  fileTypeValueChange = output<string>();
  goodAtValueChange = output<number[]>();

  uploadOptions = signal<{ name: string; value: string }[]>([
    { name: 'Upload Files', value: 'Upload Files' },
    { name: 'Azure Blob', value: 'Azure Blob' },
    { name: 'GitHub', value: 'GitHub' },
    { name: 'Share Point', value: 'Share Point' },
    { name: 'Confluence Wiki', value: 'Confluence Wiki' },
    { name: 'Database', value: 'Database' },
  ]);

  private dropdownStore = inject(DropdownDataStore);

  get goodAtOptions(): DropdownOption[] {
    return this.dropdownStore.goodAtOptions();
  }
  
  get practiceAreaOptions(): DropdownOption[] {
    return this.dropdownStore.practiceAreaOptions();
  }

  ngOnInit(): void {
    this.dropdownStore.loadAllData();
  }

  onUploadTypeChange(event: any) {
    this.fileTypeValueChange.emit(event);
  }

  protected onGoodAtSelectionChange(selectedValues: string[]): void {
    const safeSelectedValues = Array.isArray(selectedValues) ? selectedValues : [];
    this.kbForm().patchValue({ goodAtTags: safeSelectedValues });
    const currentFormValue = this.kbForm().value.goodAtTags || [];
    const selectedIds = this.goodAtOptions
      .filter((item: DropdownOption) => currentFormValue.includes(item.label))
      .map((item: DropdownOption) => item.id);
    this.goodAtValueChange.emit(selectedIds);
  }

  // Helper method to get current selected values for checkbox state
  protected isOptionSelected(optionValue: string): boolean {
    const currentValue = this.kbForm().get('goodAtTags')?.value;
    if (!currentValue || !Array.isArray(currentValue)) {
      return false;
    }
    return currentValue.includes(optionValue);
  }

  protected onGoodAtTagRemove(tag: string): void {
    const currentTags = this.kbForm().get('goodAtTags')?.value || [];
    const updatedTags = currentTags.filter((currentTag: string) => currentTag !== tag);
    this.kbForm().patchValue({ goodAtTags: updatedTags });
    const selectedIds = this.goodAtOptions
      .filter((item: DropdownOption) => updatedTags.includes(item.label))
      .map((item: DropdownOption) => item.id);
    this.goodAtValueChange.emit(selectedIds);
  }
}
