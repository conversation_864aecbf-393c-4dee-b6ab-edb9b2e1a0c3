<div class="kb__metadata--container">
  <article class="kb__metadata d-flex gap-3 align-items-md-start flex-column">
    <header>
      <h3 class="metadata-title">Knowledgebase Metadata</h3>
    </header>
    
    <div class="metadata-content d-flex flex-column gap-3" [formGroup]="kbForm()">
      <div class="form-field">
        <aava-textbox 
          label="Knowledge Base Name" 
          placeholder="Enter Name" 
          size="sm"
          formControlName="name"
          >
        </aava-textbox>
      </div>

      <div class="form-field">
        <aava-textbox 
          label="Knowledge Base Description" 
          placeholder="Enter Description" 
          size="sm"
          formControlName="description"
        >
        </aava-textbox>
      </div>

      <div class="form-field">
        <aava-select label="Practice Area" formControlName="practiceArea" placeholder="Practice Area" size="sm">
          @for(option of practiceAreaOptions; track option) {
            <aava-select-option [value]="option.id">{{option.label}}</aava-select-option>
          }
        </aava-select>
      </div>

      <div class="form-field">
        <aava-select
          size="sm"
          [multiple]="true"
          label="Good At"
          placeholder="Select skills and technologies"
          formControlName="goodAtTags"
          (selectionChange)="onGoodAtSelectionChange($event)"
        >
          <aava-select-option
            *ngFor="let option of goodAtOptions"
            [value]="option.value"
          >
            <aava-checkbox
              size="sm"
              [isChecked]="isOptionSelected(option.value)"
            ></aava-checkbox>
            {{ option.label }}
          </aava-select-option>
        </aava-select>

        @if(kbForm().get('goodAtTags')?.value && kbForm().get('goodAtTags')?.value.length > 0) {
          <div class="tags-container">
            @for (tag of kbForm().get('goodAtTags')?.value; track tag) {
              <aava-tag
                [label]="tag"
                size="sm"
                variant="outlined"
                [removable]="true"
                (removed)="onGoodAtTagRemove(tag)"
              ></aava-tag>
            }
          </div>
        }
      </div>

      <div class="form-field">
        <aava-select label="Select your File Type" 
          formControlName="uploadType"
          (selectionChange)="onUploadTypeChange($event)" 
          placeholder="Select" size="sm"
        >
          @for(option of uploadOptions(); track option.value) {
            <aava-select-option [value]="option.value">{{option.name}}</aava-select-option>
          }
        </aava-select>
      </div>
    </div>
  </article>
</div>