import {
  AavaDefaultCardComponent,
  AavaFileUploadComponent,
  AavaTextboxComponent,
  AavaSkeletonComponent,
  AavaTableComponent,
  TableColumn,
} from '@aava/play-core';
import { AavaSelectComponent } from '@aava/play-core';
import { AavaSelectOptionComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  effect,
  input,
  signal,
  OnInit,
  output,
  inject,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import {
  configInputFieldConfig,
  FormFieldConfig,
} from '../../constant/knowledge-form-config';
import { DropdownDataStore } from '../../../../stores/dropdown-data.store';

@Component({
  selector: 'app-configuration-widget',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AavaTextboxComponent,
    AavaSelectComponent,
    AavaSelectOptionComponent,
    AavaDefaultCardComponent,
    AavaFileUploadComponent,
    ReactiveFormsModule,
    AavaSkeletonComponent,
    AavaTableComponent,
  ],
  templateUrl: './configuration-widget.component.html',
  styleUrls: ['./configuration-widget.component.scss'],
})
export class ConfigurationWidgetComponent implements OnInit {
  kbForm = input.required<FormGroup>();
  selectedConfigType = input.required<string>();
  readonly isDisabled = input.required<boolean>();
  readonly isLoading = input<boolean>(false);
  readonly selectedFiles = input<any[]>([]); // For displaying files in review mode

  readonly fileChange = output<File[]>();

  configFields = signal(configInputFieldConfig);
  readonly currentFields = signal<FormFieldConfig[]>([]);

  allowedFormats: string[] = [
    'pdf',
    'txt',
    'doc',
    'docx',
    'pptx',
    'ppt',
    'html',
    'xls',
    'xlsx',
    'svg',
  ];
  showUploadButton: boolean = true;
  buttonTitle: string = 'Select File';
  showDialogCloseIcon: boolean = false;
  maxFileSize = 20 * 1024 * 1024; //20MB
  uploadedFiles = signal<File[]>([]);

  activeConfigFields: any = computed(() => []);

  // Table configuration for files display
  tableColumns: TableColumn[] = [
    {
      key: 'fileName',
      label: 'FILE NAME',
      type: 'text',
      width: '20%',
      align: 'left'
    },
    {
      key: 'filePath',
      label: 'FILE PATH',
      type: 'text',
      width: '20%',
      align: 'left'
    },
    {
      key: 'fileSizeBytes',
      label: 'FILE SIZE',
      type: 'text',
      width: '20%',
      align: 'left',
      formatter: (value: number) => this.formatFileSize(value)
    },
    {
      key: 'uploadDate',
      label: 'UPLOAD DATE',
      type: 'text',
      width: '20%',
      align: 'left',
      formatter: (value: string) => this.formatDate(value)
    },
    {
      key: 'source',
      label: 'SOURCE',
      type: 'text',
      width: '20%',
      align: 'left',
      formatter: (value: string) => value || 'N/A'
    }
  ];

  // Custom styles for table cells - using more specific table properties
  tableCustomStyles = {
    'max-height': '300px',
    'overflow-y': 'auto',
    'overflow-x': 'hidden',
    'table-layout': 'fixed',
    'width': '100%',
    'border-collapse': 'collapse'
  };

  // Inject dropdown store
  private dropdownStore = inject(DropdownDataStore);

  // Get scheme options from dropdown store
  readonly schemeOptions = this.dropdownStore.schemeOptions;

  constructor() {
    effect(() => {
      this.loadFormConfig();
    });
  }

  ngOnInit(): void {
    this.loadFormConfig();
    // Load schemes for dropdown
    this.dropdownStore.loadSchemes();
  }

  private loadFormConfig(): void {
    const baseFields = configInputFieldConfig[this.selectedConfigType()] || [];
    this.currentFields.set(baseFields);
    this.selectedConfigTypeChange();
  }

  selectedConfigTypeChange() {
    if (this.selectedConfigType() !== 'Upload Files') {
      this.uploadedFiles.set([]);
      this.fileChange.emit(this.uploadedFiles());
    }

    Object.values(configInputFieldConfig)
      .flat()
      .forEach(control => {
        const ctrl = this.getControl(control.formControlName);
        if (ctrl) {
          ctrl.clearValidators();
          ctrl.setValue(null);
          ctrl.markAsPristine();
          ctrl.markAsUntouched();
        }
      });

    // Apply required validators to new controls
    configInputFieldConfig[this.selectedConfigType()]?.forEach(ctrl => {
      this.getControl(ctrl.formControlName)?.setValidators(Validators.required);
    });

    // Reapply validator to uploadType itself
    this.getControl('uploadType')?.setValidators(Validators.required);

    // Mark uploadType as interacted
    this.getControl('uploadType')?.markAsTouched();
    this.getControl('uploadType')?.markAsDirty();

    // Recalculate form status
    this.kbForm().updateValueAndValidity();
  }

  // Get a typed form control
  getControl(name: string): FormControl {
    return this.kbForm().get(name) as FormControl;
  }

  filesListChanged(file: File[]) {
    this.uploadedFiles.set([...file]);
    this.fileChange.emit(this.uploadedFiles());
  }

  // Helper method for template
  trackByFieldName(index: number, field: any): string {
    return field.formControlName;
  }

  // Get dynamic options for a field
  getFieldOptions(field: FormFieldConfig): any[] {
    if (field.dataMethod === 'getScheme') {
      return this.schemeOptions();
    }
    return field.options || [];
  }

  // Helper method to format file size
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Helper method to format date
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }
}
