<aava-default-card [customStyles]="{'height': '100%'}" class="main_container">
  <article class="configuration__container d-flex gap-3 align-items-md-start flex-column">

    @if (isLoading()) {
    <!-- Skeleton loader -->
    <div class="configuration-content d-flex flex-column gap-3">
      <aava-skeleton [shape]="'rectangle'" [width]="'40%'" [height]="'30px'"></aava-skeleton>
      <aava-skeleton [shape]="'rounded'" [width]="'100%'" [height]="'150px'"></aava-skeleton>
      <aava-skeleton [shape]="'rounded'" [width]="'100%'" [height]="'150px'"></aava-skeleton>
    </div>
    } @else {
    <header>
      <h3 class="card-title">Configuration</h3>
    </header>
    <div class="configuration-content" [formGroup]="kbForm()"
      [ngClass]="{'config-form': selectedConfigType() !== 'Upload Files'}">
      @if(selectedConfigType() === 'Upload Files') {
        @if(isDisabled() && selectedFiles().length > 0) {
          <!-- Display selected files in review mode using AavaTableComponent -->
          <div class="selected-files-container">
            <aava-table
              [columns]="tableColumns"
              [data]="selectedFiles()"
              [showHeader]="true"
              [staticHeader]="true"
              [loading]="false"
              [sortable]="false"
              [expandable]="false"
              [customStyles]="tableCustomStyles"
              emptyMessage="No files available"
            ></aava-table>
          </div>
        } @else {
        <aava-file-upload (selectedList)="filesListChanged($event)" [allowedFormats]="allowedFormats" layout="icon"
          [maxFileSize]="maxFileSize" uploaderId="advanced" size="lg" deleteIconPosition="right" deleteIconName="trash"
          [uploadButtonLabel]="buttonTitle" [uploadButtonVariant]="'secondary'" [previewLayout]="'table'">
        </aava-file-upload>
        }
      } @else {
      @for(field of currentFields(); track trackByFieldName($index, field)) {
      @if (field.type === 'text') {
      <div class="form-field">
        <aava-textbox [required]="true" [label]="field.name" [placeholder]="field.placeholder"
          [formControlName]="field.formControlName" [disabled]="isDisabled()" size="sm">
        </aava-textbox>
      </div>
      } @else if (field.type === 'dropdown') {
      <div class="form-field">
        <aava-select [required]="true" [label]="field.name" [placeholder]="field.placeholder"
          [formControlName]="field.formControlName" [disabled]="isDisabled()" size="sm">
          @for(option of getFieldOptions(field); track option.value) {
          <aava-select-option [value]="option.value">{{option.label}}</aava-select-option>
          }
        </aava-select>
      </div>
      }
      }
      }
    </div>
    }
  </article>
</aava-default-card>