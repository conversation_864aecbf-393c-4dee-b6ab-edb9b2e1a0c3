.main_container {
    height: 100%;
}
.configuration-content {
    width: 100%;
}

.config-form {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
}

.form-field {
    flex: 1;
}

.card-title {
    color: #000;
    font-family: var(--Global-v1-Family-Body, Inter);
    font-size: var(--Global-v1-Size-18, 18px);
    font-weight: 500;
}

.selected-files-container {
    width: 100%;

    .files-title {
        color: #000;
        font-family: Inter;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
    }

    ::ng-deep aava-table {
        table {
            table-layout: fixed !important;
            width: 100% !important;
            
            th, td {
                width: 20% !important;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}