.card-title {
  color: #000;
  font-family: var(--Global-v1-Family-Body, Inter);
  font-size: var(--Global-v1-Size-18, 18px);
  font-weight: 500;
}

.file__retriver--content {
  gap: 1rem;
}

.label {
  font-size: 12px;
  font-weight: 500;
  color: #616874;
}

.split__card--container {
  width: 50%;
  // padding: 1rem;
}

.split__card--row {
  padding: 1rem;
  
  .description {
    font-size: 12px;
    color: #3B3F46;
  }
}

.split__card {
  gap: 12px;
  align-items: center;

  .split__icon {
    margin-bottom: 0.5rem;
  }
}

.split-container {
  display: flex;
  gap: 24px;

  .split--slider {
    width: 100%;
  }
}

// Split size slider styling
.split-size-container {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;

  aava-slider {
    flex: 1;
    width: 100%;
  }

  .value-box {
    width: 70px;
    
    ::ng-deep {
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
    }
  }
}

.field-error {
  color: #e53935;
  font-size: 12px;
}

.embedding {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  width: 100%;
}