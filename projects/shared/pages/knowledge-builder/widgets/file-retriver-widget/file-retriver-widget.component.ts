import {
  AavaDefaultCardComponent,
  AavaIconComponent,
  AavaListComponent,
  AavaListItemsComponent,
  AavaSliderComponent,
  AavaTextboxComponent,
  AavaSkeletonComponent,
} from '@aava/play-core';
import { AavaSelectComponent } from '@aava/play-core';
import { AavaSelectOptionComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, inject, input, OnInit, signal } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Model, ModelsService } from 'projects/shared/services/models.service';

@Component({
  selector: 'app-file-retriver-widget',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AavaTextboxComponent,
    AavaSelectComponent,
    AavaSelectOptionComponent,
    AavaDefaultCardComponent,
    AavaIconComponent,
    AavaSliderComponent,
    ReactiveFormsModule,
    AavaListItemsComponent,
    AavaSkeletonComponent,
  ],
  templateUrl: './file-retriver-widget.component.html',
  styleUrls: ['./file-retriver-widget.component.scss'],
})
export class FileRetriverWidgetComponent implements OnInit {
  kbForm = input.required<FormGroup>();
  readonly isDisabled = input.required<boolean>();
  readonly isLoading = input<boolean>(false);

  selectedRetriever = signal<string>('Default');
  slitCardList = signal<any[]>([
    {
      title: 'Quick',
      description:
        'General text chunking, the chunks retrieved and recalled are same.',
      retriver: 'Default',
      icon: 'file-box',
    },
    {
      title: 'Deep Context',
      description:
        'When using the Deep Context - Parent-child mode, the child -chunk is used for retrieval and the Parent chunk is used for recall as context.',
      retriver: 'Parent Doc',
      icon: 'file-check',
    },
  ]);
  modelAreaOptions = signal<any[]>([]);

  splitSize = signal<number>(5000);
  parentSplitSize = signal<number>(5000);
  childSplitSize = signal<number>(2000);

  private modelService = inject(ModelsService);

  ngOnInit(): void {
    this.getModelOption();
  }

  getModelOption() {
    this.modelService.getModels('Embedding').subscribe({
      next: (res: any) => {
        this.modelAreaOptions.set(
          res.map((model: Model) => ({
            value: model.model,
            label: model.model,
            id: model.id,
          }))
        );
      },
      error: e => console.error(e),
    });
  }

  selectRetriever(retriever: string): void {
    this.selectedRetriever.set(retriever);
    this.kbForm().get('retriever')?.setValue(retriever);
  }
  // splitSize input setter
  splitSizeChange(event: any) {
    this.splitSize.set(event);
    this.kbForm().get('splitSize')?.setValue(this.splitSize());
  }

  onParentSplitSizeChange(event: any): void {
    this.parentSplitSize.set(event);
    this.kbForm().get('parentSplitSize')?.setValue(this.parentSplitSize());
  }

  onChildSplitSizeChange(event: any): void {
    this.childSplitSize.set(event);
    this.kbForm().get('childSplitSize')?.setValue(this.childSplitSize());
  }

  onSliderInputChange(event: any, controlName: string): void {
    let inputValue: number;

    // Handle both native event and direct value
    if (event?.target) {
      inputValue = (event.target as HTMLInputElement).valueAsNumber;
    } else if (typeof event === 'number') {
      inputValue = event;
    } else {
      inputValue = Number(event);
    }

    // Cap to max value (you can also parametrize this if needed)
    const cappedValue = Math.min(inputValue, 20000);

    // Update local variable based on controlName
    if (controlName === 'parentSplitSize') {
      this.parentSplitSize.set(cappedValue);
    } else if (controlName === 'childSplitSize') {
      this.childSplitSize.set(cappedValue);
    } else if (controlName === 'splitSize') {
      this.splitSize.set(cappedValue);
    }
    // Update the corresponding form control
    this.kbForm().get(controlName)?.setValue(cappedValue);
  }

  isItemSelected(retriver: string) {
    return this.selectedRetriever() === retriver;
  }
}
