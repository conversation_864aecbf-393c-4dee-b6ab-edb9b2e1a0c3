<aava-default-card>
  <article class="kb__file--retriver d-flex flex-column">
    @if (isLoading()) {
      <!-- Skeleton loader -->
      <div class="file__retriver--content d-flex flex-column gap-3">
        <aava-skeleton [shape]="'rectangle'" [width]="'60%'" [height]="'40px'"></aava-skeleton>
        <aava-skeleton [shape]="'rounded'" [width]="'100%'" [height]="'300px'"></aava-skeleton>
      </div>
    } @else {
    <header>
      <h3 class="card-title">File Retriever</h3>
    </header>
    <div class="file__retriver--content d-flex flex-column" [formGroup]="kbForm()">
      <div class="form-field d-flex gap-2">
        @for(card of slitCardList(); track card; let i = $index) {
        <aava-list-items class="split__card--container" [customStyles]="{'height': '100%', 'cursor': 'pointer'}"
          [hoverBorder]="true" [hoverBg]="true" [outline]="true" [selected]="isItemSelected(card.retriver)"
          (itemClick)="selectRetriever(card.retriver)" [disabled]="isDisabled()">
          <div class="split__card--row">
            <div class="split__card d-flex">
              <aava-icon class="split__icon" iconSize="16" slot="icon-start" [iconName]="card.icon"
                [iconColor]="isItemSelected(card.retriver) ? 'var(--color-brand-primary)' : 'var(--icon-color)'">
              </aava-icon>
              <h4 class="split--title card-title">{{card.title}}</h4>
            </div>
            <div class="description">{{card.description}}</div>
          </div>
        </aava-list-items>
        }
      </div>

      <div class="form-field split-container">
        @if(selectedRetriever() === 'Default') {
        <div class="split--slider">
          <label class="label">Split Size</label>
          <div class="split-size-container">
            <aava-slider [required]="true" [min]="100" [max]="20000" [step]="1" [showTooltip]="false"
              (valueChange)="splitSizeChange($event)" [disabled]="isDisabled()" formControlName="splitSize">
            </aava-slider>
            <div class="value-box">
              <aava-textbox type="number" (change)="onSliderInputChange($event, 'splitSize')"
                formControlName="splitSize" [disabled]="isDisabled()" [fullWidth]="true"></aava-textbox>
            </div>
          </div>
        </div>
        <div class="split--slider"></div>
        } @else if(selectedRetriever() === 'Parent Doc') {
        <div class="split--slider">
          <label class="label">Parent Split Size</label>
          <div class="split-size-container">
            <aava-slider [required]="true" [min]="100" [max]="20000" [step]="1" [showTooltip]="false"
              (valueChange)="onParentSplitSizeChange($event)" [disabled]="isDisabled()"formControlName="parentSplitSize">
            </aava-slider>
            <div class="value-box">
              <aava-textbox type="number" (change)="onSliderInputChange($event, 'parentSplitSize')"
                formControlName="parentSplitSize" [disabled]="isDisabled()" [fullWidth]="true"></aava-textbox>
            </div>
          </div>
        </div>
        <div class="split--slider">
          <label class="label">Child Split Size</label>
          <div class="split-size-container">
            <aava-slider [required]="true" [min]="100" [max]="20000" [step]="1" [showTooltip]="false"
              (valueChange)="onChildSplitSizeChange($event)" [disabled]="isDisabled()" formControlName="childSplitSize">
            </aava-slider>
            <div class="value-box">
              <aava-textbox type="number" (change)="onSliderInputChange($event, 'childSplitSize')"
                formControlName="childSplitSize" [disabled]="isDisabled()" [fullWidth]="true"></aava-textbox>
            </div>
          </div>
        </div>
        }
      </div>

      @if(selectedRetriever() === 'Parent Doc' && parentSplitSize() <= childSplitSize()) { <div class="field-error">
        Parent split size must be greater than child split size.
    </div>
    }

    <div class="form-field embedding">
      <aava-select label="Embeding Model" formControlName="embeddingModel" placeholder="Select" size="sm"
        [required]="true" [disabled]="isDisabled()">
        @for(option of modelAreaOptions(); track option) {
        <aava-select-option [value]="option.id">{{option.label}}</aava-select-option>
        }
      </aava-select>
    </div>
    </div>
  }
  </article>
</aava-default-card>