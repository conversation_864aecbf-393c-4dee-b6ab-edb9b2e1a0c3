import { AavaButtonComponent, AavaSearchBarComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

import { ArtifactType } from '../../models/artifact.model';
import {
  GoodAtTagsService,
  GoodAtTag,
} from '../../services/good-at-tags.service';
import { RealmService, Realm } from '../../services/realm.service';
import { ArtifactFilterComponent } from '../artifact-filter';
import {
  CapabilityFilterComponent,
  CapabilityOption,
} from '../capability-filter';
import { PracticeAreaFilterComponent } from '../practice-area-filter';
import { RadioFilterComponent, RadioOption } from '../radio-filter';
import { SortFilterComponent } from '../sort-filter';

export interface FilterOption {
  id: string;
  label: string;
  value: string;
}

export interface FilterSection {
  id: string;
  title: string;
  options: FilterOption[];
}

export interface FilterSearchEvent {
  searchQuery: string;
  selectedFilters: { [sectionId: string]: string[] };
  selectedCapabilities: string[];
  selectedRealmCapabilities: string[]; // Adding selected realm capabilities
  selectedSort: string;
  selectedStatus: string;
  artifactType: ArtifactType | 'all'; // Using ArtifactType
  practiceArea: string; // Making this required instead of optional
  sampleFile: string; // Making this required instead of optional
}

@Component({
  selector: 'app-filter-search-component',
  standalone: true,
  imports: [
    CommonModule,
    AavaSearchBarComponent,
    AavaButtonComponent,
    ArtifactFilterComponent,
    CapabilityFilterComponent,
    PracticeAreaFilterComponent,
    RadioFilterComponent,
    SortFilterComponent,
  ],
  templateUrl: './filter-search-component.component.html',
  styleUrl: './filter-search-component.component.scss',
})
export class FilterSearchComponentComponent implements OnInit {
  @Input() searchPlaceholder = 'Search...';
  @Input() searchVariant: 'primary' | 'secondary' = 'primary';
  @Input() searchSize: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'xl';
  @Input() filterSections: FilterSection[] = [];
  @Input() showFilterButton = true;
  @Input() filterButtonIcon = 'list-filter';
  @Input() filterButtonVariant: 'primary' | 'secondary' = 'secondary';
  @Input() filterButtonSize: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'sm';
  @Input() outlined = true;
  @Input() pill = false;
  @Input() capabilitiesList: CapabilityOption[] = [];
  @Input() realmList: CapabilityOption[] = [];
  @Input() statusOptions: RadioOption[] = [
    { id: '1', label: 'Active', value: 'active' },
    { id: '2', label: 'Inactive', value: 'inactive' },
    { id: '3', label: 'Pending', value: 'pending' },
    { id: '4', label: 'Archived', value: 'archived' },
  ];

  @Input() sampleFileOptions: RadioOption[] = [
    { id: '1', label: 'True', value: 'true' },
    { id: '2', label: 'False', value: 'false' },
  ];

  @Output() searchChange = new EventEmitter<string>();
  @Output() filterChange = new EventEmitter<FilterSearchEvent>();
  @Output() filterModalToggle = new EventEmitter<boolean>();

  searchQuery = '';
  isFilterModalVisible = false;
  selectedFilters: { [sectionId: string]: string[] } = {};
  selectedCapabilities: string[] = [];
  selectedSort = '';
  selectedStatus = '';
  selectedSampleFile = '';
  selectedArtifactType: ArtifactType | 'all' = 'all';
  selectedPracticeArea: string = '';
  selectedRealmCapabilities: string[] = [];

  filterSectionList: FilterSection[] = [
    {
      id: 'category',
      title: 'Category',
      options: [
        { id: 'cat1', label: 'Category 1', value: 'cat1' },
        { id: 'cat2', label: 'Category 2', value: 'cat2' },
      ],
    },
  ];

  constructor(
    private goodAtTagsService: GoodAtTagsService,
    private realmService: RealmService
  ) {}

  ngOnInit() {
    // Initialize selected filters
    this.filterSections.forEach(section => {
      this.selectedFilters[section.id] = [];
    });

    // Fetch capabilities from GoodAtTagsService
    this.goodAtTagsService.getGoodAtTags().subscribe({
      next: (tags: GoodAtTag[]) => {
        this.capabilitiesList = tags.map(tag => ({
          id: String(tag.id),
          label: tag.name,
          value: tag.name.toLowerCase().replace(/\s+/g, '_'),
        }));
      },
      error: err => {
        // eslint-disable-next-line no-console
        console.error('Failed to load capabilities:', err);
      },
    });

    // Fetch realms from RealmService
    this.realmService.getRealms().subscribe({
      next: (realms: Realm[]) => {
        this.realmList = realms.map(realm => ({
          id: String(realm.realmId),
          label: realm.realmName,
          value: realm.realmName.toLowerCase().replace(/\s+/g, '_'),
        }));
      },
      error: err => {
        // eslint-disable-next-line no-console
        console.error('Failed to load realms:', err);
      },
    });
  }

  onSearchChange(query: string) {
    // eslint-disable-next-line no-console
    console.log('Search query changed to:', query);
    this.searchQuery = query;
    this.searchChange.emit(query);

    // Keep emitting search changes - search typically updates in real-time
    // but doesn't trigger full filter API call until Apply is clicked
    this.emitFilterChange();
  }

  onSearchValueChange(event: string | Event) {
    // Handle value change from the search bar component
    const query =
      typeof event === 'string'
        ? event
        : (event.target as HTMLInputElement)?.value || '';
    this.onSearchChange(query);
  }

  onSearchKeyup(event: KeyboardEvent) {
    // Handle keyup events to capture real-time typing
    const target = event.target as HTMLInputElement;
    if (target && target.value !== undefined) {
      this.onSearchChange(target.value);
    }
  }

  onFilterClick() {
    this.isFilterModalVisible = !this.isFilterModalVisible;
    this.filterModalToggle.emit(this.isFilterModalVisible);
  }

  onFilterModalClose() {
    this.isFilterModalVisible = false;
    this.filterModalToggle.emit(false);
  }

  onFilterOptionChange(sectionId: string, optionValue: string, event: Event) {
    const checkbox = event.target as HTMLInputElement;
    const sectionFilters = this.selectedFilters[sectionId] || [];

    if (checkbox.checked) {
      this.selectedFilters[sectionId] = [...sectionFilters, optionValue];
    } else {
      this.selectedFilters[sectionId] = sectionFilters.filter(
        value => value !== optionValue
      );
    }

    // Removed emitFilterChange - will only emit on Apply button click
  }

  onClearFilters() {
    this.filterSections.forEach(section => {
      this.selectedFilters[section.id] = [];
    });
    this.selectedCapabilities = [];
    this.selectedRealmCapabilities = [];
    this.selectedSort = '';
    this.selectedStatus = '';
    this.selectedSampleFile = '';
    this.selectedArtifactType = 'all';
    this.selectedPracticeArea = '';
    // Removed emitFilterChange - will only emit on Apply button click
  }

  onApplyFilters() {
    // Ensure we capture the latest search query value
    this.updateSearchQueryFromDOM();

    // Create comprehensive JSON with all selected filters
    const appliedFilters = {
      searchQuery: this.searchQuery,
      artifactType: this.selectedArtifactType,
      practiceArea: this.selectedPracticeArea,
      tags: [...this.selectedCapabilities],
      realmCapabilities: [...this.selectedRealmCapabilities],
      sort: this.selectedSort, // Changed from sortBy to sort
      status: this.selectedStatus,
      sampleFile: this.selectedSampleFile,
      generalFilters: { ...this.selectedFilters },
      timestamp: new Date().toISOString(),
      totalActiveFilters: this.getActiveFilterCount(),
    };

    this.isFilterModalVisible = false;
    this.filterModalToggle.emit(false);

    // Create filter event with all the necessary parameters for API call
    const event: FilterSearchEvent = {
      searchQuery: this.searchQuery,
      selectedFilters: { ...this.selectedFilters },
      selectedCapabilities: [...this.selectedCapabilities],
      selectedRealmCapabilities: [...this.selectedRealmCapabilities],
      selectedSort: this.selectedSort,
      selectedStatus: this.selectedStatus,
      artifactType: this.selectedArtifactType,
      practiceArea: this.selectedPracticeArea,
      sampleFile: this.selectedSampleFile,
    };

    // Log the event we're emitting
    // eslint-disable-next-line no-console
    console.log('Emitting FilterSearchEvent:', JSON.stringify(event, null, 2));

    // Emit the filter change event with the complete filter data
    this.filterChange.emit(event);
  }

  private updateSearchQueryFromDOM() {
    // Try to get the search value from the DOM as a fallback
    const searchInput = document.querySelector(
      '.filter-search__search-bar input'
    ) as HTMLInputElement;
    if (searchInput && searchInput.value !== this.searchQuery) {
      this.searchQuery = searchInput.value;
    }
  }

  isFilterSelected(sectionId: string, optionValue: string): boolean {
    return (this.selectedFilters[sectionId] || []).includes(optionValue);
  }

  getActiveFilterCount(): number {
    const generalFiltersCount = Object.values(this.selectedFilters).reduce(
      (total, filters) => total + filters.length,
      0
    );

    let activeCount =
      generalFiltersCount +
      this.selectedCapabilities.length +
      this.selectedRealmCapabilities.length;

    // Count non-default selections
    if (this.selectedArtifactType && this.selectedArtifactType !== 'all') {
      activeCount++;
    }
    if (this.selectedPracticeArea) {
      activeCount++;
    }
    if (this.selectedSort) {
      activeCount++;
    }
    if (this.selectedStatus) {
      activeCount++;
    }
    if (this.selectedSampleFile) {
      activeCount++;
    }

    return activeCount;
  }

  onCapabilitiesChange(capabilities: string[]) {
    this.selectedCapabilities = capabilities;
    console.log('Selected capabilities:', this.selectedCapabilities);
    // Removed emitFilterChange - will only emit on Apply button click
  }

  onSortChange(sortValue: string) {
    this.selectedSort = sortValue;
    // Removed emitFilterChange - will only emit on Apply button click
  }

  onStatusChange(statusValue: string) {
    this.selectedStatus = statusValue;
    // Removed emitFilterChange - will only emit on Apply button click
  }

  onSampleFileChange(sampleFile: string) {
    this.selectedSampleFile = sampleFile;
    // Removed emitFilterChange - will only emit on Apply button click
  }

  onArtifactTypeChange(artifactType: ArtifactType | 'all') {
    this.selectedArtifactType = artifactType;
    // Removed emitFilterChange - will only emit on Apply button click
  }

  onPracticeAreaChange(practiceArea: string) {
    this.selectedPracticeArea = practiceArea;
    // Removed emitFilterChange - will only emit on Apply button click
  }

  onRealmCapabilitiesChange(realmCapabilities: string[]) {
    this.selectedRealmCapabilities = realmCapabilities;
    // Removed emitFilterChange - will only emit on Apply button click
  }

  onOptionChange(event: {
    sectionId: string;
    optionValue: string;
    selected: boolean;
  }) {
    // eslint-disable-next-line no-console
    console.log('Option change event:', event);
    // Handle option change logic here
  }

  onSelectAll(sectionId: string) {
    // eslint-disable-next-line no-console
    console.log('Select all event:', sectionId);
    // Handle select all logic here
  }

  onClearSection(sectionId: string) {
    // eslint-disable-next-line no-console
    console.log('Clear section event:', sectionId);
    // Handle clear section logic here
  }

  onClearAllFilters() {
    // eslint-disable-next-line no-console
    console.log('Clear all filters clicked');
    this.filterSections.forEach(section => {
      this.selectedFilters[section.id] = [];
    });
    this.selectedCapabilities = [];
    this.selectedRealmCapabilities = [];
    this.selectedSort = '';
    this.selectedStatus = '';
    this.selectedSampleFile = '';
    this.selectedArtifactType = 'all';
    this.selectedPracticeArea = '';

    // We should still emit on clear all as it's an explicit user action
    this.emitFilterChange();
  }

  private emitFilterChange() {
    const event: FilterSearchEvent = {
      searchQuery: this.searchQuery,
      selectedFilters: { ...this.selectedFilters },
      selectedCapabilities: [...this.selectedCapabilities],
      selectedRealmCapabilities: [...this.selectedRealmCapabilities],
      selectedSort: this.selectedSort,
      selectedStatus: this.selectedStatus,
      artifactType: this.selectedArtifactType,
      practiceArea: this.selectedPracticeArea,
      sampleFile: this.selectedSampleFile,
    };

    // eslint-disable-next-line no-console
    console.log('Emitting filter change:', JSON.stringify(event, null, 2));
    this.filterChange.emit(event);
  }
}
