/* Filter Search Component Styles */
.filter-search {
  position: relative;

  /* Search Controls */
  &__controls {
    display: flex;
    align-items: center; /* Add subtle dividers between components on larger screens */
    @media (min-width: 1025px) {
      app-artifact-filter {
        border-right: 1px solid var(--color-border-default, #d1d5db);
        padding-right: 16px;
      }

      app-practice-area-filter {
        border-right: 1px solid var(--color-border-default, #d1d5db);
        padding-left: 8px;
        padding-right: 16px;
      }

      app-capability-filter {
        padding-left: 8px;
      }
    }

    @media (min-width: 769px) and (max-width: 1024px) {
      app-artifact-filter {
        border-right: 1px solid var(--color-border-default, #d1d5db);
        padding-right: 16px;
      }

      app-practice-area-filter {
        padding-left: 8px;
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      width: 100%;
      gap: 16px;
    }
  }

  &__search-bar {
    flex: 1;
    min-width: 0;

    @media (max-width: 768px) {
      width: 100%;
    }
  }

  &__filter-button {
    position: relative;
    flex-shrink: 0;
    transition: all 0.2s ease;

    &--active {
      color: var(--color-text-on-primary) !important;
      border-color: var(--color-brand-primary) !important;
    }

    @media (max-width: 768px) {
      align-self: stretch;
    }
  }

  &__filter-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--color-background-error);
    color: var(--color-text-on-primary);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    z-index: 1;
  }

  /* Modal backdrop */
  .modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1001;
    opacity: 0;
    animation: backdropFadeIn 0.2s ease-out forwards;
    display: block;
  }

  @keyframes backdropFadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Modal Content Styles */
  .filter-modal-content {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 9999;
    min-width: 600px; /* Minimum width to ensure content fits */
    background: var(--color-background-primary, #ffffff);
    border: 1px solid var(--color-border-default, #d1d5db);
    border-radius: 8px;
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 24px;

    margin-top: 8px;

    /* Animation */
    animation: modalFadeIn 0.2s ease-out;

    /* Responsive positioning */
    @media (max-width: 768px) {
      position: fixed;
      top: 50%;
      left: 50%;
      right: auto;
      transform: translate(-50%, -50%);
      width: 90vw;
      min-width: unset; /* Remove min-width on mobile */
      margin-top: 0;
    }

    /* Medium screens adjustment */
    @media (max-width: 1024px) and (min-width: 769px) {
      min-width: 500px; /* Reduced min-width for medium screens */
    }
  }

  /* Filter Components Container */
  .filter-components-container {
    display: flex;
    flex-wrap: nowrap;
    gap: 16px;
    align-items: flex-start;

    /* Set consistent width for all filter components */
    app-artifact-filter,
    app-practice-area-filter,
    app-capability-filter,
    app-sort-filter,
    app-radio-filter {
      width: 12.5rem; /* 200px */
      // flex-shrink: 0;
      // border-right: 1px solid var(--color-border-default);
    }

    /* Responsive layout */
    @media (max-width: 1024px) {
      flex-wrap: wrap;
      gap: 16px;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;

      app-artifact-filter,
      app-practice-area-filter,
      app-capability-filter,
      app-sort-filter,
      app-radio-filter {
        width: 100%; /* Full width on mobile */
        border-right: none; /* Remove border on mobile */
      }
    }

    /* Add subtle dividers between components on larger screens */
    @media (min-width: 769px) {
      /* Borders are now handled by the general selector above */
    }
  }

  /* Modal animation */
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Mobile modal animation */
  @media (max-width: 768px) {
    @keyframes modalFadeIn {
      from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
      }
    }
  }

  &__section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--color-text-primary);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  &__options {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  &__option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--color-surface-subtle-hover);
    }

    input[type='checkbox'] {
      width: 16px;
      height: 16px;
      accent-color: var(--color-brand-primary);
      cursor: pointer;
      margin: 0;
    }

    span {
      font-size: 14px;
      color: var(--color-text-secondary);
      cursor: pointer;
      flex: 1;
      line-height: 1.4;
    }
  }

  &__modal-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px 0 0;
    border-top: 1px solid var(--color-border-default);

    @media (max-width: 480px) {
      flex-direction: column-reverse;
      gap: 8px;

      aava-button {
        width: 100%;
      }
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    &__filter-button {
      transition: none;
    }
  }
}
