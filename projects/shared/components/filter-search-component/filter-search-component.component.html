<div class="filter-search">
  <!-- Search Controls -->
  <div class="filter-search__controls">
    <aava-search-bar
      #searchBar
      variant="primary"
      size="sm"
      [placeholder]="searchPlaceholder"
      [value]="searchQuery"
      (searchClick)="onSearchChange($event)"
      (valueChange)="onSearchValueChange($event)"
      (keyup)="onSearchKeyup($event)"
      class="filter-search__search-bar"
    ></aava-search-bar>

    <!-- filter button -->
    <aava-button
      *ngIf="showFilterButton"
      [iconName]="filterButtonIcon"
      iconPosition="only"
      [variant]="filterButtonVariant"
      [outlined]="outlined"
      [size]="filterButtonSize"
      [pill]="pill"
      [ariaLabel]="'Open filters (' + getActiveFilterCount() + ' active)'"
      (click)="onFilterClick()"
      class="filter-search__filter-button"
      [class.filter-search__filter-button--active]="getActiveFilterCount() > 0"
    >
      <span
        *ngIf="getActiveFilterCount() > 0"
        class="filter-search__filter-badge"
      >
        {{ getActiveFilterCount() }}
      </span>
    </aava-button>
  </div>

  <!-- Filter Modal -->

  <div class="filter-modal-content" *ngIf="isFilterModalVisible">
    <div class="filter-components-container">
      <!-- Artifacts -->
      <app-artifact-filter
        [selectedType]="selectedArtifactType"
        (typeChange)="onArtifactTypeChange($event)"
      ></app-artifact-filter>

      <!-- Practice Area -->
      <app-practice-area-filter
        [selectedArea]="selectedPracticeArea"
        (areaChange)="onPracticeAreaChange($event)"
      ></app-practice-area-filter>

      <!-- Capability Tags -->
      <app-capability-filter
        [selectedCapabilities]="selectedCapabilities"
        [capabilitiesList]="capabilitiesList"
        (capabilitiesChange)="onCapabilitiesChange($event)"
        title="Capability Tags"
      >
      </app-capability-filter>

      <!-- Realm -->
      <app-capability-filter
        [selectedCapabilities]="selectedRealmCapabilities"
        [capabilitiesList]="realmList"
        (capabilitiesChange)="onRealmCapabilitiesChange($event)"
        title="Realm"
      >
      </app-capability-filter>

      <!-- Sort and Status -->
      <app-sort-filter
        [selectedSort]="selectedSort"
        [title]="'Sort by'"
        [disabled]="false"
        (sortChange)="onSortChange($event)"
      >
      </app-sort-filter>
      <!-- <app-radio-filter
        [title]="'Status'"
        [options]="statusOptions"
        [selectedValue]="selectedStatus"
        [disabled]="false"
        (valueChange)="onStatusChange($event)"
      >
      </app-radio-filter> -->

      <!-- Sample File -->
      <app-radio-filter
        [title]="'Sample File'"
        [options]="sampleFileOptions"
        [selectedValue]="selectedSampleFile"
        [disabled]="false"
        (valueChange)="onSampleFileChange($event)"
      >
      </app-radio-filter>
    </div>

    <!-- Filter Modal Actions -->
    <div class="filter-search__modal-actions">
      <aava-button
        variant="secondary"
        size="sm"
        (click)="onFilterModalClose()"
        label="Cancel"
      >
      </aava-button>
      <aava-button
        variant="primary"
        size="sm"
        (click)="onApplyFilters()"
        label="Apply"
      >
      </aava-button>
    </div>
  </div>
</div>
