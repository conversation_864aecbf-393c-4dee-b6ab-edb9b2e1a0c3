import { AavaButtonComponent, AavaIconComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  signal,
  computed,
  effect,
  ViewChild,
  ElementRef,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { IPoint } from '@foblex/2d';
import {
  FFlowModule,
  FCreateNodeEvent,
  FCanvasComponent,
  FMoveNodesEvent,
  FZoomDirective,
} from '@foblex/flow';
import { generateGuid } from '@foblex/utils';

import { AgentCardNodeComponent } from '../agent-card-node/agent-card-node.component';

export interface CanvasNode {
  id: string;
  position: { x: number; y: number };
  data: any;
  selected?: boolean;
  type?: string;
  disabled?: boolean;
}

export interface CanvasConnection {
  id: string;
  source: string;
  target: string;
  outputId: string;
  inputId: string;
  type?: string;
  animated?: boolean;
}

export interface CanvasViewport {
  x: number;
  y: number;
  zoom: number;
}

export interface MainCanvasConfig {
  readonly enablePan?: boolean;
  readonly enableZoom?: boolean;
  readonly enableSelection?: boolean;
  readonly enableConnections?: boolean;
  readonly gridSize?: number;
  readonly snapToGrid?: boolean;
  readonly minZoom?: number;
  readonly maxZoom?: number;
  readonly defaultZoom?: number;
}

@Component({
  selector: 'app-main-canvas-foblex',
  standalone: true,
  imports: [
    CommonModule,
    FFlowModule,
    AavaButtonComponent,
    AavaIconComponent,
  ],
  templateUrl: './main-canvas-foblex.component.html',
  styleUrls: ['./main-canvas-foblex.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MainCanvasFoblexComponent implements OnInit, OnDestroy {
  // Inputs
  readonly initialNodes = input<CanvasNode[]>([]);
  readonly connections = input<CanvasConnection[]>([]);
  readonly viewport = input<CanvasViewport>({ x: 0, y: 0, zoom: 1 });
  readonly disabled = input<boolean>(false);
  readonly config = input<MainCanvasConfig>({});

  // Internal state (like working example)
  protected nodes = signal<CanvasNode[]>([]);

  // Outputs
  readonly nodeCreated = output<CanvasNode>();
  readonly nodeSelected = output<string>();
  readonly nodeDoubleClicked = output<string>();
  readonly nodeMoved = output<{
    nodeId: string;
    position: { x: number; y: number };
  }>();
  readonly nodeRemoved = output<string>();
  readonly connectionCreated = output<{ source: string; target: string }>();
  readonly connectionDeleted = output<string>();
  readonly viewportChanged = output<CanvasViewport>();
  readonly canvasClicked = output<{ x: number; y: number }>();

  @ViewChild('flowContainer', { static: true })
  flowContainer!: ElementRef<HTMLDivElement>;

  @ViewChild(FCanvasComponent, { static: true })
  protected fCanvas!: FCanvasComponent;

  @ViewChild(FZoomDirective, { static: true })
  protected fZoom!: FZoomDirective;

  // Internal state
  protected selectedNodes = signal<Set<string>>(new Set());
  protected currentViewport = signal<CanvasViewport>({ x: 0, y: 0, zoom: 1 });

  // Computed values
  protected flowConfig = computed(() => ({
    draggable: !this.disabled(),
    zoomable: this.config().enableZoom !== false,
    pannable: this.config().enablePan !== false,
    selectable: this.config().enableSelection !== false,
    gridSize: this.config().gridSize || 20,
    snapToGrid: this.config().snapToGrid || false,
    minZoom: this.config().minZoom || 0.1,
    maxZoom: this.config().maxZoom || 3,
    defaultZoom: this.config().defaultZoom || 1,
  }));

  constructor() {
    // Watch for changes in initialNodes and update internal nodes
    effect(() => {
      const newNodes = this.initialNodes();
      console.log(
        'Effect: initialNodes changed, updating local nodes:',
        newNodes.length,
        'nodes'
      );
      this.nodes.set(newNodes);
    });

    // Watch for changes in connections
    effect(() => {
      // Connections are handled by Foblex Flow directly
      // We just need to ensure the canvas is updated
      console.log(
        'Effect: Connections updated:',
        this.connections().length,
        'connections'
      );
    });
  }

  ngOnInit(): void {
    this.currentViewport.set(this.viewport());
    // Initialize nodes from input (like working example)
    this.nodes.set(this.initialNodes());
  }

  ngOnDestroy(): void {
    // Cleanup handled by Foblex Flow
  }

  protected onFlowLoaded(event: any): void {
    this.fCanvas?.resetScaleAndCenter(false);
    console.log('Foblex Flow loaded:', event);
  }

  protected onMoveNodes(event: FMoveNodesEvent): void {
    console.log('Node moved:', event);
    // Emit node movement events for each moved node
    event.fNodes.forEach(node => {
      this.nodeMoved.emit({
        nodeId: node.id,
        position: node.position,
      });
    });
  }

  protected onNodePositionChange(position: IPoint): void {
    console.log('Node position changed:', position);
    // Handle node position change.
  }

  protected onCreateNode(event: FCreateNodeEvent): void {
    if (this.disabled()) return;

    console.log('Node created from external item:', event);
    console.log('Event data:', event.data);

    // Only create nodes for actual external drops (not internal moves)
    // Check if this is a real external item drop by looking at the data
    if (!event.data || !event.data.type) {
      console.log('Skipping node creation - not an external item drop');
      return;
    }

    const newNodeId = generateGuid();
    const newNode: CanvasNode = {
      position: event.rect,
      id: newNodeId,
      data: event.data,
    };

    // Add the new node
    this.nodes.set([...this.nodes(), newNode]);

    // Emit node creation event to parent (this will save to history)
    this.nodeCreated.emit(newNode);

    // Auto-create connection to previous node if there are existing nodes
    // This will be a separate operation that gets saved to history separately
    const currentNodes = this.nodes();
    if (currentNodes.length > 1) {
      const previousNode = currentNodes[currentNodes.length - 2]; // Get the node before the new one

      // Create automatic connection
      const newConnection = {
        source: previousNode.id,
        target: newNodeId,
        outputId: `output-${previousNode.id}`,
        inputId: `input-${newNodeId}`,
      };

      console.log('Auto-creating connection:', newConnection);

      // Emit connection creation event to parent (this will save to history as separate step)
      this.connectionCreated.emit({
        source: newConnection.source,
        target: newConnection.target,
      });
    }
  }

  // Foblex Flow event handlers

  protected onNodeSelected(event: any): void {
    if (this.disabled()) return;

    const nodeId = event.nodeId;

    // Update selection state
    const current = this.selectedNodes();
    const newSelection = new Set(current);
    if (newSelection.has(nodeId)) {
      newSelection.delete(nodeId);
    } else {
      newSelection.add(nodeId);
    }
    this.selectedNodes.set(newSelection);

    this.nodeSelected.emit(nodeId);
  }

  protected onNodeDoubleClicked(event: any): void {
    if (this.disabled()) return;

    const nodeId = event.nodeId;
    this.nodeDoubleClicked.emit(nodeId);
  }

  protected onViewportChanged(event: any): void {
    if (this.disabled()) return;

    const newViewport = {
      x: event.viewport?.x || 0,
      y: event.viewport?.y || 0,
      zoom: event.viewport?.zoom || 1,
    };

    this.currentViewport.set(newViewport);
    this.viewportChanged.emit(newViewport);
  }

  protected onCanvasClicked(event: any): void {
    if (this.disabled()) return;

    const x = event.canvas?.x || 0;
    const y = event.canvas?.y || 0;

    this.canvasClicked.emit({ x, y });
    this.selectedNodes.set(new Set());
  }

  protected onRemoveNode(nodeId: string): void {
    if (this.disabled()) return;
    console.log('current nodes', this.nodes());
    console.log('current connections', this.connections());
    // Simply emit the node removed event
    this.nodeRemoved.emit(nodeId);
  }

  // Helper methods

  protected isNodeSelected(nodeId: string): boolean {
    return this.selectedNodes().has(nodeId);
  }

  // Zoom controls
  protected zoomIn(): void {
    this.fZoom.zoomIn();
    console.log('Zoom in');
  }

  protected zoomOut(): void {
    this.fZoom.zoomOut();
    console.log('Zoom out');
  }

  protected resetZoom(): void {
    this.fCanvas.resetScaleAndCenter(false);
    console.log('Reset zoom');
  }

  protected fitToView(): void {
    this.fCanvas.resetScaleAndCenter(true);
    console.log('Fit to view');
  }
}
