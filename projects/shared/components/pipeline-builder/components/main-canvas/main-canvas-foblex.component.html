<div #flowContainer class="main-canvas-foblex" [class.disabled]="disabled()">
  <f-flow
    fDraggable
    (fLoaded)="onFlowLoaded($event)"
    (fCreateNode)="onCreateNode($event)"
    (fMoveNodes)="onMoveNodes($event)"
  >
    <!-- <f-background>
      <f-circle-pattern></f-circle-pattern>
    </f-background> -->
    <f-canvas fZoom>
      <!-- Nodes -->
      @for (node of nodes(); track node.id) {
        <div
          fNode
          [fNodeId]="node.id"
          [fNodePosition]="node.position"
          (fNodePositionChange)="onNodePositionChange($event)"
          fDragHandle
          class="canvas-node"
        >
          <!-- Hidden connection points for Foblex Flow -->
          <div
            fNodeInput
            [fInputId]="'input-' + node.id"
            class="hidden-connection-point"
          ></div>

          <!-- Node content -->
          <div class="agent-card-node">
            <!-- Icon wrapper with light blue background -->
            <div class="icon-wrapper">
              <aava-icon iconName="bot" size="sm" iconColor="#0084FF">
              </aava-icon>
            </div>

            <!-- Agent name -->
            <div class="agent-name">
              {{ node.data.name }}
            </div>

            <!-- Remove button -->
            <aava-button
              class="remove-button"
              (click)="onRemoveNode(node.id)"
              type="button"
              [attr.aria-label]="'Remove ' + node.data.name"
              [iconPosition]="'only'"
              [iconName]="'x'"
              [size]="'xs'"
              [iconColor]="'#5f6368'"
            >
            </aava-button>
          </div>

          <div
            fNodeOutput
            [fOutputId]="'output-' + node.id"
            class="hidden-connection-point"
          ></div>
        </div>
      }

      <!-- Connections -->
      @for (connection of connections(); track connection.id) {
        <f-connection
          [fConnectionId]="connection.id"
          [fOutputId]="connection.outputId"
          [fInputId]="connection.inputId"
          fType="bezier"
        ></f-connection>
      }
    </f-canvas>
  </f-flow>

  <!-- Zoom Controls (Optional - Foblex Flow has built-in controls) -->
  <div class="zoom-controls" *ngIf="config().enableZoom">
    <aava-button
      variant="primary"
      [outlined]="true"
      size="sm"
      (click)="zoomIn()"
      iconName="zoom-in"
      [iconPosition]="'only'"
      [disabled]="disabled()"
    >
    </aava-button>

    <aava-button
      variant="primary"
      [outlined]="true"
      size="sm"
      (click)="zoomOut()"
      iconName="zoom-out"
      [iconPosition]="'only'"
      [disabled]="disabled()"
    >
    </aava-button>

    <!-- <aava-button
      variant="secondary"
      size="sm"
      (click)="resetZoom()"
      iconName="refresh"
      [iconPosition]="'only'"
      [disabled]="disabled()"
      title="Reset Zoom"
    >
    </aava-button>

    <aava-button
      variant="secondary"
      size="sm"
      (click)="fitToView()"
      iconName="maximize"
      [iconPosition]="'only'"
      [disabled]="disabled()"
      title="Fit to View"
    >
    </aava-button> -->
  </div>
</div>
