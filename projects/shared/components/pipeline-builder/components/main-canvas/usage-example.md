# Main Canvas Component Usage

## Overview

The `MainCanvasComponent` is the core canvas component for the pipeline builder that provides drag & drop functionality, pan/zoom controls, node management, and connection handling. It's designed to work within the `PipelineCanvasPanelComponent` and provides an infinite, interactive canvas experience.

## Key Features

- **Infinite Canvas**: Pan and zoom with no boundaries
- **Drag & Drop**: Move nodes around the canvas
- **Multi-Selection**: Select multiple nodes with Ctrl/Cmd + click
- **Connection Management**: Create and manage connections between nodes
- **Zoom Controls**: Built-in zoom in/out, fit to view, reset view
- **Grid System**: Optional grid background with snap-to-grid
- **Keyboard Shortcuts**: Delete, Escape, Ctrl+A, Ctrl+C, Ctrl+V
- **Touch Support**: Full touch gesture support for mobile devices
- **Responsive Design**: Adapts to different screen sizes

## Basic Usage

```typescript
import {
  MainCanvasComponent,
  CanvasNode,
  CanvasConnection,
} from './main-canvas';

// In your component
const nodes: CanvasNode[] = [
  {
    id: 'node-1',
    type: 'agent',
    data: {
      id: 'agent-1',
      name: 'Data Processor',
      tags: ['AI-powered automation'],
      icon: 'sparkles',
      type: 'agent',
    },
    position: { x: 100, y: 100 },
    selected: false,
    disabled: false,
  },
];

const connections: CanvasConnection[] = [
  {
    id: 'conn-1',
    source: 'node-1',
    target: 'node-2',
    type: 'default',
    animated: false,
  },
];
```

```html
<app-main-canvas
  [nodes]="nodes"
  [connections]="connections"
  [viewport]="viewport"
  [disabled]="false"
  [config]="canvasConfig"
  (nodeMoved)="onNodeMoved($event)"
  (nodeSelected)="onNodeSelected($event)"
  (nodeDoubleClicked)="onNodeDoubleClicked($event)"
  (connectionCreated)="onConnectionCreated($event)"
  (connectionDeleted)="onConnectionDeleted($event)"
  (viewportChanged)="onViewportChanged($event)"
  (canvasClicked)="onCanvasClicked($event)"
>
</app-main-canvas>
```

## Component Features

### Inputs

- `nodes: CanvasNode[]` (optional, default: []) - Array of nodes to display
- `connections: CanvasConnection[]` (optional, default: []) - Array of connections between nodes
- `viewport: CanvasViewport` (optional, default: {x: 0, y: 0, zoom: 1}) - Current viewport state
- `disabled: boolean` (optional, default: false) - Disable all interactions
- `config: MainCanvasConfig` (optional) - Configuration object for customization

### Outputs

- `nodeMoved: {nodeId: string, position: {x: number, y: number}}` - Emitted when a node is moved
- `nodeSelected: {nodeId: string, selected: boolean}` - Emitted when a node is selected/deselected
- `nodeDoubleClicked: {nodeId: string}` - Emitted when a node is double-clicked
- `connectionCreated: {source: string, target: string}` - Emitted when a connection is created
- `connectionDeleted: {connectionId: string}` - Emitted when a connection is deleted
- `viewportChanged: CanvasViewport` - Emitted when viewport changes (pan/zoom)
- `canvasClicked: {x: number, y: number}` - Emitted when canvas is clicked

### MainCanvasConfig Interface

```typescript
interface MainCanvasConfig {
  readonly enablePan?: boolean; // Enable panning (default: true)
  readonly enableZoom?: boolean; // Enable zooming (default: true)
  readonly enableSelection?: boolean; // Enable multi-selection (default: true)
  readonly enableConnections?: boolean; // Enable connections (default: true)
  readonly gridSize?: number; // Grid size in pixels (default: 20)
  readonly snapToGrid?: boolean; // Snap nodes to grid (default: false)
  readonly minZoom?: number; // Minimum zoom level (default: 0.1)
  readonly maxZoom?: number; // Maximum zoom level (default: 3)
  readonly defaultZoom?: number; // Default zoom level (default: 1)
  readonly backgroundColor?: string; // Canvas background color
  readonly gridColor?: string; // Grid line color
  readonly selectionColor?: string; // Selection box color
}
```

## Interaction Patterns

### Panning

- **Mouse**: Click and drag on empty canvas
- **Touch**: Single finger drag
- **Keyboard**: Arrow keys (if implemented)

### Zooming

- **Mouse**: Mouse wheel
- **Touch**: Pinch gesture
- **Controls**: Zoom in/out buttons, fit to view, reset view

### Node Selection

- **Single Select**: Click on node
- **Multi-Select**: Ctrl/Cmd + click on nodes
- **Select All**: Ctrl/Cmd + A
- **Clear Selection**: Click on empty canvas or Escape key

### Node Movement

- **Drag**: Click and drag a node
- **Snap to Grid**: Enable in config for precise positioning

## Example Implementation

```typescript
export class PipelineBuilderComponent {
  nodes: CanvasNode[] = [];
  connections: CanvasConnection[] = [];
  viewport: CanvasViewport = { x: 0, y: 0, zoom: 1 };
  disabled = false;

  canvasConfig: MainCanvasConfig = {
    enablePan: true,
    enableZoom: true,
    enableSelection: true,
    enableConnections: true,
    gridSize: 20,
    snapToGrid: false,
    minZoom: 0.1,
    maxZoom: 3,
    defaultZoom: 1,
    backgroundColor: 'var(--surface-bg, #f8f9fa)',
    gridColor: 'var(--border-color, #e5e7eb)',
    selectionColor: 'var(--primary-color, #3b82f6)',
  };

  onNodeMoved(event: {
    nodeId: string;
    position: { x: number; y: number };
  }): void {
    const nodeIndex = this.nodes.findIndex(n => n.id === event.nodeId);
    if (nodeIndex !== -1) {
      this.nodes[nodeIndex] = {
        ...this.nodes[nodeIndex],
        position: event.position,
      };
    }
  }

  onNodeSelected(event: { nodeId: string; selected: boolean }): void {
    const nodeIndex = this.nodes.findIndex(n => n.id === event.nodeId);
    if (nodeIndex !== -1) {
      this.nodes[nodeIndex] = {
        ...this.nodes[nodeIndex],
        selected: event.selected,
      };
    }
  }

  onNodeDoubleClicked(event: { nodeId: string }): void {
    console.log('Node double-clicked:', event.nodeId);
    // Open node configuration modal, etc.
  }

  onConnectionCreated(event: { source: string; target: string }): void {
    const newConnection: CanvasConnection = {
      id: `conn-${Date.now()}`,
      source: event.source,
      target: event.target,
      type: 'default',
      animated: false,
    };
    this.connections.push(newConnection);
  }

  onConnectionDeleted(event: { connectionId: string }): void {
    this.connections = this.connections.filter(
      c => c.id !== event.connectionId
    );
  }

  onViewportChanged(event: CanvasViewport): void {
    this.viewport = event;
  }

  onCanvasClicked(event: { x: number; y: number }): void {
    console.log('Canvas clicked at:', event.x, event.y);
  }
}
```

## Advanced Configuration

### Custom Grid

```typescript
const customGridConfig: MainCanvasConfig = {
  gridSize: 50,
  snapToGrid: true,
  gridColor: '#e0e0e0',
  backgroundColor: '#f5f5f5',
};
```

### Disabled Interactions

```typescript
const readOnlyConfig: MainCanvasConfig = {
  enablePan: false,
  enableZoom: false,
  enableSelection: false,
  enableConnections: false,
};
```

### High Performance

```typescript
const performanceConfig: MainCanvasConfig = {
  enableConnections: false, // Disable connections for better performance
  gridSize: 0, // Disable grid for better performance
  minZoom: 0.5, // Limit zoom range
  maxZoom: 2,
};
```

## Integration with Pipeline Canvas Panel

```html
<!-- In pipeline-canvas-panel.component.html -->
<div class="canvas-section" [ngStyle]="canvasStyles()">
  <app-main-canvas
    [nodes]="canvasNodes"
    [connections]="canvasConnections"
    [viewport]="canvasViewport"
    [disabled]="disabled()"
    [config]="canvasConfig"
    (nodeMoved)="onNodeMoved($event)"
    (nodeSelected)="onNodeSelected($event)"
    (nodeDoubleClicked)="onNodeDoubleClicked($event)"
    (connectionCreated)="onConnectionCreated($event)"
    (connectionDeleted)="onConnectionDeleted($event)"
    (viewportChanged)="onViewportChanged($event)"
    (canvasClicked)="onCanvasClicked($event)"
  >
  </app-main-canvas>
</div>
```

## Keyboard Shortcuts

- **Delete/Backspace**: Delete selected nodes
- **Escape**: Clear selection
- **Ctrl/Cmd + A**: Select all nodes
- **Ctrl/Cmd + C**: Copy selected nodes
- **Ctrl/Cmd + V**: Paste nodes

## Touch Gestures

- **Single Touch**: Pan canvas
- **Pinch**: Zoom in/out
- **Tap**: Select node
- **Long Press**: Context menu (if implemented)

## Performance Considerations

- **Large Node Count**: Consider virtualization for 100+ nodes
- **Complex Connections**: Limit animated connections
- **Mobile Devices**: Reduce grid size and disable some features
- **Memory Usage**: Clean up event listeners on destroy

## Accessibility

- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: ARIA labels and roles
- **Focus Management**: Proper focus handling
- **High Contrast**: Support for high contrast themes

This component provides a robust foundation for building interactive pipeline builders with professional-grade canvas functionality.
