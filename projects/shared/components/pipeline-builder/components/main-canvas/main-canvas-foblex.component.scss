.main-canvas-foblex {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  user-select: none;

  &.disabled {
    cursor: not-allowed;
    pointer-events: none;
  }

  // Foblex Flow container
  f-flow {
    width: 100%;
    height: 100%;
    display: block;
  }

  f-canvas {
    width: 100%;
    height: 100%;
    display: block;
  }

  // .canvas-node {
  //   position: relative;
  //   cursor: grab;
  //   z-index: 10;
  //   transition: transform 0.1s ease-out;

  //   &.selected {
  //     z-index: 20;
  //   }

  //   &:hover {
  //     z-index: 15;
  //     transform: translateY(-2px);
  //   }

  //   &:active {
  //     cursor: grabbing;
  //   }

  //   // Foblex Flow node styling
  //   &[fNode] {
  //     border-radius: 8px;
  //     background: white;
  //     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  //     transition: all 0.2s ease;

  //     &:hover {
  //       box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  //       transform: translateY(-2px);
  //     }

  //     &.selected {
  //       box-shadow: 0 0 0 2px #3b82f6;
  //       transform: translateY(-2px);
  //     }
  //   }
  // }

  .zoom-controls {
    position: absolute;
    top: 16px;
    right: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 100;

    .zoom-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: white;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:hover:not(:disabled) {
        background: #f8f9fa;
        border-color: #007bff;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:active:not(:disabled) {
        transform: translateY(1px);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      svg {
        color: #666;
      }
    }
  }

  .zoom-level {
    position: absolute;
    bottom: 16px;
    right: 16px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
    color: #666;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 100;
  }
}

// Foblex Flow specific styles
f-flow {
  --f-flow-background: #f8f9fa;
  --f-flow-grid-color: #e5e7eb;
  --f-flow-selection-color: #3b82f6;
  --f-flow-connection-color: #666;
  --f-flow-connection-width: 2px;
}

// Circle background pattern
::ng-deep f-flow {
  .f-background {
    line {
      stroke: var(--background-element-color, rgba(0, 0, 0, 0.1));
    }

    circle {
      fill: var(--background-element-color, rgba(0, 0, 0, 0.1));
    }
  }
}

// Node connectors
.f-node-input,
.f-node-output {
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 0.2px solid var(--node-border-color, #ddd);
  background-color: var(--node-background-color, #ffffff);
  z-index: 11;

  &.left {
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
  }

  &.right {
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
  }
}

.f-node-input {
  border-radius: 4px;
}

// Connection styling
f-connection {
  --f-connection-color: #666;
  --f-connection-width: 2px;
  --f-connection-hover-color: #3b82f6;
  --f-connection-hover-width: 3px;
}

// Node styling
[fNode] {
  --f-node-border-radius: 8px;
  --f-node-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --f-node-hover-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  --f-node-selected-box-shadow: 0 0 0 2px #3b82f6;
}

// Canvas node styling
.canvas-node {
  position: relative;
  overflow: visible;
}

// Agent card node styling
.agent-card-node {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 8px 12px;
  min-width: 120px;
  max-width: 200px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  .icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: #e6f3ff;
    border-radius: 8px;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .agent-name {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: #3c4043;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 8px;
  }

  .remove-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: none;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    flex-shrink: 0;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f1f3f4;
    }

    &:active {
      background-color: #e8eaed;
    }
  }
}

// Hidden connection points - required by Foblex Flow but invisible to users
.hidden-connection-point {
  position: absolute;
  width: 8px;
  height: 8px;
  opacity: 0;
  pointer-events: none;
  z-index: -1;
  border-radius: 50%;
}

// Position input points on the left side of nodes
.hidden-connection-point[fNodeInput] {
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
}

// Position output points on the right side of nodes
.hidden-connection-point[fNodeOutput] {
  right: -4px;
  top: 50%;
  transform: translateY(-50%);
}

// CSS Variables for theming
::ng-deep :root {
  --background-element-color: rgba(0, 0, 0, 0.1);
  --node-background-color: #ffffff;
  --node-border-color: rgba(60, 60, 67, 0.36);
  --node-color: rgba(60, 60, 67, 0.78);
  --primary-color: #007bff;
  --primary-hover: #0056b3;
}
