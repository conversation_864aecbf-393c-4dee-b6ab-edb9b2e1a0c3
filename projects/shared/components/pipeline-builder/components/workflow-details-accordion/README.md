# Workflow Details Accordion Component

A configurable accordion component for workflow configuration with optional fields and Aava UI components.

## Features

- **Accordion-style UI** with collapsible header and body
- **Configurable fields** - show/hide any field via config
- **Aava UI components** - uses aava-textbox, aava-select, aava-toggle
- **Numerical field validation** - proper min/max/step values for numerical inputs
- **Responsive design** - grid layout that adapts to screen size
- **TypeScript interfaces** - fully typed data and configuration

## Usage

### Basic Usage

```typescript
import {
  WorkflowDetailsAccordionComponent,
  WorkflowDetailsData,
  WorkflowDetailsConfig,
} from './workflow-details-accordion';

@Component({
  template: `
    <app-workflow-details-accordion
      [data]="workflowData()"
      [config]="workflowConfig()"
      (nameChanged)="onNameChange($event)"
      (descriptionChanged)="onDescriptionChange($event)"
      (temperatureChanged)="onTemperatureChange($event)"
      (modelChanged)="onModelChange($event)"
      (enableManagerLlmChanged)="onEnableManagerLlmChange($event)"
    ></app-workflow-details-accordion>
  `,
})
export class MyComponent {
  readonly workflowData = signal<WorkflowDetailsData>({
    name: 'My Workflow',
    description: 'Workflow description',
    temperature: '0.7',
    selectedModel: 'gpt-4',
    enableManagerLlm: true,
  });

  readonly workflowConfig = signal<WorkflowDetailsConfig>({
    title: 'Workflow Settings',
    showName: true,
    showDescription: true,
    showTemperature: true,
    showModel: true,
    showEnableManagerLlm: true,
  });
}
```

### Custom Configuration

```typescript
// Show only specific fields
const customConfig: WorkflowDetailsConfig = {
  title: 'Basic Settings',
  showName: true,
  showDescription: true,
  showTemperature: true,
  showTopP: false, // Hide Top P
  showMaxRpm: false, // Hide Max RPM
  showMaxToken: false, // Hide Max Token
  showMaxIteration: false, // Hide Max Iteration
  showMaxExecutionTime: false, // Hide Max Execution Time
  showModel: true,
  showEnableManagerLlm: true,
};
```

### Disabled State

```typescript
// Disable the entire accordion
<app-workflow-details-accordion
  [data]="workflowData()"
  [config]="workflowConfig()"
  [disabled]="true"
></app-workflow-details-accordion>
```

## Data Interface

```typescript
export interface WorkflowDetailsData {
  readonly name?: string;
  readonly description?: string;
  readonly temperature?: string;
  readonly topP?: string;
  readonly maxRpm?: string;
  readonly maxToken?: string;
  readonly maxIteration?: string;
  readonly maxExecutionTime?: string;
  readonly selectedModel?: string;
  readonly enableManagerLlm?: boolean;
}
```

## Configuration Interface

```typescript
export interface WorkflowDetailsConfig {
  readonly showName?: boolean; // Default: true
  readonly showDescription?: boolean; // Default: true
  readonly showTemperature?: boolean; // Default: true
  readonly showTopP?: boolean; // Default: true
  readonly showMaxRpm?: boolean; // Default: true
  readonly showMaxToken?: boolean; // Default: true
  readonly showMaxIteration?: boolean; // Default: true
  readonly showMaxExecutionTime?: boolean; // Default: true
  readonly showModel?: boolean; // Default: true
  readonly showEnableManagerLlm?: boolean; // Default: true
  readonly title?: string; // Default: 'Workflow Details'
  readonly disabled?: boolean; // Default: false
}
```

## Available Fields

### Text Fields

- **Name** - `aava-textbox` (single line)
- **Description** - `aava-textbox` (multiline)

### Numerical Fields

- **Temperature** - Range: 0.1-1.0, Step: 0.1
- **Top P** - Range: 0.1-1.0, Step: 0.1
- **Max RPM** - Range: 1-10000, Step: 1
- **Max Token** - Range: 100-32000, Step: 100
- **Max Iteration** - Range: 1-100, Step: 1
- **Max Execution Time** - Range: 1-3600, Step: 1

### Select Fields

- **Model** - `aava-select` with dropdown options from DropdownDataStore

### Toggle Fields

- **Enable Manager LLM** - `aava-toggle` (boolean)

## Events

All fields emit change events:

- `nameChanged`
- `descriptionChanged`
- `temperatureChanged`
- `topPChanged`
- `maxRpmChanged`
- `maxTokenChanged`
- `maxIterationChanged`
- `maxExecutionTimeChanged`
- `modelChanged`
- `enableManagerLlmChanged`

## Styling

The component uses CSS Grid for responsive layout:

- **Desktop**: 2-3 columns for numerical fields
- **Mobile**: Single column layout
- **Accordion styling**: Custom styling with borders and shadows

## Dependencies

- `@aava/play-core` - Aava UI components
- `DropdownDataStore` - For model options
- Angular Forms module for two-way binding
