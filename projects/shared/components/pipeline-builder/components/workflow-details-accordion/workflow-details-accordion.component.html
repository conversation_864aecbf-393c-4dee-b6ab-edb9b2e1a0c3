<div class="custom-accordion" [class.disabled]="isDisabled()">
  <!-- Header -->
  <div
    class="accordion-header"
    (click)="toggleAccordion()"
    [class.open]="isOpen()"
    [attr.aria-expanded]="isOpen()"
    role="button"
    tabindex="0"
    (keydown.enter)="toggleAccordion()"
    (keydown.space)="toggleAccordion()"
  >
    <span class="accordion-title">{{ accordionTitle() }}</span>
    <aava-icon
      [iconName]="isOpen() ? 'chevron-up' : 'chevron-down'"
      size="xs"
      color="#616874"
    ></aava-icon>
  </div>

  <!-- Content -->
  <div class="accordion-content" [class.open]="isOpen()" [@slideInOut]>
    <div class="form-fields">
      <!-- Name Field -->
      <div class="form-field" *ngIf="config().showName !== false">
        <aava-textbox
          name="workflow-name"
          label="Name"
          placeholder="Enter workflow name"
          [ngModel]="data().name"
          [disabled]="isDisabled() || disabled()"
          (ngModelChange)="onNameChange($event)"
          size="sm"
          variant="default"
        ></aava-textbox>
      </div>

      <!-- Description Field -->
      <div class="form-field" *ngIf="config().showDescription !== false">
        <aava-textbox
          name="workflow-description"
          label="Description"
          placeholder="Enter workflow description"
          [ngModel]="data().description"
          [disabled]="isDisabled() || disabled()"
          (ngModelChange)="onDescriptionChange($event)"
          size="sm"
          variant="default"
        ></aava-textbox>
      </div>

      <!-- Enable Manager LLM Field -->
      <div class="form-field" *ngIf="config().showEnableManagerLlm !== false">
        <aava-toggle
          name="workflow-manager-llm"
          size="sm"
          title="Enable"
          [ngModel]="data().enableManagerLlm"
          [disabled]="isDisabled() || disabled()"
          (ngModelChange)="onEnableManagerLlmChange($event)"
        ></aava-toggle>
      </div>

      <!-- Model Field -->
      <div class="form-field" *ngIf="config().showModel !== false">
        <aava-select
          name="workflow-model"
          size="sm"
          label="Model"
          placeholder="Select Model"
          [ngModel]="data().selectedModel"
          [disabled]="isDisabled() || disabled()"
          (selectionChange)="onModelChange($event)"
        >
          <aava-select-option
            *ngFor="let option of modelOptions()"
            [value]="option.value"
          >
            {{ option.label }}
          </aava-select-option>
        </aava-select>
      </div>

      <!-- Numerical Fields Grid -->
      <div class="form-fields-grid">
        <!-- Temperature Field -->
        <div class="form-field" *ngIf="config().showTemperature !== false">
          <aava-textbox
            name="workflow-temperature"
            label="Temperature"
            placeholder="Enter temperature (0.1-1.0)"
            [ngModel]="data().temperature"
            [disabled]="isDisabled() || disabled()"
            (ngModelChange)="onTemperatureChange($event)"
            type="number"
            min="0.1"
            max="1.0"
            step="0.1"
            size="sm"
            variant="default"
          ></aava-textbox>
        </div>

        <!-- Top P Field -->
        <div class="form-field" *ngIf="config().showTopP !== false">
          <aava-textbox
            name="workflow-top-p"
            label="Top P"
            placeholder="Enter top P (0.1-1.0)"
            [ngModel]="data().topP"
            [disabled]="isDisabled() || disabled()"
            (ngModelChange)="onTopPChange($event)"
            type="number"
            min="0.1"
            max="1.0"
            step="0.1"
            size="sm"
            variant="default"
          ></aava-textbox>
        </div>

        <!-- Max RPM Field -->
        <div class="form-field" *ngIf="config().showMaxRpm !== false">
          <aava-textbox
            name="workflow-max-rpm"
            label="Max RPM"
            placeholder="Enter max RPM"
            [ngModel]="data().maxRpm"
            [disabled]="isDisabled() || disabled()"
            (ngModelChange)="onMaxRpmChange($event)"
            type="number"
            min="1"
            max="10000"
            step="1"
            size="sm"
            variant="default"
          ></aava-textbox>
        </div>

        <!-- Max Token Field -->
        <div class="form-field" *ngIf="config().showMaxToken !== false">
          <aava-textbox
            name="workflow-max-token"
            label="Max Token"
            placeholder="Enter max tokens"
            [ngModel]="data().maxToken"
            [disabled]="isDisabled() || disabled()"
            (ngModelChange)="onMaxTokenChange($event)"
            type="number"
            min="100"
            max="32000"
            step="100"
            size="sm"
            variant="default"
          ></aava-textbox>
        </div>

        <!-- Max Iteration Field -->
        <div class="form-field" *ngIf="config().showMaxIteration !== false">
          <aava-textbox
            name="workflow-max-iteration"
            label="Max Iteration"
            placeholder="Enter max iterations"
            [ngModel]="data().maxIteration"
            [disabled]="isDisabled() || disabled()"
            (ngModelChange)="onMaxIterationChange($event)"
            type="number"
            min="1"
            max="100"
            step="1"
            size="sm"
            variant="default"
          ></aava-textbox>
        </div>

        <!-- Max Execution Time Field -->
        <div class="form-field" *ngIf="config().showMaxExecutionTime !== false">
          <aava-textbox
            name="workflow-max-execution-time"
            label="Max Execution Time"
            placeholder="Enter max execution time (seconds)"
            [ngModel]="data().maxExecutionTime"
            [disabled]="isDisabled() || disabled()"
            (ngModelChange)="onMaxExecutionTimeChange($event)"
            type="number"
            min="1"
            max="3600"
            step="1"
            size="sm"
            variant="default"
          ></aava-textbox>
        </div>
      </div>
    </div>
  </div>
</div>
