.custom-accordion {
  position: relative;
  width: 100%;

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  .accordion-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: transparent;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
    color: #3b3f46;
    min-width: 0;

    &:hover {
      background: #f8f9fa;
      border-color: #d0d0d0;
    }

    &.open {
      background: #f0f8ff;
      border-color: #007bff;
      color: #007bff;
    }

    .accordion-title {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .accordion-content {
    position: absolute;
    top: calc(100% + 8px); // Add 8px gap between header and dropdown
    left: 0;
    right: 0;
    z-index: 9999; // Higher z-index to ensure it appears above everything
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px;
    // max-height: 400px;
    overflow: visible; // Changed from overflow-y: auto to allow dropdowns to show
    transform: translateY(-10px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;

    &.open {
      transform: translateY(0);
      opacity: 1;
      visibility: visible;
    }
  }

  .form-fields {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
    overflow: visible; // Ensure form fields don't clip their children
  }

  .form-field {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 0;
    overflow: visible; // Ensure form fields don't clip their children
  }

  .form-fields-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }

  // Ensure form components take full width
  ::ng-deep aava-textbox,
  ::ng-deep aava-select,
  ::ng-deep aava-toggle {
    width: 100%;
  }

  // Fix Aava select dropdown positioning and z-index
  ::ng-deep .aava-select {
    .select-option-container {
      position: absolute !important;
      top: 100% !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 100000 !important;
      pointer-events: auto !important;

      .aava-select-panel {
        position: relative !important;
        z-index: 100000 !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: auto !important;
        transform: none !important;
        width: 100% !important;
        max-height: 200px !important;
        overflow-y: auto !important;
        margin: 0 !important;
        padding: 0 !important;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .custom-accordion {
    .form-fields-grid {
      grid-template-columns: 1fr;
    }
  }
}
