import {
  AavaTextboxComponent,
  AavaSelectComponent,
  AavaSelectOptionComponent,
  AavaToggleComponent,
  AavaIconComponent,
} from '@aava/play-core';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
  inject,
  OnInit,
  signal,
  ElementRef,
  HostListener,
} from '@angular/core';
import { FormsModule } from '@angular/forms';

import { DropdownDataStore } from '../../../../stores/dropdown-data.store';

export interface WorkflowDetailsData {
  readonly name?: string;
  readonly description?: string;
  readonly temperature?: string;
  readonly topP?: string;
  readonly maxRpm?: string;
  readonly maxToken?: string;
  readonly maxIteration?: string;
  readonly maxExecutionTime?: string;
  readonly selectedModel?: string;
  readonly enableManagerLlm?: boolean;
}

export interface WorkflowDetailsConfig {
  readonly showName?: boolean;
  readonly showDescription?: boolean;
  readonly showTemperature?: boolean;
  readonly showTopP?: boolean;
  readonly showMaxRpm?: boolean;
  readonly showMaxToken?: boolean;
  readonly showMaxIteration?: boolean;
  readonly showMaxExecutionTime?: boolean;
  readonly showModel?: boolean;
  readonly showEnableManagerLlm?: boolean;
  readonly title?: string;
  readonly disabled?: boolean;
}

@Component({
  selector: 'app-workflow-details-accordion',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AavaTextboxComponent,
    AavaSelectComponent,
    AavaSelectOptionComponent,
    AavaToggleComponent,
    AavaIconComponent,
  ],
  templateUrl: './workflow-details-accordion.component.html',
  styleUrls: ['./workflow-details-accordion.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('slideInOut', [
      state('in', style({ height: '*', opacity: 1 })),
      state('out', style({ height: '0px', opacity: 0 })),
      transition('in => out', animate('200ms ease-in-out')),
      transition('out => in', animate('200ms ease-in-out')),
    ]),
  ],
})
export class WorkflowDetailsAccordionComponent implements OnInit {
  // Store injection
  private readonly dropdownStore = inject(DropdownDataStore);

  // Element reference for click outside detection
  private readonly elementRef = inject(ElementRef);

  // Signal inputs
  readonly data = input.required<WorkflowDetailsData>();
  readonly config = input<WorkflowDetailsConfig>({});
  readonly disabled = input<boolean>(false);

  // Accordion state
  private readonly _isOpen = signal<boolean>(false);
  readonly isOpen = this._isOpen.asReadonly();

  // Signal outputs
  readonly nameChanged = output<string>();
  readonly descriptionChanged = output<string>();
  readonly temperatureChanged = output<string>();
  readonly topPChanged = output<string>();
  readonly maxRpmChanged = output<string>();
  readonly maxTokenChanged = output<string>();
  readonly maxIterationChanged = output<string>();
  readonly maxExecutionTimeChanged = output<string>();
  readonly modelChanged = output<string>();
  readonly enableManagerLlmChanged = output<boolean>();

  // Dynamic dropdown options from store
  readonly modelOptions = this.dropdownStore.modelOptions;

  // Store signals
  readonly loading = this.dropdownStore.loading;
  readonly error = this.dropdownStore.error;

  // Computed values
  readonly accordionTitle = computed(
    () => this.config().title || 'Workflow Details'
  );
  readonly isDisabled = computed(
    () => this.disabled() || this.config().disabled
  );

  ngOnInit(): void {
    // Load dropdown data when component initializes
     if (this.config().showModel) {
    this.dropdownStore.loadModelsOnly();
  }
  }

  // Listen for clicks outside the accordion to close it
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (this.isOpen() && !this.isDisabled()) {
      const clickedInside = this.elementRef.nativeElement.contains(
        event.target as Node
      );
      if (!clickedInside) {
        this._isOpen.set(false);
      }
    }
  }

  // Accordion methods
  toggleAccordion(): void {
    if (!this.isDisabled()) {
      this._isOpen.update(isOpen => !isOpen);
    }
  }

  // Event handlers
  onNameChange(value: string): void {
    this.nameChanged.emit(value);
  }

  onDescriptionChange(value: string): void {
    this.descriptionChanged.emit(value);
  }

  onTemperatureChange(value: string): void {
    this.temperatureChanged.emit(value);
  }

  onTopPChange(value: string): void {
    this.topPChanged.emit(value);
  }

  onMaxRpmChange(value: string): void {
    this.maxRpmChanged.emit(value);
  }

  onMaxTokenChange(value: string): void {
    this.maxTokenChanged.emit(value);
  }

  onMaxIterationChange(value: string): void {
    this.maxIterationChanged.emit(value);
  }

  onMaxExecutionTimeChange(value: string): void {
    this.maxExecutionTimeChanged.emit(value);
  }

  onModelChange(value: string): void {
    this.modelChanged.emit(value);
  }

  onEnableManagerLlmChange(value: boolean): void {
    this.enableManagerLlmChanged.emit(value);
  }
}
