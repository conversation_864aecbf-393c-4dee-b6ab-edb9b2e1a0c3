<div class="pipeline-library-panel" [class.disabled]="disabled()">
  <!-- Filter Section -->
  <div class="filter-section">
    <app-filter-component
      [config]="config().filterConfig || {}"
      [searchValue]="searchValue()"
      [isOpen]="false"
      (searchChanged)="onSearchChange($event)"
      (filterChanged)="onFilterChange($event)"
    ></app-filter-component>
  </div>

  <!-- Agents List Section -->
  <div class="agents-section">
    <div class="agents-list" [class.empty]="!hasAgents() && !isLoading()">
      @if (isLoading()) {
        <!-- Skeleton Loaders -->
        @for (skeleton of [1, 2, 3, 4, 5, 6]; track skeleton) {
          <div class="agent-card-skeleton">
            <!-- Card container with padding -->
            <div class="skeleton-card-content">
              <!-- Title line -->
              <aava-skeleton
                [width]="'70%'"
                [height]="'20px'"
                [shape]="'rounded'"
                [animation]="'pulse'"
                class="skeleton-title"
              ></aava-skeleton>

              <!-- Description line -->
              <aava-skeleton
                [width]="'90%'"
                [height]="'16px'"
                [shape]="'rounded'"
                [animation]="'pulse'"
                class="skeleton-description"
              ></aava-skeleton>

              <!-- Tags line -->
              <div class="skeleton-tags">
                <aava-skeleton
                  [width]="'60px'"
                  [height]="'24px'"
                  [shape]="'rounded'"
                  [animation]="'pulse'"
                  class="skeleton-tag"
                ></aava-skeleton>
                <aava-skeleton
                  [width]="'80px'"
                  [height]="'24px'"
                  [shape]="'rounded'"
                  [animation]="'pulse'"
                  class="skeleton-tag"
                ></aava-skeleton>
                <aava-skeleton
                  [width]="'70px'"
                  [height]="'24px'"
                  [shape]="'rounded'"
                  [animation]="'pulse'"
                  class="skeleton-tag"
                ></aava-skeleton>
              </div>
            </div>
          </div>
        }
      } @else if (hasAgents()) {
        @for (agent of agents(); track agent.id) {
          <app-agent-card-node
            [data]="agent"
            [draggable]="!disabled()"
            [isExternalItem]="true"
            [disableModal]="true"
            (cardClicked)="onAgentClick($event)"
            (dragStarted)="onAgentDragStart($event)"
            class="agent-card"
          >
          </app-agent-card-node>
        }
      } @else {
        <div class="empty-state">
          <div class="empty-icon">
            <aava-icon iconName="search" size="lg"></aava-icon>
          </div>
          <h4>No agents found</h4>
          <p>
            @if (searchValue()) {
              Try adjusting your search terms
            } @else {
              No agents available in the library
            }
          </p>
        </div>
      }
    </div>
  </div>

  <!-- Create Agent Section -->
  @if (config().showCreateButton) {
    <div class="create-section">
      <aava-button
        variant="primary"
        [outlined]="true"
        size="sm"
        (click)="onCreateAgentClick()"
        [disabled]="disabled()"
        [label]="config().createButtonText || 'Create Agent'"
        [width]="'100%'"
      >
      </aava-button>
    </div>
  }
</div>
