.pipeline-library-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1rem;
  gap: 1rem;
  position: relative; // Ensure proper positioning context
  overflow: visible; // Allow filter popup to overflow

  border-radius: var(--Global-V1-Radius-Rad8, 1rem);
  border: 0.5px solid var(--Brand-Neutral-n-100, #d1d3d8);
  background: var(
    --Surface-Fill-Light-Surface-White-6,
    rgba(255, 255, 255, 0.6)
  );

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  // Filter Section
  .filter-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: relative; // Ensure proper positioning context
    z-index: 10; // Ensure filter popup appears above other content

    // Override filter popup positioning for narrow panels
    ::ng-deep .filter-controls {
      .search-container {
        flex: 0 0 85% !important; // 90% width
      }

      .filter-button-container {
        flex: 0 0 0% !important; // 10% width
      }
    }

    // Ensure filter component takes full width and fits properly
    app-filter-component {
      width: 100%;
    }
  }

  // Agents Section
  .agents-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; // Allow flex child to shrink
    position: relative; // Ensure proper stacking context
    z-index: 1; // Lower than filter popup

    .agents-list {
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      &.empty {
        justify-content: center;
        align-items: center;
        min-height: 200px;
      }

      .agent-card {
        flex-shrink: 0;
      }

      .agent-card-skeleton {
        flex-shrink: 0;
        margin-bottom: 0.5rem;
        border: 1px solid var(--Brand-Neutral-n-100, #d1d3d8);
        border-radius: 0.5rem;
        background: var(
          --Surface-Fill-Light-Surface-White-6,
          rgba(255, 255, 255, 0.6)
        );

        .skeleton-card-content {
          padding: 1rem;
          display: flex;
          flex-direction: column;
          gap: 0.75rem;

          .skeleton-title {
            margin-bottom: 0.25rem;
          }

          .skeleton-description {
            margin-bottom: 0.5rem;
          }

          .skeleton-tags {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;

            .skeleton-tag {
              border-radius: 0.25rem;
            }
          }
        }
      }

      .empty-state {
        text-align: center;
        color: var(--text-secondary, #6b7280);
        padding: 2rem 1rem;

        .empty-icon {
          margin-bottom: 1rem;
          opacity: 0.5;

          aava-icon {
            color: var(--text-tertiary, #9ca3af);
          }
        }

        h4 {
          margin: 0 0 0.5rem 0;
          font-size: 1rem;
          font-weight: 600;
          color: var(--text-primary, #1f2937);
        }

        p {
          margin: 0;
          font-size: 0.875rem;
          line-height: 1.4;
        }
      }
    }
  }

  // Create Agent Section
  .create-section {
    margin-top: auto;
    padding-top: 1rem;
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .pipeline-library-panel {
    padding: 0.75rem;
    gap: 0.75rem;
  }
}

@media (max-width: 768px) {
  .pipeline-library-panel {
    height: 200px;
    overflow-y: auto;
    border-right: none;
    border-bottom: 1px solid var(--border-color, #e5e7eb);
  }
}
