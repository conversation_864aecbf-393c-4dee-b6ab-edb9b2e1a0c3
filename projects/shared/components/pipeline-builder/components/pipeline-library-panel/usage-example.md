# Pipeline Library Panel Component Usage

## Overview

The `PipelineLibraryPanelComponent` is a container component that provides the left sidebar functionality for the pipeline builder. It includes search, filtering, agent listing, and creation capabilities.

## Basic Usage

```typescript
import {
  PipelineLibraryPanelComponent,
  AgentCardNodeData,
} from './pipeline-library-panel';

// In your component
const agents: AgentCardNodeData[] = [
  {
    id: 'agent-1',
    name: 'Agent Alpha',
    description: 'AI-powered automation agent',
    icon: 'bot',
    type: 'agent',
  },
  {
    id: 'agent-2',
    name: 'Agent Beta',
    description: 'Data processing specialist',
    icon: 'database',
    type: 'agent',
  },
];

const config = {
  searchPlaceholder: 'Search agents...',
  filterButtonText: 'Filter',
  createButtonText: 'Create Agent',
  agentsTitle: 'Available Agents',
  showFilter: true,
  showCreateButton: true,
};
```

```html
<app-pipeline-library-panel
  [agents]="agents"
  [config]="config"
  [searchValue]="searchValue"
  [disabled]="false"
  (searchChanged)="onSearchChange($event)"
  (filterClicked)="onFilterClick()"
  (createAgentClicked)="onCreateAgentClick()"
  (agentClicked)="onAgentClick($event)"
  (agentDragStarted)="onAgentDragStart($event)"
>
</app-pipeline-library-panel>
```

## Component Features

### Inputs

- `agents: AgentCardNodeData[]` (required) - Array of available agents
- `config: LibraryPanelConfig` (optional) - Configuration object for customization
- `searchValue: string` (optional) - Current search value
- `disabled: boolean` (optional, default: false) - Disable all interactions

### Outputs

- `searchChanged: string` - Emitted when search value changes
- `filterClicked: void` - Emitted when filter button is clicked
- `createAgentClicked: void` - Emitted when create agent button is clicked
- `agentClicked: AgentCardNodeData` - Emitted when an agent card is clicked
- `agentDragStarted: DragEvent` - Emitted when agent drag starts

### LibraryPanelConfig Interface

```typescript
interface LibraryPanelConfig {
  readonly searchPlaceholder?: string; // Search input placeholder
  readonly filterButtonText?: string; // Filter button text
  readonly createButtonText?: string; // Create button text
  readonly agentsTitle?: string; // Agents section title
  readonly showFilter?: boolean; // Show/hide filter button
  readonly showCreateButton?: boolean; // Show/hide create button
}
```

## Built-in Features

### Search Functionality

- **Real-time filtering** of agents based on name and description
- **Case-insensitive search** with trim whitespace
- **Empty state handling** with contextual messages
- **Agent count display** showing filtered results

### Responsive Design

- **Desktop**: Full sidebar with proper spacing
- **Tablet**: Reduced padding and spacing
- **Mobile**: Horizontal layout with bottom border

### Accessibility

- **Keyboard navigation** support
- **Screen reader friendly** with proper ARIA labels
- **Focus management** for interactive elements

## Example Implementation

```typescript
// In your pipeline builder component
export class PipelineBuilderComponent {
  agents: AgentCardNodeData[] = [
    {
      id: 'agent-1',
      name: 'Agent Alpha',
      description: 'AI-powered automation agent',
      icon: 'bot',
      type: 'agent',
    },
    {
      id: 'agent-2',
      name: 'Agent Beta',
      description: 'Data processing specialist',
      icon: 'database',
      type: 'agent',
    },
  ];

  searchValue = '';
  disabled = false;

  config: LibraryPanelConfig = {
    searchPlaceholder: 'Search agents...',
    filterButtonText: 'Filter',
    createButtonText: 'Create Agent',
    agentsTitle: 'Available Agents',
    showFilter: true,
    showCreateButton: true,
  };

  onSearchChange(value: string): void {
    this.searchValue = value;
    console.log('Search changed:', value);
  }

  onFilterClick(): void {
    console.log('Filter clicked');
    // Implement filter logic
  }

  onCreateAgentClick(): void {
    console.log('Create agent clicked');
    // Navigate to agent creation
  }

  onAgentClick(agent: AgentCardNodeData): void {
    console.log('Agent clicked:', agent.name);
    // Handle agent selection
  }

  onAgentDragStart(event: DragEvent): void {
    console.log('Agent drag started:', event);
    // Handle drag start
  }
}
```

## Customization Examples

### Minimal Configuration

```typescript
const minimalConfig: LibraryPanelConfig = {
  searchPlaceholder: 'Find agents...',
  agentsTitle: 'Agents',
  showFilter: false,
  showCreateButton: false,
};
```

### Custom Styling

The component uses CSS custom properties for theming:

- `--surface-elevated`: Background color
- `--border-color`: Border colors
- `--text-primary`: Primary text color
- `--text-secondary`: Secondary text color
- `--text-tertiary`: Tertiary text color

## Integration with Agent Card Node

The component automatically uses `AgentCardNodeComponent` for each agent, providing:

- **Consistent styling** across all agent cards
- **Drag and drop functionality** when not disabled
- **Click handling** for agent selection
- **Visual feedback** for interactions

## Empty States

The component handles various empty states:

- **No agents available**: Shows "No agents available in the library"
- **No search results**: Shows "Try adjusting your search terms"
- **Loading state**: Can be handled by parent component

This ensures a good user experience regardless of the data state.
