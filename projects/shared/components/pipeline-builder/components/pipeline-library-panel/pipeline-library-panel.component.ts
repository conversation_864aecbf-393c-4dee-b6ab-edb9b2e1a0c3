import {
  AavaButtonComponent,
  AavaIconComponent,
  AavaSkeletonComponent,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
} from '@angular/core';
import { FormsModule } from '@angular/forms';

// Local components
import {
  FilterComponentComponent,
  FilterComponentConfig,
  FilterChangeEvent,
} from '../../../filter-component';
import { AgentCardNodeComponent, AgentCardNodeData } from '../agent-card-node';

export interface LibraryPanelConfig {
  readonly createButtonText?: string;
  readonly agentsTitle?: string;
  readonly showCreateButton?: boolean;
  readonly filterConfig?: FilterComponentConfig;
}

@Component({
  selector: 'app-pipeline-library-panel',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AavaButtonComponent,
    AavaIconComponent,
    AavaSkeletonComponent,
    AgentCardNodeComponent,
    FilterComponentComponent,
  ],
  templateUrl: './pipeline-library-panel.component.html',
  styleUrls: ['./pipeline-library-panel.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PipelineLibraryPanelComponent {
  // Signal inputs
  readonly agents = input.required<AgentCardNodeData[]>();
  readonly config = input<LibraryPanelConfig>({
    createButtonText: 'Create Agent',
    agentsTitle: 'Available Agents',
    showCreateButton: true,
    filterConfig: {
      searchPlaceholder: 'Search agents...',
      orientation: 'vertical',
      showArtifactType: false,
      showPracticeArea: true,
      showCapabilityTags: true,
      showRealm: false,
      showSortBy: true,
      showStatus: false,
      showSampleFiles: false,
    },
  });
  readonly searchValue = input<string>('');
  readonly disabled = input<boolean>(false);
  readonly isLoading = input<boolean>(false);

  // Signal outputs
  readonly searchChanged = output<string>();
  readonly filterChanged = output<FilterChangeEvent>();
  readonly createAgentClicked = output<void>();
  readonly agentClicked = output<AgentCardNodeData>();
  readonly agentDragStarted = output<DragEvent>();

  // Computed values
  readonly hasAgents = computed(() => this.agents().length > 0);

  // Event handlers
  protected onSearchChange(value: string): void {
    this.searchChanged.emit(value);
  }

  protected onFilterChange(event: FilterChangeEvent): void {
    this.filterChanged.emit(event);
  }

  protected onCreateAgentClick(): void {
    this.createAgentClicked.emit();
  }

  protected onAgentClick(agent: AgentCardNodeData): void {
    this.agentClicked.emit(agent);
  }

  protected onAgentDragStart(event: DragEvent): void {
    this.agentDragStarted.emit(event);
  }
}
