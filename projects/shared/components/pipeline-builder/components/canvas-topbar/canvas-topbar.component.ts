// Aava components
import { AavaButtonComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
} from '@angular/core';
import { FormsModule } from '@angular/forms';

import {
  WorkflowDetailsAccordionComponent,
  WorkflowDetailsData,
  WorkflowDetailsConfig,
} from '../workflow-details-accordion';

export interface CanvasTopbarConfig {
  readonly undoText?: string;
  readonly redoText?: string;
  readonly runText?: string;
  readonly showUndoRedo?: boolean;
  readonly showRun?: boolean;
  readonly workflowConfig?: WorkflowDetailsConfig;
  readonly workflowConfigLLM?: WorkflowDetailsConfig;
}

@Component({
  selector: 'app-canvas-topbar',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AavaButtonComponent,
    WorkflowDetailsAccordionComponent,
  ],
  templateUrl: './canvas-topbar.component.html',
  styleUrls: ['./canvas-topbar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CanvasTopbarComponent {
  // Signal inputs
  readonly workflowData = input.required<WorkflowDetailsData>();
  readonly canUndo = input<boolean>(false);
  readonly canRedo = input<boolean>(false);
  readonly disabled = input<boolean>(false);
  readonly canRunWorkflow = input<boolean>(false);
  readonly hasMinimumAgents = input<boolean>(false);
  readonly hasWorkflowDetails = input<boolean>(false);
  readonly config = input<CanvasTopbarConfig>({
    undoText: 'Undo',
    redoText: 'Redo',
    runText: 'Run',
    showUndoRedo: false,
    showRun: false,
  });

  // Signal outputs
  readonly pipelineNameChanged = output<string>();
  readonly descriptionChanged = output<string>();
  readonly temperatureChanged = output<string>();
  readonly topPChanged = output<string>();
  readonly maxRpmChanged = output<string>();
  readonly maxTokenChanged = output<string>();
  readonly maxIterationChanged = output<string>();
  readonly maxExecutionTimeChanged = output<string>();
  readonly modelChanged = output<string>();
  readonly managerLlmToggled = output<boolean>();
  readonly undoClicked = output<void>();
  readonly redoClicked = output<void>();
  readonly runClicked = output<void>();

  // Computed values
  readonly workflowConfig = computed(
    () =>
      this.config().workflowConfig || {
        title: 'Workflow Details *',
        showName: true,
        showDescription: true,
        showTemperature: false,
        showTopP: false,
        showMaxRpm: false,
        showMaxToken: false,
        showMaxIteration: false,
        showMaxExecutionTime: false,
        showModel: false,
        showEnableManagerLlm: false,
      }
  );
  readonly workflowConfigLLM = computed(
    () =>
      this.config().workflowConfigLLM || {
        title: 'Enable Manager LLM',
        showName: false,
        showDescription: false,
        showTemperature: true,
        showTopP: true,
        showMaxRpm: true,
        showMaxToken: true,
        showMaxIteration: true,
        showMaxExecutionTime: true,
        showModel: true,
        showEnableManagerLlm: true,
      }
  );
  readonly canPerformUndo = computed(() => this.canUndo() && !this.disabled());
  readonly canPerformRedo = computed(() => this.canRedo() && !this.disabled());
  readonly canRun = computed(() => this.canRunWorkflow() && !this.disabled());

  // Event handlers
  protected onPipelineNameChange(value: string): void {
    this.pipelineNameChanged.emit(value);
  }

  protected onDescriptionChange(value: string): void {
    this.descriptionChanged.emit(value);
  }

  protected onTemperatureChange(value: string): void {
    this.temperatureChanged.emit(value);
  }

  protected onTopPChange(value: string): void {
    this.topPChanged.emit(value);
  }

  protected onMaxRpmChange(value: string): void {
    this.maxRpmChanged.emit(value);
  }

  protected onMaxTokenChange(value: string): void {
    this.maxTokenChanged.emit(value);
  }

  protected onMaxIterationChange(value: string): void {
    this.maxIterationChanged.emit(value);
  }

  protected onMaxExecutionTimeChange(value: string): void {
    this.maxExecutionTimeChanged.emit(value);
  }

  protected onModelChange(value: string): void {
    this.modelChanged.emit(value);
  }

  protected onManagerLlmToggle(value: boolean): void {
    this.managerLlmToggled.emit(value);
  }

  protected onUndoClick(): void {
    if (this.canPerformUndo()) {
      this.undoClicked.emit();
    }
  }

  protected onRedoClick(): void {
    if (this.canPerformRedo()) {
      this.redoClicked.emit();
    }
  }

  protected onRunClick(): void {
    if (this.canRun()) {
      this.runClicked.emit();
      // Navigation is handled by the parent component after API completion
    }
  }

  protected getRunButtonTooltip(): string {
    if (this.canRun()) {
      return 'Run workflow';
    }

    const reasons: string[] = [];

    if (!this.hasMinimumAgents()) {
      reasons.push('Add at least 2 agents to the workflow');
    }

    if (!this.hasWorkflowDetails()) {
      reasons.push('Fill in workflow name and description');
    }

    return reasons.length > 0 ? reasons.join(' • ') : 'Cannot run workflow';
  }
}
