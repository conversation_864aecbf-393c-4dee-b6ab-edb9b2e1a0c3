# Canvas Topbar Component Usage

## Overview

The `CanvasTopbarComponent` is a presentational component that provides the top toolbar functionality for the pipeline builder's right panel. It includes pipeline name input, Manager LLM toggle, and action buttons (Undo, Redo, Run).

## Basic Usage

```typescript
import { CanvasTopbarComponent, CanvasTopbarConfig } from './canvas-topbar';

// In your component
const config: CanvasTopbarConfig = {
  pipelineNamePlaceholder: 'Enter pipeline name...',
  managerLlmText: 'Enable Manager LLM',
  undoText: 'Undo',
  redoText: 'Redo',
  runText: 'Run Pipeline',
  showManagerLlm: true,
  showUndoRedo: true,
  showRun: true,
};
```

```html
<app-canvas-topbar
  [pipelineName]="pipelineName"
  [managerLlmEnabled]="managerLlmEnabled"
  [canUndo]="canUndo"
  [canRedo]="canRedo"
  [disabled]="false"
  [config]="config"
  (pipelineNameChanged)="onPipelineNameChange($event)"
  (managerLlmToggled)="onManagerLlmToggle($event)"
  (undoClicked)="onUndoClick()"
  (redoClicked)="onRedoClick()"
  (runClicked)="onRunClick()"
>
</app-canvas-topbar>
```

## Component Features

### Inputs

- `pipelineName: string` (optional) - Current pipeline name value
- `managerLlmEnabled: boolean` (optional, default: false) - Manager LLM toggle state
- `canUndo: boolean` (optional, default: false) - Whether undo is available
- `canRedo: boolean` (optional, default: false) - Whether redo is available
- `disabled: boolean` (optional, default: false) - Disable all interactions
- `config: CanvasTopbarConfig` (optional) - Configuration object for customization

### Outputs

- `pipelineNameChanged: string` - Emitted when pipeline name changes
- `managerLlmToggled: boolean` - Emitted when Manager LLM toggle changes
- `undoClicked: void` - Emitted when undo button is clicked
- `redoClicked: void` - Emitted when redo button is clicked
- `runClicked: void` - Emitted when run button is clicked

### CanvasTopbarConfig Interface

```typescript
interface CanvasTopbarConfig {
  readonly pipelineNamePlaceholder?: string; // Pipeline name input placeholder
  readonly managerLlmText?: string; // Manager LLM button text
  readonly undoText?: string; // Undo button tooltip text
  readonly redoText?: string; // Redo button tooltip text
  readonly runText?: string; // Run button text
  readonly showManagerLlm?: boolean; // Show/hide Manager LLM button
  readonly showUndoRedo?: boolean; // Show/hide Undo/Redo buttons
  readonly showRun?: boolean; // Show/hide Run button
}
```

## Built-in Features

### Manager LLM Toggle

- **Visual State**: Button changes from secondary to primary when enabled
- **Icon Change**: Settings icon when disabled, check icon when enabled
- **State Management**: Toggles between enabled/disabled states

### Action Buttons

- **Undo/Redo**: Icon-only buttons with tooltips, disabled when not available
- **Run**: Primary button with play icon, always available when not disabled
- **Consistent Sizing**: All buttons maintain consistent height and spacing

### Responsive Design

- **Desktop**: Full horizontal layout with all sections visible
- **Tablet**: Reduced spacing and padding
- **Mobile**: Stacked vertical layout with action buttons at top

## Example Implementation

```typescript
// In your pipeline builder component
export class PipelineBuilderComponent {
  pipelineName = 'My Pipeline';
  managerLlmEnabled = false;
  canUndo = false;
  canRedo = false;
  disabled = false;

  config: CanvasTopbarConfig = {
    pipelineNamePlaceholder: 'Enter pipeline name...',
    managerLlmText: 'Enable Manager LLM',
    undoText: 'Undo last action',
    redoText: 'Redo last action',
    runText: 'Run Pipeline',
    showManagerLlm: true,
    showUndoRedo: true,
    showRun: true,
  };

  onPipelineNameChange(value: string): void {
    this.pipelineName = value;
    console.log('Pipeline name changed:', value);
  }

  onManagerLlmToggle(enabled: boolean): void {
    this.managerLlmEnabled = enabled;
    console.log('Manager LLM toggled:', enabled);
  }

  onUndoClick(): void {
    console.log('Undo clicked');
    // Implement undo logic
    this.canRedo = true;
    this.canUndo = false;
  }

  onRedoClick(): void {
    console.log('Redo clicked');
    // Implement redo logic
    this.canUndo = true;
    this.canRedo = false;
  }

  onRunClick(): void {
    console.log('Run clicked');
    // Implement run logic
  }
}
```

## Customization Examples

### Minimal Configuration

```typescript
const minimalConfig: CanvasTopbarConfig = {
  pipelineNamePlaceholder: 'Pipeline Name',
  showManagerLlm: false,
  showUndoRedo: false,
  showRun: true,
};
```

### Custom Styling

The component uses CSS custom properties for theming:

- `--surface-elevated`: Background color
- `--border-color`: Border colors
- `--text-primary`: Primary text color
- `--text-secondary`: Secondary text color

## Layout Structure

### Desktop Layout

```
[Pipeline Name Input] [Manager LLM Button] | [Undo] [Redo] [Run] | [Future Features]
```

### Mobile Layout

```
[Undo] [Redo] [Run]
[Pipeline Name Input]
[Manager LLM Button]
```

## Accessibility

- **Keyboard Navigation**: All interactive elements are keyboard accessible
- **Tooltips**: Undo/Redo buttons have descriptive tooltips
- **ARIA Labels**: Proper labeling for screen readers
- **Focus Management**: Logical tab order through all elements

## Integration with Pipeline Builder

The component is designed to integrate seamlessly with the pipeline builder:

1. **Pipeline Name**: Syncs with the main pipeline name state
2. **Manager LLM**: Controls whether Manager LLM is enabled for the pipeline
3. **Action Buttons**: Provides undo/redo functionality and pipeline execution
4. **State Management**: Reflects the current state of the pipeline builder

This ensures a consistent and intuitive user experience across the entire pipeline builder interface.
