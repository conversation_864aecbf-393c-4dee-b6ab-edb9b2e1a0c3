.canvas-topbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 0.75rem;
  // border: 0.5px solid var(--Brand-Neutral-n-100, #d1d3d8);
  // background: var(--Surface-Fill-Dark-Surface-Dark-1, rgba(30, 30, 30, 0.1));
  padding: 1rem;
  gap: 1rem;
  min-height: 4rem;

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  // Left Section: Workflow Details Accordion
  .topbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    min-width: 0; // Allow flex child to shrink

    .workflow-details-section {
      min-width: 200px;
      max-width: 800px; // Increased to accommodate side-by-side layout
      flex: 0 0 auto; // Don't grow, maintain fixed size
      display: flex;
      gap: 0.5rem;

      // Individual accordion width control
      .workflow-basic-accordion {
        flex: 0 0 200px; // Fixed width for basic accordion
        min-width: 180px;
        max-width: 250px;
      }

      .workflow-advanced-accordion {
        flex: 0 0 250px; // Fixed width for advanced accordion
        min-width: 250px;
        max-width: 350px;
      }

      // Custom accordion styling for header
      ::ng-deep .custom-accordion {
        .accordion-header {
          padding: 6px 10px;
          font-size: 13px;
          border: 1px solid #d0d0d0;
          background: #f8f9fa;

          &:hover {
            background: #e9ecef;
            border-color: #007bff;
          }

          &.open {
            background: #e3f2fd;
            border-color: #007bff;
            color: #007bff;
          }
        }

        // Select dropdowns inherit z-index from accordion component
      }
    }
  }

  // Center Section: Action Buttons
  .topbar-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 0 0 auto;

    .action-buttons {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      // Ensure consistent button heights
      aava-button {
        height: 2.5rem;
      }
    }
  }

  // Right Section: Future expansion
  .topbar-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 0 0 auto;
    min-width: 0;
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .canvas-topbar {
    padding: 0.75rem;
    gap: 0.75rem;

    .topbar-left {
      gap: 0.75rem;

      .workflow-details-section {
        min-width: 150px;
        max-width: 600px; // Increased for side-by-side layout
        flex-wrap: wrap; // Allow wrapping on smaller screens

        .workflow-basic-accordion {
          flex: 0 0 180px; // Slightly smaller on tablet
          min-width: 160px;
          max-width: 220px;
        }

        .workflow-advanced-accordion {
          flex: 0 0 250px; // Slightly smaller on tablet
          min-width: 220px;
          max-width: 300px;
        }
      }
    }

    .topbar-center {
      .action-buttons {
        gap: 0.25rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .canvas-topbar {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
    padding: 0.75rem;

    .topbar-left {
      flex-direction: column;
      align-items: stretch;
      gap: 0.5rem;

      .workflow-details-section {
        min-width: unset;
        max-width: unset;
        display: flex;
        flex-direction: column; // Stack vertically on mobile
        gap: 0.5rem;
      }
    }

    .topbar-center {
      order: -1; // Move action buttons to top on mobile
      justify-content: center;

      .action-buttons {
        justify-content: center;
        width: 100%;
      }
    }

    .topbar-right {
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  .canvas-topbar {
    .topbar-center {
      .action-buttons {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.25rem;

        aava-button {
          flex: 1;
          min-width: calc(50% - 0.125rem);
        }
      }
    }
  }
}
