<div class="canvas-topbar" [class.disabled]="disabled()">
  <!-- Left Section: Workflow Details Accordion -->
  <div class="topbar-left">
    <div class="workflow-details-section">
      <app-workflow-details-accordion
        class="workflow-basic-accordion"
        [data]="workflowData()"
        [config]="workflowConfig()"
        [disabled]="disabled()"
        (nameChanged)="onPipelineNameChange($event)"
        (descriptionChanged)="onDescriptionChange($event)"
      ></app-workflow-details-accordion>

      <app-workflow-details-accordion
        class="workflow-advanced-accordion"
        [data]="workflowData()"
        [config]="workflowConfigLLM()"
        [disabled]="disabled()"
        (temperatureChanged)="onTemperatureChange($event)"
        (topPChanged)="onTopPChange($event)"
        (maxRpmChanged)="onMaxRpmChange($event)"
        (maxTokenChanged)="onMaxTokenChange($event)"
        (maxIterationChanged)="onMaxIterationChange($event)"
        (maxExecutionTimeChanged)="onMaxExecutionTimeChange($event)"
        (modelChanged)="onModelChange($event)"
        (enableManagerLlmChanged)="onManagerLlmToggle($event)"
      ></app-workflow-details-accordion>
    </div>
  </div>

  <!-- Center Section: Action Buttons -->
  <div class="topbar-center">
    <div class="action-buttons">
      @if (config().showUndoRedo) {
        <aava-button
          variant="secondary"
          size="sm"
          (click)="onUndoClick()"
          iconName="undo"
          [iconPosition]="'only'"
          [disabled]="!canPerformUndo()"
          [title]="config().undoText || 'Undo'"
        >
        </aava-button>

        <aava-button
          variant="secondary"
          size="sm"
          (click)="onRedoClick()"
          iconName="redo"
          [iconPosition]="'only'"
          [disabled]="!canPerformRedo()"
          [title]="config().redoText || 'Redo'"
        >
        </aava-button>
      }

      @if (config().showRun) {
        <aava-button
          variant="primary"
          size="sm"
          (click)="onRunClick()"
          iconName="play"
          [disabled]="!canRun()"
          [label]="config().runText || 'Run'"
          [title]="getRunButtonTooltip()"
        >
        </aava-button>
      }
    </div>
  </div>
</div>
