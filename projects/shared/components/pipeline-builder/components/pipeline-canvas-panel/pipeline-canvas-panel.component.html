<div class="pipeline-canvas-panel" [class.disabled]="disabled()">
  <!-- Topbar Section -->
  @if (config().showTopbar) {
    <div class="topbar-section">
      <app-canvas-topbar
        [workflowData]="workflowData()"
        [canUndo]="canUndo()"
        [canRedo]="canRedo()"
        [disabled]="disabled()"
        [canRunWorkflow]="canRunWorkflow()"
        [hasMinimumAgents]="hasMinimumAgents()"
        [hasWorkflowDetails]="hasWorkflowDetails()"
        [config]="topbarConfig()"
        (pipelineNameChanged)="onPipelineNameChange($event)"
        (descriptionChanged)="onDescriptionChange($event)"
        (temperatureChanged)="onTemperatureChange($event)"
        (topPChanged)="onTopPChange($event)"
        (maxRpmChanged)="onMaxRpmChange($event)"
        (maxTokenChanged)="onMaxTokenChange($event)"
        (maxIterationChanged)="onMaxIterationChange($event)"
        (maxExecutionTimeChanged)="onMaxExecutionTimeChange($event)"
        (modelChanged)="onModelChange($event)"
        (managerLlmToggled)="onManagerLlmToggle($event)"
        (undoClicked)="onUndoClick()"
        (redoClicked)="onRedoClick()"
        (runClicked)="onRunClick()"
      >
      </app-canvas-topbar>
    </div>
  }

  <!-- Canvas Section -->
  <div class="canvas-section" [ngStyle]="canvasStyles()">
    @if (isLoading()) {
      <div class="canvas-skeleton-container">
        <aava-skeleton height="100%" width="100%"></aava-skeleton>
      </div>
    } @else {
      <app-main-canvas-foblex
        [initialNodes]="canvasNodes()"
        [connections]="canvasConnections()"
        [viewport]="canvasViewport()"
        [disabled]="disabled()"
        [config]="config().canvasConfig || {}"
        (nodeCreated)="onNodeCreated($event)"
        (nodeSelected)="onNodeSelected($event)"
        (nodeDoubleClicked)="onNodeDoubleClicked($event)"
        (nodeRemoved)="onNodeRemoved($event)"
        (connectionCreated)="onConnectionCreated($event)"
        (connectionDeleted)="onConnectionDeleted($event)"
        (viewportChanged)="onViewportChanged($event)"
        (canvasClicked)="onCanvasClicked($event)"
      >
      </app-main-canvas-foblex>
    }
  </div>
</div>
