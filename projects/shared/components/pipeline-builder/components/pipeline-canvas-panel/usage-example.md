# Pipeline Canvas Panel Component Usage

## Overview

The `PipelineCanvasPanelComponent` is a container component that provides the main right panel functionality for the pipeline builder. It combines the `CanvasTopbarComponent` with a canvas area and manages the overall layout and state.

## Basic Usage

```typescript
import {
  PipelineCanvasPanelComponent,
  CanvasPanelConfig,
} from './pipeline-canvas-panel';

// In your component
const config: CanvasPanelConfig = {
  showTopbar: true,
  topbarConfig: {
    pipelineNamePlaceholder: 'Enter pipeline name...',
    managerLlmText: 'Enable Manager LLM',
    undoText: 'Undo',
    redoText: 'Redo',
    runText: 'Run Pipeline',
    showManagerLlm: true,
    showUndoRedo: true,
    showRun: true,
  },
  canvasHeight: '100%',
  canvasBackground: 'var(--surface-bg, #f8f9fa)',
};
```

```html
<app-pipeline-canvas-panel
  [pipelineName]="pipelineName"
  [managerLlmEnabled]="managerLlmEnabled"
  [canUndo]="canUndo"
  [canRedo]="canRedo"
  [disabled]="false"
  [config]="config"
  (pipelineNameChanged)="onPipelineNameChange($event)"
  (managerLlmToggled)="onManagerLlmToggle($event)"
  (undoClicked)="onUndoClick()"
  (redoClicked)="onRedoClick()"
  (runClicked)="onRunClick()"
>
</app-pipeline-canvas-panel>
```

## Component Features

### Inputs

- `pipelineName: string` (optional) - Current pipeline name value
- `managerLlmEnabled: boolean` (optional, default: false) - Manager LLM toggle state
- `canUndo: boolean` (optional, default: false) - Whether undo is available
- `canRedo: boolean` (optional, default: false) - Whether redo is available
- `disabled: boolean` (optional, default: false) - Disable all interactions
- `config: CanvasPanelConfig` (optional) - Configuration object for customization

### Outputs

- `pipelineNameChanged: string` - Emitted when pipeline name changes
- `managerLlmToggled: boolean` - Emitted when Manager LLM toggle changes
- `undoClicked: void` - Emitted when undo button is clicked
- `redoClicked: void` - Emitted when redo button is clicked
- `runClicked: void` - Emitted when run button is clicked

### CanvasPanelConfig Interface

```typescript
interface CanvasPanelConfig {
  readonly showTopbar?: boolean; // Show/hide the topbar
  readonly topbarConfig?: CanvasTopbarConfig; // Configuration for the topbar
  readonly canvasHeight?: string; // Height of the canvas area
  readonly canvasBackground?: string; // Background color of the canvas
}
```

## Built-in Features

### Topbar Integration

- **Seamless Integration**: Uses `CanvasTopbarComponent` internally
- **State Management**: Passes through all topbar-related inputs and outputs
- **Configurable**: Can show/hide the topbar via configuration

### Canvas Area

- **Flexible Layout**: Canvas area takes up remaining space
- **Configurable Styling**: Customizable height and background color
- **Placeholder Content**: Shows current state information for development
- **Responsive Design**: Adapts to different screen sizes

### Layout Management

- **Flexbox Layout**: Uses flexbox for proper space distribution
- **Overflow Handling**: Proper overflow management for canvas area
- **Responsive Design**: Mobile-first responsive design

## Example Implementation

```typescript
// In your pipeline builder component
export class PipelineBuilderComponent {
  pipelineName = 'My Pipeline';
  managerLlmEnabled = false;
  canUndo = false;
  canRedo = false;
  disabled = false;

  config: CanvasPanelConfig = {
    showTopbar: true,
    topbarConfig: {
      pipelineNamePlaceholder: 'Enter pipeline name...',
      managerLlmText: 'Enable Manager LLM',
      undoText: 'Undo last action',
      redoText: 'Redo last action',
      runText: 'Run Pipeline',
      showManagerLlm: true,
      showUndoRedo: true,
      showRun: true,
    },
    canvasHeight: '100%',
    canvasBackground: 'var(--surface-bg, #f8f9fa)',
  };

  onPipelineNameChange(value: string): void {
    this.pipelineName = value;
    console.log('Pipeline name changed:', value);
  }

  onManagerLlmToggle(enabled: boolean): void {
    this.managerLlmEnabled = enabled;
    console.log('Manager LLM toggled:', enabled);
  }

  onUndoClick(): void {
    console.log('Undo clicked');
    // Implement undo logic
    this.canRedo = true;
    this.canUndo = false;
  }

  onRedoClick(): void {
    console.log('Redo clicked');
    // Implement redo logic
    this.canUndo = true;
    this.canRedo = false;
  }

  onRunClick(): void {
    console.log('Run clicked');
    // Implement run logic
  }
}
```

## Customization Examples

### Minimal Configuration

```typescript
const minimalConfig: CanvasPanelConfig = {
  showTopbar: false,
  canvasHeight: '500px',
  canvasBackground: '#ffffff',
};
```

### Custom Topbar Configuration

```typescript
const customConfig: CanvasPanelConfig = {
  showTopbar: true,
  topbarConfig: {
    pipelineNamePlaceholder: 'Enter your pipeline name...',
    managerLlmText: 'Enable AI Manager',
    showManagerLlm: false,
    showUndoRedo: true,
    showRun: true,
  },
  canvasHeight: 'calc(100vh - 200px)',
  canvasBackground: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
};
```

## Layout Structure

### Desktop Layout

```
┌─────────────────────────────────────────────────────────┐
│                    Canvas Topbar                        │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                    Canvas Area                          │
│                  (Placeholder)                          │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### Mobile Layout

```
┌─────────────────────────────────────────────────────────┐
│                    Canvas Topbar                        │
│                  (Stacked Layout)                       │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                    Canvas Area                          │
│                  (Placeholder)                          │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## Integration with Main Canvas

The component is designed to integrate with the `main-canvas` component:

1. **Canvas Area**: The placeholder will be replaced with the actual canvas component
2. **State Management**: All state is passed through to child components
3. **Event Handling**: Events are properly bubbled up to parent components
4. **Responsive Design**: Layout adapts to different screen sizes

## Development Features

### Placeholder Content

- **State Display**: Shows current pipeline state for development
- **Debug Information**: Displays all input values for testing
- **Visual Feedback**: Clear indication of component state

### Responsive Design

- **Mobile-First**: Designed with mobile devices in mind
- **Flexible Layout**: Adapts to different screen sizes
- **Touch-Friendly**: Optimized for touch interactions

This component serves as the foundation for the right panel of the pipeline builder, providing a clean separation between the topbar and canvas areas while maintaining proper state management and event handling.
