.pipeline-canvas-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 0.75rem;
  border: 0.5px solid var(--Brand-Neutral-n-100, #d1d3d8);
  background: var(--Surface-Fill-Dark-Surface-Dark-1, rgba(30, 30, 30, 0.1));
  /* Surface Blur/Blur 10 */
  backdrop-filter: blur(calc(var(--Blur-Blur---10, 100px) / 2));
  overflow: hidden;
  position: relative;

  // Small circles pattern background
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle, #9ca3af 1px, transparent 1px);
    background-size: 16px 16px;
    background-position: 0 0;
    opacity: 0.6;
    z-index: 0;
    pointer-events: none;
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  // Topbar Section
  .topbar-section {
    flex-shrink: 0;
  }

  // Canvas Section
  .canvas-section {
    flex: 1;
    position: relative;
    overflow: hidden;
    min-height: 0; // Allow flex child to shrink

    // Skeleton container for canvas loading
    .canvas-skeleton-container {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 0.5rem;
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .pipeline-canvas-panel {
    .canvas-section {
    }
  }
}

@media (max-width: 768px) {
}
