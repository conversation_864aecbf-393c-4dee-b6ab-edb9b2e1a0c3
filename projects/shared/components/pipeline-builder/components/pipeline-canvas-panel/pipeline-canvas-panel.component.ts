import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
} from '@angular/core';

// Local components
import { AavaSkeletonComponent } from '@aava/play-core';
import { CanvasTopbarComponent, CanvasTopbarConfig } from '../canvas-topbar';
import {
  MainCanvasFoblexComponent,
  CanvasNode,
  CanvasConnection,
  CanvasViewport,
  MainCanvasConfig,
} from '../main-canvas/main-canvas-foblex.component';
import { WorkflowDetailsData } from '../workflow-details-accordion';

export interface CanvasPanelConfig {
  readonly showTopbar?: boolean;
  readonly topbarConfig?: CanvasTopbarConfig;
  readonly canvasHeight?: string;
  readonly canvasBackground?: string;
  readonly canvasConfig?: MainCanvasConfig;
  readonly isLoading?: boolean;
}

@Component({
  selector: 'app-pipeline-canvas-panel',
  standalone: true,
  imports: [
    CommonModule,
    CanvasTopbarComponent,
    MainCanvasFoblexComponent,
    AavaSkeletonComponent,
  ],
  templateUrl: './pipeline-canvas-panel.component.html',
  styleUrls: ['./pipeline-canvas-panel.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PipelineCanvasPanelComponent {
  // Signal inputs
  readonly workflowData = input.required<WorkflowDetailsData>();
  readonly canUndo = input<boolean>(false);
  readonly canRedo = input<boolean>(false);
  readonly disabled = input<boolean>(false);
  readonly isLoading = input<boolean>(false);
  readonly canRunWorkflow = input<boolean>(false);
  readonly hasMinimumAgents = input<boolean>(false);
  readonly hasWorkflowDetails = input<boolean>(false);
  readonly canvasNodes = input<CanvasNode[]>([]);
  readonly canvasConnections = input<CanvasConnection[]>([]);
  readonly canvasViewport = input<CanvasViewport>({ x: 0, y: 0, zoom: 1 });
  readonly config = input<CanvasPanelConfig>({
    showTopbar: true,
    topbarConfig: {
      undoText: 'Undo',
      redoText: 'Redo',
      runText: 'Run',
      showUndoRedo: false,
      showRun: true,
    },
    canvasHeight: '100%',
    canvasConfig: {
      enablePan: true,
      enableZoom: true,
      enableSelection: true,
      enableConnections: true,
      gridSize: 20,
      snapToGrid: false,
      minZoom: 0.1,
      maxZoom: 3,
      defaultZoom: 1,
    },
  });

  // Signal outputs
  readonly pipelineNameChanged = output<string>();
  readonly descriptionChanged = output<string>();
  readonly temperatureChanged = output<string>();
  readonly topPChanged = output<string>();
  readonly maxRpmChanged = output<string>();
  readonly maxTokenChanged = output<string>();
  readonly maxIterationChanged = output<string>();
  readonly maxExecutionTimeChanged = output<string>();
  readonly modelChanged = output<string>();
  readonly managerLlmToggled = output<boolean>();
  readonly undoClicked = output<void>();
  readonly redoClicked = output<void>();
  readonly runClicked = output<void>();
  readonly nodeCreated = output<CanvasNode>();
  readonly nodeSelected = output<string>();
  readonly nodeDoubleClicked = output<string>();
  readonly nodeMoved = output<{
    nodeId: string;
    position: { x: number; y: number };
  }>();
  readonly nodeRemoved = output<string>();
  readonly connectionCreated = output<{ source: string; target: string }>();
  readonly connectionDeleted = output<string>();
  readonly viewportChanged = output<CanvasViewport>();
  readonly canvasClicked = output<{ x: number; y: number }>();

  // Computed values
  readonly topbarConfig = computed(() => this.config().topbarConfig || {});
  readonly canvasStyles = computed(() => ({
    height: this.config().canvasHeight || '100%',
    'background-color':
      this.config().canvasBackground || 'var(--surface-bg, #f8f9fa)',
  }));

  // Event handlers
  protected onPipelineNameChange(value: string): void {
    this.pipelineNameChanged.emit(value);
  }

  protected onDescriptionChange(value: string): void {
    this.descriptionChanged.emit(value);
  }

  protected onTemperatureChange(value: string): void {
    this.temperatureChanged.emit(value);
  }

  protected onTopPChange(value: string): void {
    this.topPChanged.emit(value);
  }

  protected onMaxRpmChange(value: string): void {
    this.maxRpmChanged.emit(value);
  }

  protected onMaxTokenChange(value: string): void {
    this.maxTokenChanged.emit(value);
  }

  protected onMaxIterationChange(value: string): void {
    this.maxIterationChanged.emit(value);
  }

  protected onMaxExecutionTimeChange(value: string): void {
    this.maxExecutionTimeChanged.emit(value);
  }

  protected onModelChange(value: string): void {
    this.modelChanged.emit(value);
  }

  protected onManagerLlmToggle(enabled: boolean): void {
    this.managerLlmToggled.emit(enabled);
  }

  protected onUndoClick(): void {
    this.undoClicked.emit();
  }

  protected onRedoClick(): void {
    this.redoClicked.emit();
  }

  protected onRunClick(): void {
    this.runClicked.emit();
  }

  // Canvas event handlers

  protected onNodeSelected(nodeId: string): void {
    this.nodeSelected.emit(nodeId);
  }

  protected onNodeDoubleClicked(nodeId: string): void {
    this.nodeDoubleClicked.emit(nodeId);
  }

  protected onNodeCreated(node: CanvasNode): void {
    this.nodeCreated.emit(node);
  }

  protected onNodeMoved(event: {
    nodeId: string;
    position: { x: number; y: number };
  }): void {
    this.nodeMoved.emit(event);
  }

  protected onNodeRemoved(nodeId: string): void {
    this.nodeRemoved.emit(nodeId);
  }

  protected onConnectionCreated(event: {
    source: string;
    target: string;
  }): void {
    this.connectionCreated.emit(event);
  }

  protected onConnectionDeleted(connectionId: string): void {
    this.connectionDeleted.emit(connectionId);
  }

  protected onViewportChanged(event: CanvasViewport): void {
    this.viewportChanged.emit(event);
  }

  protected onCanvasClicked(event: { x: number; y: number }): void {
    this.canvasClicked.emit(event);
  }
}
