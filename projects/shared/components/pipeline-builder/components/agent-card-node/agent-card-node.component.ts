import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
  inject,
  AfterViewInit,
  ElementRef,
} from '@angular/core';
import { FExternalItemDirective } from '@foblex/flow';

import { AgentDetailsModalService } from '../../../../services/agent-details-modal.service';
import { AgentDetailsData } from '../../../agent-details-modal';
import {
  ReusableCardComponent,
  ReusableCardData,
} from '../../../reusable-card';

export interface AgentCardNodeData {
  readonly id: string;
  readonly name: string;
  readonly tags?: string[];
  readonly icon: string;
  readonly type: string;
  readonly disabled?: boolean;
  readonly selected?: boolean;
  readonly data?: any;
}

@Component({
  selector: 'app-agent-card-node',
  standalone: true,
  imports: [CommonModule, ReusableCardComponent, FExternalItemDirective],
  templateUrl: './agent-card-node.component.html',
  styleUrls: ['./agent-card-node.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgentCardNodeComponent implements AfterViewInit {
  // Service injection
  private readonly agentDetailsModalService = inject(AgentDetailsModalService);
  private readonly elementRef = inject(ElementRef);

  // Signal inputs
  readonly data = input.required<AgentCardNodeData>();
  readonly draggable = input<boolean>(true);
  readonly isExternalItem = input<boolean>(false);
  readonly disableModal = input<boolean>(false);

  // Signal outputs
  readonly cardClicked = output<AgentCardNodeData>();
  readonly dragStarted = output<DragEvent>();

  // Computed values
  readonly cardData = computed(() => this.transformToCardData());
  readonly isInteractive = computed(() => !this.data().disabled);

  ngAfterViewInit(): void {
    // Re-enable pointer events on the host element of this component.
    // The fExternalItem directive will have already set it to 'none' on children.
    this.elementRef.nativeElement.style.pointerEvents = 'auto';
  }

  protected onCardClick(event: Event): void {
    // Prevent default to avoid conflicts with drag operations
    event.preventDefault();
    event.stopPropagation();

    if (this.isInteractive()) {
      // Only open modal if not disabled
      if (!this.disableModal()) {
        const agentData = this.transformToAgentDetailsData();
        this.agentDetailsModalService.openAgentDetails(agentData);
      }

      // Always emit the card click event for any other listeners
      this.cardClicked.emit(this.data());
    }
  }

  protected onDragStart(event: DragEvent): void {
    if (this.draggable() && this.isInteractive()) {
      // Set drag data
      if (event.dataTransfer) {
        event.dataTransfer.setData('text/plain', JSON.stringify(this.data()));
        event.dataTransfer.effectAllowed = 'copy';
      }
      this.dragStarted.emit(event);
    }
  }

  private transformToCardData(): ReusableCardData {
    const agentData = this.data();

    return {
      // Header Section
      headerIcon: {
        iconName: agentData.icon,
        iconSize: 16,
        iconColor: '#2563eb', // Primary blue
      },
      title: agentData.name,

      // Content Section
      tags: agentData.tags?.map(tag => ({
        label: tag,
        color: 'default',
        size: 'sm',
        pill: false,
      })),

      // Rating Section
      rating: {
        iconName: 'eye',
        iconSize: 16,
        iconColor: '#2563eb', // Primary blue
        clickable: false,
      },

      // Meta Info Section (showing type)
      metaLeft: {
        text: 'Agent',
      },
      metaRight: {
        iconName: 'grip-vertical',
        iconSize: 16,
        iconColor: '#2563eb', // Primary blue
      },

      // General
      disabled: agentData.disabled || false,
      selected: agentData.selected || false,

      // Custom styles for agent card
      customStyles: {
        '--card-cursor': this.draggable() ? 'grab' : 'pointer',
        '--card-transition': 'all 0.2s ease',
        '--card-hover-transform': 'translateY(-2px)',
        '--card-hover-shadow': '0 4px 12px rgba(0, 0, 0, 0.1)',
      },
    };
  }

  private transformToAgentDetailsData(): AgentDetailsData {
    const agentData = this.data();

    // For now, we'll use mock data since the agent card doesn't have all the required fields
    // In a real implementation, you'd fetch the full agent details from a service
    return {
      id: agentData.id,
      name: agentData.name,
      role: agentData.type || 'Agent',
      practiceArea: 'Software Development', // This would come from the agent data
      goodAt: agentData.tags || [],
      model: 'GPT 3.5', // This would come from the agent data
      temperature: 0.3,
      maxRpm: 100,
      maxToken: 4000,
      topP: 0.95,
      maxIteration: 5,
      maxExecutionTime: 300,
      knowledgeBase: ['KB File 1', 'KB File 2', 'KB File 3'], // This would come from the agent data
      guardrails: ['Software Development', 'Management'], // This would come from the agent data
      tools: ['Serper Dev', 'Website Scraper'], // This would come from the agent data
      goal: 'This agent is designed to help with various tasks and provide assistance.',
      backStory:
        'This agent was created to assist users with their workflow and automation needs.',
      description: `This is ${agentData.name}, a specialized agent designed to help with ${agentData.type || 'various'} tasks.`,
    };
  }
}
