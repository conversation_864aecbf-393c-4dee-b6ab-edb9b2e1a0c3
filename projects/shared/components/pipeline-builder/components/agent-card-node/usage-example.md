# Agent Card Node Component Usage

## Overview

The `AgentCardNodeComponent` is a presentational component that wraps the `ReusableCardComponent` with agent-specific configuration. It's designed to be used in the pipeline builder's library panel for displaying draggable agent cards.

## Basic Usage

```typescript
import { Agent<PERSON>ardNodeComponent, AgentCardNodeData } from './agent-card-node';

// In your component
const agentData: AgentCardNodeData = {
  id: 'agent-1',
  name: 'Agent Alpha',
  description: 'AI-powered automation agent',
  icon: 'bot',
  type: 'agent',
  disabled: false,
  selected: false,
};
```

```html
<app-agent-card-node
  [data]="agentData"
  [draggable]="true"
  (cardClicked)="onAgentClick($event)"
  (dragStarted)="onDragStart($event)"
>
</app-agent-card-node>
```

## Component Features

### Inputs

- `data: AgentCardNodeData` (required) - Agent data configuration
- `draggable: boolean` (optional, default: true) - Whether the card can be dragged

### Outputs

- `cardClicked: AgentCardNodeData` - Emitted when the card is clicked
- `dragStarted: DragEvent` - Emitted when drag starts

### AgentCardNodeData Interface

```typescript
interface AgentCardNodeData {
  readonly id: string; // Unique identifier
  readonly name: string; // Agent name
  readonly description: string; // Agent description
  readonly icon: string; // Lucide icon name
  readonly type: 'agent'; // Always 'agent'
  readonly disabled?: boolean; // Disable interaction
  readonly selected?: boolean; // Visual selection state
}
```

## Styling Features

### Visual States

- **Default**: Clean card with hover effects
- **Draggable**: Cursor changes to grab/grabbing
- **Selected**: Blue border with shadow
- **Disabled**: Reduced opacity, no interaction

### Custom Styling

The component uses CSS custom properties for theming:

- `--card-cursor`: Cursor style (grab/grabbing/default)
- `--card-transition`: Transition animation
- `--card-hover-transform`: Hover transform effect
- `--card-hover-shadow`: Hover shadow effect

## Example Implementation

```typescript
// In your library panel component
export class LibraryPanelComponent {
  agents: AgentCardNodeData[] = [
    {
      id: 'agent-1',
      name: 'Agent Alpha',
      description: 'AI-powered automation agent',
      icon: 'bot',
      type: 'agent',
    },
    {
      id: 'agent-2',
      name: 'Agent Beta',
      description: 'Data processing specialist',
      icon: 'database',
      type: 'agent',
    },
  ];

  onAgentClick(agent: AgentCardNodeData): void {
    console.log('Agent clicked:', agent.name);
  }

  onDragStart(event: DragEvent): void {
    console.log('Drag started:', event);
  }
}
```

```html
<!-- In your template -->
<div class="agents-list">
  @for (agent of agents; track agent.id) {
  <app-agent-card-node
    [data]="agent"
    [draggable]="true"
    (cardClicked)="onAgentClick($event)"
    (dragStarted)="onDragStart($event)"
  >
  </app-agent-card-node>
  }
</div>
```

## Integration with Reusable Card

The component automatically transforms `AgentCardNodeData` into `ReusableCardData`:

- **Header Icon**: Uses the agent's icon with primary blue color
- **Title**: Agent name
- **Description**: Agent description
- **Meta Left**: Shows "Agent" type indicator
- **Custom Styles**: Agent-specific hover and interaction effects

This ensures consistency with the design system while providing agent-specific functionality.
