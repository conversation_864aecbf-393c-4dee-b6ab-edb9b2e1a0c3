.agent-card-node {
  cursor: pointer;
  position: relative;

  &.draggable {
    // cursor: grab;

    &:active {
      cursor: grabbing;
    }
  }

  // &.selected {
  //   border: 2px solid var(--primary-500, #2563eb);
  //   box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  // }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }

  // Override reusable-card styles for agent-specific appearance
  // ::ng-deep {
  //   .aava-default-card {
  //     border-radius: 8px;
  //     border: 1px solid var(--border-color, #e5e7eb);
  //     background: var(--surface-elevated, #ffffff);

  //     &:hover {
  //       border-color: var(--primary-300, #93c5fd);
  //     }
  //   }

  //   .card-header {
  //     padding: 0.75rem;

  //     .header-icon {
  //       background-color: var(--primary-50, #eff6ff);
  //       border-radius: 6px;
  //       padding: 0.5rem;
  //       display: flex;
  //       align-items: center;
  //       justify-content: center;
  //     }
  //   }

  //   .card-content {
  //     padding: 0 0.75rem 0.75rem 0.75rem;

  //     .card-description {
  //       font-size: 0.875rem;
  //       line-height: 1.4;
  //       color: var(--text-secondary, #6b7280);
  //       margin: 0.5rem 0 0 0;
  //     }
  //   }

  //   .card-meta {
  //     padding: 0 0.75rem 0.75rem 0.75rem;

  //     .meta-info {
  //       font-size: 0.75rem;
  //       color: var(--text-tertiary, #9ca3af);

  //       .meta-icon {
  //         margin-right: 0.25rem;
  //       }
  //     }
  //   }
  // }
}
