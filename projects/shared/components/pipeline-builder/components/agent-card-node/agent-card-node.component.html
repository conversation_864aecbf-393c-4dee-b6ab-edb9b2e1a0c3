@if (isExternalItem()) {
  <div
    fExternalItem
    [fData]="data()"
    [fPreviewMatchSize]="true"
    class="agent-card-node"
    [class.draggable]="draggable()"
    [class.selected]="data().selected"
    [class.disabled]="data().disabled"
    (click)="onCardClick($event)"
  >
    <app-reusable-card [data]="cardData()" [width]="'100%'" [height]="'auto'">
    </app-reusable-card>
  </div>
} @else {
  <div
    class="agent-card-node"
    [class.draggable]="draggable()"
    [class.selected]="data().selected"
    [class.disabled]="data().disabled"
  >
    <app-reusable-card [data]="cardData()" [width]="'100%'" [height]="'auto'">
    </app-reusable-card>
  </div>
}
