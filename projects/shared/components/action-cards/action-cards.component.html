<div class="action-cards-container">
  @for (card of actionCards; track card.text) {
    <aava-default-card
      class="action-card"
      [class.disabled]="card.disabled"
      id="action-card"
      [class.icon-border]="card.iconBorder"
      [style.background]="card.background || card.bg"
      [style.border-color]="card.border"
      [style.background-image]="card.background?.includes('gradient') ? card.background : 'none'"
      (click)="onCardClick(card.text, card)"
      >
      <div class="action-card-content">
        <div class="icon-container">
          <aava-icon
            [iconName]="card.iconName"
            [iconSize]="16"
            [iconColor]="card.disabled ? '#9CA3AF' : '#9661F1'">
          </aava-icon>
        </div>
        <p class="action-text" [class.disabled-text]="card.disabled">{{ card.text }}</p>
      </div>
    </aava-default-card>
  }
</div>
