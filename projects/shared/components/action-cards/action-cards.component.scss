.action-cards-container {
  display: flex;
  flex-direction: column;
  gap: 18px;
  height: 100%;
}

.action-card{
  border-radius: 12px;
}
::ng-deep #action-card .ava-default-card-container .ava-default-card{
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  border-radius: 12px;
  padding: 16px 12px;
  box-shadow: none;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
}

.icon-border .icon-container {
  border: 1px solid #E2E8F0;
}

.action-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  gap: 4px;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: white;
  border-radius: 50%;
  padding: 10px;
  flex-shrink: 0;
  margin-bottom: 8px;
}

.action-text {
  color: #9661F1;
  font-family: Inter, sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  margin: 0;
  flex: 1;
  text-align: center;
}

.disabled-text {
  color: #9CA3AF !important;
}

.disabled {
  pointer-events: none;
  cursor: default;
  opacity: 0.6;
}
