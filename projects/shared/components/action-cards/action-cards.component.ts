import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { AavaIconComponent, AavaDefaultCardComponent } from "@aava/play-core";
import { CommonModule } from '@angular/common';

type VariantType = 'launchpad' | 'console';

interface ActionCardItem {
  iconName: string;
  text: string;
  bg?: string;
  background?: string; // For gradient support
  border?: string;
  iconBorder?: boolean;
  disabled?: boolean;
}

@Component({
  selector: 'app-action-cards',
  standalone: true,
  imports: [AavaIconComponent, AavaDefaultCardComponent, CommonModule],
  templateUrl: './action-cards.component.html',
  styleUrl: './action-cards.component.scss'
})
export class ActionCardsComponent implements OnChanges, OnInit {
  @Input() variant: VariantType = 'launchpad';
  @Output() cardClicked = new EventEmitter<string>();
  
  actionCards: ActionCardItem[] = [];
  
  private launchpadCards: ActionCardItem[] = [
    {
      iconName: 'play',
      text: 'Resume playground',
      background: 'linear-gradient(82deg, #E6F3FF 0.87%, #F2EBFD 98.34%)',
      border: '#B0D9FF',
      iconBorder: false,
      disabled: true
    },
    {
      iconName: 'plus',
      text: 'Create',
      background: '#FFFFFF',
      border: '#D6C2F9',
      iconBorder: true
    }
  ];

  private consoleCards: ActionCardItem[] = [
    {
      iconName: 'plus',
      text: 'Create',
      background: 'linear-gradient(82deg, #E6F3FF 0.87%, #F2EBFD 98.34%)',
      border: '#B0D9FF',
      iconBorder: true
    }
  ];

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['variant']) {
      this.updateActionCards();
    }
  }

  private updateActionCards(): void {
    switch (this.variant) {
      case 'console':
        this.actionCards = [...this.consoleCards];
        break;
      case 'launchpad':
      default:
        this.actionCards = [...this.launchpadCards];
        break;
    }
  }

  ngOnInit(): void {
    this.updateActionCards();
  }

  onCardClick(text: string, card: ActionCardItem) {
    if (card.disabled) {
      return; // Don't emit click event for disabled cards
    }
    this.cardClicked.emit(text);
  }
  
}
