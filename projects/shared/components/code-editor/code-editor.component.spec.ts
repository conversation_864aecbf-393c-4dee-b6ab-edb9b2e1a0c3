import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CodeEditorComponent } from './code-editor.component';

describe('CodeEditorComponent', () => {
  let component: CodeEditorComponent;
  let fixture: ComponentFixture<CodeEditorComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CodeEditorComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(CodeEditorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.title).toBe('Code Editor');
    expect(component.value).toBe('');
    expect(component.language).toBe('plaintext');
    expect(component.theme).toBe('light');
    expect(component.readOnly).toBe(false);
    expect(component.showLineNumbers).toBe(true);
    expect(component.height).toBe(400);
  });

  it('should emit value change when text is entered', () => {
    spyOn(component.valueChange, 'emit');
    component.value = 'test code';
    component.ngOnChanges({
      value: {
        currentValue: 'test code',
        previousValue: '',
        firstChange: true,
        isFirstChange: () => true,
      },
    });
    expect(component.valueChange.emit).toHaveBeenCalledWith('test code');
  });

  it('should emit language change when language is selected', () => {
    spyOn(component.languageChange, 'emit');
    component.onLanguageSelect('python');
    expect(component.languageChange.emit).toHaveBeenCalledWith('python');
  });

  it('should return correct language display name', () => {
    expect(component.getLanguageDisplayName('python')).toBe('Python');
    expect(component.getLanguageDisplayName('javascript')).toBe('JavaScript');
    expect(component.getLanguageDisplayName('json')).toBe('JSON');
  });
});
