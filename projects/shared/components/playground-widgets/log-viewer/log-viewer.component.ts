import { CommonModule } from '@angular/common';
import {
  Component,
  ElementRef,
  Input,
  OnDestroy,
  OnInit,
  ViewChild,
  AfterViewChecked,
} from '@angular/core';
import { Subject } from 'rxjs';
import { LoadingOverlayComponent } from '../../loading-overlay/loading-overlay.component';

export interface LogEntry {
  id: string;
  timestamp: Date;
  message: string;
  details?: string[];
}

// Interface for color-coded logs similar to your previous version
export interface ColoredLogEntry {
  content: string;
  color: string;
  timestamp?: Date;
}

@Component({
  selector: 'app-log-viewer',
  standalone: true,
  imports: [CommonModule, LoadingOverlayComponent],
  templateUrl: './log-viewer.component.html',
  styleUrl: './log-viewer.component.scss',
})
export class LogViewerComponent implements OnInit, OnDestroy, AfterViewChecked {
  @Input() playgroundType: string = 'pipeline';
  @Input() loaderText: string = 'Loading execution logs...';
  @ViewChild('logContainer', { static: false }) logContainer!: ElementRef;

  @Input() logs: LogEntry[] = [];
  @Input() coloredLogs: ColoredLogEntry[] = []; // New input for colored logs

  @Input() isLoading = false;
  @Input() autoScroll = true;

  private destroy$ = new Subject<void>();

  ngOnInit() {
    // Only load mock data if no colored logs are provided and playground type is not pipeline
    if (this.coloredLogs.length === 0 && this.playgroundType !== 'pipeline') {
      this.loadMockLogs();
    }
  }

  ngAfterViewChecked() {
    if (this.autoScroll && this.logContainer) {
      this.scrollToBottom();
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadMockLogs() {
    // Simulate API data matching Figma design
    this.logs = [
      {
        id: '1',
        timestamp: new Date('2024-01-15T10:14:23Z'),
        message: 'Input Received',
        details: [
          'Support Ticket ID: #CST-2025-0671',
          'Ticket Content: "Hi, I placed an order 3 days ago but haven\'t received any update on the shipping. Can you confirm when my package will arrive?"',
        ],
      },
      {
        id: '2',
        timestamp: new Date('2024-01-15T10:14:25Z'),
        message: 'Pre-processing',
        details: [
          'Cleaned text (removed signatures, greetings, formatting)',
          'Token count: 42',
        ],
      },
      {
        id: '3',
        timestamp: new Date('2024-01-15T10:14:26Z'),
        message: 'Analysis',
        details: [
          'Detected Issue Type: Delivery Delay',
          'Sentiment: Neutral → Slightly Frustrated',
          'Urgency Level: Medium',
        ],
      },
      {
        id: '4',
        timestamp: new Date('2024-01-15T10:14:27Z'),
        message: 'Summarization',
        details: [
          'Draft summary generated:',
          '"Customer is requesting an update on a delayed order placed 3 days ago. Wants confirmation of delivery status."',
        ],
      },
      {
        id: '5',
        timestamp: new Date('2024-01-15T10:14:28Z'),
        message: 'Output Finalized',
        details: [
          'Summary saved to CRM system.',
          'Forwarded to Support Queue: Delivery Team.',
        ],
      },
      {
        id: '6',
        timestamp: new Date('2024-01-15T10:14:23Z'),
        message: 'Input Received',
        details: [
          'Support Ticket ID: #CST-2025-0671',
          'Ticket Content: "Hi, I placed an order 3 days ago but haven\'t received any update on the shipping. Can you confirm when my package will arrive?"',
        ],
      },
      {
        id: '7',
        timestamp: new Date('2024-01-15T10:14:25Z'),
        message: 'Pre-processing',
        details: [
          'Cleaned text (removed signatures, greetings, formatting)',
          'Token count: 42',
        ],
      },
      {
        id: '8',
        timestamp: new Date('2024-01-15T10:14:26Z'),
        message: 'Analysis',
        details: [
          'Detected Issue Type: Delivery Delay',
          'Sentiment: Neutral → Slightly Frustrated',
          'Urgency Level: Medium',
        ],
      },
      {
        id: '9',
        timestamp: new Date('2024-01-15T10:14:27Z'),
        message: 'Summarization',
        details: [
          'Draft summary generated:',
          '"Customer is requesting an update on a delayed order placed 3 days ago. Wants confirmation of delivery status."',
        ],
      },
      {
        id: '10',
        timestamp: new Date('2024-01-15T10:14:28Z'),
        message: 'Output Finalized',
        details: [
          'Summary saved to CRM system.',
          'Forwarded to Support Queue: Delivery Team.',
        ],
      },
    ];
  }

  formatTimestamp(timestamp: Date): string {
    return `[${timestamp.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })}]`;
  }

  trackByLogId(index: number, log: LogEntry): string {
    return log.id;
  }

  trackByColoredLogIndex(index: number, log: ColoredLogEntry): number {
    return index;
  }

  private scrollToBottom(): void {
    try {
      this.logContainer.nativeElement.scrollTop = this.logContainer.nativeElement.scrollHeight;
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  formatColoredLogTimestamp(timestamp?: Date): string {
    if (!timestamp) {
      return `[${new Date().toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      })}]`;
    }
    return `[${timestamp.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })}]`;
  }
}
