/**
 * Configuration for individual filter options
 */
export interface FilterOption {
  readonly id: string;
  readonly label: string;
  readonly value: string;
}

/**
 * Configuration for radio button groups
 */
export interface RadioFilterConfig {
  readonly title: string;
  readonly options: FilterOption[];
  readonly selectedValue?: string;
  readonly disabled?: boolean;
}

/**
 * Configuration for select dropdowns
 */
export interface SelectFilterConfig {
  readonly title: string;
  readonly options: FilterOption[];
  readonly selectedValues?: string[];
  readonly multiple?: boolean;
  readonly disabled?: boolean;
  readonly placeholder?: string;
}

/**
 * Main filter component configuration
 */
export interface FilterComponentConfig {
  readonly searchPlaceholder?: string;
  readonly orientation?: 'horizontal' | 'vertical';
  readonly showArtifactType?: boolean;
  readonly showPracticeArea?: boolean;
  readonly showCapabilityTags?: boolean;
  readonly showRealm?: boolean;
  readonly showSortBy?: boolean;
  readonly showStatus?: boolean;
  readonly showSampleFiles?: boolean;

  // Filter data
  readonly artifactTypeOptions?: FilterOption[];
  readonly practiceAreaOptions?: FilterOption[];
  readonly capabilityTagsOptions?: FilterOption[];
  readonly realmOptions?: FilterOption[];
  readonly sortByOptions?: FilterOption[];
  readonly statusOptions?: FilterOption[];
  readonly sampleFilesOptions?: FilterOption[];
}

/**
 * Filter state containing current selections
 */
export interface FilterState {
  readonly searchQuery: string;
  readonly artifactType?: string;
  readonly practiceArea?: string;
  readonly capabilityTags: string[];
  readonly realm: string[];
  readonly sortBy?: string;
  readonly status?: string;
  readonly sampleFiles?: string;
  readonly isOpen: boolean;
}

/**
 * Filter change event data
 */
export interface FilterChangeEvent {
  readonly searchQuery: string;
  readonly artifactType?: string;
  readonly practiceArea?: string;
  readonly capabilityTags: string[];
  readonly realm: string[];
  readonly sortBy?: string;
  readonly status?: string;
  readonly sampleFiles?: string;
}
