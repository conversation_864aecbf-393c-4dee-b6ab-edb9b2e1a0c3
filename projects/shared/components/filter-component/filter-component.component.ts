import {
  AavaTextboxComponent,
  AavaButtonComponent,
  AavaSelectComponent,
  AavaSelectOptionComponent,
  AavaTagComponent,
  AavaIconComponent,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  signal,
  computed,
  ElementRef,
  ViewChild,
  HostListener,
  inject,
  OnInit,
} from '@angular/core';
import { FormsModule } from '@angular/forms';

import {
  GoodAtTagsService,
  GoodAtTag,
} from '../../services/good-at-tags.service';
import {
  PracticeAreasService,
  PracticeArea,
} from '../../services/practice-areas.service';

import {
  FilterComponentConfig,
  FilterState,
  FilterChangeEvent,
  FilterOption,
} from './filter-config.interface';

@Component({
  selector: 'app-filter-component',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AavaTextboxComponent,
    AavaButtonComponent,
    AavaSelectComponent,
    AavaSelectOptionComponent,
    AavaTagComponent,
    AavaIconComponent,
  ],
  templateUrl: './filter-component.component.html',
  styleUrls: ['./filter-component.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FilterComponentComponent implements OnInit {
  @ViewChild('filterPopup', { static: false })
  filterPopup!: ElementRef<HTMLDivElement>;

  // Service injections
  private goodAtTagsService = inject(GoodAtTagsService);
  private practiceAreasService = inject(PracticeAreasService);

  // Inputs
  readonly config = input.required<FilterComponentConfig>();
  readonly searchValue = input<string>('');
  readonly isOpen = input<boolean>(false);

  // Outputs
  readonly searchChanged = output<string>();
  readonly filterChanged = output<FilterChangeEvent>();
  readonly filterToggled = output<boolean>();

  // Internal state
  private readonly internalState = signal<FilterState>({
    searchQuery: this.searchValue(),
    artifactType: undefined,
    practiceArea: undefined,
    capabilityTags: [],
    realm: [],
    sortBy: undefined,
    status: undefined,
    sampleFiles: undefined,
    isOpen: this.isOpen(),
  });

  // Data loading signals
  readonly goodAtTags = signal<GoodAtTag[]>([]);
  readonly practiceAreas = signal<PracticeArea[]>([]);
  readonly isLoadingData = signal<boolean>(false);

  // Computed properties
  readonly currentState = computed(() => this.internalState());
  readonly isFilterOpen = computed(() => this.currentState().isOpen);

  // Default configurations
  readonly defaultArtifactTypeOptions: FilterOption[] = [
    { id: 'all', label: 'All Artifacts', value: 'all' },
    { id: 'agent', label: 'Agent', value: 'agent' },
    { id: 'pipeline', label: 'Pipeline', value: 'pipeline' },
    { id: 'knowledge', label: 'Knowledge Base', value: 'knowledge' },
    { id: 'tool', label: 'Tool', value: 'tool' },
    { id: 'guardrail', label: 'Guardrail', value: 'guardrail' },
  ];

  readonly defaultPracticeAreaOptions: FilterOption[] = [
    { id: 'all', label: 'All Category', value: 'all' },
    { id: 'application', label: 'Application', value: 'application' },
    { id: 'business', label: 'Business', value: 'business' },
    { id: 'database', label: 'Database', value: 'database' },
  ];

  readonly defaultCapabilityTagsOptions: FilterOption[] = [
    { id: 'automation', label: 'Automation', value: 'automation' },
    { id: 'integration', label: 'Integration', value: 'integration' },
    { id: 'validation', label: 'Validation', value: 'validation' },
    { id: 'generation', label: 'Generation', value: 'generation' },
    { id: 'ux-testing', label: 'UX-testing', value: 'ux-testing' },
    { id: 'review', label: 'Review', value: 'review' },
    { id: 'instruction', label: 'Instruction', value: 'instruction' },
    { id: 'parser', label: 'Parser', value: 'parser' },
  ];

  readonly defaultRealmOptions: FilterOption[] = [
    { id: 'ascendion', label: 'Ascendion', value: 'ascendion' },
    { id: 'axos', label: 'Axos', value: 'axos' },
    { id: 'hp', label: 'HP', value: 'hp' },
    { id: 'nvidia', label: 'Nvidia', value: 'nvidia' },
  ];

  readonly defaultSortByOptions: FilterOption[] = [
    {
      id: 'recently-created',
      label: 'Recently Created',
      value: 'recently-created',
    },
    { id: 'most-viewed', label: 'Most Viewed', value: 'most-viewed' },
    { id: 'most-used', label: 'Most Used', value: 'most-used' },
    { id: 'highest-rated', label: 'Highest Rated', value: 'highest-rated' },
  ];

  readonly defaultStatusOptions: FilterOption[] = [
    { id: 'active', label: 'Active', value: 'active' },
    { id: 'draft', label: 'Draft', value: 'draft' },
    { id: 'deprecated', label: 'Deprecated', value: 'deprecated' },
  ];

  readonly defaultSampleFilesOptions: FilterOption[] = [
    { id: 'true', label: 'True', value: 'true' },
    { id: 'false', label: 'False', value: 'false' },
  ];

  // Computed properties
  readonly orientation = computed(
    () => this.config().orientation || 'horizontal'
  );

  // Computed filter configurations
  readonly artifactTypeConfig = computed(() => ({
    title: 'Artifact Type',
    options:
      this.config().artifactTypeOptions || this.defaultArtifactTypeOptions,
    selectedValue: this.currentState().artifactType,
    disabled: false,
  }));

  readonly practiceAreaConfig = computed(() => {
    const options =
      this.config().practiceAreaOptions ||
      this.practiceAreas().map(area => ({
        id: area.name,
        label: area.name,
        value: area.name,
      }));

    // console.log('Practice Area Config:', {
    //   options: JSON.stringify(options, null, 2),
    //   selectedValue: this.currentState().practiceArea,
    //   practiceAreas: JSON.stringify(this.practiceAreas(), null, 2),
    // });

    return {
      title: 'Practice Area',
      options,
      selectedValue: this.currentState().practiceArea,
      multiple: false,
      disabled: false,
      placeholder: 'Select',
    };
  });

  readonly capabilityTagsConfig = computed(() => {
    const options =
      this.config().capabilityTagsOptions ||
      this.goodAtTags().map(tag => ({
        id: tag.name,
        label: tag.name,
        value: tag.name,
      }));

    // console.log('Capability Tags Config:', {
    //   options: JSON.stringify(options, null, 2),
    //   selectedValues: this.currentState().capabilityTags,
    //   goodAtTags: JSON.stringify(this.goodAtTags(), null, 2),
    // });

    return {
      title: 'Capability Tags',
      options,
      selectedValues: this.currentState().capabilityTags,
      multiple: true,
      disabled: false,
      placeholder: 'Search',
    };
  });

  readonly realmConfig = computed(() => ({
    title: 'Realm',
    options: this.config().realmOptions || this.defaultRealmOptions,
    selectedValues: this.currentState().realm,
    multiple: true,
    disabled: false,
    placeholder: 'Search',
  }));

  readonly sortByConfig = computed(() => ({
    title: 'Sort By',
    options: this.config().sortByOptions || this.defaultSortByOptions,
    selectedValue: this.currentState().sortBy,
    multiple: false,
    disabled: false,
    placeholder: 'Select',
  }));

  readonly statusConfig = computed(() => ({
    title: 'Status',
    options: this.config().statusOptions || this.defaultStatusOptions,
    selectedValue: this.currentState().status,
    disabled: false,
  }));

  readonly sampleFilesConfig = computed(() => ({
    title: 'Sample Files',
    options: this.config().sampleFilesOptions || this.defaultSampleFilesOptions,
    selectedValue: this.currentState().sampleFiles,
    disabled: false,
  }));

  // Event handlers
  ngOnInit(): void {
    this.loadDropdownData();
  }

  private loadDropdownData(): void {
    this.isLoadingData.set(true);

    // Load both services in parallel
    Promise.all([
      this.goodAtTagsService.getGoodAtTags().toPromise(),
      this.practiceAreasService.getPracticeAreas().toPromise(),
    ])
      .then(([goodAtTags, practiceAreas]) => {
        // console.log('Loaded goodAtTags:', JSON.stringify(goodAtTags, null, 2));
        // console.log('Loaded practiceAreas:', JSON.stringify(practiceAreas, null, 2));
        this.goodAtTags.set(goodAtTags || []);
        this.practiceAreas.set(practiceAreas || []);
        this.isLoadingData.set(false);
      })
      .catch(error => {
        console.error('Error loading dropdown data:', error);
        this.isLoadingData.set(false);
      });
  }

  protected onSearchChange(value: string): void {
    this.internalState.update(state => ({
      ...state,
      searchQuery: value,
    }));
    this.searchChanged.emit(value);
  }

  protected onFilterToggle(): void {
    const newState = !this.isFilterOpen();
    this.internalState.update(state => ({
      ...state,
      isOpen: newState,
    }));
    this.filterToggled.emit(newState);
  }

  protected onArtifactTypeChange(value: string): void {
    this.updateState({ artifactType: value });
  }

  protected onPracticeAreaChange(value: string): void {
    this.updateState({ practiceArea: value });
  }

  protected onCapabilityTagsChange(values: string[]): void {
    this.updateState({ capabilityTags: values });
  }

  protected onRealmChange(values: string[]): void {
    this.updateState({ realm: values });
  }

  protected onSortByChange(value: string): void {
    this.updateState({ sortBy: value });
  }

  protected onStatusChange(value: string): void {
    this.updateState({ status: value });
  }

  protected onSampleFilesChange(value: string): void {
    this.updateState({ sampleFiles: value });
  }

  protected onTagRemove(tagValue: string, type: 'capability' | 'realm'): void {
    if (type === 'capability') {
      const newTags = this.currentState().capabilityTags.filter(
        tag => tag !== tagValue
      );
      this.updateState({ capabilityTags: newTags });
    } else {
      const newTags = this.currentState().realm.filter(tag => tag !== tagValue);
      this.updateState({ realm: newTags });
    }
  }

  protected onApplyFilters(): void {
    const state = this.currentState();
    const event: FilterChangeEvent = {
      searchQuery: state.searchQuery,
      artifactType: state.artifactType,
      practiceArea: state.practiceArea,
      capabilityTags: state.capabilityTags,
      realm: state.realm,
      sortBy: state.sortBy,
      status: state.status,
      sampleFiles: state.sampleFiles,
    };
    this.filterChanged.emit(event);
    this.onFilterToggle(); // Close the popup
  }

  protected onCancelFilters(): void {
    this.onFilterToggle(); // Close the popup
  }

  // Click outside to close
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (
      this.isFilterOpen() &&
      this.filterPopup &&
      !this.filterPopup.nativeElement.contains(event.target as Node)
    ) {
      this.onFilterToggle();
    }
  }

  private updateState(updates: Partial<FilterState>): void {
    this.internalState.update(state => ({
      ...state,
      ...updates,
    }));
  }
}
