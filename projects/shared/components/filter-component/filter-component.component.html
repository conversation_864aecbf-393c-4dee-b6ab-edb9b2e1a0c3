<div class="filter-component" [class.vertical]="orientation() === 'vertical'">
  <!-- Search and Filter Button Row -->
  <div class="filter-controls">
    <!-- Search Textbox (90% width) -->
    <div class="search-container">
      <aava-textbox
        size="sm"
        [placeholder]="config().searchPlaceholder || 'Search...'"
        [ngModel]="currentState().searchQuery"
        (ngModelChange)="onSearchChange($event)"
        variant="default"
        class="search-input"
      >
        <aava-icon
          iconName="search"
          size="16"
          color="rgba(108, 117, 125, 1)"
          slot="prefix"
        ></aava-icon>
      </aava-textbox>
    </div>

    <!-- Filter But<PERSON> (10% width) -->
    <div class="filter-button-container">
      <aava-button
        id="filter-button"
        size="sm"
        variant="primary"
        [outlined]="true"
        (userClick)="onFilterToggle()"
        [class.active]="isFilterOpen()"
        class="filter-button"
        [ariaLabel]="'Open filters'"
        iconName="list-filter"
        iconPosition="only"
        [iconSize]="16"
        color="rgba(108, 117, 125, 1)"
      >
      </aava-button>
    </div>
  </div>

  <!-- Filter Popup -->
  <div
    #filterPopup
    class="filter-popup"
    [class.open]="isFilterOpen()"
    *ngIf="isFilterOpen()"
  >
    <div class="filter-content">
      <!-- Filter Categories Row -->
      <div
        class="filter-categories"
        [class.vertical]="orientation() === 'vertical'"
      >
        <!-- Artifact Type -->
        <div
          class="filter-category"
          *ngIf="config().showArtifactType !== false"
        >
          <h4 class="filter-title">{{ artifactTypeConfig().title }}</h4>
          <div class="radio-group">
            <label
              *ngFor="let option of artifactTypeConfig().options"
              class="radio-option"
            >
              <input
                type="radio"
                name="artifact-type"
                [value]="option.value"
                [checked]="option.value === artifactTypeConfig().selectedValue"
                (change)="onArtifactTypeChange(option.value)"
                [disabled]="artifactTypeConfig().disabled"
                class="radio-input"
              />
              <span class="radio-label">{{ option.label }}</span>
            </label>
          </div>
        </div>

        <!-- Practice Area -->
        <div
          class="filter-category"
          *ngIf="config().showPracticeArea !== false"
        >
          <h4 class="filter-title">{{ practiceAreaConfig().title }}</h4>
          <aava-select
            size="sm"
            [multiple]="practiceAreaConfig().multiple"
            [placeholder]="practiceAreaConfig().placeholder"
            [disabled]="practiceAreaConfig().disabled"
            [ngModel]="practiceAreaConfig().selectedValue"
            (selectionChange)="onPracticeAreaChange($event)"
            class="select-input"
          >
            <aava-select-option
              *ngFor="let option of practiceAreaConfig().options"
              [value]="option.value"
            >
              {{ option.label }}
            </aava-select-option>
          </aava-select>
        </div>

        <!-- Capability Tags -->
        <div
          class="filter-category"
          *ngIf="config().showCapabilityTags !== false"
        >
          <h4 class="filter-title">{{ capabilityTagsConfig().title }}</h4>
          <aava-select
            size="sm"
            [multiple]="capabilityTagsConfig().multiple"
            [placeholder]="capabilityTagsConfig().placeholder"
            [disabled]="capabilityTagsConfig().disabled"
            [ngModel]="capabilityTagsConfig().selectedValues"
            (selectionChange)="onCapabilityTagsChange($event)"
            class="select-input"
          >
            <aava-select-option
              *ngFor="let option of capabilityTagsConfig().options"
              [value]="option.value"
            >
              {{ option.label }}
            </aava-select-option>
          </aava-select>

          <!-- Selected Tags -->
          <div
            class="selected-tags"
            *ngIf="currentState().capabilityTags.length > 0"
          >
            <aava-tag
              *ngFor="let tag of currentState().capabilityTags"
              [label]="tag"
              size="sm"
              variant="outlined"
              [removable]="true"
              (removed)="onTagRemove(tag, 'capability')"
              class="selected-tag"
            ></aava-tag>
          </div>
        </div>

        <!-- Realm -->
        <div class="filter-category" *ngIf="config().showRealm !== false">
          <h4 class="filter-title">{{ realmConfig().title }}</h4>
          <aava-select
            size="sm"
            [multiple]="realmConfig().multiple"
            [placeholder]="realmConfig().placeholder"
            [disabled]="realmConfig().disabled"
            [ngModel]="realmConfig().selectedValues"
            (selectionChange)="onRealmChange($event)"
            class="select-input"
          >
            <aava-select-option
              *ngFor="let option of realmConfig().options"
              [value]="option.value"
            >
              {{ option.label }}
            </aava-select-option>
          </aava-select>

          <!-- Selected Tags -->
          <div class="selected-tags" *ngIf="currentState().realm.length > 0">
            <aava-tag
              *ngFor="let tag of currentState().realm"
              [label]="tag"
              size="sm"
              variant="outlined"
              [removable]="true"
              (removed)="onTagRemove(tag, 'realm')"
              class="selected-tag"
            ></aava-tag>
          </div>
        </div>

        <!-- Sort By -->
        <div class="filter-category" *ngIf="config().showSortBy !== false">
          <h4 class="filter-title">{{ sortByConfig().title }}</h4>
          <aava-select
            size="sm"
            [multiple]="sortByConfig().multiple"
            [placeholder]="sortByConfig().placeholder"
            [disabled]="sortByConfig().disabled"
            [ngModel]="sortByConfig().selectedValue"
            (selectionChange)="onSortByChange($event)"
            class="select-input"
          >
            <aava-select-option
              *ngFor="let option of sortByConfig().options"
              [value]="option.value"
            >
              {{ option.label }}
            </aava-select-option>
          </aava-select>
        </div>

        <!-- Status -->
        <div class="filter-category" *ngIf="config().showStatus !== false">
          <h4 class="filter-title">{{ statusConfig().title }}</h4>
          <div class="radio-group">
            <label
              *ngFor="let option of statusConfig().options"
              class="radio-option"
            >
              <input
                type="radio"
                name="status"
                [value]="option.value"
                [checked]="option.value === statusConfig().selectedValue"
                (change)="onStatusChange(option.value)"
                [disabled]="statusConfig().disabled"
                class="radio-input"
              />
              <span class="radio-label">{{ option.label }}</span>
            </label>
          </div>
        </div>

        <!-- Sample Files -->
        <div class="filter-category" *ngIf="config().showSampleFiles !== false">
          <h4 class="filter-title">{{ sampleFilesConfig().title }}</h4>
          <div class="radio-group">
            <label
              *ngFor="let option of sampleFilesConfig().options"
              class="radio-option"
            >
              <input
                type="radio"
                name="sample-files"
                [value]="option.value"
                [checked]="option.value === sampleFilesConfig().selectedValue"
                (change)="onSampleFilesChange(option.value)"
                [disabled]="sampleFilesConfig().disabled"
                class="radio-input"
              />
              <span class="radio-label">{{ option.label }}</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="filter-actions">
        <aava-button
          size="sm"
          variant="secondary"
          (userClick)="onCancelFilters()"
          class="cancel-button"
          label="Cancel"
        >
        </aava-button>
        <aava-button
          size="sm"
          variant="primary"
          (userClick)="onApplyFilters()"
          class="apply-button"
          label="Apply"
        >
        </aava-button>
      </div>
    </div>
  </div>
</div>
