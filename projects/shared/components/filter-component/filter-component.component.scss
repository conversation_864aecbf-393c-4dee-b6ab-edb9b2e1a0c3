::ng-deep #filter-button .ava-button.ava-button--outlined {
  background: white !important;
}

.filter-component {
  position: relative;
  width: 100%;

  // Search and Filter Button Row (90:10 ratio)
  .filter-controls {
    display: flex;
    gap: 8px;
    width: 100%;
    align-items: center;

    .search-container {
      flex: 0 0 97%; // 90% width
      min-width: 0; // Allow flex item to shrink below content size

      .search-input {
        width: 100%;
      }
    }

    .filter-button-container {
      flex: 0 0 5%; // 10% width
      display: flex;
      justify-content: flex-end;

      .filter-button {
        width: 100%;
        min-width: 40px; // Ensure button has minimum width

        // &.active {
        //   background-color: rgba(59, 130, 246, 0.1);
        //   border-color: rgba(59, 130, 246, 1);
        //   color: rgba(59, 130, 246, 1);

        //   aava-icon {
        //     color: rgba(59, 130, 246, 1) !important;
        //   }
        // }
      }
    }
  }

  // Filter Popup
  .filter-popup {
    position: absolute;
    top: 100%;
    left: 0; // Position it to the left to prevent overflow
    right: 0; // Take full width of parent
    z-index: 9999;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    margin-top: 4px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease-in-out;
    width: 100%; // Take full width of parent
    max-width: 100%; // Prevent overflow
    min-width: 280px; // Minimum width for usability (reduced for narrow panels)

    &.open {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .filter-content {
      padding: 16px;

      // Filter Categories Grid
      .filter-categories {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 16px;
        margin-bottom: 20px;
        width: 100%; // Take full width
        max-width: 100%; // Prevent overflow

        // Vertical orientation - single column
        &.vertical {
          grid-template-columns: 1fr;
          gap: 12px;
        }

        .filter-category {
          display: flex;
          flex-direction: column;
          gap: 8px;
          width: 100%;
          min-width: 0; // Allow flex item to shrink below content size
          overflow: hidden;
          max-width: 100%; // Allow full width in parent container

          .filter-title {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            line-height: 1.4;
          }

          .radio-group {
            display: flex;
            flex-direction: column;
            gap: 6px;

            .radio-option {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 13px;
              color: #6b7280;
              cursor: pointer;

              .radio-input {
                margin: 0;
                cursor: pointer;
              }

              .radio-label {
                cursor: pointer;
              }

              &:hover {
                color: #374151;
              }
            }
          }

          .select-input {
            width: 100%;
            max-width: 100%;
            overflow: hidden;

            // Fix for multiselect growing width issue
            ::ng-deep .aava-select {
              .aava-select-container {
                width: 100% !important;
                max-width: 100% !important;
                overflow: hidden; // Hide horizontal overflow

                .aava-select {
                  width: 100% !important;
                  max-width: 100% !important;

                  .aava-select-placeholder {
                    // Limit the display text width and handle overflow
                    width: 100% !important;
                    max-width: 100% !important;
                    overflow: hidden !important;
                    text-overflow: ellipsis !important;
                    white-space: nowrap !important;
                    display: block !important;
                  }
                }
              }
            }
          }

          .selected-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-top: 6px;

            .selected-tag {
              font-size: 12px;
            }
          }
        }
      }

      // Action Buttons
      .filter-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        padding-top: 16px;
        border-top: 1px solid #e5e7eb;

        .cancel-button,
        .apply-button {
          min-width: 80px;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .filter-component {
    .filter-popup {
      .filter-content {
        .filter-categories {
          grid-template-columns: repeat(4, 1fr);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .filter-component {
    .filter-popup {
      .filter-content {
        .filter-categories {
          grid-template-columns: 1fr; // Single column on mobile
        }
      }
    }
  }
}

// Vertical orientation specific styles
.filter-component.vertical {
  .filter-popup {
    .filter-content {
      .filter-categories {
        grid-template-columns: 1fr;
        gap: 12px;
      }
    }
  }
}

@media (max-width: 1024px) {
  .filter-component {
    .filter-popup {
      .filter-content {
        .filter-categories {
          grid-template-columns: repeat(5, 1fr);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .filter-component {
    .filter-popup {
      .filter-content {
        .filter-categories {
          grid-template-columns: repeat(4, 1fr);
          gap: 16px;
        }
      }
    }
  }
}

@media (max-width: 640px) {
  .filter-component {
    .filter-popup {
      .filter-content {
        .filter-categories {
          grid-template-columns: repeat(3, 1fr);
          gap: 12px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .filter-component {
    .filter-popup {
      .filter-content {
        .filter-categories {
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;
        }

        .filter-actions {
          flex-direction: column;
          gap: 8px;

          .cancel-button,
          .apply-button {
            width: 100%;
          }
        }
      }
    }
  }
}

@media (max-width: 320px) {
  .filter-component {
    .filter-popup {
      .filter-content {
        .filter-categories {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .filter-component {
    .filter-popup {
      background: #1f2937;
      border-color: #374151;

      .filter-content {
        .filter-categories {
          .filter-category {
            .filter-title {
              color: #f9fafb;
            }

            .radio-group {
              .radio-option {
                color: #d1d5db;
              }
            }
          }
        }

        .filter-actions {
          border-top-color: #374151;
        }
      }
    }
  }
}

// Fix dropdown positioning issues in modals
::ng-deep .filter-component {
  // Ensure the filter popup has proper stacking context
  .filter-popup {
    position: relative;
    z-index: 1000;
    // Prevent the popup from creating a new stacking context
    isolation: auto;
  }

  // Disable the Aava select's JavaScript positioning by overriding the container
  .aava-select {
    .select-option-container {
      position: absolute !important;
      top: 100% !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 100000 !important;
      // Prevent the JavaScript from overriding this
      pointer-events: auto !important;

      .aava-select-panel {
        position: relative !important;
        z-index: 100000 !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: auto !important;
        transform: none !important;
        width: 100% !important;
        max-height: 200px !important;
        overflow-y: auto !important;
        // Override any JavaScript positioning
        margin: 0 !important;
        padding: 0 !important;
      }
    }
  }
}

// Global fix for Aava select dropdowns in modals
::ng-deep .aava-select-panel {
  position: fixed !important;
  z-index: 100000 !important;
  // Ensure it's positioned relative to the viewport
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
}

// Fix for multiselect width growing issue globally
::ng-deep .filter-component .aava-select {
  width: 100% !important;
  max-width: 100% !important;

  .aava-select-container {
    // Ensure consistent width for all select inputs
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
    box-sizing: border-box !important;

    .aava-select {
      width: 100% !important;
      max-width: 100% !important;
      box-sizing: border-box !important;

      .aava-select-placeholder {
        width: 100% !important;
        max-width: 100% !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
        display: block !important;
        box-sizing: border-box !important;
      }
    }
  }
}

// More aggressive fix for multiselect content
::ng-deep .filter-component {
  .filter-categories {
    .filter-category {
      .select-input {
        width: 100% !important;
        max-width: 100% !important;
        overflow: hidden !important;

        aava-select {
          width: 100% !important;
          max-width: 100% !important;
          display: block !important;

          .aava-select-container {
            width: 100% !important;
            max-width: 100% !important;
            overflow: hidden !important;

            .aava-select {
              width: 100% !important;
              max-width: 100% !important;
              overflow: hidden !important;

              .aava-select-placeholder {
                width: 100% !important;
                max-width: 100% !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                white-space: nowrap !important;
              }
            }
          }
        }
      }
    }
  }
}

// Ensure modal overlays don't interfere with select dropdowns
::ng-deep .cdk-overlay-container {
  z-index: 1000 !important;
}

::ng-deep .cdk-overlay-pane {
  z-index: 1000 !important;
}

// Responsive breakpoints for more compact layout
@media (max-width: 768px) {
  .filter-component {
    .filter-popup {
      right: 0;
      left: auto;
      width: calc(100vw - 32px); // Full width minus padding on mobile
      max-width: 400px;

      .filter-content {
        padding: 12px;

        .filter-categories {
          grid-template-columns: 1fr;
          gap: 12px;
          width: 100%;
        }
      }
    }
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .filter-component {
    .filter-popup {
      right: 0;
      left: auto;
      max-width: 500px;

      .filter-content {
        .filter-categories {
          grid-template-columns: repeat(auto-fit, minmax(160px, max-content));
          gap: 14px;
        }
      }
    }
  }
}
