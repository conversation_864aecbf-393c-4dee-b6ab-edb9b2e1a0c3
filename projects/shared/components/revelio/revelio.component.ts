import { CommonModule } from '@angular/common';
import {
  Component,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  AfterViewChecked,
  On<PERSON>nit,
  On<PERSON><PERSON>roy,
  HostListener,
  ChangeDetectorRef,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  ViewEncapsulation,
  Input,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MarkdownModule } from 'ngx-markdown';
import { Router } from '@angular/router';

import { RevelioDataService } from '../../services/revelio-data.service';
import { RevelioSkeletonComponent } from '../revelio-skeleton/revelio-skeleton.component';
import { SiriTextBoxComponent } from '../siri-text-box/siri-text-box.component';
import {
  WidgetBoxComponent,
  AIResponseData,
  SearchResult,
} from '../widget-box/widget-box.component';

// Interface for chat message
export interface RevelioChatMessage {
  from: 'user' | 'ai';
  text: string;
  type?: 'text' | 'paragraph' | 'card';
  timestamp?: Date;
}

// Interface for response data
interface ResponseData {
  response?: string;
  type: 'text' | 'markdown' | 'html' | 'json';
}

@Component({
  selector: 'app-revelio',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MarkdownModule,
    SiriTextBoxComponent,
    RevelioSkeletonComponent,
    WidgetBoxComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './revelio.component.html',
  styleUrls: ['./revelio.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class RevelioComponent implements OnInit, AfterViewChecked, OnDestroy {
  // State management
  isOpen = false;
  currentInput = '';
  searchValue = '';
  isLoadingResponse = false;
  aiResponse = '';
  aiResponseType: 'text' | 'paragraph' | 'card' = 'text';
  showResponse = false;

  // WidgetBox data for AI responses
  aiWidgetData: AIResponseData | null = null;

  // Search results array for display
  searchResults: SearchResult[] = [];

  // Internal properties
  private isUserScrolling = false;
  private scrollTimeout: ReturnType<typeof setTimeout> | null = null;
  private shouldScrollToBottom = true;
  _toggleRevelio = false;

  // Optional output to emit user messages to parent
  @Output() messageSent = new EventEmitter<RevelioChatMessage>();
  @Input() set toggleRevelio(value: boolean) {
    this._toggleRevelio = value;
    this.toggleChat();
  }
  get toggleRevelio(): boolean {
    return this._toggleRevelio;
  }

  @ViewChild('chatContainer') private chatContainer!: ElementRef;
  @ViewChild('popoverElement') private popoverElement!: ElementRef;
  @ViewChild('chatInput') private chatInput!: ElementRef;

  cdr = inject(ChangeDetectorRef);
  private router = inject(Router);
  private revelioDataService = inject(RevelioDataService);

  ngOnInit() {
    // Initialize loading and response state
    this.isLoadingResponse = false;
    this.showResponse = false;
    this.aiResponse = '';

    // Initialize with a welcome message
    this.addAIMessage("Hello! I'm here to help. How can I assist you today?");
  }

  ngAfterViewChecked() {
    if (this.shouldScrollToBottom && !this.isUserScrolling) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  ngOnDestroy() {
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
  }

  // Toggle chat popover
  toggleChat() {
    this.isOpen = !this.isOpen;
    if (this.isOpen) {
      // Focus input when opening - fallback to DOM access since component doesn't expose focus method
      setTimeout(() => {
        if (this.chatInput?.nativeElement) {
          const inputElement =
            this.chatInput.nativeElement.querySelector('input');
          if (inputElement) {
            inputElement.focus();
          }
        }
      }, 100);
    }
  } // Close chat popover
  closeChat() {
    this.isOpen = false;
    // Optionally hide the response when chat is closed
    // this.showResponse = false;
  }

  // Handle search click from ava-search-bar
  onSearchClick(searchValue: string) {
    this.currentInput = searchValue || this.searchValue; // Use the bound value if searchValue is empty
    this.onSubmit();
  }

  // Handle message submission
  onSubmit() {
    // If currentInput is empty, use the bound searchValue
    if (!this.currentInput.trim()) {
      this.currentInput = this.searchValue;
    }

    if (this.currentInput.trim()) {
      const userMessage: RevelioChatMessage = {
        from: 'user',
        text: this.currentInput.trim(),
        type: 'text',
        timestamp: new Date(),
      };

      // Emit message to parent if needed
      this.messageSent.emit(userMessage);

      // Reset response state and set loading state for skeleton
      this.showResponse = false;
      this.aiResponse = '';
      this.isLoadingResponse = true;
      this.searchResults = []; // Clear previous search results

      // Clear input using multiple approaches for reliability
      this.currentInput = '';
      this.searchValue = ''; // Clear the bound value - this should clear the search bar

      // Force change detection first
      this.cdr.detectChanges();

      // Additional approach: Try to clear using the component's internal methods
      setTimeout(() => {
        this.clearSearchBarProgrammatically();
      }, 10);

      // Schedule scroll to bottom
      this.shouldScrollToBottom = true;

      // Simulate AI response (replace with actual AI integration)
      this.simulateAIResponse(userMessage.text);
    }
  }

  // Handle input changes from ava-search-bar
  onInputChange(event: unknown) {
    const value = this.extractValueFromEvent(event);
    this.currentInput = value;
    this.searchValue = value;
  }

  private extractValueFromEvent(event: unknown): string {
    if (typeof event === 'string') {
      return event;
    }

    if (event && typeof event === 'object') {
      const eventObj = event as Record<string, unknown>;

      // Check common event value paths
      if (typeof eventObj['value'] === 'string') {
        return eventObj['value'];
      }

      if (eventObj['detail'] && typeof eventObj['detail'] === 'object') {
        const detail = eventObj['detail'] as Record<string, unknown>;
        if (typeof detail['value'] === 'string') {
          return detail['value'];
        }
      }

      if (eventObj['target'] && typeof eventObj['target'] === 'object') {
        const target = eventObj['target'] as Record<string, unknown>;
        if (typeof target['value'] === 'string') {
          return target['value'];
        }
      }
    }

    return '';
  }

  // Clear the search bar programmatically
  private clearSearchBarProgrammatically() {
    if (this.chatInput?.nativeElement) {
      try {
        const inputElement =
          this.chatInput.nativeElement.querySelector('input');
        if (inputElement) {
          inputElement.value = '';
          inputElement.dispatchEvent(new Event('input', { bubbles: true }));
        }
      } catch (error) {
        // Silent fail
      }
    }
  }

  // Handle Enter key press
  onKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.onSubmit();
    }
  }

  // Handle Enter key press from text-box component
  onTextBoxEnter(event: KeyboardEvent) {
    this.onKeyPress(event);
  }

  // Handle microphone click from siri-text-box component
  onMicClick() {
    // Microphone implementation will be added in future
  }

  // Add AI message to chat
  private addAIMessage(
    text: string,
    type: 'text' | 'paragraph' | 'card' = 'text'
  ) {
    // We're just keeping this method signature for API compatibility
    // but not storing messages in an array anymore
    this.shouldScrollToBottom = true;
  }

  // Simulate AI response (replace with actual AI service integration)
  private simulateAIResponse(userInput: string) {
    // Show loading state in widget
    this.aiWidgetData = {
      text: '',
      type: 'text',
      isLoading: true,
      showActions: false,
    };
    this.showResponse = true;

    // Call the actual API instead of simulating
    this.callAvaAPI(userInput);
  }

  // Call the AVA API with the user input using the data service
  private callAvaAPI(userInputText: string) {
    // Save the search query to history
    this.revelioDataService.saveToHistory(userInputText);

    // Determine the search mode based on the current route
    const currentUrl = this.router.url;
    const isMarketplace = currentUrl.includes('/marketplace');
    const mode = isMarketplace ? 'find' : 'help';

    // Call the search API through the service with the appropriate mode
    this.revelioDataService.search(userInputText, mode).subscribe({
      next: response => {
        // Handle successful response
        this.handleApiResponse(response);
      },
      error: error => {
        // Handle error response
        this.handleApiError(error);
      },
    });
  }

  // Handle successful API response
  private handleApiResponse(response: unknown) {
    // Check if this is a search results response
    if (response && typeof response === 'object') {
      const responseObj = response as Record<string, unknown>;

      // Check if it's the new search API format
      if (responseObj['results'] && Array.isArray(responseObj['results'])) {
        const searchResults = responseObj['results'] as SearchResult[];

        // Store search results in component
        this.searchResults = searchResults;

        // Clear loading state
        this.isLoadingResponse = false;
        this.showResponse = true;

        // Add to messages array for chat history
        this.addAIMessage(
          `Found ${searchResults.length} search results for your query`,
          'card'
        );
        return;
      }
    }

    this.handleTextResponse(response);
  }

  private handleTextResponse(response: unknown) {
    let responseText = '';
    let responseType: 'text' | 'markdown' | 'html' | 'json' = 'text';

    // Extract the response text based on the API response structure
    if (response && typeof response === 'object') {
      const responseObj = response as Record<string, unknown>;
      if (
        responseObj['result'] ||
        responseObj['output'] ||
        responseObj['message']
      ) {
        responseText =
          (responseObj['result'] as string) ||
          (responseObj['output'] as string) ||
          (responseObj['message'] as string);
      } else if (typeof response === 'string') {
        responseText = response;
      } else {
        responseText = JSON.stringify(response, null, 2);
        responseType = 'json';
      }
    } else if (typeof response === 'string') {
      responseText = response;
    } else {
      responseText = 'Received response from API';
    }

    // Update widget with the API response
    this.updateWidgetWithResponse({
      response: responseText,
      type: responseType,
    });
  }

  // Handle API error
  private handleApiError(error: unknown) {
    console.error('API Error:', error);

    let errorMessage =
      'Sorry, I encountered an error while processing your request.';

    if (error && typeof error === 'object') {
      const errorObj = error as Record<string, unknown>;
      if (errorObj['error'] && typeof errorObj['error'] === 'object') {
        const errorDetail = errorObj['error'] as Record<string, unknown>;
        if (errorDetail['message']) {
          errorMessage = `Error: ${errorDetail['message']}`;
        }
      } else if (errorObj['message']) {
        errorMessage = `Error: ${errorObj['message']}`;
      } else if (errorObj['status']) {
        errorMessage = `API Error (${errorObj['status']}): ${
          errorObj['statusText'] || 'Unknown error'
        }`;
      }
    }

    // Update widget with error message
    this.updateWidgetWithResponse({
      response: errorMessage,
      type: 'text',
    });
  }

  private updateWidgetWithResponse(responseData: ResponseData) {
    // Handle text-based responses
    const responseText = responseData.response || 'No response';
    this.aiWidgetData = {
      text: responseText,
      type: responseData.type,
      isLoading: false,
      showActions: true,
      timestamp: new Date(),
    };

    // Clear old loading state and set response data (for backward compatibility)
    this.isLoadingResponse = false;
    this.showResponse = true;
    this.aiResponse = responseText;
    this.aiResponseType = responseData.type === 'markdown' ? 'card' : 'text';

    // Also add to messages array for potential future use
    this.addAIMessage(
      responseText,
      responseData.type === 'markdown' ? 'card' : 'text'
    );
  }

  // Widget event handlers
  onWidgetActionClicked(action?: string) {
    // Handle widget actions (you can implement specific logic here)
    if (action?.startsWith('view:')) {
      // Implement view logic here - e.g., navigate to result details
      // You can emit an event to parent component or navigate to a route
    } else if (action?.startsWith('copy:')) {
      // Implement copy logic here - e.g., copy result URL to clipboard
      // You can construct a URL and copy it to clipboard
    }
  }

  onWidgetTextCopied() {
    // Handle text copied event (you can show a toast notification here)
  }

  // Scroll to bottom of chat
  private scrollToBottom() {
    if (this.chatContainer?.nativeElement) {
      const scrollContainer = this.chatContainer.nativeElement;
      scrollContainer.scrollTop = scrollContainer.scrollHeight;
    }
  }

  // Close popover when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    if (this.isOpen && this.popoverElement?.nativeElement) {
      const target = event.target as HTMLElement;
      const popover = this.popoverElement.nativeElement;
      const chatIcon = document.querySelector('.revelio-chat-icon');

      // Check if click is outside popover and chat icon
      if (!popover.contains(target) && !chatIcon?.contains(target)) {
        this.closeChat();
      }
    }
  }

  // Track by function for search results
  trackByResultId(index: number, result: SearchResult): number {
    return result.id;
  }

  // Get highlighted text with fallback to original text
  getHighlightedText(
    highlights: string[] | undefined,
    originalText: string
  ): string {
    if (highlights && highlights.length > 0) {
      return highlights[0];
    }
    return originalText;
  }

  // Handle viewing a specific result
  onViewResult(result: SearchResult) {
    this.onWidgetActionClicked(`view:${result.id}`);
  }

  // Handle search result actions from Aava cards
  onSearchResultAction(action: string, result: SearchResult) {
    // Emit the action for parent component handling
    this.onWidgetActionClicked(`${action}:${result.id}`);
  }

  /**
   * Create AIResponseData object for search results
   */
  getSearchResultResponse(searchResult: SearchResult): AIResponseData {
    return {
      type: 'search-result',
      searchResult: searchResult,
      isLoading: false,
      timestamp: new Date(),
      showActions: true,
    };
  }

  /**
   * Create AIResponseData object for no results message
   */
  getNoResultResponse(): AIResponseData {
    return {
      type: 'text',
      text: "No result found. Why don't you try asking something else? Or create your own artifact by navigating to /artifact page.",
      isLoading: false,
      timestamp: new Date(),
      showActions: false,
    };
  }
}
