<div
  #popoverElement
  class="revelio-popover"
  [class.open]="_toggleRevelio"
  [class.closed]="!_toggleRevelio"
>
  <!-- Ava Search Bar -->

  <app-siri-text-box
    [config]="{
      placeholder: 'Ask me',
      showMicIcon: true,
      showSearchIcon: true,
      clearable: true,
      animated: true,
    }"
    theme="dark"
    (valueChange)="onInputChange($event)"
    (enter)="onTextBoxEnter($event)"
    (micClick)="onMicClick()"
  ></app-siri-text-box>

  <!-- Loading Skeleton -->
  <div class="response-area" *ngIf="isLoadingResponse">
    <!-- <aava-skeleton
      shape="rounded"
      width="100%"
      height="120px"
      animation="wave"
    ></aava-skeleton> -->
    <app-revelio-skeleton></app-revelio-skeleton>
  </div>

  <!-- AI Response Display using WidgetBox -->
  <div *ngIf="showResponse && !isLoadingResponse" class="response-area">
    <!-- Display search results using AavaCardComponent -->
    <div *ngIf="searchResults.length > 0">
      <div class="search-result-item">
        <!-- Using widget-box component instead -->
        <app-widget-box
          [aiResponse]="getSearchResultResponse(searchResults[0])"
          variant="detailed"
          [showTimestamp]="true"
        ></app-widget-box>
      </div>
    </div>

    <!-- Using widget-box component instead -->
    <app-widget-box
      *ngIf="aiWidgetData && searchResults.length === 0"
      [aiResponse]="getNoResultResponse()"
      variant="compact"
      [showTimestamp]="false"
    ></app-widget-box>
  </div>
</div>
