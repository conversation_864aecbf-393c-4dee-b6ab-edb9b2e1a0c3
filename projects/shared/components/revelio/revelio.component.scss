// Revelio Chat Component Styles
:host {
  position: relative;
  display: inline-block;
}

.revelio-chat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

// Pop-over with minimal styling
.revelio-popover {
  position: absolute;
  right: 0;
  width: 350px;
  max-width: calc(100vw - 24px);
  max-height: 100vh;
  margin-right: 10px;
  display: flex;
  flex-direction: column;
  overflow: auto;
  z-index: 1000;

  opacity: 0;
  pointer-events: none;

  &.open {
    opacity: 1;
    pointer-events: all;
  }

  &.closed {
    opacity: 0;
    pointer-events: none;
  }

  // Ensure Siri text box integrates well with the popover
  app-siri-text-box {
    margin-bottom: 12px;
  }
}

// Search Area for Aava Search Bar with Glass Design
.search-area {
  // Glass morphism container
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 0px;
  box-shadow:
    0 8px 32px rgba(31, 38, 135, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow:
      0 12px 40px rgba(31, 38, 135, 0.35),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }

  &:focus-within {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 99, 107, 0.5); // Matching your brand color
    box-shadow:
      0 12px 40px rgba(31, 38, 135, 0.35),
      0 0 0 2px rgba(255, 99, 107, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }

  .ava-textbox--glass-50 .ava-textbox__container {
    background: none !important;
    border: none !important;
  }

  // Dark theme adjustments (if supported)
  [data-theme='dark'] & {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.1);

    &:hover {
      background: rgba(0, 0, 0, 0.3);
      border-color: rgba(255, 255, 255, 0.2);
    }

    &:focus-within {
      background: rgba(0, 0, 0, 0.4);
    }

    ::ng-deep aava-search-bar {
      .search-input {
        color: rgba(255, 255, 255, 0.9);

        &::placeholder {
          color: rgba(255, 255, 255, 0.6);
        }

        &:focus {
          background: rgba(255, 255, 255, 0.05);
        }
      }
    }
  }
} // Response Area for Skeleton Loader and AI Response
.response-area {
  ::ng-deep .ai-response-skeleton {
    .skeleton__line {
      border-radius: 6px;
    }
  }

  ::ng-deep .revelio-ai-response-internal {
    .description-container {
      margin-top: 0;
      border: none;
      padding: 0;
    }
  }
}

// AI Response Container with Play Design System Card
.ai-response-container {
  margin-top: 8px;

  .ava-card {
    width: 100%;

    .ava-card-header {
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-weight: 600;
        font-size: 16px;
        line-height: 1.4;
      }
    }

    .ava-card-content {
      font-size: 14px;
      line-height: 1.6;

      ::ng-deep {
        p {
          margin: 0 0 12px 0;

          &:last-child {
            margin-bottom: 0;
          }
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin: 16px 0 8px 0;

          &:first-child {
            margin-top: 0;
          }
        }

        ul,
        ol {
          margin: 8px 0;
          padding-left: 20px;

          li {
            margin: 4px 0;
          }
        }

        code {
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 13px;
        }

        pre {
          padding: 12px;
          border-radius: 8px;
          overflow-x: auto;
          margin: 12px 0;

          code {
            padding: 0;
          }
        }

        strong {
          font-weight: 600;
        }
      }
    }
  }
}

// AAVA Default Card with Glassmorphism Styling
.glass-card {
  display: block;
  position: relative;
  width: 100%;
  padding: 20px;
  border-radius: 16px;
  overflow: hidden;

  /* Glassmorphism Core Properties */
  background: var(--glass-background-color, rgba(255, 255, 255, 0.25));
  backdrop-filter: blur(var(--glass-backdrop-blur, 12px));
  -webkit-backdrop-filter: blur(var(--glass-backdrop-blur, 12px));

  /* Border with enhanced glassmorphic effect */
  border: 2px solid;
  border-image: linear-gradient(
      135deg,
      rgba(var(--rgb-white, 255, 255, 255), 0.8) 0%,
      rgba(var(--rgb-brand-primary, 37, 99, 235), 0.3) 25%,
      rgba(var(--rgb-white, 255, 255, 255), 0.6) 50%,
      rgba(var(--rgb-brand-secondary, 3, 189, 212), 0.3) 75%,
      rgba(var(--rgb-white, 255, 255, 255), 0.8) 100%
    )
    1;

  /* Fallback solid border for better browser support */
  border-color: var(--glass-border-color, rgba(255, 255, 255, 0.5));

  /* Shadow for depth */
  box-shadow: var(--glass-elevation, 0 8px 32px rgba(31, 38, 135, 0.37));

  /* Inner glow effect for additional depth */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(var(--rgb-white, 255, 255, 255), 0.1) 0%,
      rgba(var(--rgb-white, 255, 255, 255), 0.05) 50%,
      rgba(var(--rgb-white, 255, 255, 255), 0.1) 100%
    );
    border-radius: inherit;
    pointer-events: none;
    z-index: -1;
  }

  /* Hover state for enhanced interactivity */
  &:hover {
    box-shadow:
      var(--glass-elevation, 0 8px 32px rgba(31, 38, 135, 0.37)),
      0 4px 16px rgba(var(--rgb-brand-primary, 37, 99, 235), 0.1);
    background: rgba(var(--rgb-white, 255, 255, 255), 0.35);

    /* Enhanced border on hover */
    border-image: linear-gradient(
        135deg,
        rgba(var(--rgb-brand-primary, 37, 99, 235), 0.6) 0%,
        rgba(var(--rgb-white, 255, 255, 255), 0.9) 25%,
        rgba(var(--rgb-brand-secondary, 3, 189, 212), 0.5) 50%,
        rgba(var(--rgb-white, 255, 255, 255), 0.9) 75%,
        rgba(var(--rgb-brand-primary, 37, 99, 235), 0.6) 100%
      )
      1;
    border-color: rgba(var(--rgb-brand-primary, 37, 99, 235), 0.4);
  }

  /* Style the content inside the card */
  aava-card-content {
    display: block;
    position: relative;
    z-index: 1;

    /* Text styling */
    color: var(--color-text-primary, var(--global-color-gray-700));
    font-size: 14px;
    line-height: 1.6;

    /* Content elements */
    ::ng-deep {
      p {
        margin: 0 0 12px 0;

        &:last-child {
          margin-bottom: 0;
        }
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 16px 0 8px 0;
        color: var(--color-text-primary, var(--global-color-gray-700));
        font-weight: 600;

        &:first-child {
          margin-top: 0;
        }
      }

      ul,
      ol {
        margin: 8px 0;
        padding-left: 20px;

        li {
          margin: 4px 0;
        }
      }

      /* Code styling with glassmorphic background */
      code {
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 13px;
        background: rgba(var(--rgb-brand-primary, 37, 99, 235), 0.1);
        border: 1px solid rgba(var(--rgb-brand-primary, 37, 99, 235), 0.2);
        color: var(--color-brand-primary, var(--global-color-blue-500));
        font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
      }

      /* Pre blocks with enhanced glassmorphic styling */
      pre {
        padding: 16px;
        border-radius: 12px;
        overflow-x: auto;
        margin: 12px 0;
        background: rgba(var(--rgb-brand-primary, 37, 99, 235), 0.05);
        border: 1px solid rgba(var(--rgb-brand-primary, 37, 99, 235), 0.15);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);

        code {
          padding: 0;
          background: transparent;
          border: none;
          color: inherit;
        }
      }

      /* Links with brand colors */
      a {
        color: var(--color-brand-primary, var(--global-color-blue-500));
        text-decoration: none;

        &:hover {
          color: var(--color-brand-primary-hover, var(--global-color-blue-700));
          text-decoration: underline;
        }
      }

      /* Enhanced emphasis styling */
      strong,
      b {
        font-weight: 600;
        color: var(--color-text-primary, var(--global-color-gray-700));
      }

      em,
      i {
        font-style: italic;
        color: var(--color-text-secondary, var(--global-color-aqua-500));
      }
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    padding: 16px;
    border-radius: 12px;

    aava-card-content {
      font-size: 13px;
    }
  }

  /* Dark theme adjustments (if supported) */
  [data-theme='dark'] & {
    background: rgba(var(--rgb-black, 0, 0, 0), 0.3);
    border-color: rgba(var(--rgb-white, 255, 255, 255), 0.1);

    &::before {
      background: linear-gradient(
        135deg,
        rgba(var(--rgb-white, 255, 255, 255), 0.05) 0%,
        rgba(var(--rgb-white, 255, 255, 255), 0.02) 50%,
        rgba(var(--rgb-white, 255, 255, 255), 0.05) 100%
      );
    }

    &:hover {
      background: rgba(var(--rgb-black, 0, 0, 0), 0.4);
      border-color: rgba(var(--rgb-brand-primary, 37, 99, 235), 0.4);
    }
  }
}

// AI Response Card styling (legacy - keeping for backward compatibility)
.ai-response-card {
  width: 100%;
  margin-top: 8px;

  .card-header {
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-weight: 600;
      font-size: 16px;
      line-height: 1.4;
    }
  }

  .card-content {
    font-size: 14px;
    line-height: 1.6;

    p {
      margin: 0 0 12px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin: 16px 0 8px 0;

      &:first-child {
        margin-top: 0;
      }
    }

    ul,
    ol {
      margin: 8px 0;
      padding-left: 20px;
    }

    li {
      margin: 4px 0;
    }

    code {
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 13px;
    }

    pre {
      padding: 12px;
      border-radius: 8px;
      overflow-x: auto;
      margin: 12px 0;
    }
  }
}

// Trail Input Section Styling
.trail-input-section {
  margin-top: 16px;
}

// Responsive adjustments
@media (max-width: 375px) {
  .revelio-popover {
    width: 280px;
    right: 0;
    max-width: calc(100vw - 12px);
  }
}

// Search Results Container
.search-results-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 100vh;
  overflow-y: auto;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.4);
    }
  }

  // Hide scrollbar for Firefox
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);

  // Wrapper div for each search result
  .search-result-item {
    width: 100%;

    // Minimal styling for aava-default-card - only dimensions
    aava-default-card {
      width: 100%;
      min-height: 200px;
    }

    // Enhanced emphasis styling for em tags in search results
    ::ng-deep {
      em {
        font-style: italic;
        font-weight: 600;
        color: var(--color-brand-primary, #3b82f6);
        background: rgba(var(--rgb-brand-primary, 59, 130, 246), 0.1);
        padding: 2px 4px;
        border-radius: 3px;
        text-decoration: underline;
        text-decoration-color: var(--color-brand-primary, #3b82f6);
        text-decoration-thickness: 1px;
        text-underline-offset: 2px;
      }
    }
  }
}

// Legacy search results styling (keeping for backward compatibility)
.legacy-search-results-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.4);
    }
  }

  // Hide scrollbar for Firefox
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);

  // Individual search result card styling using AavaCardComponent
  .search-result-card {
    margin-bottom: 8px;

    &:hover {
      // Apply hover effects to the aava-card component
      ::ng-deep aava-card {
        box-shadow:
          0 10px 25px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }
    }

    // Style the aava-card component itself
    ::ng-deep aava-card {
      display: block;
      width: 100%;
    }

    // Card Header styling
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid rgba(226, 232, 240, 0.5);

      .result-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        line-height: 1.3;
        color: var(--color-text-primary, #1f2937);
        flex: 1;
        margin-right: 12px;

        // Highlighted text styling
        ::ng-deep mark,
        ::ng-deep .highlight {
          background-color: rgba(255, 193, 7, 0.3);
          border-radius: 4px;
          padding: 2px 4px;
          font-weight: 700;
          box-decoration-break: clone;
          -webkit-box-decoration-break: clone;
        }
      }

      .result-score {
        font-size: 12px;
        font-weight: 600;
        color: #1e40af;
        background: rgba(59, 130, 246, 0.1);
        padding: 4px 12px;
        border-radius: 16px;
        border: 1px solid rgba(59, 130, 246, 0.2);
        white-space: nowrap;
      }
    }

    // Card Content styling
    .card-content {
      // Meta tags container
      .result-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 16px;

        .meta-tag {
          font-size: 11px;
          font-weight: 600;
          padding: 4px 10px;
          border-radius: 12px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          border: 1px solid;

          &.entity {
            background: rgba(16, 185, 129, 0.1);
            color: #047857;
            border-color: rgba(16, 185, 129, 0.3);
          }

          &.organization {
            background: rgba(59, 130, 246, 0.1);
            color: #1d4ed8;
            border-color: rgba(59, 130, 246, 0.3);
          }

          &.domain {
            background: rgba(139, 92, 246, 0.1);
            color: #6d28d9;
            border-color: rgba(139, 92, 246, 0.3);
          }

          &.team {
            background: rgba(245, 101, 101, 0.1);
            color: #b91c1c;
            border-color: rgba(245, 101, 101, 0.3);
          }
        }
      }

      // Description styling
      .result-description {
        margin-bottom: 16px;

        p {
          margin: 0;
          font-size: 14px;
          line-height: 1.6;
          color: var(--color-text-primary, #374151);

          // Highlighted description text
          ::ng-deep mark,
          ::ng-deep .highlight {
            background-color: rgba(255, 193, 7, 0.3);
            border-radius: 3px;
            padding: 1px 3px;
            font-weight: 600;
            box-decoration-break: clone;
            -webkit-box-decoration-break: clone;
          }
        }
      }

      // Stats container
      .result-stats {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        padding: 12px 0;
        border-top: 1px solid rgba(226, 232, 240, 0.5);

        .stat-item {
          font-size: 13px;
          color: var(--color-text-secondary, #6b7280);

          strong {
            font-weight: 600;
            color: var(--color-text-primary, #374151);
          }
        }
      }
    }

    // Card Actions styling following AAVA button patterns
    .card-actions {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding-top: 16px;
      border-top: 1px solid rgba(226, 232, 240, 0.5);

      .action-btn {
        font-size: 13px;
        font-weight: 500;
        padding: 8px 16px;
        border-radius: 8px;
        border: 1px solid;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;

        &:focus {
          outline: 2px solid rgba(59, 130, 246, 0.5);
          outline-offset: 2px;
        }

        &--primary {
          background: #3b82f6;
          color: white;
          border-color: #3b82f6;

          &:hover {
            background: #2563eb;
            border-color: #2563eb;
          }
        }

        &--secondary {
          background: white;
          color: #374151;
          border-color: #d1d5db;

          &:hover {
            background: #f9fafb;
            border-color: #9ca3af;
          }
        }

        &--ghost {
          background: transparent;
          color: #6b7280;
          border-color: transparent;

          &:hover {
            background: rgba(107, 114, 128, 0.1);
            color: #374151;
          }
        }
      }
    }
  }
}

// Dark theme adjustments for search results
[data-theme='dark'] {
  .search-results-container {
    .search-result-card {
      .card-header {
        border-bottom-color: rgba(75, 85, 99, 0.4);

        .result-title {
          color: var(--color-text-primary-dark, #f9fafb);

          ::ng-deep mark,
          ::ng-deep .highlight {
            background-color: rgba(255, 193, 7, 0.4);
            color: #1f2937;
          }
        }

        .result-score {
          background: rgba(59, 130, 246, 0.2);
          color: #93c5fd;
          border-color: rgba(59, 130, 246, 0.3);
        }
      }

      .card-content {
        .result-meta {
          .meta-tag {
            &.entity {
              background: rgba(16, 185, 129, 0.2);
              color: #6ee7b7;
              border-color: rgba(16, 185, 129, 0.4);
            }

            &.organization {
              background: rgba(59, 130, 246, 0.2);
              color: #93c5fd;
              border-color: rgba(59, 130, 246, 0.4);
            }

            &.domain {
              background: rgba(139, 92, 246, 0.2);
              color: #c4b5fd;
              border-color: rgba(139, 92, 246, 0.4);
            }

            &.team {
              background: rgba(245, 101, 101, 0.2);
              color: #fca5a5;
              border-color: rgba(245, 101, 101, 0.4);
            }
          }
        }

        .result-description p {
          color: var(--color-text-primary-dark, #e5e7eb);

          ::ng-deep mark,
          ::ng-deep .highlight {
            background-color: rgba(255, 193, 7, 0.4);
            color: #1f2937;
          }
        }

        .result-stats {
          border-top-color: rgba(75, 85, 99, 0.4);

          .stat-item {
            color: var(--color-text-secondary-dark, #9ca3af);

            strong {
              color: var(--color-text-primary-dark, #f3f4f6);
            }
          }
        }
      }

      .card-actions {
        border-top-color: rgba(75, 85, 99, 0.4);

        .action-btn {
          &--secondary {
            background: rgba(75, 85, 99, 0.5);
            color: #e5e7eb;
            border-color: rgba(107, 114, 128, 0.5);

            &:hover {
              background: rgba(75, 85, 99, 0.7);
              border-color: rgba(107, 114, 128, 0.7);
            }
          }

          &--ghost {
            color: #9ca3af;

            &:hover {
              background: rgba(107, 114, 128, 0.2);
              color: #e5e7eb;
            }
          }
        }
      }
    }
  }
}
