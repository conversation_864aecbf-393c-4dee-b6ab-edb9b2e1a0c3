<div class="modal-body">
  <div class="form-container">
    <form [formGroup]="addRealmForm" class="realm__form">
      <div class="form-row realmname">
        <div class="form-group half-width">
         
          <label class="filter-label required">Name of the Realm</label>
          <aava-textbox
            class="input-field"
            formControlName="realmName"
            id="realmName"
            name="realmName"
            placeholder="Enter Realm Name"
            [required]="true"
            [fullWidth]="false"
            size="md"
          >
          </aava-textbox>
        </div>
        <div class="form-group empty-space"></div>
      </div>
      <div class="form-row">
        <div class="form-group">
          <label class="filter-label required">Choose Organization</label>
          <aava-dropdown
            [dropdownTitle]="'Select Organization'"
            [options]="orgOptions"
            [selectedValue]="selectedOrgName"
            [disabled]="false"
            (selectionChange)="onOrgSelect($event)"
            [search]="true"
          ></aava-dropdown>
        </div>
        <div class="form-group">
          <label class="filter-label required">Choose Domain</label>
          <aava-dropdown
            [dropdownTitle]="'Select Domain'"
            [options]="domainOptions"
            [selectedValue]="selectedDomainName"
            [disabled]="!selectedOrg"
            (selectionChange)="onDomainSelect($event)"
            [search]="true"
          ></aava-dropdown>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label class="filter-label required">Choose Project</label>
          <aava-dropdown
            [dropdownTitle]="'Select Project'"
            [options]="projectOptions"
            [selectedValue]="selectedProjectName"
            [disabled]="!selectedDomain"
            (selectionChange)="onProjectSelect($event)"
            [search]="true"
          ></aava-dropdown>
        </div>
        <div class="form-group">
          <label class="filter-label required">Choose Team</label>
          <aava-dropdown
            [dropdownTitle]="'Select Team'"
            [options]="teamOptions"
            [selectedValue]="selectedTeamName"
            [disabled]="!selectedProject"
            (selectionChange)="onTeamSelect($event)"
            [search]="true"
          ></aava-dropdown>
        </div>
      </div>

      <div class="button-group">
        <aava-button
          label="Cancel"
          variant="secondary"
          iconPosition="right"
          size="md"
          pressedEffect="ripple"
          (userClick)="closeRealmPopup()"
          [customStyles]="{
            border: '1px solid #0084FF',
            color: '#0084FF',
          }"
        ></aava-button>
        <aava-button
          label="Save"
          variant="info"
          iconPosition="right"
          size="md"
          pressedEffect="ripple"
          (userClick)="createRealm()"
          [customStyles]="{
            border: '1px solid #0084FF',
            color: '#FFF',
            background: '#0084FF',
          }"
          [disabled]="!addRealmForm.valid"
        ></aava-button>
      </div>
    </form>
  </div>
</div>
