.modal-body {
  height: 55vh;
  overflow-y: auto;
}
.form-container {
  padding: 8px;
  font-family: Arial, sans-serif;
}

.form-row {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.form-group {
  flex: 1;
  display: flex;
  gap: 16px;
  flex-direction: column;
}

.realmname {
  margin-bottom: 8px;
}
.add-user-section {
  margin-top: 20px;
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 30px;
}
.add-user-section {
  margin-top: 20px;
}

.section-label {
  font-weight: 600;
  margin-bottom: 8px;
  display: inline-block;
}

.user-box {
  background-color: #f5f6f7;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 16px;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 10px;
}
