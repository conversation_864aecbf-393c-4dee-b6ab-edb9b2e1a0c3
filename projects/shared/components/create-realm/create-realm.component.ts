import {
  AavaTagComponent,
  AavaButtonComponent,
  AavaTextboxComponent,
  DropdownOption,
  AavaSelectOptionComponent,
  AavaIconComponent,
  AavaSelectComponent,
  AavaDropdownComponent,
  AavaDialogService,
  AavaAutocompleteComponent,
  AavaToastService,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, Inject, Input, OnInit, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { OrgConfigService } from 'projects/shared/services/org-config.service';

interface DropDown {
  title: string;
  description: string;
  options: DropdownOption[];
  suboptions?: Record<string, DropdownOption[]>;
  config: {
    dropdownTitle: string;
    search?: boolean;
    selectedValue?: string;
  };
}

interface SelectionEvent {
  selectedValue: string;
  selectedOption?: DropdownOption;
  subOption?: DropdownOption;
}

export interface UserTag {
  label: string;
  color?:
    | 'default'
    | 'primary'
    | 'success'
    | 'warning'
    | 'error'
    | 'info'
    | 'custom';
  customStyle?: Record<string, string>;
  variant?: 'filled' | 'outlined';
  removable?: boolean;
  disabled?: boolean;
  icon?: string;
  iconPosition?: 'start' | 'end';
  avatar?: string;
  pill?: boolean;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  customClass?: string;
  iconColor?: string;
}
@Component({
  selector: 'app-create-realm',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    AavaTagComponent,
    AavaButtonComponent,
    AavaTextboxComponent,
    AavaDropdownComponent,
    AavaAutocompleteComponent,
  ],
  templateUrl: './create-realm.component.html',
  styleUrls: ['./create-realm.component.scss'],
})
export class CreateRealmComponent implements OnInit {
  addRealmForm!: FormGroup;

  hierarchyData = signal<any[]>([]);
  orgOptions: DropdownOption[] = [];
  domainOptions: DropdownOption[] = [];
  projectOptions: DropdownOption[] = [];
  teamOptions: DropdownOption[] = [];

  selectedOrgName: string = '';
  selectedDomainName: string = '';
  selectedProjectName: string = '';
  selectedTeamName: string = '';

  selectedOrg: string = '';
  selectedDomain: string = '';
  selectedProject: string = '';
  selectedTeam: string = '';

  selectedRealmTag: any[] = [];
  realmsList = signal<any[]>([]);
  realmsListLoading = signal<boolean>(false);
  @Input() realm: any;
  constructor(
    private orgConfigService: OrgConfigService,
    private formBuilder: FormBuilder,
    private toastService: AavaToastService
  ) {}
  Organisation = [
    { value: 1, label: 'Aava' },
    { value: 2, label: 'Ascendion' },
    { value: 3, label: 'Axos' },
    { value: 4, label: 'Hp' },
  ];

  removableTags: UserTag[] = [
    {
      label: 'User',
      color: 'default',
      removable: true,
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    },
  ];

  ngOnInit(): void {
    console.log('Editing realm:', this.realm);

    this.getOrgList();
    this.getRealmsList();
    this.addRealmForm = this.getAddRealmForm();
    if (this.realm) {
      this.update(this.realm); // Pass the full object
    }
    this.getOrgListAndThenUpdateIfEditing();
  }

  getAddRealmForm() {
    return this.formBuilder.group({
      realmName: [null, [Validators.required]],
      orgId: [null, [Validators.required]],
      domainId: [null, [Validators.required]],
      projectId: [null, [Validators.required]],
      teamId: [null, [Validators.required]],
    });
  }

  getOrgList() {
    this.orgConfigService.getOrganizationHierarchy().subscribe({
      next: (data: any) => {
        this.hierarchyData.set(data);
        this.loadOrganizations();
      },
      error: (err: any) => {
        console.error(err);
      },
    });
  }

  loadOrganizations(): void {
    this.orgOptions = this.hierarchyData().map(org => ({
      name: org.organizationName,
      value: org.orgId.toString(),
    }));
  }

  getRealmsList() {
    this.realmsListLoading.set(true);
    this.orgConfigService.getAllRealms().subscribe({
      next: (realms: any) => {
        this.realmsList.set(
          realms.map((opt: any) => ({
            label: opt.realmName,
            value: opt.realmId,
          }))
        );
        this.realmsListLoading.set(false);
      },
      error: e => {
        console.error(e);
        this.realmsListLoading.set(false);
      },
    });
  }

  onOrgSelect(event: any) {
    const selectedOrgId = event.selectedOptions?.[0]?.value;
    const selectedOrgName = event.selectedOptions?.[0]?.name;
    if (selectedOrgId) {
      this.selectedOrg = selectedOrgId;
      this.selectedOrgName = selectedOrgName;
      this.addRealmForm.patchValue({
        orgId: selectedOrgId,
        domainId: '',
        projectId: '',
        teamId: '',
      });
      this.loadDomains(selectedOrgId);
      this.selectedDomain = '';
      this.selectedProject = '';
      this.selectedTeam = '';
      this.selectedDomainName = '';
      this.selectedProjectName = '';
      this.selectedTeamName = '';
    }
  }

  loadDomains(orgId: string): void {
    const org = this.hierarchyData().find(o => o.orgId.toString() === orgId);
    if (org) {
      this.domainOptions = org.domains.map((domain: any) => ({
        name: domain.domainName,
        value: domain.domainId.toString(),
      }));
    } else {
      this.domainOptions = [];
    }
  }

  onDomainSelect(event: any): void {
    const selectedDomainId = event.selectedOptions?.[0]?.value;
    const selectedDomainName = event.selectedOptions?.[0]?.name;
    if (selectedDomainId) {
      this.selectedDomain = selectedDomainId;
      this.selectedDomainName = selectedDomainName;
      this.addRealmForm.patchValue({
        domainId: selectedDomainId,
        projectId: '',
        teamId: '',
      });
      this.loadProjects(selectedDomainId);
      this.selectedProject = '';
      this.selectedTeam = '';
      this.selectedProjectName = '';
      this.selectedTeamName = '';
    }
  }

  loadProjects(domainId: string): void {
    const org = this.hierarchyData().find(o =>
      o.domains.some((d: any) => d.domainId.toString() === domainId)
    );
    if (org) {
      const domain = org.domains.find(
        (d: any) => d.domainId.toString() === domainId
      );
      if (domain) {
        this.projectOptions = domain.projects.map((project: any) => ({
          name: project.projectName,
          value: project.projectId.toString(),
        }));
      } else {
        this.projectOptions = [];
      }
    } else {
      this.projectOptions = [];
    }
  }

  onProjectSelect(event: any): void {
    const selectedProjectId = event.selectedOptions?.[0]?.value;
    const selectedProjectName = event.selectedOptions?.[0]?.name;
    if (selectedProjectId) {
      this.selectedProject = selectedProjectId;
      this.selectedProjectName = selectedProjectName;
      this.addRealmForm.patchValue({ projectId: selectedProjectId, team: '' });
      this.loadTeams(selectedProjectId);
      this.selectedTeam = '';
      this.selectedTeamName = '';
    }
  }

  loadTeams(projectId: string): void {
    const org = this.hierarchyData().find(o =>
      o.domains.some((d: any) =>
        d.projects.some((p: any) => p.projectId.toString() === projectId)
      )
    );
    if (org) {
      const domain = org.domains.find((d: any) =>
        d.projects.some((p: any) => p.projectId.toString() === projectId)
      );
      if (domain) {
        const project = domain.projects.find(
          (p: any) => p.projectId.toString() === projectId
        );
        if (project) {
          this.teamOptions = project.teams.map((team: any) => ({
            name: team.teamName,
            value: team.teamId.toString(),
          }));
        } else {
          this.teamOptions = [];
        }
      } else {
        this.teamOptions = [];
      }
    } else {
      this.teamOptions = [];
    }
  }

  onTeamSelect(event: any): void {
    const selectedTeamId = event.selectedOptions?.[0]?.value;
    const selectedTeamName = event.selectedOptions?.[0]?.name;
    if (selectedTeamId) {
      this.selectedTeam = selectedTeamId;
      this.selectedTeamName = selectedTeamName;
      this.addRealmForm.patchValue({ teamId: selectedTeamId });
    }
  }

  createRealm() {
    if (this.addRealmForm.valid) {
      const { realmName, teamId } = this.addRealmForm.value;
      this.orgConfigService.createRealm(realmName, teamId).subscribe({
        next: (res: any) => {
          this.selectedRealmTag.push({
            label: res.realmName,
            value: res.realmId,
          });
          this.success(realmName);
          this.closeRealmPopup();
        },
        error: e => console.error(e),
      });
    }
  }

  closeRealmPopup() {
    this.addRealmForm.reset();
    this.selectedOrgName = '';
    this.selectedDomainName = '';
    this.selectedProjectName = '';
    this.selectedTeamName = '';
    this.selectedOrg = '';
    this.selectedDomain = '';
    this.selectedProject = '';
    this.selectedTeam = '';
  }

  success(message: string) {
    this.toastService.success({
      title: 'Created Successfully!',
      message: `${message} has been Created Successfully`,
      duration: 2000,
      customWidth: '350px',
      design: 'modern',
      size: 'medium',
      showCloseButton: true,
    });
  }

  //update
  getOrgListAndThenUpdateIfEditing() {
    this.orgConfigService.getOrganizationHierarchy().subscribe({
      next: (data: any) => {
        this.hierarchyData.set(data);
        this.loadOrganizations();
        if (this.realm) {
          this.update(this.realm);
        }
      },
      error: (err: any) => {
        console.error(err);
      },
    });
  }

  update(realm: any) {
    if (!realm) return;
    this.addRealmForm.patchValue({
      realmName: realm.realmName,
      orgId: realm.orgId,
      domainId: realm.domainId,
      projectId: realm.projectId,
      teamId: realm.teamId,
    });

    this.selectedOrg = realm.orgId?.toString() ?? '';
    this.selectedDomain = realm.domainId?.toString() ?? '';
    this.selectedProject = realm.projectId?.toString() ?? '';
    this.selectedTeam = realm.teamId?.toString() ?? '';

    this.selectedOrgName = realm.orgName ?? '';
    this.selectedDomainName = realm.domainName ?? '';
    this.selectedProjectName = realm.projectName ?? '';
    this.selectedTeamName = realm.teamName ?? '';

    // Load hierarchy dropdowns with correct structure
    if (this.selectedOrg) {
      this.loadDomains(this.selectedOrg);
      if (this.selectedDomain) {
        this.loadProjects(this.selectedDomain);
        if (this.selectedProject) {
          this.loadTeams(this.selectedProject);
        }
      }
    }
  }

  onOptionSelected(event: any) {
    const exists = this.selectedRealmTag.some(
      item => item.value === event.value
    );
    if (!exists) {
      this.selectedRealmTag.push(event);
    }
  }
  onValueChange(event: any) {}

  removeRealmTag(tagValue: number) {
    this.selectedRealmTag = this.selectedRealmTag.filter(
      tag => tag.value !== tagValue
    );
  }
  onRemove(label: string) {
    alert(`Removed tag: ${label}`);
  }

  onClick(label: string) {
    alert(`Clicked tag: ${label}`);
  }

  selectionChange(event: any) {
    console.log('Selection changed:', event);
  }
}
