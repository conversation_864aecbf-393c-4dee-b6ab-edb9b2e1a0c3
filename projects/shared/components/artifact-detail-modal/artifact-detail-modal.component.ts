import {
  AavaButtonComponent,
  AavaDefaultCardComponent,
  AavaDialogService,
  AavaTagComponent,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  inject,
} from '@angular/core';
import { Router } from '@angular/router';
import { ArtifactType } from 'projects/shared/models/artifact.model';

interface MarketplaceAgent {
  id: string;
  title: string;
  description: string;
  category: string;
  entityType: string;
  author: string;
  status: 'active' | 'inactive' | 'pending' | 'error';
  tags: string[];
  metadata: { [key: string]: string | number | boolean };
  createdOn?: string;
  avatarUrl?: string;
}

export interface ArtifactMetadata {
  toolName: string;
  description: string;
  createdBy: string;
  createdOn: string;
  realmAssigned: string;
  avatarUrl?: string;
}

@Component({
  selector: 'app-artifact-detail-modal',
  standalone: true,
  imports: [
    CommonModule,
    AavaDefaultCardComponent,
    AavaTagComponent,
    AavaButtonComponent,
  ],
  templateUrl: './artifact-detail-modal.component.html',
  styleUrls: ['./artifact-detail-modal.component.scss'],
})
export class ArtifactDetailModalComponent implements OnInit {
  @Input() data: any = {};

  @Output() modalClosed = new EventEmitter<void>();
  @Output() artifactTypeSelected = new EventEmitter<ArtifactType>();
  @Output() testClicked = new EventEmitter<void>();

  // Router injection
  private router = inject(Router);
  private originalAgent: MarketplaceAgent | null = null;
  constructor(private dialogService: AavaDialogService) {}

  ngOnInit() {
    // Check if data was passed via dialog service and update metadata
    this.setDialogData(this.data);
  }

  // Method to set data from dialog service
  setDialogData(data: MarketplaceAgent) {
    this.originalAgent = data;
    this.updateMetadataFromDialogData(data);
  }

  // Convert agent data to metadata format
  private updateMetadataFromDialogData(agent: MarketplaceAgent) {
    if (agent) {
      this.data = {
        toolName: agent.title,
        description: agent.description,
        createdBy: agent.author,
        createdOn: agent.createdOn,
        realmAssigned: agent.category,
        avatarUrl: agent.avatarUrl,
      };
    }
  }

  onModalClose() {
    this.modalClosed.emit();

    this.dialogService.close();
  }

  onArtifactTypeClick(artifactType: ArtifactType) {
    this.artifactTypeSelected.emit(artifactType);
    this.modalClosed.emit();
  }

  onTestClick() {
    this.onModalClose();

    if (!this.originalAgent) return;

    const routeMap: Record<string, string> = {
      agent: 'agent',
      tool: 'tools',
      workflow: 'pipelines',
    };

    const route = routeMap[this.originalAgent.entityType];
    if (!route) return;

    this.router.navigate([`/build/${route}/playground`], {
      queryParams: { id: this.originalAgent.id },
    });
  }
}
