.complex-card {
  width: 850px;
  max-width: 100%;
  margin: 0 auto;
  max-height: 80vh; /* Limit height to 80% of viewport height */
  overflow-y: auto; /* Enable vertical scrolling */

  // Responsive breakpoints
  @media (max-width: 900px) {
    width: calc(100% - 40px);
    margin: 0 20px;
    max-height: 85vh; /* Slightly taller on smaller screens */
  }

  @media (max-width: 768px) {
    width: 90%;
    margin: 0 auto;
    max-height: 85vh;
  }

  @media (max-width: 480px) {
    width: 95%;
    margin: 0 auto;
    max-height: 90vh; /* Even taller on mobile screens */
  }

  @media (max-width: 320px) {
    width: 98%;
    margin: 0 auto;
    max-height: 90vh;
  }

  .card-content {
    display: flex;
    flex-direction: column;
    gap: var(--Global-V1-Spacing-Space6, 1rem);
    padding: 0.5rem;
    overflow-y: auto; /* Enable vertical scrolling within the content */

    @media (max-width: 768px) {
      gap: 0.75rem;
      padding: 0.5rem 0.25rem;
    }

    @media (max-width: 320px) {
      gap: 0.5rem;
      padding: 0.25rem 0.125rem;
    }
  }

  .card-label {
    color: var(--Brand-Neutral-n-400, #898e99);
  }

  .card-header {
    justify-content: space-between;
    align-items: center;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 1rem;
  }

  .card-title {
    color: var(--Text-Title, #14161f);
    font-family: Mulish;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;

    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    min-width: 0;

    @media (max-width: 768px) {
      font-size: 20px;
    }

    @media (max-width: 480px) {
      font-size: 18px;
    }

    @media (max-width: 320px) {
      font-size: 16px;
    }
  }

  .card-description {
    color: var(--Text-Title, #14161f);
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin: 0;

    @media (max-width: 320px) {
      font-size: 14px;
      line-height: 18px;
    }
  }

  .meta-info-section {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  }

  .meta-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .meta-text {
    color: #898e99;

    /* Truncate at 1st line */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .action-section {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-bottom: 0.5rem; /* Add padding at the bottom */

    @media (max-width: 480px) {
      justify-content: center;
      margin-top: 0.5rem;
    }

    @media (max-width: 320px) {
      margin-top: 0.75rem;
    }
  }

  .action-right {
    display: flex;
    align-items: center;
    gap: 24px;
  }
}
