<aava-default-card class="complex-card">
  <div class="card-content">
    <div class="card-header">
      <div class="header-left">
        <p class="card-title">Metadata Information</p>
      </div>
    </div>
    <!-- Header Section -->

    <div class="card-header">
      <div class="body-medium-default-100-inter card-label">Artifact Name</div>
      <h2 class="card-title">{{ data.toolName }}</h2>
    </div>

    <!-- Description -->

    <div class="card-header">
      <div class="body-medium-default-100-inter card-label">Description</div>
      <h2 class="card-description">{{ data.description }}</h2>
    </div>

    <!-- Meta Info Section -->
    <div class="meta-info-section">
      <div class="meta-left">
        <span class="body-bold-default-100-inter meta-text">Created By </span>
        <aava-tag
          label="{{ data.createdBy }}"
          color="default"
          size="lg"
          [pill]="false"
        ></aava-tag>
      </div>
      <div class="meta-left">
        <span class="body-bold-default-100-inter meta-text">Created On </span>
        <aava-tag
          label="{{ data.createdOn | date: 'dd-MM-YYYY' }}"
          color="default"
          size="lg"
          [pill]="false"
        ></aava-tag>
      </div>
      <div class="meta-left">
        <span class="body-bold-default-100-inter meta-text"
          >Realm Assigned
        </span>
        <aava-tag
          label="{{ data.realmAssigned }}"
          color="default"
          size="lg"
          [pill]="false"
        ></aava-tag>
      </div>
    </div>

    <!-- Action Section -->
    <div class="action-section">
      <div class="action-right">
        <aava-button
          iconName="chevron-right"
          iconPosition="right"
          variant="primary"
          label="Test"
          size="sm"
          [pill]="false"
          (userClick)="onTestClick()"
        >
        </aava-button>
      </div>
    </div>
  </div>
</aava-default-card>
