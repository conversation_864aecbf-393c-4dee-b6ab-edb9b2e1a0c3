<div class="radio-filter">
  <!-- Title -->
  <div class="filter-title">
    <h3>{{ title }}</h3>
  </div>

  <!-- Radio Options -->
  <div class="filter-options">
    <div
      *ngFor="let option of options; trackBy: trackByOptionId"
      class="filter-option"
      [class.selected]="isOptionSelected(option.value)"
    >
      <label class="filter-option-label">
        <input
          type="radio"
          [name]="title + '-radio'"
          [value]="option.value"
          [checked]="isOptionSelected(option.value)"
          [disabled]="disabled"
          (change)="onOptionChange(option.value)"
          class="filter-radio"
        />
        <span class="radio-indicator"></span>
        <span class="filter-label-text">{{ option.label }}</span>
      </label>
    </div>
  </div>
</div>
