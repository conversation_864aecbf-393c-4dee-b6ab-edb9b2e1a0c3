import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';

export interface RadioOption {
  id: string;
  label: string;
  value: string;
}

@Component({
  selector: 'app-radio-filter',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './radio-filter.component.html',
  styleUrl: './radio-filter.component.scss',
})
export class RadioFilterComponent {
  @Input() selectedValue: string = '';
  @Input() title = 'Filter';
  @Input() options: RadioOption[] = [];
  @Input() disabled = false;
  @Output() valueChange = new EventEmitter<string>();

  onOptionChange(value: string): void {
    this.selectedValue = value;
    this.valueChange.emit(value);
  }

  isOptionSelected(value: string): boolean {
    return this.selectedValue === value;
  }

  trackByOptionId(index: number, option: RadioOption): string {
    return option.id;
  }
}
