# Radio Filter Component

A reusable component for displaying a list of items with radio buttons. This component follows the same styling patterns as other filter components in the application.

## Features

- Single selection with radio buttons
- Customizable title
- Keyboard navigation support
- Disabled state support
- Dark mode support
- Accessible radio button implementation

## Usage

```typescript
import { RadioFilterComponent, RadioOption } from './radio-filter.component';

// In your component
export class YourComponent {
  selectedValue = '';

  radioOptions: RadioOption[] = [
    { id: '1', label: 'Option 1', value: 'option1' },
    { id: '2', label: 'Option 2', value: 'option2' },
    { id: '3', label: 'Option 3', value: 'option3' },
  ];

  onValueChange(value: string) {
    console.log('Selected value:', value);
  }
}
```

```html
<app-radio-filter
  [title]="'Choose Option'"
  [options]="radioOptions"
  [selectedValue]="selectedValue"
  [disabled]="false"
  (valueChange)="onValueChange($event)"
>
</app-radio-filter>
```

## Inputs

- `title` (string): The title displayed above the radio options (default: 'Filter')
- `options` (RadioOption[]): Array of radio options to display
- `selectedValue` (string): The currently selected value
- `disabled` (boolean): Whether the radio buttons are disabled (default: false)

## Outputs

- `valueChange`: Emitted when a radio option is selected, returns the selected value

## RadioOption Interface

```typescript
interface RadioOption {
  id: string; // Unique identifier for the option
  label: string; // Display text for the option
  value: string; // Value that will be emitted when selected
}
```

## Styling

The component uses CSS custom properties for theming and supports both light and dark modes. It maintains consistency with other filter components in the application.
