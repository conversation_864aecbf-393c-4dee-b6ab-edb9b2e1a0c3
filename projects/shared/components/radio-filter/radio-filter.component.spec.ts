import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RadioFilterComponent } from './radio-filter.component';

describe('RadioFilterComponent', () => {
  let component: RadioFilterComponent;
  let fixture: ComponentFixture<RadioFilterComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RadioFilterComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(RadioFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit valueChange when option is selected', () => {
    spyOn(component.valueChange, 'emit');
    const testValue = 'test-value';

    component.onOptionChange(testValue);

    expect(component.selectedValue).toBe(testValue);
    expect(component.valueChange.emit).toHaveBeenCalledWith(testValue);
  });

  it('should correctly identify selected option', () => {
    component.selectedValue = 'test-value';

    expect(component.isOptionSelected('test-value')).toBe(true);
    expect(component.isOptionSelected('other-value')).toBe(false);
  });

  it('should track options by id', () => {
    const option = { id: 'test-id', label: 'Test', value: 'test' };

    expect(component.trackByOptionId(0, option)).toBe('test-id');
  });
});
