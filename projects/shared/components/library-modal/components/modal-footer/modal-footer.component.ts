import { AavaButtonComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
} from '@angular/core';

import { ModalFooterConfig } from '../../../interfaces';

@Component({
  selector: 'app-modal-footer',
  standalone: true,
  imports: [CommonModule, AavaButtonComponent],
  templateUrl: './modal-footer.component.html',
  styleUrls: ['./modal-footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModalFooterComponent {
  // Signal inputs
  readonly config = input.required<ModalFooterConfig>();
  readonly isLoading = input<boolean>(false);

  // Signal outputs
  readonly cancelClicked = output<void>();
  readonly applyClicked = output<void>();

  // Computed values
  readonly showCancelButton = computed(() => this.config().showCancelButton);
  readonly showApplyButton = computed(() => this.config().showApplyButton);
  readonly isApplyDisabled = computed(
    () => this.config().isApplyDisabled || this.isLoading()
  );

  protected onCancelClick(): void {
    this.cancelClicked.emit();
  }

  protected onApplyClick(): void {
    if (!this.isApplyDisabled()) {
      this.applyClicked.emit();
    }
  }
}
