.modal-footer {
  padding: 0.5rem;

  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    min-height: 1rem;

    .cancel-section {
      flex-shrink: 0;
    }

    .apply-section {
      flex-shrink: 0;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .modal-footer {
    padding: 1rem;

    .footer-content {
      flex-direction: column-reverse;
      gap: 0.75rem;

      .cancel-section,
      .apply-section {
        width: 100%;

        aava-button {
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .modal-footer {
    padding: 0.75rem;

    .footer-content {
      gap: 0.5rem;
    }
  }
}
