.modal-content-wrapper {
  position: relative;
  width: 100%;
  box-sizing: border-box;

  &.loading {
    pointer-events: none;
    opacity: 0.7;
  }

  &.hide-scrollbar {
    // Hide scrollbar for webkit browsers
    &::-webkit-scrollbar {
      display: none;
    }

    // Hide scrollbar for Firefox
    scrollbar-width: none;
  }

  .content-area {
    width: 100%;
    height: 100%;
    min-height: 0; // Allows flex children to shrink
    padding-bottom: 0.5rem;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: inherit;

    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.75rem;

      .spinner {
        width: 2rem;
        height: 2rem;
        border: 2px solid rgba(229, 231, 235, 1);
        border-top: 2px solid rgba(59, 130, 246, 1);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        margin: 0;
        font-size: 0.875rem;
        color: rgba(107, 114, 128, 1);
        font-weight: 500;
      }
    }
  }
}

// Spinner animation
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive design
@media (max-width: 768px) {
  .modal-content-wrapper {
    .loading-overlay {
      .loading-spinner {
        gap: 0.5rem;

        .spinner {
          width: 1.5rem;
          height: 1.5rem;
        }

        .loading-text {
          font-size: 0.75rem;
        }
      }
    }
  }
}

// Custom scrollbar styling (when not hidden)
.modal-content-wrapper:not(.hide-scrollbar) {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(243, 244, 246, 1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 1);
    border-radius: 3px;

    &:hover {
      background: rgba(107, 114, 128, 1);
    }
  }
}
