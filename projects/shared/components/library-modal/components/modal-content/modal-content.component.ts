import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  computed,
} from '@angular/core';

import { ModalContentConfig } from '../../../interfaces';

@Component({
  selector: 'app-modal-content',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './modal-content.component.html',
  styleUrls: ['./modal-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModalContentComponent {
  // Signal inputs
  readonly config = input<ModalContentConfig>({});
  readonly isLoading = input<boolean>(false);

  // Computed values
  readonly contentStyles = computed(() => {
    const config = this.config();
    return {
      padding: config.padding || '1.5rem',
      maxHeight: config.maxHeight || '60vh',
      overflow: config.overflow || 'auto',
      backgroundColor: config.backgroundColor || 'rgba(255, 255, 255, 1)',
      borderRadius: config.borderRadius || '0',
      scrollbarWidth: config.showScrollbar === false ? 'none' : 'auto',
    };
  });

  readonly contentClasses = computed(() => {
    const config = this.config();
    const classes = ['modal-content'];

    if (config.showScrollbar === false) {
      classes.push('hide-scrollbar');
    }

    if (this.isLoading()) {
      classes.push('loading');
    }

    return classes.join(' ');
  });
}
