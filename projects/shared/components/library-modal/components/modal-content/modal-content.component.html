<div
  class="modal-content-wrapper"
  [class]="contentClasses()"
  [ngStyle]="contentStyles()"
>
  <!-- Loading State -->
  @if (isLoading()) {
    <div class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p class="loading-text">Loading...</p>
      </div>
    </div>
  }

  <!-- Content Projection -->
  <div class="content-area">
    <ng-content></ng-content>
  </div>
</div>
