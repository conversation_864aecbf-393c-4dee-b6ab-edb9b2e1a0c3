<div class="library-modal-content surface--fill4--blur9">
  <div style="display: flex; flex-direction: column; gap: 10px">
    <div dialog-header>
      <app-modal-header
        [config]="headerConfig"
        [searchValue]="searchQuery()"
        [isFilterOpen]="isFilterOpen()"
        (searchChanged)="onSearchChange($event)"
        (filterChanged)="onFilterChange($event)"
      >
      </app-modal-header>
    </div>

    <div dialog-body>
      <app-modal-content [config]="contentConfig">
        @if (isLoading()) {
          <div class="row g-3">
            <!-- Create Card Skeleton -->
            <div class="col-lg-4 col-md-6 col-sm-12">
              <aava-skeleton height="166px" width="100%"></aava-skeleton>
            </div>

            <!-- Item Card Skeletons -->
            @for (item of [1, 2, 3, 4, 5, 6]; track item) {
              <div class="col-lg-4 col-md-6 col-sm-12">
                <aava-skeleton height="172px" width="100%"></aava-skeleton>
              </div>
            }
          </div>
        } @else if (error()) {
          <div class="error-state">
            <p>{{ error() }}</p>
            <button class="btn btn-primary" (click)="loadData()">Retry</button>
          </div>
        } @else {
          <div class="row g-3">
            <!-- Create Card -->
            <div class="col-lg-4 col-md-6 col-sm-12">
              <app-create-card
                [data]="createCardData"
                [height]="'166px'"
                (cardPressed)="onCreateClick()"
              ></app-create-card>
            </div>

            <!-- Item Cards -->
            @for (item of filteredItems(); track item.id) {
              <div class="col-lg-4 col-md-6 col-sm-12">
                <app-reusable-card
                  [data]="getItemCardData(item)"
                  [height]="'172px'"
                  (cardClicked)="onCardClicked(item)"
                ></app-reusable-card>
              </div>
            }
          </div>

          @if (isEmpty()) {
            <div class="empty-state">
              <p>No items found</p>
            </div>
          }
        }
      </app-modal-content>
    </div>

    <div dialog-footer>
      <app-modal-footer
        [config]="footerConfig()"
        (cancelClicked)="onCancelClick()"
        (applyClicked)="onApplyClick()"
      ></app-modal-footer>
    </div>
  </div>
</div>
