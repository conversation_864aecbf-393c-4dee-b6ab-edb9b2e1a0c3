.library-modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 90vh;
  border-radius: 0.75rem;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(calc(var(--Blur-Blur---9, 80px) / 2));
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05);

  // Ensure proper flex layout
  app-modal-header {
    flex-shrink: 0;
  }

  app-modal-content {
    flex: 1;
    min-height: 0; // Allows flex children to shrink
  }

  app-modal-footer {
    flex-shrink: 0;
  }
}

// Responsive design
@media (max-width: 768px) {
  .library-modal-content {
    max-height: 95vh;
    border-radius: 0.5rem;
    margin: 0.5rem;
  }
}

@media (max-width: 480px) {
  .library-modal-content {
    max-height: 100vh;
    border-radius: 0;
    margin: 0;
  }
}

// Animation for modal appearance
.library-modal-content {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Focus management
.library-modal-content {
  &:focus-within {
    outline: none;
  }
}

// Custom scrollbar for modal content
app-modal-content {
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(243, 244, 246, 1);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 1);
    border-radius: 3px;

    &:hover {
      background: rgba(107, 114, 128, 1);
    }
  }
}

// Bootstrap grid handles the layout, no custom CSS needed

// Empty state styling
.empty-state {
  text-align: center;
  padding: 2rem;
  color: #666;

  p {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
  }
}

// Loading state styling - now using aava-skeleton components

// Error state styling
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  color: #dc3545;

  p {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 500;
  }

  .btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    background-color: #007bff;
    color: white;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.15s ease-in-out;

    &:hover {
      background-color: #0056b3;
    }
  }
}
::ng-deep .ava-dialog-content {
  background: transparent !important;
}
