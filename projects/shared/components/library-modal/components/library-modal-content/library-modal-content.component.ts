import { AavaDialogService, AavaSkeletonComponent } from '@aava/play-core';
import {
  Component,
  inject,
  output,
  ChangeDetectorRef,
  signal,
  computed,
  OnInit,
  OnDestroy,
} from '@angular/core';
import {
  Subject,
  takeUntil,
  debounceTime,
  distinctUntilChanged,
  BehaviorSubject,
} from 'rxjs';

import { ModalResultService } from '../../../../services/modal-result.service';
import {
  KnowledgeBaseService,
  KnowledgeBaseItem,
} from '../../../../services/knowledge-base.service';
import { ToolService } from '../../../../services/tool.service';
import { GuardrailService } from '../../../../services/guardrail.service';
import { TeamIdService } from '../../../../services/team-id.service';
import { UserTool, Guardrail } from '../../../../models/artifact.model';
import { CreateCardComponent, CreateCardData } from '../../../create-card';
import { LibraryItem, LibraryModalConfig } from '../../../interfaces';
import {
  ReusableCardComponent,
  ReusableCardData,
} from '../../../reusable-card';
import { CardGridComponent } from '../card-grid';
import { ModalContentComponent } from '../modal-content';
import { ModalFooterComponent } from '../modal-footer';
import { ModalHeaderComponent } from '../modal-header';
import { FilterChangeEvent } from '../../../filter-component';

@Component({
  selector: 'app-library-modal-content',
  standalone: true,
  imports: [
    ModalHeaderComponent,
    ModalContentComponent,
    ModalFooterComponent,
    ReusableCardComponent,
    CreateCardComponent,
    AavaSkeletonComponent,
  ],
  templateUrl: './library-modal-content.component.html',
  styleUrls: ['./library-modal-content.component.scss'],
})
export class LibraryModalContentComponent implements OnInit, OnDestroy {
  private dialogService = inject(AavaDialogService);
  private modalResultService = inject(ModalResultService);
  private cdr = inject(ChangeDetectorRef);
  private knowledgeBaseService = inject(KnowledgeBaseService);
  private toolService = inject(ToolService);
  private guardrailService = inject(GuardrailService);
  private teamIdService = inject(TeamIdService);
  private destroy$ = new Subject<void>();
  private searchSubject$ = new BehaviorSubject<string>('');

  // Output for returning selected items
  readonly itemsSelected = output<LibraryItem[]>();

  // Output for create card click
  readonly createClicked = output<void>();

  // These will be set by the dialog service when opening
  config: LibraryModalConfig = {
    title: 'Library',
    searchPlaceholder: 'Search...',
    createButtonText: 'Create New',
    itemType: 'knowledge',
    gridColumns: 3,
    showFilters: false,
    showUsageCount: false,
    showCreateCard: true,
    modalSize: 'lg',
    maxSelections: 10,
    allowMultiSelect: false,
  };

  // Initial data that can be passed from parent components
  initialItems: LibraryItem[] = [];
  initialSelectedItems: LibraryItem[] = [];
  initialSearchQuery: string = '';
  initialIsFilterOpen: boolean = false;
  initialCurrentFilters: Record<string, unknown> = {};
  // Data signals
  readonly items = signal<LibraryItem[]>([]);
  readonly selectedItems = signal<LibraryItem[]>([]);
  readonly searchQuery = signal<string>('');
  readonly isFilterOpen = signal<boolean>(false);
  readonly currentFilters = signal<Record<string, unknown>>({});
  readonly isLoading = signal<boolean>(false);
  readonly error = signal<string | null>(null);

  // Computed configurations
  get headerConfig() {
    return {
      title: this.config.title,
      showCloseButton: true,
      showSearchBar: true,
      showFilterButton: this.config.showFilters || true,
      searchPlaceholder: this.config.searchPlaceholder,
    };
  }

  readonly footerConfig = computed(() => ({
    showCancelButton: true,
    showApplyButton: true,
    cancelButtonText: 'Cancel',
    applyButtonText: 'Apply',
    cancelButtonVariant: 'default' as const,
    applyButtonVariant: 'primary' as const,
    isApplyDisabled:
      this.config.allowMultiSelect && this.selectedItems().length === 0,
  }));

  get contentConfig() {
    return {
      padding: '1rem',
      paddingTop: '0',
      maxHeight: '55vh',
      overflow: 'auto' as const,
      backgroundColor: 'transparent',
      showScrollbar: false,
    };
  }

  get gridConfig() {
    return {
      columns: this.config.gridColumns,
      gap: '1rem',
      responsiveBreakpoints: {
        mobile: Math.min(2, this.config.gridColumns),
        tablet: Math.min(3, this.config.gridColumns),
        desktop: this.config.gridColumns,
      },
    };
  }

  getItemCardData(item: LibraryItem): ReusableCardData {
    const isSelected = this.isItemSelected(item);
    return {
      // Header Section - Icon + Title on left, People icon + Count on right
      headerIcon: {
        iconName: item.icon,
        iconSize: 20,
        iconColor: item.iconColor || '#000000',
      },
      title: item.title,

      // Use rating section for people icon + usage count
      rating:
        item.usageCount !== undefined
          ? {
              value: item.usageCount,
              iconName: 'user',
              iconSize: 16,
              iconColor: '#898E99',
              showIcon: true,
            }
          : undefined,

      // Content Section - Description text
      description: item.description,

      // Action Section - Dynamic button based on selection state
      actionRight: {
        button: {
          iconName: isSelected ? 'check' : 'plus',
          label: isSelected ? 'Selected' : 'Use',
          iconPosition: 'right',
          variant: isSelected ? 'success' : 'primary',
          size: 'sm',
          pill: false,
          outlined: isSelected ? false : true,
          width: 'auto',
          height: 'auto',
          action: () => this.onItemAction(item),
        },
      },

      // General
      disabled: !item.isActive,
      selected: isSelected,
    };
  }

  get createCardData(): CreateCardData {
    return {
      title: this.config.createButtonText,
      iconColor: 'rgba(51, 157, 255, 1)',
      textColor: 'rgba(51, 157, 255, 1)',
      iconWrapperBgColor: 'rgba(255, 255, 255, 0.8)',
      cardBgColor:
        'linear-gradient(82deg, rgba(230, 243, 255, 0.90) 0.87%, rgba(242, 235, 253, 0.90) 98.34%)',
      disabled: false,
    };
  }

  readonly filteredItems = computed(() => {
    // Since search and filtering are now handled by the API,
    // we just return the items as they come from the server
    return this.items();
  });

  readonly isEmpty = computed(() => this.filteredItems().length === 0);

  onCreateClick() {
    // Emit the create event through modal result service
    this.modalResultService.emitEvent({
      type: 'createClicked',
    });

    // Also emit the create event to parent component (for direct usage)
    this.createClicked.emit();
  }

  onItemAction(item: LibraryItem) {
    // Handle item action (Use button click) - toggle selection
    this.onItemSelection(item);
    // Trigger change detection to update button states
    this.cdr.detectChanges();
  }

  onItemSelection(item: LibraryItem) {
    // Handle item selection (card click)
    if (this.config.allowMultiSelect) {
      const isSelected = this.isItemSelected(item);
      if (isSelected) {
        this.selectedItems.set(
          this.selectedItems().filter(selected => selected.id !== item.id)
        );
      } else {
        this.selectedItems.set([...this.selectedItems(), item]);
      }
    } else {
      this.selectedItems.set([item]);
    }
  }

  onCardClicked(item: LibraryItem) {
    this.onItemSelection(item);
  }

  onCancelClick() {
    this.dialogService.close();
  }

  onApplyClick() {
    // Emit selected items and close modal
    this.itemsSelected.emit(this.selectedItems());
    this.modalResultService.setResult(this.selectedItems());
    this.dialogService.close();
  }

  isItemSelected(item: LibraryItem): boolean {
    return this.selectedItems().some(selected => selected.id === item.id);
  }

  // Lifecycle methods
  ngOnInit(): void {
    // Initialize signals with initial data
    this.items.set(this.initialItems);
    this.selectedItems.set(this.initialSelectedItems);
    this.searchQuery.set(this.initialSearchQuery);
    this.isFilterOpen.set(this.initialIsFilterOpen);
    this.currentFilters.set(this.initialCurrentFilters);

    // Set up debounced search
    this.setupDebouncedSearch();

    // Load data if no initial items provided
    if (this.initialItems.length === 0) {
      this.loadInitialData();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.searchSubject$.complete();
  }

  private getActionButtonText(): string {
    const itemType = this.config.itemType;
    switch (itemType) {
      case 'tools':
        return 'Use +';
      case 'knowledge':
        return 'Add +';
      case 'guardrails':
        return 'Select +';
      default:
        return 'Select +';
    }
  }

  private getActionButtonIcon(): string {
    const itemType = this.config.itemType;
    switch (itemType) {
      case 'tools':
        return 'play';
      case 'knowledge':
        return 'plus';
      case 'guardrails':
        return 'check';
      default:
        return 'play';
    }
  }

  private getGradientColors(): string[] {
    const itemType = this.config.itemType;
    switch (itemType) {
      case 'tools':
        return ['rgba(59, 130, 246, 1)', 'rgba(147, 51, 234, 1)'];
      case 'knowledge':
        return ['rgba(16, 185, 129, 1)', 'rgba(59, 130, 246, 1)'];
      case 'guardrails':
        return ['rgba(239, 68, 68, 1)', 'rgba(245, 158, 11, 1)'];
      default:
        return ['rgba(59, 130, 246, 1)', 'rgba(147, 51, 234, 1)'];
    }
  }

  // API Integration Methods
  private loadInitialData(): void {
    this.loadData();
  }

  loadData(): void {
    this.isLoading.set(true);
    this.error.set(null);

    const filters = this.currentFilters();
    const searchQuery = this.searchQuery();

    switch (this.config.itemType) {
      case 'knowledge':
        this.loadKnowledgeBases(filters, searchQuery);
        break;
      case 'tools':
        this.loadTools(filters, searchQuery);
        break;
      case 'guardrails':
        this.loadGuardrails(filters, searchQuery);
        break;
      default:
        console.warn('Unknown item type:', this.config.itemType);
        this.isLoading.set(false);
    }
  }

  private loadKnowledgeBases(
    filters: Record<string, unknown>,
    searchQuery: string
  ): void {
    const params = this.buildKnowledgeBaseParams(filters, searchQuery);

    this.knowledgeBaseService
      .getAllKnowledgeBases(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: knowledgeBases => {
          const libraryItems =
            this.transformKnowledgeBasesToLibraryItems(knowledgeBases);
          this.items.set(libraryItems);
          this.isLoading.set(false);
        },
        error: error => {
          console.error('Error loading knowledge bases:', error);
          this.error.set('Failed to load knowledge bases');
          this.isLoading.set(false);
        },
      });
  }

  private loadTools(
    filters: Record<string, unknown>,
    searchQuery: string
  ): void {
    const params = this.buildToolParams(filters, searchQuery);

    this.toolService
      .getAll(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          // Handle the actual API response structure (same as agent-builder-page)
          const tools = response?.userToolDetails || response || [];

          if (Array.isArray(tools)) {
            const libraryItems = this.transformToolsToLibraryItems(tools);
            this.items.set(libraryItems);
          } else {
            this.items.set([]);
          }
          this.isLoading.set(false);
        },
        error: error => {
          console.error('Error loading tools:', error);
          this.error.set('Failed to load tools');
          this.isLoading.set(false);
        },
      });
  }

  private loadGuardrails(
    filters: Record<string, unknown>,
    searchQuery: string
  ): void {
    const params = this.buildGuardrailParams(filters);

    this.guardrailService
      .getAll(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: guardrails => {
          const libraryItems =
            this.transformGuardrailsToLibraryItems(guardrails);
          this.items.set(libraryItems);
          this.isLoading.set(false);
        },
        error: error => {
          console.error('Error loading guardrails:', error);
          this.error.set('Failed to load guardrails');
          this.isLoading.set(false);
        },
      });
  }

  // Filter event handlers
  onSearchChange(searchQuery: string): void {
    this.searchQuery.set(searchQuery);
    // Emit to debounced search subject
    this.searchSubject$.next(searchQuery);
  }

  onFilterChange(filterEvent: FilterChangeEvent): void {
    this.currentFilters.set({
      searchQuery: filterEvent.searchQuery,
      artifactType: filterEvent.artifactType,
      practiceArea: filterEvent.practiceArea,
      capabilityTags: filterEvent.capabilityTags,
      realm: filterEvent.realm,
      sortBy: filterEvent.sortBy,
      status: filterEvent.status,
      sampleFiles: filterEvent.sampleFiles,
    });
    this.loadData();
  }

  onFilterToggle(isOpen: boolean): void {
    this.isFilterOpen.set(isOpen);
  }

  private setupDebouncedSearch(): void {
    // Set up debounced search with 500ms delay
    this.searchSubject$
      .pipe(debounceTime(500), distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe(searchQuery => {
        this.loadData();
      });
  }

  // Data transformation methods
  private transformKnowledgeBasesToLibraryItems(
    knowledgeBases: KnowledgeBaseItem[]
  ): LibraryItem[] {
    return knowledgeBases.map(kb => ({
      id: kb.id.toString(),
      title: kb.name,
      description: kb.description,
      icon: 'book-open-text',
      iconColor: '#10B981',
      category: 'Knowledge Base',
      isActive: kb.isactive,
      usageCount: 0, // Not available in current API
    }));
  }

  private transformToolsToLibraryItems(tools: any[]): LibraryItem[] {
    return tools.map(tool => ({
      id: tool.id?.toString() || '0',
      title: tool.name || tool.toolName, // Handle both field names
      description:
        tool.toolDescription ||
        tool.description ||
        `Tool created by ${tool.createdBy}`,
      icon: 'wrench',
      iconColor: '#3B82F6',
      category: tool.teamInfo?.domain || 'General',
      isActive: !tool.isDeleted && tool.status === 'APPROVED',
      usageCount: 0, // Not available in current API
    }));
  }

  private transformGuardrailsToLibraryItems(
    guardrails: Guardrail[]
  ): LibraryItem[] {
    return guardrails.map(guardrail => ({
      id: guardrail.id?.toString() || '0',
      title: guardrail.name,
      description: guardrail.description,
      icon: 'shield',
      iconColor: '#EF4444',
      category: 'Guardrail',
      isActive: true, // Guardrails don't have status in the interface
      usageCount: 0, // Not available in current API
    }));
  }

  // Parameter building methods
  private buildToolParams(
    filters: Record<string, unknown>,
    searchQuery?: string
  ): any {
    const params: any = {};

    // Default parameters (same as agent-builder-page)
    const teamId = this.teamIdService.getTeamIdFromCookies() || 23;
    params.teamId = teamId;
    params.status = 'APPROVED'; // Default to approved tools only

    // Override status if provided in filters
    if (filters['status']) {
      params.status = filters['status'];
    }

    if (searchQuery) {
      params.search = searchQuery;
    }

    // Note: Tool service supports: teamId, status, createdBy, page, limit, toolType, search
    // practiceArea and tags are not supported by the current API
    // These would need to be implemented in the backend API

    return params;
  }

  private buildGuardrailParams(filters: Record<string, unknown>): any {
    const params: any = {};

    // Note: Guardrail service getAll() method doesn't actually use parameters
    // It just makes a simple GET request to /guardrails
    // The search() method exists but uses FormData instead of query parameters
    // For now, we'll return empty params since the API doesn't support filtering

    return params;
  }

  private buildKnowledgeBaseParams(
    filters: Record<string, unknown>,
    searchQuery?: string
  ): any {
    const params: any = {};

    if (filters['status']) {
      params.status = filters['status'];
    }

    if (filters['practiceArea']) {
      params.practiceArea = filters['practiceArea'];
    }

    if (
      filters['capabilityTags'] &&
      Array.isArray(filters['capabilityTags']) &&
      filters['capabilityTags'].length > 0
    ) {
      params.tags = filters['capabilityTags'];
    }

    if (searchQuery) {
      params.search = searchQuery;
    }

    return params;
  }
}
