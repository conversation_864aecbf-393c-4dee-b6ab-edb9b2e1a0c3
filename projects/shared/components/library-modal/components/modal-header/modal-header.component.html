<header class="modal-header-library">
  <div class="header-content-library">
    <!-- Title Section -->
    <div class="title-section-library">
      <h2 class="modal-title">{{ config().title }}</h2>
    </div>

    <!-- Actions Section -->
    <div class="actions-section-library">
      <!-- Filter Component -->
      @if (showSearchBar() || showFilterButton()) {
        <div class="filter-component-container">
          <app-filter-component
            [config]="defaultFilterConfig()"
            [searchValue]="searchValue()"
            [isOpen]="isFilterOpenComputed()"
            (searchChanged)="onSearchChange($event)"
            (filterChanged)="onFilterChange($event)"
            (filterToggled)="onFilterToggle($event)"
            class="modal-filter-component"
          ></app-filter-component>
        </div>
      }
    </div>
  </div>
</header>
