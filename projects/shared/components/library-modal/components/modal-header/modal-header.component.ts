import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
  signal,
} from '@angular/core';

import {
  FilterComponentComponent,
  FilterComponentConfig,
  FilterChangeEvent,
} from '../../../filter-component';
import { ModalHeaderConfig } from '../../../interfaces';

@Component({
  selector: 'app-modal-header',
  standalone: true,
  imports: [CommonModule, FilterComponentComponent],
  templateUrl: './modal-header.component.html',
  styleUrls: ['./modal-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModalHeaderComponent {
  // Signal inputs
  readonly config = input.required<ModalHeaderConfig>();
  readonly searchValue = input<string>('');
  readonly isFilterOpen = input<boolean>(false);
  readonly filterConfig = input<FilterComponentConfig>({});

  // Signal outputs
  readonly searchChanged = output<string>();
  readonly filterChanged = output<FilterChangeEvent>();
  readonly closeClicked = output<void>();

  // Internal state
  private readonly isFilterOpenState = signal<boolean>(false);

  // Computed values
  readonly showSearchBar = computed(() => this.config().showSearchBar);
  readonly showFilterButton = computed(() => this.config().showFilterButton);
  readonly showCloseButton = computed(() => this.config().showCloseButton);
  readonly isFilterOpenComputed = computed(() => this.isFilterOpenState());

  // Default filter configuration
  readonly defaultFilterConfig = computed<FilterComponentConfig>(() => ({
    searchPlaceholder: this.config().searchPlaceholder || 'Search...',
    showArtifactType: false,
    showPracticeArea: true,
    showCapabilityTags: true,
    showRealm: false,
    showSortBy: true,
    showStatus: true,
    showSampleFiles: false,
    ...this.filterConfig(),
  }));

  protected onSearchChange(value: string): void {
    this.searchChanged.emit(value);
  }

  protected onFilterChange(event: FilterChangeEvent): void {
    this.filterChanged.emit(event);
  }

  protected onFilterToggle(isOpen: boolean): void {
    this.isFilterOpenState.set(isOpen);
  }

  protected onCloseClick(): void {
    this.closeClicked.emit();
  }
}
