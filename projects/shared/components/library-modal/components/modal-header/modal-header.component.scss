.modal-header-library {
  padding: 1.5rem 1.5rem 0 1.5rem;

  .header-content-library {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-height: 2rem;

    .title-section-library {
      .modal-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: rgba(17, 24, 39, 1);
        line-height: 1.5;
      }
    }

    .actions-section-library {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .filter-component-container {
        flex: 1;
        min-width: 0; // Allow flex item to shrink below content size

        .modal-filter-component {
          width: 100%;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .modal-header-library {
    padding: 1rem 1rem 0 1rem;

    .header-content-library {
      .title-section {
        .modal-title {
          font-size: 1.125rem;
          text-align: center;
        }
      }

      .actions-section-library {
        flex-direction: column;
        align-items: stretch;

        .filter-component-container {
          min-width: 150px;
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .modal-header-library {
    .header-content-library {
      .actions-section-library {
        .filter-component-container {
          min-width: 120px;
        }
      }
    }
  }
}
