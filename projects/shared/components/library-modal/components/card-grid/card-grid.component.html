<div
  class="card-grid"
  [class]="gridClasses()"
  [ngStyle]="gridStyles()"
  [ngClass]="responsiveClasses()"
>
  <!-- Loading State -->
  @if (isLoading()) {
    <div class="loading-placeholder">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p class="loading-text">Loading items...</p>
      </div>
    </div>
  }

  <!-- Empty State -->
  @if (isEmpty() && !isLoading()) {
    <div class="empty-state">
      <div class="empty-icon">
        <svg
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M9 12l2 2 4-4" />
          <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3" />
          <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3" />
          <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3" />
          <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3" />
        </svg>
      </div>
      <h3 class="empty-title">No items found</h3>
      <p class="empty-description">
        There are no items to display at the moment.
      </p>
    </div>
  }

  <!-- Content Projection for Cards -->
  @if (!isLoading() && !isEmpty()) {
    <ng-content></ng-content>
  }
</div>
