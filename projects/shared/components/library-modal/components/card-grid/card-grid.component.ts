import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  computed,
} from '@angular/core';

import { CardGridConfig } from '../../../interfaces';

@Component({
  selector: 'app-card-grid',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './card-grid.component.html',
  styleUrls: ['./card-grid.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CardGridComponent {
  // Signal inputs
  readonly config = input.required<CardGridConfig>();
  readonly isLoading = input<boolean>(false);
  readonly isEmpty = input<boolean>(false);

  // Computed values
  readonly gridStyles = computed(() => {
    const config = this.config();
    const obj = {
      'grid-template-columns': `repeat(${config.columns}, 1fr)`,
      gap: config.gap,
      'grid-auto-rows': 'minmax(auto, max-content)',
    };
    console.log(obj);
    return obj;
  });

  readonly gridClasses = computed(() => {
    const classes = ['card-grid'];

    if (this.isLoading()) {
      classes.push('card-grid--loading');
    }

    if (this.isEmpty()) {
      classes.push('card-grid--empty');
    }
    console.log(classes);

    return classes.join(' ');
  });

  readonly responsiveClasses = computed(() => {
    const config = this.config();
    const obj = {
      'mobile-columns': config.responsiveBreakpoints.mobile,
      'tablet-columns': config.responsiveBreakpoints.tablet,
      'desktop-columns': config.responsiveBreakpoints.desktop,
    };
    console.log(obj);
    return obj;
  });
}
