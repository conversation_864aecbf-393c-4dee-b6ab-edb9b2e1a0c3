.card-grid {
  display: grid;
  width: 100%;
  min-height: 200px;
  padding-bottom: 0.5rem;

  &--loading {
    align-items: center;
    justify-content: center;
  }

  &--empty {
    align-items: center;
    justify-content: center;
  }

  .loading-placeholder {
    grid-column: 1 / -1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;

    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;

      .spinner {
        width: 2.5rem;
        height: 2.5rem;
        border: 3px solid rgba(229, 231, 235, 1);
        border-top: 3px solid rgba(59, 130, 246, 1);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        margin: 0;
        font-size: 1rem;
        color: rgba(107, 114, 128, 1);
        font-weight: 500;
      }
    }
  }

  .empty-state {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;

    .empty-icon {
      width: 4rem;
      height: 4rem;
      color: rgba(156, 163, 175, 1);
      margin-bottom: 1rem;
    }

    .empty-title {
      margin: 0 0 0.5rem 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: rgba(17, 24, 39, 1);
    }

    .empty-description {
      margin: 0;
      font-size: 0.875rem;
      color: rgba(107, 114, 128, 1);
      line-height: 1.5;
      max-width: 24rem;
    }
  }
}

// Spinner animation
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive breakpoints
@media (max-width: 480px) {
  .card-grid {
    &.mobile-columns-1 {
      grid-template-columns: 1fr !important;
    }

    &.mobile-columns-2 {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    &.mobile-columns-3 {
      grid-template-columns: repeat(3, 1fr) !important;
    }
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .card-grid {
    &.tablet-columns-1 {
      grid-template-columns: 1fr !important;
    }

    &.tablet-columns-2 {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    &.tablet-columns-3 {
      grid-template-columns: repeat(3, 1fr) !important;
    }

    &.tablet-columns-4 {
      grid-template-columns: repeat(4, 1fr) !important;
    }
  }
}

@media (min-width: 769px) {
  .card-grid {
    &.desktop-columns-1 {
      grid-template-columns: 1fr !important;
    }

    &.desktop-columns-2 {
      grid-template-columns: repeat(2, 1fr) !important;
    }

    &.desktop-columns-3 {
      grid-template-columns: repeat(3, 1fr) !important;
    }

    &.desktop-columns-4 {
      grid-template-columns: repeat(4, 1fr) !important;
    }

    &.desktop-columns-5 {
      grid-template-columns: repeat(5, 1fr) !important;
    }

    &.desktop-columns-6 {
      grid-template-columns: repeat(6, 1fr) !important;
    }
  }
}

// Card minimum width constraints
.card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); // always 3 equal columns
  gap: 16px; // optional spacing
}

// Custom minimum width for specific configurations
.card-grid {
  &[style*='--min-width'] {
    grid-template-columns: repeat(auto-fit, minmax(var(--min-width), 1fr));
  }
}

// Responsive design adjustments
@media (max-width: 768px) {
  .card-grid {
    .loading-placeholder,
    .empty-state {
      padding: 2rem 1rem;

      .loading-spinner {
        gap: 0.75rem;

        .spinner {
          width: 2rem;
          height: 2rem;
        }

        .loading-text {
          font-size: 0.875rem;
        }
      }

      .empty-icon {
        width: 3rem;
        height: 3rem;
        margin-bottom: 0.75rem;
      }

      .empty-title {
        font-size: 1rem;
      }

      .empty-description {
        font-size: 0.75rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .card-grid {
    .loading-placeholder,
    .empty-state {
      padding: 1.5rem 0.75rem;
    }
  }
}
