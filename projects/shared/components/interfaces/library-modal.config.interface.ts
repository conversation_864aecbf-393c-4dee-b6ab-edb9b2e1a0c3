import {
  LibraryType,
  LibraryFilters,
  LibraryItem,
} from './library-item.interface';

/**
 * Main configuration interface for library modal
 */
export interface LibraryModalConfig {
  readonly title: string; // "Tools Library", "Knowledge Base Library", "Guardrails Library"
  readonly searchPlaceholder: string; // "Search tools", "Search knowledge", "Search guardrails"
  readonly createButtonText: string; // "Create Tool", "Add Knowledge", "Add Guardrail"
  readonly itemType: LibraryType;
  readonly gridColumns: number; // 3, 4, etc.
  readonly showFilters: boolean;
  readonly showUsageCount: boolean;
  readonly showCreateCard: boolean;
  readonly modalSize: 'sm' | 'md' | 'lg' | 'xl';
  readonly maxSelections?: number; // For multi-select scenarios
  readonly allowMultiSelect: boolean;
}

/**
 * Configuration for modal header
 */
export interface ModalHeaderConfig {
  readonly title: string;
  readonly showCloseButton: boolean;
  readonly showSearchBar: boolean;
  readonly showFilterButton: boolean;
  readonly searchPlaceholder?: string;
  readonly filterOptions?: string[];
}

/**
 * Configuration for modal footer
 */
export interface ModalFooterConfig {
  readonly showCancelButton: boolean;
  readonly showApplyButton: boolean;
  readonly cancelButtonText: string; // "Cancel", "Close"
  readonly applyButtonText: string; // "Apply", "Save", "Select"
  readonly cancelButtonVariant:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger'
    | 'info'
    | 'purple'
    | 'emerald';
  readonly applyButtonVariant:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger'
    | 'info'
    | 'purple'
    | 'emerald';
  readonly isApplyDisabled?: boolean;
}

/**
 * Configuration for modal content area
 */
export interface ModalContentConfig {
  readonly padding?: string;
  readonly maxHeight?: string;
  readonly overflow?: 'auto' | 'hidden' | 'scroll' | 'visible';
  readonly backgroundColor?: string;
  readonly borderRadius?: string;
  readonly showScrollbar?: boolean;
}

/**
 * Configuration for card grid layout
 */
export interface CardGridConfig {
  readonly columns: number;
  readonly gap: string; // CSS gap value
  readonly responsiveBreakpoints: {
    readonly mobile: number; // columns on mobile
    readonly tablet: number; // columns on tablet
    readonly desktop: number; // columns on desktop
  };
  readonly cardMinWidth?: string; // Minimum card width
  readonly cardMaxWidth?: string; // Maximum card width
}

/**
 * Event data for library modal interactions
 */
export interface LibraryModalEvents {
  readonly itemSelected: (item: LibraryItem) => void;
  readonly itemDeselected: (item: LibraryItem) => void;
  readonly createClicked: () => void;
  readonly searchChanged: (query: string) => void;
  readonly filterChanged: (filters: LibraryFilters) => void;
  readonly applyClicked: (selectedItems: LibraryItem[]) => void;
  readonly cancelClicked: () => void;
  readonly modalClosed: () => void;
}
