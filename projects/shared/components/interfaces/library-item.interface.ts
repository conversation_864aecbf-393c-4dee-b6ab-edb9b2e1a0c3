/**
 * Generic interface for library items (tools, knowledge base, guardrails, etc.)
 */
export interface LibraryItem {
  readonly id: string;
  readonly title: string;
  readonly description: string;
  readonly icon: string;
  readonly iconColor: string;
  readonly usageCount?: number;
  readonly isSelected?: boolean;
  readonly category?: string;
  readonly tags?: string[];
  readonly isActive?: boolean;
  readonly createdAt?: Date;
  readonly updatedAt?: Date;
}

/**
 * Configuration for item card display
 */
export interface ItemCardConfig {
  readonly actionButtonText: string; // "Use +", "Add +", "Select +"
  readonly actionButtonVariant:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger'
    | 'info'
    | 'purple'
    | 'emerald';
  readonly showUsageCount: boolean;
  readonly showSelection: boolean;
  readonly showCategory: boolean;
  readonly showTags: boolean;
  readonly cardSize: 'sm' | 'md' | 'lg';
}

/**
 * Configuration for create card display
 */
export interface CreateCardConfig {
  readonly title: string; // "Create", "Add New", "Import"
  readonly icon: string; // "plus", "upload", "import", etc.
  readonly gradientColors: string[]; // For background gradient
  readonly buttonText: string; // "Create Tool", "Add Knowledge", etc.
  readonly buttonVariant:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger'
    | 'info'
    | 'purple'
    | 'emerald';
  readonly cardSize: 'sm' | 'md' | 'lg';
}

/**
 * Library type enumeration
 */
export type LibraryType = 'tools' | 'knowledge' | 'guardrails';

/**
 * Search and filter options
 */
export interface LibraryFilters {
  readonly searchQuery?: string;
  readonly category?: string;
  readonly tags?: string[];
  readonly showActiveOnly?: boolean;
  readonly sortBy?: 'title' | 'usage' | 'created' | 'updated';
  readonly sortOrder?: 'asc' | 'desc';
}
