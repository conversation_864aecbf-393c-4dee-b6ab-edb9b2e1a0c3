import { AavaButtonComponent, AavaDialogService } from '@aava/play-core';
import { AavaTextboxComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-sendback-modal',
  standalone: true,
  imports: [
    AavaButtonComponent,
    AavaTextboxComponent,
    FormsModule,
    CommonModule,
  ],
  template: `
    <div class="modal-container">
      <div dialog-header class="modal-header flex-row justify-content-center">
        <div class="body-bold-default-400 header-content">
          Confirm Send back
        </div>
      </div>

      <div dialog-body>
        <div class="modal-body">
          <aava-textbox
            label="Reasons for Send back"
            variant="default"
            placeholder="Enter your feedback"
            size="lg"
            [(ngModel)]="reason"
          ></aava-textbox>
        </div>
      </div>

      <div dialog-footer class="dialog-footer">
        <aava-button
          label="Cancel"
          variant="secondary"
          size="md"
          (userClick)="cancel()"
          width="165px"
        ></aava-button>

        <aava-button
          label="Send back"
          variant="primary"
          size="md"
          width="165px"
          (userClick)="reject()"
        ></aava-button>
      </div>
    </div>
  `,
  styles: [
    `
      .modal-header {
        display: flex;
        padding: 0;
      }

      .modal-container {
        padding: 1.5rem;
      }

      .modal-header {
        color: #3b3f46;
      }

      .header-content {
        font-weight: 700;
      }

      .modal-body {
        padding: 1rem 0;
      }

      .dialog-footer {
        display: flex;
        width: 100%;
        gap: 0.75rem;
      }
    `,
  ],
})
export class SendbackModalComponent {
  @Output() closed = new EventEmitter<string | null>();
  reason: string = '';

  constructor(private dialogService: AavaDialogService) {}

  cancel() {
    this.closed.emit(null);
  }

  reject() {
    this.closed.emit(this.reason);
  }
}
