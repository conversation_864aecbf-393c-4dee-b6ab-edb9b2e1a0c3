  <div class="tool-metadata-card">
    <h2 class="card-title">Tool Metadata</h2>

    @if (loading()) {
      <!-- Skeleton Loaders -->
      <div class="skeleton-fields">
        @for (field of [1, 2, 3, 4, 5]; track field) {
          <div class="skeleton-field">
            <aava-skeleton
              [width]="'100%'"
              [height]="'40px'"
              [shape]="'rectangle'"
              [animation]="'wave'"
            ></aava-skeleton>
          </div>
        }
      </div>
    } @else {
      <!-- Actual Form -->
      <form [formGroup]="metadataForm" class="form-fields">
      <!-- Tool Name -->
      <div class="form-field">
        <aava-textbox
          label="Tool Name"
          placeholder="Enter tool name"
          size="sm"
          [required]="true"
          variant="default"
          formControlName="name"
          [disabled]="disabled()"
          [error]="nameError()">
        </aava-textbox>
      </div>

      <!-- Tool Description -->
      <div class="form-field">
        <aava-textbox
          label="Tool Description"
          placeholder="Enter tool description"
          size="sm"
          [required]="true"
          variant="default"
          formControlName="description"
          [disabled]="disabled()"
          [error]="descriptionError()">
        </aava-textbox>
      </div>

      <!-- Tool Class Name -->
      <div class="form-field">
        <aava-textbox
          label="Tool Class"
          placeholder="Enter tool class name (e.g., CalculatorTool)"
          size="sm"
          [required]="true"
          variant="default"
          formControlName="tool_class_name"
          [disabled]="disabled()"
          [error]="classNameError()">
        </aava-textbox>
      </div>

      
      <!-- Practice Area -->
      <div class="form-field">
        <aava-select
          label="Practice Area"
          size="sm"
          [required]="true"
          variant="default"
          formControlName="practiceArea"
          [disabled]="disabled()">
          @for (option of practiceAreaOptions; track option.value) {
            <aava-select-option [value]="option.value">
              {{ option.label }}
            </aava-select-option>
          }
        </aava-select>
      </div>

      <!-- Good at Section -->
    <div class="form-field good-at-field">
      <aava-select
        size="sm"
        [multiple]="true"
        label="Good At"
        [required]="true"
        placeholder="Select skills and technologies"
        formControlName="goodAtTags"
        [disabled]="disabled()"
        (selectionChange)="onGoodAtSelectionChange($event)"
      >
        <aava-select-option
          *ngFor="let option of goodAtOptions"
          [value]="option.value"
        >
          <aava-checkbox
            size="sm"
            [isChecked]="isOptionSelected(option.value)"
          ></aava-checkbox>
          {{ option.label }}
        </aava-select-option>
      </aava-select>

      <!-- Selected Tags Display -->
      <div
        class="selected-tags-container"
        *ngIf="metadataForm.get('goodAtTags')?.value && metadataForm.get('goodAtTags')?.value.length > 0"
      >
        @for (tag of metadataForm.get('goodAtTags')?.value; track tag) {
          <aava-tag
            [label]="tag"
            size="sm"
            variant="outlined"
            [removable]="true"
            [disabled]="disabled()"
            (removed)="onGoodAtTagRemove(tag)"
          ></aava-tag>
        }
      </div>
    </div>
    </form>
    }
  </div>
