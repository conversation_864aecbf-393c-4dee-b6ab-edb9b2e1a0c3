import { Component, ChangeDetectionStrategy, input, output, OnInit, OnDestroy, OnChanges, SimpleChanges, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { AavaTextboxComponent } from '@aava/play-core';
import { AavaSelectComponent } from '@aava/play-core';
import { AavaSelectOptionComponent } from '@aava/play-core';
import { AavaTagComponent } from '@aava/play-core';
import { AavaCheckboxComponent } from '@aava/play-core';
import { AavaSkeletonComponent } from '@aava/play-core';

import { DropdownDataStore } from '../../../../stores/dropdown-data.store';

export interface MetadataWidgetData {
  readonly name: string;
  readonly description: string;
  readonly tool_class_name: string;
  readonly practiceArea?: string;
  readonly goodAtTags?: string[];
}

@Component({
  selector: 'app-metadata-widget',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    AavaTextboxComponent,
    AavaSelectComponent,
    AavaSelectOptionComponent,
    AavaCheckboxComponent,
    AavaTagComponent,
    AavaSkeletonComponent
  ],
  templateUrl: './metadata-widget.component.html',
  styleUrl: './metadata-widget.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MetadataWidgetComponent implements OnInit, OnDestroy, OnChanges {
  private readonly destroy$ = new Subject<void>();
  private dropdownStore = inject(DropdownDataStore);

  // Signal inputs
  readonly data = input<MetadataWidgetData>({ name: '', description: '', tool_class_name: '', practiceArea: '', goodAtTags: [] });
  readonly disabled = input<boolean>(false);
  readonly loading = input<boolean>(false);

  // Signal outputs
  readonly dataChanged = output<MetadataWidgetData>();

  // Form
  metadataForm!: FormGroup;

  // Validation error signals
  protected readonly nameError = signal<string>('');
  protected readonly descriptionError = signal<string>('');
  protected readonly classNameError = signal<string>('');

  // Get options from dropdown store
  get practiceAreaOptions() {
    return this.dropdownStore.practiceAreaOptions();
  }

  get goodAtOptions() {
    return this.dropdownStore.goodAtOptions();
  }

  constructor(private fb: FormBuilder) {
    this.initializeForm();
  }

  ngOnInit(): void {
    // Update form when data changes
    this.updateFormFromData();

    // Listen to form changes and validate only touched fields
    this.metadataForm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.validateTouchedFields();
        if (this.metadataForm.valid) {
          this.emitFormData();
        }
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['data'] && !changes['data'].firstChange) {
      this.updateFormFromData();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.metadataForm = this.fb.group({
      name: [''],
      description: [''],
      tool_class_name: [''],
      practiceArea: [''],
      goodAtTags: [[]]
    });
  }

  private updateFormFromData(): void {
    const currentData = this.data();
    this.metadataForm.patchValue({
      name: currentData.name,
      description: currentData.description,
      tool_class_name: currentData.tool_class_name,
      practiceArea: currentData.practiceArea || '',
      goodAtTags: currentData.goodAtTags || []
    }, { emitEvent: false });
  }

  private emitFormData(): void {
    const formValue = this.metadataForm.value;
    this.dataChanged.emit({
      name: formValue.name,
      description: formValue.description,
      tool_class_name: formValue.tool_class_name,
      practiceArea: formValue.practiceArea,
      goodAtTags: formValue.goodAtTags || []
    });
  }

  protected isOptionSelected(value: string): boolean {
    return this.metadataForm.get('goodAtTags')?.value?.includes(value) || false;
  }

  onGoodAtSelectionChange(event: any): void {
    const selectedValues = Array.isArray(event) ? event : (event.value || []);
    this.metadataForm.patchValue({
      goodAtTags: selectedValues
    });
    this.emitFormData();
  }

  onGoodAtTagRemove(tagToRemove: string): void {
    const currentTags = this.metadataForm.get('goodAtTags')?.value || [];
    const updatedTags = currentTags.filter((tag: string) => tag !== tagToRemove);
    this.metadataForm.patchValue({
      goodAtTags: updatedTags
    });
    this.emitFormData();
  }

  // Simple validation - only for touched fields
  private validateTouchedFields(): void {
    const nameControl = this.metadataForm.get('name');
    const descControl = this.metadataForm.get('description');
    const classControl = this.metadataForm.get('tool_class_name');

    // Only validate if field was touched (user interacted with it)
    if (nameControl?.touched) {
      this.nameError.set(this.getFieldError(nameControl.value, 'Tool name'));
    }

    if (descControl?.touched) {
      this.descriptionError.set(this.getFieldError(descControl.value, 'Tool description'));
    }

    if (classControl?.touched) {
      const value = classControl.value?.trim() || '';
      if (!value) {
        this.classNameError.set('Tool class name is required');
      } else if (!value.trim()) {
        this.classNameError.set('Cannot be only whitespace');
      } else if (!/^[A-Z][a-zA-Z0-9]*$/.test(value)) {
        this.classNameError.set('Must start with uppercase letter and contain only letters/numbers');
      } else {
        this.classNameError.set('');
      }
    }
  }

  private getFieldError(value: string, fieldName: string): string {
    const trimmed = value?.trim() || '';
    if (!trimmed) return `${fieldName} is required`;
    if (!trimmed.trim()) return 'Cannot be only whitespace';
    return '';
  }

}
