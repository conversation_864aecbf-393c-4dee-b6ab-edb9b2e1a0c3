.compiler_container {
  display: flex;
  flex-direction: column;
  gap: var(--Global-V1-Spacing-Space8, 16px);
  padding: 16px;
  width: 100%;
  border-radius: var(--Global-V1-Radius-Rad7, 14px);
  border: 0.5px solid var(--Brand-Neutral-n-100, #D1D3D8);
  background: var(--Surface-Fill-Dark-Surface-Dark-7, rgba(30, 30, 30, 0.70));
  box-shadow: 0 2px 4px 0 var(--Brand-Neutral-n-100, #D1D3D8);
  box-sizing: border-box;
  height: calc(100vh - 195px)
}

.compiler-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}