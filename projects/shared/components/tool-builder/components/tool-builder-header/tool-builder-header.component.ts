import { AavaButtonComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, computed, input, output } from '@angular/core';

@Component({
  selector: 'app-tool-builder-header',
  imports: [CommonModule, AavaButtonComponent],
  templateUrl: './tool-builder-header.component.html',
  styleUrl: './tool-builder-header.component.scss',
})
export class ToolBuilderHeaderComponent {
  readonly isFormValid = input.required<boolean>();
  readonly isSaveEnabled = input.required<boolean>();
  readonly isRunEnabled = input.required<boolean>();
  readonly saveButtonLabel = input<string>('Save');

  // Signal outputs
  readonly saveClicked = output<void>();
  readonly runClicked = output<void>();

  // Computed values
  protected saveButtonAriaLabel = computed(() =>
    this.isSaveEnabled() ? 'Save tool' : 'Complete required fields and compile code to save tool'
  );

  protected runButtonAriaLabel = computed(() =>
    this.isRunEnabled() ? 'Run tool' : 'Save the tool first to enable run'
  );

  protected onSaveClick(): void {
    this.saveClicked.emit();
  }

  protected onRunClick(): void {
    this.runClicked.emit();
  }
}
