<app-libraries-main-layout [isThirdColumnVisible]="true">
  <app-tool-builder-header
    header
    [isFormValid]="isFormValid()"
    [isSaveEnabled]="isSaveEnabled()"
    [isRunEnabled]="isRunEnabled()"
    [saveButtonLabel]="saveButtonLabel()"
    (saveClicked)="onSaveTool()"
    (runClicked)="onRunTool()"
  >
  </app-tool-builder-header>

  <app-metadata-widget
    firstRow
    [data]="metadataData()"
    [loading]="isMetadataLoading()"
    (dataChanged)="onMetadataChange($event)"
  >
  </app-metadata-widget>

  <app-code-editor
    secondRow
    title="Tool Definition"
    language="python"
    theme="light"
    [value]="codeContent()"
    [loading]="isCodeEditorLoading()"
    [showPrimaryButton]="true"
    primaryButtonLabel="Compile"
    height="100%"
    (valueChange)="onCodeChange($event)"
    (primaryButtonClick)="onCompileClick($event)"
    [showActionButtons]="true"
  >
  </app-code-editor>

  <app-compiler-widget thirdRow>
    @if (isCompiling()) {
      <!-- Skeleton Lines for Compiling State -->
      <div class="compiler-skeleton-lines">
        @for (line of [1, 2, 3, 4]; track line) {
          <div class="skeleton-line">
            <aava-skeleton
              [width]="'100%'"
              [height]="'26px'"
              [shape]="'rectangle'"
              [animation]="'pulse'"
            ></aava-skeleton>
          </div>
        }     
      </div>
    } @else if (!validationResults()) {
      <div class="compiler-placeholder">
        <p class="placeholder-text">Press compile to begin compiler</p>
      </div>
    } @else {
      <div class="validation-results">
        <div class="security-issues-container">
          @if (hasSecurityIssues()) {
            @for (issue of getSecurityIssues(); track $index) {
              <div class="issue-card">
                <div class="issue-header">
                  <span class="issue-title">Issue {{ $index + 1 }}</span>
                </div>
                <div class="issue-content">
                  <div class="issue-field">
                    <span class="field-label">Type:<span class="field-value">{{ issue.type }}</span></span>
                  </div>
                  <div class="issue-field">
                    <span class="field-label">Description:</span>
                    <span class="field-value">{{ issue.description }}</span>
                  </div>
                  <div class="issue-field">
                    <span class="field-label">Severity:</span>
                    <span class="field-value severity-{{ issue.severity }}">{{ issue.severity }}</span>
                  </div>
                  <div class="issue-field">
                    <span class="field-label">Line Number:</span>
                    <span class="field-value">{{ issue.line_number }}</span>
                  </div>
                  <div class="issue-field">
                    <span class="field-label">Suggestion:</span>
                    <span class="field-value suggestion">{{ issue.suggestion }}</span>
                  </div>
                </div>
              </div>
            }
          } @else {
            <div class="no-issues-container">
              <div class="no-issues-header">
                <p class="no-issues-text">✅ No security issues found.</p>
              </div>
              @if (getAnalysisText()) {
                <div class="analysis-section">
                  <div class="analysis-header">
                    <span class="analysis-title">Security Analysis</span>
                  </div>
                  <div class="analysis-content">
                    <p class="analysis-text">{{ getAnalysisText() }}</p>
                  </div>
                </div>
              }
            </div>
          }
        </div>
      </div>
    }
  </app-compiler-widget>
</app-libraries-main-layout>
