// Skeleton loader styles
.compiler-skeleton-lines {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem; // Gap between groups

  .skeleton-line {
    width: 100%;
    line-height: 1;
    border-radius: 4px;
    overflow: hidden;
  }
}

// Compiler widget content styles
.compiler-container {
  position: relative;
  height: 100%;
  min-height: 200px;
}

.compiler-loading {
  position: absolute;
  top: -10px;    /* Extend beyond padding */
  left: -10px;   /* Extend beyond padding */
  right: -10px;  /* Extend beyond padding */
  bottom: -10px; /* Extend beyond padding */
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.9) 0%, 
    rgba(255, 255, 255, 0.7) 25%, 
    rgba(255, 255, 255, 0.8) 50%, 
    rgba(255, 255, 255, 0.7) 75%, 
    rgba(255, 255, 255, 0.9) 100%
  ); /* Smooth linear gradient overlay */
  z-index: 10; /* Ensure it's above other content */
}

.compiler-placeholder {
  display: flex;
  height: 100%;
  min-height: 200px;
}

.placeholder-text {
  color: var(--Brand-Neutral-n-100, #D1D3D8);
  font-family: var(--Global-v1-Family-Body, Inter);
  font-size: var(--Global-v1-Size-12, 12px);
  font-style: normal;
  font-weight: 500;
  line-height: var(--Global-v1-Line-height-16, 16px);
  margin: 0;
  text-align: center;
}

.validation-results {
  flex: 1;
  overflow: hidden;
  height: 100%;
}

.security-issues-container {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
}

.issue-card {
  margin-bottom: 32px; /* Two lines of space between issues */
  padding: 0; /* Remove padding since no border */
  background: transparent; /* Remove background */
}

.issue-header {
  margin-bottom: 12px;
}

.issue-title {
  color: var(--Brand-Neutral-n-100, #D1D3D8);
  font-family: var(--Global-v1-Family-Body, Inter);
  font-size: var(--Global-v1-Size-12, 12px);
  font-style: normal;
  font-weight: 600;
  line-height: var(--Global-v1-Line-height-16, 16px);
}

.issue-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.issue-field {
  display: flex;
  flex-direction: row;
  gap: 4px; /* Reduce gap between label and value */
  margin-bottom: 4px;
  align-items: flex-start; /* Align items to top */
}

.field-label {
  color: var(--Brand-Neutral-n-100, #D1D3D8);
  font-family: var(--Global-v1-Family-Body, Inter);
  font-size: var(--Global-v1-Size-12, 12px);
  font-style: normal;
  font-weight: 700; /* Make labels bold */
  line-height: var(--Global-v1-Line-height-16, 16px);
  flex-shrink: 0; /* Prevent label from shrinking */
}

.field-value {
  color: var(--Brand-Neutral-n-100, #D1D3D8);
  font-family: var(--Global-v1-Family-Body, Inter);
  font-size: var(--Global-v1-Size-12, 12px);
  font-style: normal;
  font-weight: 400;
  line-height: var(--Global-v1-Line-height-16, 16px);
  flex: 1;
  word-wrap: break-word;
  overflow-wrap: break-word; /* Better text wrapping */
  hyphens: auto; /* Allow hyphenation for long words */
}

.field-value.suggestion {
  color: #4A9EFF;
}

.severity-high {
  color: #FF4A4A;
}

.severity-medium {
  color: #FFA500;
}

.severity-low {
  color: #90EE90;
}

.no-issues-container {
  height: 100%;
  overflow-y: auto;
}

.no-issues-header {
  margin-bottom: 32px; /* Two lines of space */
  padding: 0; /* Remove padding */
  background: transparent; /* Remove background */
  text-align: left; /* Align left instead of center */
}

.no-issues-text {
  color: #90EE90;
  font-family: var(--Global-v1-Family-Body, Inter);
  font-size: var(--Global-v1-Size-12, 12px);
  font-style: normal;
  font-weight: 600;
  line-height: var(--Global-v1-Line-height-16, 16px);
  margin: 0;
}

.analysis-section {
  padding: 0; /* Remove padding */
  background: transparent; /* Remove background */
}

.analysis-header {
  margin-bottom: 12px;
}

.analysis-title {
  color: var(--Brand-Neutral-n-100, #D1D3D8);
  font-family: var(--Global-v1-Family-Body, Inter);
  font-size: var(--Global-v1-Size-12, 12px);
  font-style: normal;
  font-weight: 600;
  line-height: var(--Global-v1-Line-height-16, 16px);
}

.analysis-content {
  margin-top: 8px;
}

.analysis-text {
  color: var(--Brand-Neutral-n-100, #D1D3D8);
  font-family: var(--Global-v1-Family-Body, Inter);
  font-size: var(--Global-v1-Size-12, 12px);
  font-style: normal;
  font-weight: 500;
  line-height: var(--Global-v1-Line-height-16, 16px);
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
