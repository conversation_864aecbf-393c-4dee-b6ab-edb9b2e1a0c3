import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  signal,
  inject,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, firstValueFrom } from 'rxjs';
import { LibrariesMainLayoutComponent } from '@shared';
import { CodeEditorComponent } from 'projects/shared/components/code-editor/code-editor.component';
import {
  AavaDialogService,
  AavaCubicalLoadingComponent,
  AavaSkeletonComponent,
} from '@aava/play-core';

import {
  MetadataWidgetComponent,
  MetadataWidgetData,
} from '../../widgets/metadata-widget/metadata-widget.component';
import { CompilerWidgetComponent } from '../../widgets/compiler-widget/compiler-widget.component';
import { ToolBuilderHeaderComponent } from '../tool-builder-header/tool-builder-header.component';
import { ToolService } from '../../../../services/tool.service';
import { TeamIdService } from '../../../../services/team-id.service';
import { DropdownDataStore } from '../../../../stores/dropdown-data.store';
import { TokenStorageService } from '@shared';
import { ToastWrapperService } from 'projects/shared/services/toast-wrapper.service';
import { RevelioStore } from '../../../../stores/revelio.store';

@Component({
  selector: 'app-tool-builder',
  imports: [
    ToolBuilderHeaderComponent,
    CodeEditorComponent,
    MetadataWidgetComponent,
    CompilerWidgetComponent,
    CommonModule,
    LibrariesMainLayoutComponent,
    AavaSkeletonComponent,
  ],
  templateUrl: './tool-builder.component.html',
  styleUrl: './tool-builder.component.scss',
})
export class ToolBuilderComponent implements OnInit, OnDestroy {
  // Service injection
  private readonly dialogService = inject(AavaDialogService);
  private readonly toolService = inject(ToolService);
  private readonly teamIdService = inject(TeamIdService);
  private readonly dropdownStore = inject(DropdownDataStore);
  private readonly tokenStorage = inject(TokenStorageService);
  private readonly route = inject(ActivatedRoute);
  private readonly toastWrapper = inject(ToastWrapperService);
  private readonly revelioStore = inject(RevelioStore);

  // Component lifecycle
  private readonly destroy$ = new Subject<void>();

  compilerVisible = true;
  // Metadata data signals
  protected toolName = signal<string>('');
  protected toolDescription = signal<string>('');
  protected toolClassName = signal<string>('');
  protected practiceArea = signal<string>('');
  protected goodAtTags = signal<string[]>([]);

  // Code content signal
  protected readonly codeContent = signal<string>('');

  // Validation results signal
  protected readonly validationResults = signal<any>(null);
  protected readonly isCompiling = signal<boolean>(false);
  protected readonly hasCompiled = signal<boolean>(false);
  protected readonly hasSecurityWarnings = signal<boolean>(false);
  protected readonly isToolSaved = signal<boolean>(false);

  // Loading states for skeleton loaders
  protected readonly isMetadataLoading = signal<boolean>(true);
  protected readonly isCodeEditorLoading = signal<boolean>(true);

  // Practice area and tags tracking
  private readonly practiceAreaIdSignal = signal<number>(1);
  private readonly goodAtTagIdsSignal = signal<number[]>([]);

  // Edit mode tracking
  private readonly isEditMode = signal<boolean>(false);
  private readonly currentToolId = signal<number | null>(null);

  executionId: string = '';

  constructor(private router: Router) {}

  ngOnInit(): void {
    this.executionId = crypto.randomUUID();

    //console.log('🚀 ToolBuilder ngOnInit');
    //console.log('📋 Initial codeContent:', this.codeContent());
    this.loadDropdownData();

    // Check if we're editing an existing tool (from route params, not query params)
    const toolId = this.route.snapshot.params['id'];
    const queryToolId = this.route.snapshot.queryParams['id'];

    if (toolId && toolId !== 'create') {
      // Coming from route parameter (e.g., back from playground) - set edit mode
      this.isEditMode.set(true);
      this.currentToolId.set(parseInt(toolId));
      // Load existing tool data
      this.loadToolById(parseInt(toolId));
      this.isToolSaved.set(true);
    } else if (queryToolId) {
      // Coming from query parameter (e.g., after saving) - stay in creation mode but store ID
      this.isEditMode.set(false);
      this.currentToolId.set(parseInt(queryToolId));
      this.isToolSaved.set(true);
      // Don't load data, keep current form state
      this.simulateComponentLoading();
    } else {
      // New tool creation mode
      this.isEditMode.set(false);
      this.currentToolId.set(null);
      // Check for prompt from RevelioStore only for new tools
      this.checkForPrompt();
      // Simulate component loading for new tools
      this.simulateComponentLoading();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private async loadDropdownData(): Promise<void> {
    try {
      await Promise.all([
        this.dropdownStore.loadPracticeAreas(),
        this.dropdownStore.loadGoodAtTags(),
      ]);
    } catch (error) {
      console.error('Failed to load dropdown data:', error);
    }
  }

  /**
   * Check for prompt from RevelioStore and handle tool creation
   */
  private checkForPrompt(): void {
    const prompt = this.revelioStore.prompt();

    // For tool builder, if there's any prompt, treat it as tool creation
    if (prompt && prompt.trim().length > 0) {
      console.log('🎯 Found tool creation prompt:', prompt);
      this.handlePromptBasedToolCreation(prompt);
    }
  }

  /**
   * Handle tool creation from prompt using clean service method
   */
  private handlePromptBasedToolCreation(prompt: string): void {
    const userSignature = this.getUserSignature();

    this.toolService.generateToolFromPrompt(prompt, userSignature).subscribe({
      next: response => {
        console.log('✅ Tool generation successful:', response);
        this.populateToolFromResponse(response);
        this.hideSkeletonLoaders();
      },
      error: error => {
        console.error('❌ Tool generation failed:', error);
        this.hideSkeletonLoaders();
        this.toastWrapper.error(
          'Failed to generate tool from prompt. Please try again.'
        );
      },
    });
  }

  private hideSkeletonLoaders(): void {
    this.isMetadataLoading.set(false);
    this.isCodeEditorLoading.set(false);
  }

  /**
   * Load tool data by ID for editing
   */
  private loadToolById(toolId: number): void {
    console.log('🔄 Loading tool data for ID:', toolId);

    this.toolService.getById(toolId).subscribe({
      next: (response: any) => {
        console.log('✅ Tool data loaded:', response);

        // Extract tool from the response structure
        const tool = response?.userToolDetail;

        if (tool) {
          // Map tool data to form fields using the actual API response structure
          this.toolName.set(tool.name || '');
          this.toolDescription.set(tool.description || '');

          // Get tool class name and code from toolConfigs
          const toolConfigs = tool.toolConfigs;
          if (toolConfigs?.tool_class_name) {
            this.toolClassName.set(toolConfigs.tool_class_name);
          }

          if (toolConfigs?.tool_class_def) {
            this.codeContent.set(toolConfigs.tool_class_def);
          }

          // Map practice area - convert ID to string for dropdown
          if (tool.practiceArea) {
            // Find practice area name from dropdown store
            const practiceAreaOption = this.dropdownStore
              .practiceAreaOptions()
              .find(option => option.id === tool.practiceArea);
            if (practiceAreaOption) {
              this.practiceArea.set(practiceAreaOption.value);
              this.practiceAreaIdSignal.set(tool.practiceArea);
            }
          }

          // Map tags - convert IDs to tag names for display
          if (tool.tags && Array.isArray(tool.tags)) {
            const tagNames = tool.tags
              .map((tagId: number) => {
                const tagOption = this.dropdownStore
                  .goodAtOptions()
                  .find(option => option.id === tagId);
                return tagOption?.value;
              })
              .filter(Boolean) as string[];
            this.goodAtTags.set(tagNames);
            this.goodAtTagIdsSignal.set(tool.tags);
          }

          console.log('✅ Tool fields populated from API');
        }

        this.hideSkeletonLoaders();
      },
      error: error => {
        console.error('❌ Failed to load tool:', error);
        this.hideSkeletonLoaders();
        this.toastWrapper.error('Failed to load tool data. Please try again.');
      },
    });
  }

  /**
   * Populate tool fields from API response with correct field mapping
   */
  private populateToolFromResponse(response: any): void {
    try {
      if (response?.response?.choices?.[0]?.text) {
        const toolDataText = response.response.choices[0].text;
        const toolData = JSON.parse(toolDataText);

        console.log('📊 Parsed tool data:', toolData);

        // Map response fields correctly to metadata fields
        if (toolData.tool_name) {
          this.toolName.set(toolData.tool_name);
        }
        if (toolData.tool_description) {
          this.toolDescription.set(toolData.tool_description);
        }
        if (toolData.tool_class) {
          this.toolClassName.set(toolData.tool_class);
        }

        // Populate code content (note: API uses 'tool_class_defination' - keeping as is)
        if (toolData.tool_class_defination) {
          this.codeContent.set(toolData.tool_class_defination);
        }

        console.log('✅ Tool fields populated successfully');

        // Clear the prompt from store after successful population
        setTimeout(() => {
          this.revelioStore.clearResults();
        }, 2000);
      }
    } catch (error) {
      console.error('Error parsing tool response:', error);
    }
  }

  /**
   * Simulate component loading states - only if no prompt is being processed
   */
  private simulateComponentLoading(): void {
    const prompt = this.revelioStore.prompt();
    const promptType = this.revelioStore.promptType();

    // If we have a tool prompt, keep skeletons visible until API completes
    if (prompt && promptType === 'tool') {
      // Skeletons will be hidden by the API response handlers
      return;
    }

    // Normal loading simulation for non-prompt scenarios
    setTimeout(() => {
      this.isMetadataLoading.set(false);
    }, 1000);

    setTimeout(() => {
      this.isCodeEditorLoading.set(false);
    }, 1500);
  }

  // Computed values
  protected isFormValid = computed(() => {
    return !!(
      this.toolName().trim() &&
      this.toolDescription().trim() &&
      this.toolClassName().trim() &&
      this.codeContent().trim() &&
      this.practiceArea().trim()
    );
  });

  protected isSaveEnabled = computed(() => {
    const enabled = this.isFormValid() && this.hasCompiled();
    //console.log('Save button enabled:', enabled, 'Form valid:', this.isFormValid(), 'Has compiled:', this.hasCompiled());
    return enabled;
  });

  protected isRunEnabled = computed(() => {
    const enabled = this.isFormValid() && this.isToolSaved();
    //console.log('Run button enabled:', enabled, 'Form valid:', this.isFormValid(), 'Tool saved:', this.isToolSaved());
    return enabled;
  });

  protected metadataData = computed(() => ({
    name: this.toolName(),
    description: this.toolDescription(),
    tool_class_name: this.toolClassName(),
    practiceArea: this.practiceArea(),
    goodAtTags: this.goodAtTags(),
  }));

  protected saveButtonLabel = computed(() => {
    return this.isEditMode() ? 'Update' : 'Save';
  });

  protected async onSaveTool(): Promise<void> {
    if (!this.isSaveEnabled()) return;

    // Check for security issues and show appropriate confirmation
    const hasSecurityIssues = this.hasSecurityIssues();

    try {
      let result;

      const isUpdate = this.isEditMode();
      const actionText = isUpdate ? 'update' : 'save';
      const actionTextCapitalized = isUpdate ? 'Update' : 'Save';

      if (hasSecurityIssues) {
        // Show warning confirmation for security issues
        result = await this.dialogService.confirmation({
          title: 'Security Issues Detected',
          message: `Security issues have been found in your code. Are you sure you want to continue and ${actionText} this tool?`,
          confirmButtonText: `Yes, ${actionTextCapitalized} Anyway`,
          cancelButtonText: 'Cancel',
          confirmButtonVariant: 'danger',
        });
      } else {
        // Show normal confirmation for clean code
        result = await this.dialogService.confirmation({
          title: `${actionTextCapitalized} Tool`,
          message: `Your code has passed security validation. Do you want to ${actionText} this tool?`,
          confirmButtonText: actionTextCapitalized,
          cancelButtonText: 'Cancel',
          confirmButtonVariant: 'primary',
        });
      }

      if (result.confirmed) {
        await this.saveTool();
      }
    } catch (error) {
      console.error('Error showing confirmation dialog:', error);
    }
  }

  protected onRunTool(): void {
    if (this.isRunEnabled()) {
      // Get tool ID from route params or query params
      const routeToolId = this.route.snapshot.params['id'];
      const queryToolId = this.route.snapshot.queryParams['id'];
      const toolId =
        routeToolId && routeToolId !== 'create' ? routeToolId : queryToolId;

      if (toolId && toolId !== 'create') {
        this.navigateToPlayground(toolId);
      } else {
        this.dialogService.error({
          title: 'Error',
          message:
            'Tool ID not found. Please save the tool first before running it.',
        });
      }
    }
  }

  /**
   * Navigate to playground with tool data and ID
   */
  private navigateToPlayground(toolId: string): void {
    this.router.navigate(['/build/tools/playground'], {
      queryParams: {
        type: 'tool',
        id: toolId,
      },
      state: {
        toolData: {
          name: this.toolName(),
          description: this.toolDescription(),
          tool_class_name: this.toolClassName(),
        },
      },
    });
  }

  protected onMetadataChange(data: MetadataWidgetData): void {
    this.toolName.set(data.name);
    this.toolDescription.set(data.description);
    this.toolClassName.set(data.tool_class_name);

    // Handle practice area
    if (data.practiceArea) {
      this.practiceArea.set(data.practiceArea);
      // Get practice area ID from dropdown store
      const practiceAreaData = this.dropdownStore
        .practiceAreaOptions()
        .find(option => option.value === data.practiceArea);
      if (practiceAreaData?.id) {
        this.practiceAreaIdSignal.set(practiceAreaData.id);
      }
    }

    // Handle good at tags
    if (data.goodAtTags && data.goodAtTags.length > 0) {
      this.goodAtTags.set(data.goodAtTags);
      // Get good at tag IDs from dropdown store
      const tagIds = data.goodAtTags
        .map(tagName => {
          const tagData = this.dropdownStore
            .goodAtOptions()
            .find(option => option.value === tagName);
          return tagData?.id || 0;
        })
        .filter(id => id > 0);
      this.goodAtTagIdsSignal.set(tagIds);
    } else {
      this.goodAtTags.set([]);
      this.goodAtTagIdsSignal.set([]);
    }
  }

  /**
   * Save the tool
   */
  private async saveTool(): Promise<void> {
    // Get team ID from cookies (same as guardrails)
    const teamId = this.teamIdService.getTeamIdFromCookies() || 23;

    // Get practice area ID
    const practiceAreaId = this.practiceAreaIdSignal() || 8;

    // Get good at tag IDs
    const tagIds = this.goodAtTagIdsSignal();

    const toolData = {
      toolName: this.toolName(),
      toolDescription: this.toolDescription(),
      toolConfig: {
        image: '',
        tool_class_def: this.codeContent(), // Use actual code content
        tool_class_name: this.toolClassName(),
      },
      status: 'CREATED', // Set to CREATED for save action
      teamId: teamId,
      tags: tagIds, // Use actual tag IDs
      practiceArea: practiceAreaId,
    };

    // Show loading dialog
    // this.dialogService.loading({
    //   title: 'Saving Tool',
    //   message: 'Please wait while we save your tool...',
    // });

    try {
      let response;
      let successMessage;

      const currentId = this.currentToolId();
      if (this.isEditMode() && currentId) {
        // Update existing tool - use the custom payload method with ID
        const updateData = { id: currentId, ...toolData };
        response = await firstValueFrom(
          this.toolService.updateWithCustomPayload(updateData)
        );
        successMessage = 'Tool updated successfully!';
      } else {
        // Create new tool
        response = await firstValueFrom(
          this.toolService.createWithCustomPayload(toolData)
        );
        successMessage = 'Tool saved successfully!';
      }

      // Show success message
      await this.toastWrapper.success(successMessage);

      // Mark tool as saved to enable run button and store tool ID
      const resultToolId = response?.toolId || response?.id || currentId;
      if (resultToolId) {
        this.isToolSaved.set(true);

        // For new tool creation, just store the ID but don't switch to edit mode yet
        // Edit mode will be activated when navigating back from playground
        if (!this.isEditMode()) {
          this.currentToolId.set(resultToolId);
          // Update the route to include the tool ID for future runs, but stay in creation mode
          this.router.navigate([], {
            relativeTo: this.route,
            queryParams: { id: resultToolId },
            queryParamsHandling: 'merge',
            replaceUrl: true,
          });
        }
      } else {
        console.warn('No tool ID returned from API');
      }
    } catch (error) {
      console.error('Error saving/updating tool:', error);
      const errorMessage = this.isEditMode()
        ? 'Failed to update tool. Please try again.'
        : 'Failed to save tool. Please try again.';
      await this.toastWrapper.error(errorMessage);
    }
  }

  /**
   * Show success message with API response
   */
  private async showSuccessWithResponse(
    message: string,
    response: any
  ): Promise<void> {
    try {
      // Use API response message if available, otherwise use default
      let successMessage = message;
      if (response?.message) {
        successMessage = response.message;
      }

      // Add tool ID if available
      if (response?.toolId || response?.id) {
        successMessage += `\n\nTool ID: ${response.toolId || response.id}`;
      }

      await this.dialogService.success({
        title: 'Success',
        message: successMessage,
      });
    } catch (error) {
      console.error('Error showing success message:', error);
    }
  }

  /**
   * Handle code content changes
   */
  protected onCodeChange(code: string): void {
    this.codeContent.set(code);
    // Reset compilation state when code changes
    this.hasCompiled.set(false);
    this.validationResults.set(null);
    this.isToolSaved.set(false);
  }

  /**
   * Handle compile button click
   */
  protected onCompileClick(code: string): void {
    //console.log('🔥 Compile button clicked!');
    //console.log('📝 Code received:', code);

    // Set loading state
    this.isCompiling.set(true);
    this.validationResults.set(null);

    // Get user signature
    const userSignature = this.getUserSignature();

    // Prepare compile payload
    const compilePayload = {
      executionId: this.executionId,
      prompt: code,
      mode: 'VALIDATE_TOOLS',
      promptOverride: false,
      userSignature: userSignature,
    };

    //console.log('📦 Compile payload:', compilePayload);

    // Call compile API and handle response
    this.toolService.compile(compilePayload).subscribe({
      next: response => {
        //console.log('✅ Compilation successful:', response);

        // Update validation results and stop loading
        this.validationResults.set(response);
        this.isCompiling.set(false);
        this.hasCompiled.set(true);

        // Check for security issues
        const hasSecurityIssues = this.hasSecurityIssues();
        this.hasSecurityWarnings.set(hasSecurityIssues);

        // Reset save state to require re-save after new compilation
        this.isToolSaved.set(false);
      },
      error: (error: any) => {
        console.error('❌ Compilation failed:', error);

        // Set error result and stop loading
        this.validationResults.set({
          error: true,
          message:
            error?.message ||
            'Failed to validate tool code. Please check your code and try again.',
        });
        this.isCompiling.set(false);
        this.hasCompiled.set(false);
        this.hasSecurityWarnings.set(false);
        this.isToolSaved.set(false);
      },
    });
  }

  /**
   * Get user signature for API calls
   */
  private getUserSignature(): string {
    const email =
      this.tokenStorage.getCookie('da_username') || '<EMAIL>';
    return email;
  }

  /**
   * Format validation output for display
   */
  protected formatValidationOutput(): string {
    const results = this.validationResults();
    if (results) {
      return JSON.stringify(results, null, 2);
    }
    return '';
  }

  /**
   * Get parsed security issues from validation results
   */
  protected getSecurityIssues(): any[] {
    const results = this.validationResults();
    if (results?.response?.choices?.[0]?.text) {
      try {
        // Extract JSON from the text response
        const jsonMatch = results.response.choices[0].text.match(
          /```json\n([\s\S]*?)\n```/
        );
        if (jsonMatch) {
          const parsedData = JSON.parse(jsonMatch[1]);
          return parsedData.issues || [];
        }
      } catch (error) {
        console.error('Error parsing security issues:', error);
      }
    }
    return [];
  }

  /**
   * Check if security issues were found
   */
  protected hasSecurityIssues(): boolean {
    const results = this.validationResults();
    if (results?.response?.choices?.[0]?.text) {
      try {
        const jsonMatch = results.response.choices[0].text.match(
          /```json\n([\s\S]*?)\n```/
        );
        if (jsonMatch) {
          const parsedData = JSON.parse(jsonMatch[1]);
          return parsedData.security_issues_found === true;
        }
      } catch (error) {
        console.error('Error parsing security issues:', error);
      }
    }
    return false;
  }

  /**
   * Get analysis text from validation results
   */
  protected getAnalysisText(): string {
    const results = this.validationResults();
    if (results?.response?.choices?.[0]?.text) {
      try {
        // Extract analysis section after the JSON
        const text = results.response.choices[0].text;
        const analysisMatch = text.match(
          /```\n\n###?\s*(?:Explanation|Analysis):\s*([\s\S]*?)$/
        );
        if (analysisMatch) {
          return analysisMatch[1].trim();
        }
      } catch (error) {
        console.error('Error parsing analysis text:', error);
      }
    }
    return '';
  }
}
