# Practice Area Filter Component

A reusable Angular component for filtering content by practice areas with radio button selection and customizable options.

## Features

- Radio button selection for practice areas
- Customizable options and labels
- TypeScript support with proper typing
- Responsive design
- Dark mode support
- Accessibility features

## Usage

### Basic Usage

```typescript
import { PracticeAreaFilterComponent } from '@shared/components/practice-area-filter';

@Component({
  selector: 'app-example',
  standalone: true,
  imports: [PracticeAreaFilterComponent],
  template: `
    <app-practice-area-filter
      [selectedArea]="selectedPracticeArea"
      (areaChange)="onPracticeAreaChange($event)"
    >
    </app-practice-area-filter>
  `,
})
export class ExampleComponent {
  selectedPracticeArea = '';

  onPracticeAreaChange(area: string) {
    this.selectedPracticeArea = area;
    // Handle practice area change logic
  }
}
```

### With Custom Options

```typescript
import {
  PracticeAreaFilterComponent,
  PracticeAreaOption,
} from '@shared/components/practice-area-filter';

@Component({
  selector: 'app-custom-example',
  standalone: true,
  imports: [PracticeAreaFilterComponent],
  template: `
    <app-practice-area-filter
      [customOptions]="customPracticeAreas"
      [selectedArea]="selectedArea"
      (areaChange)="onAreaChange($event)"
    >
    </app-practice-area-filter>
  `,
})
export class CustomExampleComponent {
  customPracticeAreas: PracticeAreaOption[] = [
    {
      id: 'legal',
      label: 'Legal Services',
      value: 'legal',
      description: 'Legal and compliance solutions',
    },
    {
      id: 'hr',
      label: 'Human Resources',
      value: 'hr',
      description: 'HR management and recruitment',
    },
  ];

  selectedArea = '';

  onAreaChange(area: string) {
    this.selectedArea = area;
  }
}
```

## API Reference

### Inputs

| Property        | Type                           | Default           | Description                               |
| --------------- | ------------------------------ | ----------------- | ----------------------------------------- |
| `selectedArea`  | `string`                       | `''`              | Currently selected practice area value    |
| `title`         | `string`                       | `'Practice Area'` | Title displayed above the filter options  |
| `customOptions` | `PracticeAreaOption[] \| null` | `null`            | Custom filter options (overrides default) |

### Outputs

| Event        | Type                   | Description                                  |
| ------------ | ---------------------- | -------------------------------------------- |
| `areaChange` | `EventEmitter<string>` | Emitted when practice area selection changes |

### Interfaces

```typescript
export interface PracticeAreaOption {
  id: string;
  label: string;
  value: string;
  description?: string; // Optional description text
}
```

## Default Practice Areas

The component includes the following default practice areas:

- Healthcare - Medical and healthcare solutions
- Finance - Financial services and banking
- Technology - Technology and software solutions
- Education - Educational and training programs
- Retail - Retail and e-commerce solutions
- Manufacturing - Manufacturing and industrial processes

## Styling

The component uses CSS custom properties and is designed to work with both light and dark themes. The styling is contained within the component and follows the design system patterns used throughout the application.

## Accessibility

- Full keyboard navigation support
- Screen reader friendly
- Focus indicators
- Semantic HTML structure
- ARIA labels where appropriate

## Examples

### Custom Title

```typescript
<app-practice-area-filter
  title="Select Practice Area"
  [selectedArea]="selectedArea"
  (areaChange)="onAreaChange($event)">
</app-practice-area-filter>
```

### Integration with Filter Search Component

This component is designed to work alongside the `ArtifactFilterComponent` in the `FilterSearchComponent`:

```html
<div class="filter-components-container">
  <app-artifact-filter></app-artifact-filter>
  <app-practice-area-filter></app-practice-area-filter>
</div>
```
