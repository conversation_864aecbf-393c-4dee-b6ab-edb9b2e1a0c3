<div class="practice-area-filter">
  <!-- Title -->
  <div class="filter-title">
    <h3>{{ title }}</h3>
  </div>

  <!-- Practice Area Options -->
  <div class="filter-options">
    <div
      *ngFor="let option of practiceAreaOptions; trackBy: trackByOptionId"
      class="filter-option"
      [class.selected]="selectedArea === option.value"
    >
      <label class="filter-option-label">
        <input
          type="radio"
          [name]="'practice-area'"
          [value]="option.value"
          [checked]="selectedArea === option.value"
          (change)="onAreaChange(option.value)"
          class="filter-radio"
        />
        <div class="radio-indicator"></div>
        <span class="filter-label-text">{{ option.label }}</span>
      </label>
    </div>
  </div>
</div>
