.practice-area-filter {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: var(--color-background-primary, #ffffff);
  border-radius: 8px;

  .filter-title {
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--color-text-primary, #1f2937);
      line-height: 1.5;
    }
  }

  .filter-options {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .filter-option {
      .filter-option-label {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 12px;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.2s ease;
        position: relative;

        &:hover {
          background: var(--color-surface-subtle-hover, #f9fafb);
        }

        .filter-radio {
          opacity: 0;
          position: absolute;
          width: 0;
          height: 0;
        }

        .radio-indicator {
          width: 16px;
          height: 16px;
          border: 2px solid var(--color-border-default, #d1d5db);
          border-radius: 50%;
          position: relative;
          transition: all 0.2s ease;
          flex-shrink: 0;

          &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            width: 6px;
            height: 6px;
            background: var(--color-brand-primary, #3b82f6);
            border-radius: 50%;
            transition: transform 0.2s ease;
          }
        }

        .filter-label-text {
          font-size: 14px;
          color: var(--color-text-primary, #374151);
          line-height: 1.5;
          font-weight: 500;
        }

        // Hover state
        &:hover .radio-indicator {
          border-color: var(--color-border-interactive, #3b82f6);
        }

        // Focus state (keyboard navigation)
        .filter-radio:focus-visible + .radio-indicator {
          box-shadow: 0 0 0 2px
            rgba(var(--rgb-brand-primary, 59, 130, 246), 0.5);
        }
      }

      // Selected state
      &.selected .filter-option-label {
        background: var(--color-background-secondary, #eff6ff);

        .radio-indicator {
          border-color: var(--color-border-interactive, #3b82f6);
          background: var(--color-background-primary, #ffffff);

          &::after {
            transform: translate(-50%, -50%) scale(1);
          }
        }

        .filter-label-text {
          color: var(--color-text-interactive, #1e40af);
          font-weight: 600;
        }
      }
    }
  }
}

// Dark mode support
[data-theme='dark'] {
  .practice-area-filter {
    background: var(--neutral-50, #1f2937);

    .filter-title h3 {
      color: var(--neutral-900, #f9fafb);
    }

    .filter-options {
      .filter-option {
        .filter-option-label {
          &:hover {
            background: var(--neutral-100, #374151);
          }

          .radio-indicator {
            border-color: var(--neutral-200, #4b5563);
          }

          .filter-label-text {
            color: var(--neutral-900, #f9fafb);
          }

          &:hover .radio-indicator {
            border-color: var(--pulse-blue-600, #60a5fa);
          }
        }

        &.selected .filter-option-label {
          background: var(--pulse-blue-50, #1e3a8a);

          .radio-indicator {
            border-color: var(--pulse-blue-600, #60a5fa);
            background: var(--neutral-50, #1f2937);

            &::after {
              background: var(--pulse-blue-600, #60a5fa);
            }
          }

          .filter-label-text {
            color: var(--pulse-blue-700, #93c5fd);
          }
        }
      }
    }
  }
}
