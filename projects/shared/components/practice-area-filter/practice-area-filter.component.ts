import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';

export interface PracticeAreaOption {
  id: string;
  label: string;
  value: string;
}

@Component({
  selector: 'app-practice-area-filter',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './practice-area-filter.component.html',
  styleUrl: './practice-area-filter.component.scss',
})
export class PracticeAreaFilterComponent {
  @Input() selectedArea: string = '';
  @Input() title = 'Practice Area';
  @Input() customOptions: PracticeAreaOption[] | null = null;

  @Output() areaChange = new EventEmitter<string>();

  // Default practice area options
  get practiceAreaOptions(): PracticeAreaOption[] {
    if (this.customOptions) {
      return this.customOptions;
    }

    return [
      {
        id: 'healthcare',
        label: 'Healthcare',
        value: 'healthcare',
      },
      {
        id: 'finance',
        label: 'Finance',
        value: 'finance',
      },
      {
        id: 'technology',
        label: 'Technology',
        value: 'technology',
      },
      {
        id: 'education',
        label: 'Education',
        value: 'education',
      },
      {
        id: 'retail',
        label: 'Retail',
        value: 'retail',
      },
      {
        id: 'manufacturing',
        label: 'Manufacturing',
        value: 'manufacturing',
      },
    ];
  }

  onAreaChange(area: string) {
    if (this.selectedArea !== area) {
      this.selectedArea = area;
      this.areaChange.emit(area);
    }
  }

  trackByOptionId(index: number, option: PracticeAreaOption): string {
    return option.id;
  }
}
