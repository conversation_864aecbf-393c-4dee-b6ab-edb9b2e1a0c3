<div class="agent-builder-page" [class.execute-mode]="executeMode">
  <!-- Header -->
  @if (isSkeletonLoading()) {
    <!-- <aava-skeleton height="2.5rem" width="100%"></aava-skeleton> -->
  } @else {
    <app-agent-builder-header
      *ngIf="showHeader && !executeMode"
      [isFormValid]="isFormValidForUI()"
      [lastSavedTime]="lastSavedTime()"
      [timeUpdateTrigger]="timeUpdateTrigger()"
      (runClicked)="onRunAgent()"
    ></app-agent-builder-header>
  }

  <!-- Main Content -->
  <main class="agent-builder-content">
    <!-- Left Column -->
    <div class="left-column">
      <!-- Agent Widget -->
      @if (isSkeletonLoading()) {
        <aava-skeleton height="400px" width="100%"></aava-skeleton>
      } @else {
        <app-agent-widget
          [data]="agentData()"
          [disabled]="executeMode"
          [executeMode]="executeMode"
          [agentNameError]="agentNameError()"
          [agentDetailsError]="agentDetailsError()"
          (agentNameChanged)="onAgentNameChange($event)"
          (agentDetailsChanged)="onAgentDetailsChange($event)"
          (practiceAreaChanged)="onPracticeAreaChange($event)"
          (tagAdded)="onTagAdded($event)"
          (tagRemoved)="onTagRemoved($event)"
          (goodAtSelectionChanged)="onGoodAtSelectionChange($event)"
          (imageChanged)="onImageChanged($event)"
          (imageEditClicked)="onImageEditClicked()"
          (infoClicked)="onAgentInfoClicked()"
        ></app-agent-widget>
      }

      <!-- Knowledge Base Widget -->
      @if (isSkeletonLoading()) {
        <aava-skeleton height="200px" width="100%"></aava-skeleton>
      } @else {
        <app-knowledge-base-widget
          [data]="knowledgeBaseData()"
          [disabled]="executeMode"
          [executeMode]="executeMode"
          (addClicked)="onKnowledgeBaseAddClicked()"
          (knowledgeBaseRemoved)="onKnowledgeBaseRemoved($event)"
          (infoClicked)="onKnowledgeBaseInfoClicked()"
        ></app-knowledge-base-widget>
      }
    </div>

    <!-- Middle Column -->
    <div class="middle-column">
      <!-- Behaviour Widget -->
      @if (isSkeletonLoading()) {
        <aava-skeleton height="500px" width="100%"></aava-skeleton>
      } @else {
        <app-behaviour-widget
          [data]="behaviourData()"
          [disabled]="executeMode"
          [executeMode]="executeMode"
          (goalChanged)="onGoalChange($event)"
          (backStoryChanged)="onBackStoryChange($event)"
          (descriptionChanged)="onDescriptionChange($event)"
          (expectedOutputChanged)="onExpectedOutputChange($event)"
          (agentRoleChanged)="onAgentRoleChange($event)"
          (improveClicked)="onImproveClicked()"
          (infoClicked)="onBehaviourInfoClicked()"
        ></app-behaviour-widget>
      }
    </div>

    <!-- Right Column -->
    <div class="right-column">
      <!-- AI Model Widget -->
      @if (isSkeletonLoading()) {
        <aava-skeleton height="300px" width="100%"></aava-skeleton>
      } @else {
        <app-ai-model-widget
          [data]="aiModelData()"
          [disabled]="executeMode"
          (modelChanged)="onModelChange($event)"
          (temperatureChanged)="onTemperatureChange($event)"
          (maxTokenChanged)="onMaxTokenChange($event)"
          (topPChanged)="onTopPChange($event)"
          (maxIterationChanged)="onMaxIterationChange($event)"
          (maxRpmChanged)="onMaxRpmChange($event)"
          (maxExecutionTimeChanged)="onMaxExecutionTimeChange($event)"
          (infoClicked)="onAIModelInfoClicked()"
        ></app-ai-model-widget>
      }

      <!-- Tools Widget -->
      @if (isSkeletonLoading()) {
        <aava-skeleton height="200px" width="100%"></aava-skeleton>
      } @else {
        <app-tools-widget
          [data]="toolsData()"
          [disabled]="executeMode"
          [executeMode]="executeMode"
          (addClicked)="onToolsAddClicked()"
          (toolRemoved)="onToolRemoved($event)"
          (infoClicked)="onToolsInfoClicked()"
        ></app-tools-widget>
      }

      <!-- Guardrails Widget -->
      @if (isSkeletonLoading()) {
        <aava-skeleton height="200px" width="100%"></aava-skeleton>
      } @else {
        <app-guardrails-widget
          [data]="guardrailsData()"
          [disabled]="guardrailsDisabled() || executeMode"
          [executeMode]="executeMode"
          (addClicked)="onGuardrailsAddClicked()"
          (guardrailRemoved)="onGuardrailRemoved($event)"
          (infoClicked)="onGuardrailsInfoClicked()"
        ></app-guardrails-widget>
      }
    </div>
  </main>

  <!-- Knowledge Base Library Modal is now opened via dialog service -->
</div>
