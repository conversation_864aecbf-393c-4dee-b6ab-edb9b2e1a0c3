.agent-builder-page {
  min-height: 100vh;
  // border-radius: 0.75rem 0.75rem 0.75rem 0.75rem;
  // background: rgba(255, 255, 255, 0.6);
  // box-shadow: var(--Elevation-01-X, 0) var(--Elevation-01-Y, 2px)
  //   var(--Elevation-01-Blur, 4px) var(--Elevation-01-Spread, 0)
  //   var(--Brand-Neutral-n-100, #d1d3d8);
  margin: 1rem;

  // Execute mode styling
  &.execute-mode {
    margin: 0; // Remove margin in execute mode for better integration
    border-radius: 0; // Remove border radius in execute mode
    box-shadow: none; // Remove shadow in execute mode
    min-height: auto; // Allow natural height in execute mode
  }
}

.agent-builder-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  // max-width: 1400px;
  margin: 0 auto;
  // padding: 32px 24px 24px 24px;
  // background: rgba(255, 255, 255, 0.6);
  // border-bottom-right-radius: 0.75rem;
  // border-bottom-left-radius: 0.75rem;
  // transition: opacity 0.3s ease;

  &.hidden {
    opacity: 0;
    pointer-events: none;
  }
}

.left-column,
.middle-column,
.right-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// Responsive Design
@media (max-width: 1200px) {
  .agent-builder-content {
    grid-template-columns: 1fr 1fr;

    .right-column {
      grid-column: 1 / -1;
    }
  }
}

@media (max-width: 768px) {
  .agent-builder-content {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0 16px 16px 16px;
  }
}
