/* eslint-disable complexity */
/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 * Agent Builder Page Component
 *
 * This component can be used in two modes:
 * 1. Normal Mode (default): Full functionality with header, form interactions, and autosave
 * 2. Execute Mode: View-only mode for displaying agent data without interactions
 *
 * Usage Examples:
 *
 * Normal Mode (Agent Builder):
 * ```html
 * <app-agent-builder-page [showHeader]="true"></app-agent-builder-page>
 * ```
 *
 * Execute Mode (Playground/View-only):
 * ```html
 * <app-agent-builder-page
 *   [executeMode]="true"
 *   [agentId]="'123'"
 *   [showHeader]="false">
 * </app-agent-builder-page>
 * ```
 *
 * Execute Mode Features:
 * - All form fields are disabled/read-only
 * - No header displayed
 * - No page-level loading overlay
 * - No autosave functionality
 * - Data loads normally from API using agentId
 * - Perfect for visual display in playground
 */
import {
  AavaDialogService,
  AavaToastService,
  AavaSkeletonComponent,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  signal,
  computed,
  effect,
  inject,
  OnInit,
  OnDestroy,
  Input,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { KnowledgeBaseStore } from '@shared';
import { Subject, takeUntil } from 'rxjs';

import { Agent, AgentConfig } from '../../../../models/artifact.model';
import { GuardrailService } from '../../../../services/guardrail.service';
import { KnowledgeBaseService } from '../../../../services/knowledge-base.service';
import { ModalResultService } from '../../../../services/modal-result.service';
import { TeamIdService } from '../../../../services/team-id.service';
import { ToolService } from '../../../../services/tool.service';
import { AgentStore } from '../../../../stores/agent.store';
import { DropdownDataStore } from '../../../../stores/dropdown-data.store';
import { RevelioStore } from '../../../../stores/revelio.store';
import { LibraryItem, LibraryModalConfig } from '../../../interfaces';
import { LibraryModalContentComponent } from '../../../library-modal/components/library-modal-content/library-modal-content.component';
import {
  AgentWidgetComponent,
  BehaviourWidgetComponent,
  AIModelWidgetComponent,
  ToolsWidgetComponent,
  GuardrailsWidgetComponent,
  KnowledgeBaseWidgetComponent,
} from '../../widgets';
import { AgentBuilderHeaderComponent } from '../agent-builder-header';

@Component({
  selector: 'app-agent-builder-page',
  standalone: true,
  imports: [
    CommonModule,
    AgentBuilderHeaderComponent,
    AgentWidgetComponent,
    BehaviourWidgetComponent,
    AIModelWidgetComponent,
    ToolsWidgetComponent,
    GuardrailsWidgetComponent,
    KnowledgeBaseWidgetComponent,
    AavaSkeletonComponent,
  ],
  templateUrl: './agent-builder-page.component.html',
  styleUrls: ['./agent-builder-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgentBuilderPageComponent implements OnInit, OnDestroy {
  @Input() showHeader: boolean = true;
  @Input() executeMode: boolean = false; // When true, disables all form interactions and hides header
  @Input() agentId?: string; // Agent ID to load in execute mode
  // Service injection
  private dialogService = inject(AavaDialogService);
  private toastService = inject(AavaToastService);
  private modalResultService = inject(ModalResultService);
  private teamIdService = inject(TeamIdService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private agentStore = inject(AgentStore);
  private revelioStore = inject(RevelioStore);
  private dropdownStore = inject(DropdownDataStore);
  private toolService = inject(ToolService);
  private knowledgeBaseService = inject(KnowledgeBaseService);
  private guardrailService = inject(GuardrailService);
  // private agentBuilderTourService = inject(AgentBuilderTourService);
  private destroy$ = new Subject<void>();
  private autosaveTimeout: any;
  private knowledgeBaseStore = inject(KnowledgeBaseStore);

  // Agent data signals
  protected agentName = signal<string>('');
  protected agentDetails = signal<string>('');
  protected practiceArea = signal<string>('Software Development');
  protected practiceAreaId = signal<number | null>(null);
  protected goodAtTags = signal<string[]>([]);
  protected goodAtTagIds = signal<number[]>([]);

  // Behaviour data signals
  protected goal = signal<string>('');
  protected backStory = signal<string>('');
  protected description = signal<string>('');
  protected expectedOutput = signal<string>('');
  protected agentRole = signal<string>('');

  // AI Model data signals
  protected selectedModel = signal<string>('');
  protected selectedModelId = signal<number | null>(null);
  protected temperature = signal<string>('');
  protected maxToken = signal<string>('');
  protected topP = signal<string>('');
  protected maxIteration = signal<string>('');
  protected maxRpm = signal<string>('');
  protected maxExecutionTime = signal<string>('');

  // Agent Image signal
  protected agentImage = signal<string>('');

  // Agent Status signal
  protected agentStatus = signal<string>('DRAFTED');

  // Tools data signals
  protected tools = signal<string[]>([]);

  // Guardrails data signals
  protected guardrails = signal<string[]>([]);
  protected guardrailsDisabled = signal<boolean>(true); // Disable guardrails by default

  // Form state signals
  protected isDirty = signal<boolean>(false);
  protected hasInitialSave = signal<boolean>(false);
  protected isLoading = signal<boolean>(true);
  protected isSkeletonLoading = signal<boolean>(false);
  protected lastSavedTime = signal<Date | null>(null);
  protected timeUpdateTrigger = signal<number>(0); // Trigger for time updates
  private autosaveCount = 0;
  private readonly baseDelay = 1000; // 1 second base delay
  private readonly maxDelay = 30000; // 30 seconds max delay
  private timeUpdateInterval: any;

  // Validation signals
  protected agentNameError = signal<string>('');
  protected agentRoleError = signal<string>('');
  protected agentDetailsError = signal<string>('');

  // Knowledge Base data signals
  protected knowledgeBases = signal<string[]>([]);
  protected selectedKnowledgeBaseItems = signal<LibraryItem[]>([]);
  protected selectedToolsItems = signal<LibraryItem[]>([]);
  protected selectedGuardrailsItems = signal<LibraryItem[]>([]);

  // Available tools from API for modal
  protected availableTools = signal<LibraryItem[]>([]);

  private currentModalType: 'knowledge' | 'tools' | 'guardrails' | null = null;

  constructor() {
    // Setup form validation effect in constructor to avoid injection context issues
    this.setupFormValidationEffect();
  }

  async ngOnInit(): Promise<void> {
    // Check for prompt from RevelioStore first (immediate skeleton loading)
    const prompt = this.revelioStore.prompt();
    if (prompt) {
      // Show skeleton loaders immediately
      this.isSkeletonLoading.set(true);
    }

    // Load agent data immediately for edit/execute modes
    if (this.executeMode && this.agentId) {
      // Show skeleton loading for execute mode immediately
      this.isSkeletonLoading.set(true);
      this.isLoading.set(true);
      // Execute mode: load specific agent by ID immediately
      this.loadAgentForEdit(parseInt(this.agentId));
    } else {
      // Check if we're editing an existing agent
      const agentId = this.route.snapshot.params['id'];

      // Only treat as edit mode if agentId is a valid number
      if (agentId && !isNaN(parseInt(agentId))) {
        // Show skeleton loading for edit mode immediately
        this.isSkeletonLoading.set(true);
        this.isLoading.set(true);
        this.loadAgentForEdit(parseInt(agentId));
      }
    }

    // Load dropdown data in parallel (non-blocking)
    // await this.loadDropdownData();

    // Load knowledge bases with error handling (non-blocking)
    try {
      await this.knowledgeBaseStore.loadKnowledgeBases();
    } catch (error) {
      console.warn(
        '⚠️ Knowledge base loading failed, continuing without KB data:',
        error
      );
    }

    // Load tools data (non-blocking)
    await this.loadToolsData();

    // Handle new agent creation (only if not edit/execute mode)
    if (!this.executeMode && !this.route.snapshot.params['id']) {
      // Handle prompt with Revelio API (skeleton loading already set above)
      if (prompt) {
        await this.handlePromptWithRevelio(prompt);
      } else {
        // Check for parsed agent data from RevelioStore (fallback)
        await this.handleRevelioAgentData();
        // Set loading to false after handling Revelio data
        this.isLoading.set(false);
      }
    }

    // Listen for modal results
    this.modalResultService.result$
      .pipe(takeUntil(this.destroy$))
      .subscribe((result: LibraryItem[] | null) => {
        if (result && this.currentModalType) {
          switch (this.currentModalType) {
            case 'knowledge':
              this.selectedKnowledgeBaseItems.set(result);
              this.knowledgeBases.set(
                result.map((item: LibraryItem) => item.title)
              );
              break;
            case 'tools':
              this.selectedToolsItems.set(result);
              this.tools.set(result.map((item: LibraryItem) => item.title));
              break;
            case 'guardrails':
              this.selectedGuardrailsItems.set(result);
              this.guardrails.set(
                result.map((item: LibraryItem) => item.title)
              );
              break;
          }
          this.currentModalType = null;
          this.modalResultService.clearResult();
          this.isDirty.set(true);
          this.triggerAutosave(); // Trigger autosave after modal changes
        }
      });

    // Listen for modal events (like create clicked)
    this.modalResultService.event$
      .pipe(takeUntil(this.destroy$))
      .subscribe(event => {
        if (event && event.type === 'createClicked' && this.currentModalType) {
          this.handleCreateAction(this.currentModalType);
          this.currentModalType = null;
          this.modalResultService.clearEvent();
        }
      });

    // Form validation effect is already set up in constructor

    // Start time update interval for saved time display
    this.startTimeUpdateInterval();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.autosaveTimeout) {
      clearTimeout(this.autosaveTimeout);
    }
    if (this.timeUpdateInterval) {
      clearInterval(this.timeUpdateInterval);
    }
  }

  // Setup form validation effect in constructor to avoid injection context issues
  private setupFormValidationEffect(): void {
    // Move effect to constructor to avoid injection context issues
    if (!this.executeMode) {
      effect(() => {
        const hasRequiredFields = this.hasRequiredFields();
        const hasInitialSave = this.hasInitialSave();

        // Trigger initial autosave when required fields are filled for the first time
        if (hasRequiredFields && !hasInitialSave) {
          this.triggerAutosave();
        }
      });
    }
  }

  // Load dropdown data from APIs
  private async loadDropdownData(): Promise<void> {
    try {
      await this.dropdownStore.loadAllData();
    } catch (error) {
      console.error('❌ Error loading dropdown data:', error);
    }
  }

  // Load tools data from API
  private async loadToolsData(): Promise<void> {
    try {
      // Get team ID for filtering tools
      const teamId = this.teamIdService.getTeamIdFromCookies() || 23;

      this.toolService
        .getAll({ teamId, status: 'APPROVED' })
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: any) => {
            // Handle the actual API response structure
            const tools = response?.userToolDetails || response || [];

            if (Array.isArray(tools)) {
              const mappedTools = this.mapToolsToLibraryItems(tools);
              this.availableTools.set(mappedTools);
            } else {
              this.availableTools.set([]);
            }
          },
          error: error => {
            console.error('❌ Error loading tools data:', error);
          },
        });
    } catch (error) {
      console.error('❌ Error loading tools data:', error);
    }
  }

  // Map API tools data to LibraryItem format
  private mapToolsToLibraryItems(tools: any[]): LibraryItem[] {
    return tools.map(tool => ({
      id: tool.id.toString(),
      title: tool.name,
      description: tool.toolDescription || `Tool created by ${tool.createdBy}`,
      icon: 'wrench',
      iconColor: '#000',
      usageCount: 0, // API doesn't provide usage count
      isSelected: false,
      category: tool.teamInfo?.domain || 'General',
      tags: [tool.teamInfo?.project || 'Tool', tool.status || 'APPROVED'],
      isActive: !tool.isDeleted && tool.status === 'APPROVED',
      createdAt: new Date(tool.createdAt),
      updatedAt: new Date(tool.approvedAt || tool.createdAt),
    }));
  }

  // Handle parsed agent data from RevelioStore
  private async handleRevelioAgentData(): Promise<void> {
    const parsedData = this.revelioStore.parsedAgentData();

    if (parsedData?.agentDetail) {
      const agentDetail = parsedData.agentDetail;

      // Map basic agent information first
      this.agentName.set(agentDetail.name || '');
      this.agentDetails.set(agentDetail.agentDetails || '');

      // Map behaviour data
      this.goal.set(agentDetail.goal || '');
      this.backStory.set(agentDetail.backstory || '');
      this.description.set(agentDetail.description || '');
      this.expectedOutput.set(agentDetail.expectedOutput || '');
      this.agentRole.set(agentDetail.role || '');

      // Map AI model configuration
      if (agentDetail.agentConfigs) {
        const configs = agentDetail.agentConfigs;
        this.temperature.set(configs.temperature || '');
        this.topP.set(configs.topP || '');
        this.maxToken.set(configs.maxToken || '');
        this.maxRpm.set(configs.maxRpm || '');
        this.maxExecutionTime.set(configs.maxExecutionTime || '');

        // Store model name for later mapping after dropdown data is loaded
        if (configs.modelRef && configs.modelRef.length > 0) {
          this.selectedModel.set(configs.modelRef[0].model || '');
        }
      }

      // Dropdown data is already loaded in ngOnInit()

      // Map practice area using dynamic data
      const practiceAreaName = this.mapPracticeArea(agentDetail.practiceArea);

      this.practiceArea.set(practiceAreaName);
      this.practiceAreaId.set(agentDetail.practiceArea);

      // Map tags using dynamic data
      const tagNames = this.mapTags(agentDetail.tags);

      this.goodAtTags.set(tagNames);
      this.goodAtTagIds.set(agentDetail.tags);

      // Map model using dynamic data
      const modelName = this.selectedModel();
      if (modelName) {
        const modelData = this.dropdownStore.getModelByName(modelName);
        if (modelData) {
          this.selectedModelId.set(modelData.id);
        }
      }

      // Clear the RevelioStore data after mapping with a delay to ensure form updates
      setTimeout(() => {
        this.revelioStore.clearResults();
      }, 4000);
    }
  }

  // Handle prompt with Revelio API call (new flow)
  private async handlePromptWithRevelio(prompt: string): Promise<void> {
    try {
      // Call Revelio API
      this.revelioStore.searchAgentCreation(prompt).subscribe({
        next: parsedData => {
          if (parsedData) {
            // Handle the parsed data
            this.handleRevelioAgentData();
          } else {
            // Fallback: use prompt data
            this.handlePromptData(prompt);
          }
          // Hide skeleton loaders
          this.isSkeletonLoading.set(false);
          this.isLoading.set(false);
        },
        error: error => {
          console.error('Failed to get agent creation help:', error);
          // Show error toast
          this.toastService.error({
            message:
              'Failed to generate agent details. Using basic form instead.',
          });
          // Fallback: use prompt data
          this.handlePromptData(prompt);
          // Hide skeleton loaders
          this.isSkeletonLoading.set(false);
          this.isLoading.set(false);
        },
      });
    } catch (error) {
      console.error('Error in handlePromptWithRevelio:', error);
      // Show error toast
      this.toastService.error({
        message: 'Failed to generate agent details. Using basic form instead.',
      });
      // Fallback: use prompt data
      this.handlePromptData(prompt);
      // Hide skeleton loaders
      this.isSkeletonLoading.set(false);
      this.isLoading.set(false);
    }
  }

  // Map practice area number to string using dynamic data
  private mapPracticeArea(practiceArea: number): string {
    const practiceAreaData =
      this.dropdownStore.getPracticeAreaById(practiceArea);

    const result = practiceAreaData?.name || 'Software Development';

    return result;
  }

  // Map tags array to goodAtTags using dynamic data
  private mapTags(tags: number[]): string[] {
    const goodAtTagsData = this.dropdownStore.getGoodAtTagsByIds(tags);

    const result = goodAtTagsData.map(tag => tag.name);

    return result;
  }

  // Handle prompt data from the prompt page
  private handlePromptData(prompt: string): void {
    // For now, just set the goal based on the prompt
    // Later this will be replaced with API call processing
    this.goal.set(prompt);

    // You can add more sophisticated parsing here later
    // For example, extract agent name, role, etc. from the prompt
    if (
      prompt.toLowerCase().includes('track') ||
      prompt.toLowerCase().includes('deadline')
    ) {
      this.agentName.set('Project Tracker Agent');
      this.agentRole.set('Project Management Assistant');
    } else if (
      prompt.toLowerCase().includes('engagement') ||
      prompt.toLowerCase().includes('user')
    ) {
      this.agentName.set('Engagement Booster Agent');
      this.agentRole.set('User Engagement Specialist');
    } else if (
      prompt.toLowerCase().includes('content') ||
      prompt.toLowerCase().includes('optimize')
    ) {
      this.agentName.set('Content Optimizer Agent');
      this.agentRole.set('Content Strategy Specialist');
    } else {
      this.agentName.set('Custom Agent');
      this.agentRole.set('AI Assistant');
    }
  }

  // Computed values
  protected isFormValid = computed(() => {
    return (
      (this.agentName()?.length || 0) > 0 &&
      (this.agentRole()?.length || 0) > 0 &&
      (this.agentDetails()?.length || 0) > 0 &&
      (this.practiceArea()?.length || 0) > 0 &&
      (this.goodAtTags()?.length || 0) > 0 &&
      (this.goal()?.length || 0) > 0 &&
      (this.backStory()?.length || 0) > 0 &&
      (this.description()?.length || 0) > 0 &&
      (this.expectedOutput()?.length || 0) > 0 &&
      (this.selectedModel()?.length || 0) > 0 &&
      (this.temperature()?.length || 0) > 0 &&
      (this.maxToken()?.length || 0) > 0 &&
      (this.topP()?.length || 0) > 0
    );
  });

  // Check if form has required fields filled (for initial autosave trigger)
  protected hasRequiredFields = computed(() => this.isFormValid());

  // Combined validation state for UI (required fields + no validation errors)
  protected isFormValidForUI = computed(() => {
    return this.hasRequiredFields() && !this.hasValidationErrors();
  });

  // Computed validation state
  protected hasValidationErrors = computed(() => {
    return !!(
      this.agentNameError() ||
      this.agentRoleError() ||
      this.agentDetailsError()
    );
  });

  // Widget data computed signals
  protected agentData = computed(() => ({
    agentName: this.agentName(),
    agentDetails: this.agentDetails(),
    practiceArea: this.practiceArea(),
    goodAtTags: this.goodAtTags(),
    agentImage: this.agentImage(),
  }));

  protected behaviourData = computed(() => ({
    goal: this.goal(),
    backStory: this.backStory(),
    description: this.description(),
    expectedOutput: this.expectedOutput(),
    agentRole: this.agentRole(),
  }));

  protected aiModelData = computed(() => ({
    selectedModel: this.selectedModel(),
    temperature: this.temperature(),
    maxToken: this.maxToken(),
    topP: this.topP(),
    maxIteration: this.maxIteration(),
    maxRpm: this.maxRpm(),
    maxExecutionTime: this.maxExecutionTime(),
  }));

  protected toolsData = computed(() => ({
    tools: this.tools(),
  }));

  protected guardrailsData = computed(() => ({
    guardrails: this.guardrails(),
  }));

  protected knowledgeBaseData = computed(() => ({
    knowledgeBases: this.knowledgeBases(),
  }));

  // Header event handlers
  protected onRunAgent(): void {
    const hasRequiredFields = this.hasRequiredFields();
    const hasValidationErrors = this.hasValidationErrors();

    if (hasRequiredFields && !hasValidationErrors) {
      // Only change status to CREATED when creating a new agent (not editing)
      if (!this.agentId) {
        this.agentStatus.set('CREATED');
      }
      this.createOrUpdateAgent();
    } else if (hasValidationErrors) {
      // Show error toast if there are validation errors
      this.toastService.error({
        message: 'Please fix validation errors before running the agent',
      });
    }
  }

  // ==================== DATA TRANSFORMATION ====================

  /**
   * Transform UI form data to Agent model
   */
  private transformToAgentModel(): Agent | Omit<Agent, 'id'> {
    const agentConfig: AgentConfig = {
      temperature: parseFloat(this.temperature()) || 0.3,
      topP: parseFloat(this.topP()) || 0.95,
      maxToken: this.maxToken() || '4000',
      maxIter: parseInt(this.maxIteration()) || null,
      maxRpm: parseInt(this.maxRpm()) || 5,
      maxExecutionTime: parseInt(this.maxExecutionTime()) || 5,
      allowDelegation: false,
      isSafeCodeExecution: false,
      allowCodeExecution: false,
    };

    // Get team ID from cookies
    const teamId = this.teamIdService.getTeamIdFromCookies() || 23; // Fallback to 23

    // Check if we're editing an existing agent
    const agentId = this.route.snapshot.params['id'];

    const baseAgentData = {
      agentDetails: this.agentDetails(),
      name: this.agentName(),
      role: this.agentRole(),
      goal: this.goal(),
      backstory: this.backStory(),
      description: this.description(),
      expectedOutput: this.expectedOutput(),
      tools: this.getToolIds(),
      kbIds: this.getKnowledgeBaseIds(),
      modelId: this.selectedModelId() || 2, // Use the actual model ID or default to 2
      agentConfigs: agentConfig,
      teamId: teamId,
      status: this.agentStatus() as
        | 'CREATED'
        | 'DRAFTED'
        | 'IN_REVIEW'
        | 'APPROVED'
        | 'REJECTED',
      tags: this.getTagIds(),
      practiceArea: this.getPracticeAreaId(),
    };

    // If editing an existing agent, include the ID
    if (agentId) {
      return {
        ...baseAgentData,
        id: parseInt(agentId),
      };
    }

    // For new agents, return without ID
    return baseAgentData;
  }

  /**
   * Get tool IDs from selected tools
   */
  private getToolIds(): number[] {
    return this.selectedToolsItems().map(item => parseInt(item.id));
  }

  /**
   * Get user tools array
   */
  private getUserTools(): Array<{ toolId: number }> {
    return this.getToolIds().map(toolId => ({ toolId }));
  }

  /**
   * Get knowledge base IDs
   */
  private getKnowledgeBaseIds(): number[] {
    return this.selectedKnowledgeBaseItems().map(item => parseInt(item.id));
  }

  /**
   * Get tag IDs from good at tags
   */
  private getTagIds(): number[] {
    return this.goodAtTagIds();
  }

  /**
   * Get practice area ID
   */
  private getPracticeAreaId(): number {
    return this.practiceAreaId() || 1; // Fallback to 1 if no ID is set
  }

  // ==================== API OPERATIONS ====================

  /**
   * Create or update agent
   */
  private createOrUpdateAgent(): void {
    const agentData = this.transformToAgentModel();

    // Check if we're editing an existing agent
    const agentId = this.route.snapshot.params['id'];

    if (agentId) {
      // Update existing agent
      this.agentStore
        .updateAgent(parseInt(agentId), agentData)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: updatedAgent => {
            // Navigate to playground with agent ID
            this.router.navigate(['/build/agent/playground'], {
              queryParams: { type: 'agent', id: agentId },
              state: {
                agentData: {
                  agent: {
                    name: this.agentName(),
                    role: this.agentRole(),
                    goal: this.goal(),
                    backstory: this.backStory(),
                    description: this.description(),
                    expectedOutput: this.expectedOutput(),
                  },
                  behaviour: {
                    description: this.description(),
                  },
                },
              },
            });
          },
          error: error => {
            console.error('Failed to update agent:', error);
          },
        });
    } else {
      // Create new agent
      this.agentStore
        .createAgent(agentData)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: newAgent => {
            // Use agentId from API response for navigation

            // Navigate to agent playground with state and type parameter
            this.router.navigate(['/build/agent/playground'], {
              queryParams: { type: 'agent', id: (newAgent as any).agentId },
              state: {
                agentData: {
                  agent: {
                    name: this.agentName(),
                    role: this.agentRole(),
                    goal: this.goal(),
                    backstory: this.backStory(),
                    description: this.description(),
                    expectedOutput: this.expectedOutput(),
                  },
                  behaviour: {
                    description: this.description(),
                  },
                },
              },
            });
          },
          error: error => {
            console.error('Failed to create agent:', error);
          },
        });
    }
  }

  /**
   * Load existing agent for editing
   */
  private loadAgentForEdit(agentId: number): void {
    // Show skeleton loading while fetching agent data
    this.isSkeletonLoading.set(true);

    this.agentStore
      .loadAgentById(agentId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: agent => {
          this.populateFormFromAgent(agent);
          // Hide skeleton loading after data is loaded
          this.isSkeletonLoading.set(false);
          this.isLoading.set(false);
        },
        error: error => {
          console.error('❌ Failed to load agent:', error);
          // Hide skeleton loading even on error
          this.isSkeletonLoading.set(false);
          this.isLoading.set(false);
        },
      });
  }

  /**
   * Populate form fields from loaded agent
   */
  private populateFormFromAgent(agent: any): void {
    // Handle the actual API response structure
    const agentDetail = agent.agentDetail || agent;

    this.agentName.set(agentDetail.name || '');
    this.agentDetails.set(agentDetail.agentDetails || '');
    this.goal.set(agentDetail.goal || '');
    this.backStory.set(agentDetail.backstory || '');
    this.description.set(agentDetail.description || '');
    this.expectedOutput.set(agentDetail.expectedOutput || '');
    this.agentRole.set(agentDetail.role || '');

    // Set status from API data (keep original status for edit mode)
    this.agentStatus.set(agentDetail.status || 'DRAFTED');

    // Set practice area and good at tags from the API response
    this.practiceArea.set(this.mapPracticeArea(agentDetail.practiceArea));
    this.practiceAreaId.set(agentDetail.practiceArea);

    // Map tags using dynamic data
    const tagNames = this.mapTags(agentDetail.tags || []);
    this.goodAtTags.set(tagNames);
    this.goodAtTagIds.set(agentDetail.tags || []);

    // Reset dirty state and autosave state after loading agent data
    this.isDirty.set(false);
    this.hasInitialSave.set(false);
    this.autosaveCount = 0;

    // Set AI model configuration from the actual API response
    const config = agentDetail.agentConfigs;
    if (config) {
      this.temperature.set(config.temperature?.toString() || '');
      this.maxToken.set(config.maxToken || '');
      this.topP.set(config.topP?.toString() || '');
      this.maxIteration.set(config.maxIter?.toString() || '');
      this.maxRpm.set(config.maxRpm?.toString() || '');
      this.maxExecutionTime.set(config.maxExecutionTime?.toString() || '');

      // Set model from modelRef array
      if (config.modelRef && config.modelRef.length > 0) {
        const modelRef = config.modelRef[0];
        this.selectedModel.set(modelRef.model || '');
        this.selectedModelId.set(modelRef.modelId);
      }
    }

    // Load tools, knowledge bases, and guardrails from their IDs
    this.loadToolsFromIds(agentDetail.tools || []);
    this.loadKnowledgeBasesFromIds(agentDetail.kbIds || []);
    this.loadGuardrailsFromIds(agentDetail.guardrailIds || []);
  }

  /**
   * Load tools from their IDs and populate the tools signal
   */
  private loadToolsFromIds(toolIds: number[]): void {
    if (toolIds.length === 0) {
      this.tools.set([]);
      return;
    }

    // Get team ID for filtering tools
    const teamId = this.teamIdService.getTeamIdFromCookies() || 23;

    this.toolService
      .getAll({ teamId, status: 'APPROVED' })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          const allTools = response?.userToolDetails || response || [];
          const selectedTools = allTools
            .filter((tool: any) => toolIds.includes(tool.id))
            .map((tool: any) => tool.name || tool.toolName);
          this.tools.set(selectedTools);
        },
        error: error => {
          console.error('❌ Error loading tools:', error);
          this.tools.set([]);
        },
      });
  }

  /**
   * Load knowledge bases from their IDs and populate the knowledgeBases signal
   */
  private loadKnowledgeBasesFromIds(kbIds: number[]): void {
    if (kbIds.length === 0) {
      this.knowledgeBases.set([]);
      return;
    }

    this.knowledgeBaseService
      .getAllKnowledgeBases()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (knowledgeBases: any[]) => {
          const selectedKBs = knowledgeBases
            .filter(kb => kbIds.includes(kb.id))
            .map(kb => kb.name);
          this.knowledgeBases.set(selectedKBs);
        },
        error: error => {
          console.error('❌ Error loading knowledge bases:', error);
          this.knowledgeBases.set([]);
        },
      });
  }

  /**
   * Load guardrails from their IDs and populate the guardrails signal
   */
  private loadGuardrailsFromIds(guardrailIds: number[]): void {
    if (guardrailIds.length === 0) {
      this.guardrails.set([]);
      return;
    }

    this.guardrailService
      .getAll()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (guardrails: any[]) => {
          const selectedGuardrails = guardrails
            .filter(guardrail => guardrailIds.includes(guardrail.id))
            .map(guardrail => guardrail.name);
          this.guardrails.set(selectedGuardrails);
        },
        error: error => {
          console.error('❌ Error loading guardrails:', error);
          this.guardrails.set([]);
        },
      });
  }

  // ==================== AUTOSAVE FUNCTIONALITY ====================

  /**
   * Trigger autosave when form data changes
   */
  private triggerAutosave(): void {
    if (this.executeMode) return; // Skip autosave in execute mode
    const agentId = this.route.snapshot.params['id'];
    const hasRequiredFields = this.hasRequiredFields();
    const hasValidationErrors = this.hasValidationErrors();

    if (hasRequiredFields && !hasValidationErrors) {
      // Clear any existing timeout
      clearTimeout(this.autosaveTimeout);

      // Calculate delay with exponential backoff
      const delay = this.calculateAutosaveDelay();

      this.autosaveTimeout = setTimeout(() => {
        const agentData = this.transformToAgentModel();

        if (agentId) {
          // Edit mode: update existing agent
          this.agentStore.onLocalChange(parseInt(agentId), agentData);
          // Update last saved time
          this.lastSavedTime.set(new Date());
        } else {
          // Create mode: create new agent (autosave only, no redirection)
          this.agentStore.createAgent(agentData).subscribe({
            next: createdAgent => {
              // API returns agentId field, normalize it to id
              const normalizedAgent: Agent = {
                ...createdAgent,
                id: (createdAgent as any).agentId ?? createdAgent.id,
              };

              // Update the URL to include the new agent ID without navigation
              // This updates the browser URL without triggering navigation
              const newUrl = `/console/build/agent/${normalizedAgent.id}`;
              window.history.replaceState(null, '', newUrl);

              // Update last saved time
              this.lastSavedTime.set(new Date());
            },
            error: error => {
              console.error('Failed to autosave agent:', error);
            },
          });
        }

        // Mark as having initial save and increment count
        this.hasInitialSave.set(true);
        this.autosaveCount++;
      }, delay);
    }
  }

  /**
   * Calculate autosave delay with exponential backoff
   */
  private calculateAutosaveDelay(): number {
    if (!this.hasInitialSave()) {
      // First save: immediate (after form is complete)
      return 500; // 0.5 seconds for initial save
    }

    // Subsequent saves: exponential backoff
    const delay = Math.min(
      this.baseDelay * Math.pow(2, this.autosaveCount - 1),
      this.maxDelay
    );

    return delay;
  }

  // ==================== TIME UPDATE FUNCTIONALITY ====================

  /**
   * Start interval to update saved time display every minute
   */
  private startTimeUpdateInterval(): void {
    // Update every minute (60000ms)
    this.timeUpdateInterval = setInterval(() => {
      // Trigger change detection by updating the trigger signal
      // This will cause the computed property in the header to recalculate
      this.timeUpdateTrigger.set(Date.now());
    }, 60000); // Update every minute
  }

  // ==================== FORM EVENT HANDLERS ====================

  // Agent widget event handlers
  protected onAgentNameChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.agentName.set(value);

    // Validate agent name
    const error = this.validateAgentName(value);
    this.agentNameError.set(error);

    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onAgentRoleChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.agentRole.set(value);

    // Validate agent role
    const error = this.validateAgentRole(value);
    this.agentRoleError.set(error);

    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onAgentDetailsChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.agentDetails.set(value);

    // Validate agent details
    const error = this.validateAgentDetails(value);
    this.agentDetailsError.set(error);

    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onPracticeAreaChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.practiceArea.set(value);

    // Find the ID for the selected practice area
    const practiceAreaData = this.dropdownStore
      .practiceAreaOptions()
      .find(option => option.value === value);
    this.practiceAreaId.set(practiceAreaData?.id || null);

    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onTagAdded(tag: string): void {
    if (this.executeMode) return; // Skip in execute mode
    if (tag.trim() && !this.goodAtTags().includes(tag.trim())) {
      this.goodAtTags.set([...this.goodAtTags(), tag.trim()]);

      // Find the ID for the added tag
      const tagData = this.dropdownStore
        .goodAtOptions()
        .find(option => option.value === tag.trim());
      if (tagData?.id) {
        this.goodAtTagIds.set([...this.goodAtTagIds(), tagData.id]);
      }

      this.isDirty.set(true);
      this.triggerAutosave();
    }
  }

  protected onTagRemoved(tag: string): void {
    if (this.executeMode) return; // Skip in execute mode
    const currentTags = this.goodAtTags();
    this.goodAtTags.set(currentTags.filter(t => t !== tag));

    // Remove the corresponding ID
    const tagData = this.dropdownStore
      .goodAtOptions()
      .find(option => option.value === tag);
    if (tagData?.id) {
      this.goodAtTagIds.set(
        this.goodAtTagIds().filter(id => id !== tagData.id)
      );
    }

    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onGoodAtSelectionChange(selectedValues: string[]): void {
    if (this.executeMode) return; // Skip in execute mode
    // Update the display names
    this.goodAtTags.set(selectedValues);

    // Update the corresponding IDs
    const selectedIds = selectedValues
      .map(value => {
        const option = this.dropdownStore
          .goodAtOptions()
          .find(opt => opt.value === value);
        return option?.id;
      })
      .filter((id): id is number => id !== undefined);

    this.goodAtTagIds.set(selectedIds);

    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onModelChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.selectedModel.set(value);

    // Find the ID for the selected model
    const modelData = this.dropdownStore
      .modelOptions()
      .find(option => option.value === value);
    this.selectedModelId.set(modelData?.id || null);

    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onImageEditClicked(): void {
    // This will be handled by the agent widget component
  }

  protected onImageChanged(base64Image: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.agentImage.set(base64Image);
    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onAgentInfoClicked(): void {
    // TODO: Implement agent info tooltip
  }

  protected onBehaviourInfoClicked(): void {
    // TODO: Implement behaviour info tooltip
  }

  protected onAIModelInfoClicked(): void {
    // TODO: Implement AI model info tooltip
  }

  protected onToolsInfoClicked(): void {
    // TODO: Implement tools info tooltip
  }

  protected onGuardrailsInfoClicked(): void {
    // TODO: Implement guardrails info tooltip
  }

  // Method to toggle guardrails disabled state (for demonstration)
  protected toggleGuardrailsDisabled(): void {
    this.guardrailsDisabled.set(!this.guardrailsDisabled());
  }

  protected onKnowledgeBaseInfoClicked(): void {
    // TODO: Implement knowledge base info tooltip
  }

  // Behaviour widget event handlers
  protected onGoalChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.goal.set(value);
    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onBackStoryChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.backStory.set(value);
    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onDescriptionChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.description.set(value);
    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onExpectedOutputChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.expectedOutput.set(value);
    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onImproveClicked(): void {
    // TODO: Implement improve functionality
  }

  // AI Model widget event handlers

  protected onTemperatureChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.temperature.set(value);
    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onMaxTokenChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.maxToken.set(value);
    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onTopPChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.topP.set(value);
    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onMaxIterationChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.maxIteration.set(value);
    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onMaxRpmChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.maxRpm.set(value);
    this.isDirty.set(true);
    this.triggerAutosave();
  }

  protected onMaxExecutionTimeChange(value: string): void {
    if (this.executeMode) return; // Skip in execute mode
    this.maxExecutionTime.set(value);
    this.isDirty.set(true);
    this.triggerAutosave();
  }

  // Tools widget event handlers
  protected onToolsAddClicked(): void {
    if (this.executeMode) return; // Skip in execute mode
    this.currentModalType = 'tools';

    const toolsConfig: LibraryModalConfig = {
      title: 'Tools Library',
      searchPlaceholder: 'Search tools...',
      createButtonText: 'Create New Tool',
      itemType: 'tools',
      gridColumns: 3,
      showFilters: false,
      showUsageCount: true,
      showCreateCard: true,
      modalSize: 'lg',
      maxSelections: 5,
      allowMultiSelect: true,
    };

    // Get tools items from the API data
    const toolsItems: LibraryItem[] = this.availableTools();

    this.dialogService.openModal(
      LibraryModalContentComponent,
      {
        width: '90vw',
        maxHeight: '100vh',
      },
      {
        config: toolsConfig,
        initialItems: toolsItems,
        initialSelectedItems: this.selectedToolsItems(),
        initialSearchQuery: '',
        initialIsFilterOpen: false,
        initialCurrentFilters: {},
      }
    );
  }

  protected onToolRemoved(tool: string): void {
    if (this.executeMode) return; // Skip in execute mode
    // Remove from string array
    this.tools.set(this.tools().filter(t => t !== tool));

    // Also remove from selected items array
    this.selectedToolsItems.set(
      this.selectedToolsItems().filter(item => item.title !== tool)
    );
  }

  // Guardrails widget event handlers
  protected onGuardrailsAddClicked(): void {
    if (this.executeMode) return; // Skip in execute mode
    this.currentModalType = 'guardrails';

    const guardrailsConfig: LibraryModalConfig = {
      title: 'Guardrails Library',
      searchPlaceholder: 'Search guardrails...',
      createButtonText: 'Create New Guardrail',
      itemType: 'guardrails',
      gridColumns: 3,
      showFilters: false,
      showUsageCount: true,
      showCreateCard: true,
      modalSize: 'lg',
      maxSelections: 3,
      allowMultiSelect: true,
    };

    // Sample guardrails items
    const guardrailsItems: LibraryItem[] = [
      {
        id: 'g1',
        title: 'Content Safety Filter',
        description:
          'Prevent generation of harmful, offensive, or inappropriate content',
        category: 'Safety',
        tags: ['Safety', 'Content', 'Filter'],
        isActive: true,
        usageCount: 30,
        icon: 'shield',
        iconColor: '#000',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'g2',
        title: 'Data Privacy Guard',
        description:
          'Ensure no personal or sensitive information is exposed in responses',
        category: 'Privacy',
        tags: ['Privacy', 'Data', 'PII'],
        isActive: true,
        usageCount: 28,
        icon: 'shield',
        iconColor: '#000',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'g3',
        title: 'Fact Checker',
        description:
          'Verify factual accuracy and flag potentially incorrect information',
        category: 'Accuracy',
        tags: ['Facts', 'Accuracy', 'Verification'],
        isActive: true,
        usageCount: 24,
        icon: 'shield',
        iconColor: '#000',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'g4',
        title: 'Bias Detection',
        description:
          'Detect and mitigate bias in AI responses and recommendations',
        category: 'Fairness',
        tags: ['Bias', 'Fairness', 'Detection'],
        isActive: true,
        usageCount: 16,
        icon: 'shield',
        iconColor: '#000',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'g5',
        title: 'Rate Limiter',
        description: 'Control request frequency and prevent API abuse',
        category: 'Performance',
        tags: ['Rate Limit', 'Performance', 'API'],
        isActive: true,
        usageCount: 21,
        icon: 'shield',
        iconColor: '#000',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'g6',
        title: 'Compliance Monitor',
        description:
          'Ensure responses comply with industry regulations and standards',
        category: 'Compliance',
        tags: ['Compliance', 'Regulations', 'Standards'],
        isActive: true,
        usageCount: 18,
        icon: 'shield',
        iconColor: '#000',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'g7',
        title: 'Toxicity Filter',
        description: 'Detect and block toxic, hateful, or abusive language',
        category: 'Moderation',
        tags: ['Toxicity', 'Moderation', 'Language'],
        isActive: true,
        usageCount: 26,
        icon: 'shield',
        iconColor: '#000',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'g8',
        title: 'Confidentiality Guard',
        description: 'Prevent disclosure of confidential business information',
        category: 'Security',
        tags: ['Confidential', 'Business', 'Security'],
        isActive: true,
        usageCount: 23,
        icon: 'shield',
        iconColor: '#000',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    this.dialogService.openModal(
      LibraryModalContentComponent,
      {
        width: '90vw',
        maxHeight: '100vh',
      },
      {
        config: guardrailsConfig,
        initialItems: guardrailsItems,
        initialSelectedItems: this.selectedGuardrailsItems(),
        initialSearchQuery: '',
        initialIsFilterOpen: false,
        initialCurrentFilters: {},
      }
    );
  }

  protected onGuardrailRemoved(guardrail: string): void {
    if (this.executeMode) return; // Skip in execute mode
    // Remove from string array
    this.guardrails.set(this.guardrails().filter(g => g !== guardrail));

    // Also remove from selected items array
    this.selectedGuardrailsItems.set(
      this.selectedGuardrailsItems().filter(item => item.title !== guardrail)
    );
  }

  // Knowledge Base widget event handlers
  protected onKnowledgeBaseAddClicked(): void {
    if (this.executeMode) return; // Skip in execute mode
    this.currentModalType = 'knowledge';

    // Knowledge base specific configuration
    const knowledgeBaseConfig = {
      title: 'Knowledge Base Library',
      searchPlaceholder: 'Search knowledge base items...',
      createButtonText: 'Create',
      itemType: 'knowledge' as const,
      gridColumns: 3,
      showFilters: true,
      showUsageCount: true,
      showCreateCard: true,
      modalSize: 'lg' as const,
      maxSelections: 10,
      allowMultiSelect: true,
      footerConfig: {
        showCancelButton: true,
        showApplyButton: true,
        cancelButtonText: 'Cancel',
        applyButtonText: 'Add',
        cancelButtonVariant: 'default',
        applyButtonVariant: 'primary',
      },
      contentConfig: {
        padding: '1rem',
        maxHeight: '100vh',
        overflow: 'auto' as const,
        backgroundColor: '#fff',
        borderRadius: '0.5rem',
        showScrollbar: true,
      },
      gridConfig: {
        columns: 3,
        gap: '1.5rem',
      },
      itemCardConfig: {
        height: '172px',
      },
      createCardConfig: {
        height: '172px',
      },
      headerConfig: {
        title: 'Knowledge Base Library',
        showCloseButton: true,
        showSearchBar: true,
        showFilterButton: true,
        searchPlaceholder: 'Search knowledge base items...',
      },
    };
    const knowledgeBaseItems: LibraryItem[] =
      this.knowledgeBaseStore.libraryItems();

    // // Sample knowledge base items (you can replace this with actual data from a service)
    // const knowledgeBaseItems: LibraryItem[] = [
    //   {
    //     id: '1',
    //     title: 'Company Test Policies',
    //     description:
    //       'Internal company policies and procedures including HR guidelines, legal compliance, and operational standards',
    //     category: 'Policies',
    //     tags: ['HR', 'Legal', 'Internal'],
    //     isActive: true,
    //     usageCount: 15,
    //     icon: 'book-open-text',
    //     iconColor: '#000',
    //     createdAt: new Date(),
    //     updatedAt: new Date(),
    //   },
    //   {
    //     id: '2',
    //     title: 'Technical Documentation',
    //     description:
    //       'Technical guides, API documentation, and development resources for engineering teams',
    //     category: 'Technical',
    //     tags: ['API', 'Development', 'Guide'],
    //     isActive: true,
    //     usageCount: 8,
    //     icon: 'book-open-text',
    //     iconColor: '#000',
    //     createdAt: new Date(),
    //     updatedAt: new Date(),
    //   },
    //   {
    //     id: '3',
    //     title: 'Product Knowledge',
    //     description:
    //       'Product features, specifications, and user guides for customer support and sales teams',
    //     category: 'Product',
    //     tags: ['Features', 'Specs', 'Product'],
    //     isActive: true,
    //     usageCount: 12,
    //     icon: 'book-open-text',
    //     iconColor: '#000',
    //     createdAt: new Date(),
    //     updatedAt: new Date(),
    //   },
    //   {
    //     id: '4',
    //     title: 'Customer Support',
    //     description:
    //       'FAQ database, troubleshooting guides, and common customer issues with solutions',
    //     category: 'Support',
    //     tags: ['FAQ', 'Troubleshooting', 'Customer'],
    //     isActive: true,
    //     usageCount: 22,
    //     icon: 'book-open-text',
    //     iconColor: '#000',
    //     createdAt: new Date(),
    //     updatedAt: new Date(),
    //   },
    //   {
    //     id: '5',
    //     title: 'Sales Training',
    //     description:
    //       'Sales methodologies, objection handling, and product positioning strategies',
    //     category: 'Sales',
    //     tags: ['Training', 'Methodology', 'Strategy'],
    //     isActive: true,
    //     usageCount: 18,
    //     icon: 'book-open-text',
    //     iconColor: '#000',
    //     createdAt: new Date(),
    //     updatedAt: new Date(),
    //   },
    //   {
    //     id: '6',
    //     title: 'Marketing Assets',
    //     description:
    //       'Brand guidelines, marketing materials, and campaign resources for marketing teams',
    //     category: 'Marketing',
    //     tags: ['Brand', 'Campaigns', 'Assets'],
    //     isActive: true,
    //     usageCount: 7,
    //     icon: 'book-open-text',
    //     iconColor: '#000',
    //     createdAt: new Date(),
    //     updatedAt: new Date(),
    //   },
    //   {
    //     id: '7',
    //     title: 'Finance Procedures',
    //     description:
    //       'Financial policies, expense guidelines, and accounting procedures for finance teams',
    //     category: 'Finance',
    //     tags: ['Accounting', 'Expenses', 'Policies'],
    //     isActive: true,
    //     usageCount: 9,
    //     icon: 'book-open-text',
    //     iconColor: '#000',
    //     createdAt: new Date(),
    //     updatedAt: new Date(),
    //   },
    //   {
    //     id: '8',
    //     title: 'Security Guidelines',
    //     description:
    //       'Security protocols, data protection policies, and compliance requirements',
    //     category: 'Security',
    //     tags: ['Security', 'Compliance', 'Data Protection'],
    //     isActive: true,
    //     usageCount: 14,
    //     icon: 'shield',
    //     iconColor: '#000',
    //     createdAt: new Date(),
    //     updatedAt: new Date(),
    //   },
    //   {
    //     id: '9',
    //     title: 'Project Templates',
    //     description:
    //       'Project management templates, workflows, and best practices for project teams',
    //     category: 'Project Management',
    //     tags: ['Templates', 'Workflows', 'Best Practices'],
    //     isActive: true,
    //     usageCount: 11,
    //     icon: 'book-open-text',
    //     iconColor: '#000',
    //     createdAt: new Date(),
    //     updatedAt: new Date(),
    //   },
    //   {
    //     id: '10',
    //     title: 'Training Materials',
    //     description:
    //       'Employee onboarding resources, training modules, and skill development content',
    //     category: 'Training',
    //     tags: ['Onboarding', 'Skills', 'Development'],
    //     isActive: true,
    //     usageCount: 16,
    //     icon: 'book-open-text',
    //     iconColor: '#000',
    //     createdAt: new Date(),
    //     updatedAt: new Date(),
    //   },
    // ];

    this.dialogService.openModal(
      LibraryModalContentComponent,
      {
        width: '90vw',
        maxHeight: '100vh',
      },
      {
        config: knowledgeBaseConfig,
        initialItems: knowledgeBaseItems,
        initialSelectedItems: this.selectedKnowledgeBaseItems(),
        initialSearchQuery: '',
        initialIsFilterOpen: false,
        initialCurrentFilters: {},
      }
    );
  }

  protected onKnowledgeBaseRemoved(knowledgeBase: string): void {
    if (this.executeMode) return; // Skip in execute mode
    // Remove from string array
    this.knowledgeBases.set(
      this.knowledgeBases().filter(kb => kb !== knowledgeBase)
    );

    // Also remove from selected items array
    this.selectedKnowledgeBaseItems.set(
      this.selectedKnowledgeBaseItems().filter(
        item => item.title !== knowledgeBase
      )
    );
  }

  // ==================== MODAL EVENT HANDLERS ====================

  private handleCreateAction(
    modalType: 'knowledge' | 'tools' | 'guardrails'
  ): void {
    // Close the modal first
    this.dialogService.close();

    // Then navigate to the appropriate creation page
    switch (modalType) {
      case 'knowledge':
        this.router.navigate(['/build/knowledge/create']);
        break;
      case 'tools':
        this.router.navigate(['/build/tools']);
        break;
      case 'guardrails':
        // TODO: Add guardrails creation route when available
        break;
      default:
        break;
    }
  }

  // ==================== VALIDATION METHODS ====================

  private validateAgentName(value: string): string {
    if (!value || value.trim().length === 0) {
      return 'Agent name is required';
    }

    const trimmedValue = value.trim();

    // Check for special characters, underscores, and emojis
    // Only allow letters, numbers, and spaces
    const allowedCharsRegex = /^[A-Za-z0-9\s]+$/;
    if (!allowedCharsRegex.test(trimmedValue)) {
      return 'Agent name can only contain letters, numbers, and spaces (no special characters, underscores, or emojis)';
    }

    // Check if it's Title Case (first letter of each word is uppercase)
    // Split by spaces and check each word starts with uppercase
    const words = trimmedValue.split(/\s+/);
    const hasInvalidTitleCase = words.some(word => {
      // Skip empty words
      if (!word) return false;
      // Check if word starts with uppercase letter
      return !/^[A-Z]/.test(word);
    });

    if (hasInvalidTitleCase) {
      return 'Agent name must be in Title Case (e.g., "Resume Classifier")';
    }

    // Check for version suffixes (v1, v2, _v1, _v2, etc.)
    const versionSuffixRegex = /[vV]\d+$|_[vV]\d+$/;
    if (versionSuffixRegex.test(trimmedValue)) {
      return 'Agent name cannot contain version suffixes (e.g., v1, v2)';
    }

    // Check for acronyms (all uppercase words)
    const hasAcronym = words.some(word => /^[A-Z]{2,}$/.test(word));
    if (hasAcronym) {
      return 'Agent name cannot contain acronyms (e.g., API, JSON, XML)';
    }

    return '';
  }

  private validateAgentRole(value: string): string {
    // No validation required for now
    return '';
  }

  private validateAgentDetails(value: string): string {
    if (!value || value.trim().length === 0) {
      return 'Agent details are required';
    }

    const trimmedValue = value.trim();

    // Check character limit (≤ 100 characters)
    if (trimmedValue.length > 200) {
      return 'Agent details must be 200 characters or less';
    }

    // Check for filler phrases
    const fillerPhrases = [
      'this tool helps',
      'this agent helps',
      'this is a',
      'this is an',
      'this is the',
      'this tool is',
      'this agent is',
    ];

    const lowerValue = trimmedValue.toLowerCase();
    const hasFillerPhrase = fillerPhrases.some(phrase =>
      lowerValue.startsWith(phrase)
    );

    if (hasFillerPhrase) {
      return 'Avoid filler phrases like "This tool helps..." or "This is a..."';
    }

    return '';
  }
}
