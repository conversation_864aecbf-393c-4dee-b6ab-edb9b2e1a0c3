.agent-builder-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  // border-bottom: 1px solid #e5e7eb;
  height: 2.5rem;
  box-sizing: border-box;
  // background: rgba(255, 255, 255, 0.9);
  // border-top-right-radius: 0.75rem;
  // border-top-left-radius: 0.75rem;
}

.saved-time {
  color: var(--color-text-secondary, #6b7280);
  font-family: var(--Global-v1-Family-Body, Inter);
  font-size: var(--Global-v1-Size-12, 0.75rem);
  font-style: normal;
  font-weight: 400;
  line-height: var(--Global-v1-Line-height-16, 1rem);
  margin: 0;
  justify-self: start;
}

.page-title {
  color: var(--Colors-Text-primary, #3b3f46);
  font-family: var(--Global-v1-Family-Body, Inter);
  font-size: var(--Global-v1-Size-16, 1rem);
  font-style: normal;
  font-weight: 500;
  line-height: var(--Global-v1-Line-height-20, 1.25rem);
  margin: 0;
  display: flex;
  align-items: center;
  justify-self: center;
}

// Responsive design
@media (max-width: 768px) {
  .agent-builder-header {
    padding: 0.5rem 16px 0.5rem 16px;
  }
}
