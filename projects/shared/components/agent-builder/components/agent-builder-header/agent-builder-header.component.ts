import { AavaButtonComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
} from '@angular/core';

@Component({
  selector: 'app-agent-builder-header',
  standalone: true,
  imports: [CommonModule, AavaButtonComponent],
  templateUrl: './agent-builder-header.component.html',
  styleUrls: ['./agent-builder-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgentBuilderHeaderComponent {
  // Signal inputs
  readonly isFormValid = input.required<boolean>();
  readonly lastSavedTime = input<Date | null>(null);
  readonly timeUpdateTrigger = input<number>(0);

  // Signal outputs
  readonly runClicked = output<void>();

  // Computed values
  protected runButtonAriaLabel = computed(() =>
    this.isFormValid() ? 'Run agent' : 'Complete required fields to run agent'
  );

  protected savedTimeText = computed(() => {
    // Use the trigger to ensure this computed property recalculates
    this.timeUpdateTrigger();

    const lastSaved = this.lastSavedTime();
    if (!lastSaved) {
      return 'Not saved yet';
    }

    const now = new Date();
    const diffInMs = now.getTime() - lastSaved.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Saved just now';
    } else if (diffInMinutes === 1) {
      return 'Saved 1 min ago';
    } else if (diffInMinutes < 60) {
      return `Saved ${diffInMinutes} mins ago`;
    } else {
      const diffInHours = Math.floor(diffInMinutes / 60);
      if (diffInHours === 1) {
        return 'Saved 1 hour ago';
      } else {
        return `Saved ${diffInHours} hours ago`;
      }
    }
  });

  protected onRunClick(): void {
    this.runClicked.emit();
  }
}
