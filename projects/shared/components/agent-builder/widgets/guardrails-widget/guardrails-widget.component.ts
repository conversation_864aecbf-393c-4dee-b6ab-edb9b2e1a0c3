import { AavaButtonComponent, AavaTagComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
} from '@angular/core';

import { WidgetCardComponent, WidgetCardData } from '../widget-card';

export interface GuardrailsWidgetData {
  readonly guardrails: string[];
}

@Component({
  selector: 'app-guardrails-widget',
  standalone: true,
  imports: [
    CommonModule,
    WidgetCardComponent,
    AavaButtonComponent,
    AavaTagComponent,
  ],
  templateUrl: './guardrails-widget.component.html',
  styleUrls: ['./guardrails-widget.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GuardrailsWidgetComponent {
  // Signal inputs
  readonly data = input.required<GuardrailsWidgetData>();
  readonly disabled = input<boolean>(false);
  readonly executeMode = input<boolean>(false);
  // Signal outputs
  readonly addClicked = output<void>();
  readonly guardrailRemoved = output<string>();
  readonly infoClicked = output<void>();

  // Computed values
  readonly widgetCardData = computed<WidgetCardData>(() => ({
    title: 'Guardrails',
    titleColor: 'rgba(2, 134, 151, 1)',
    icon: 'shield',
    iconColor: 'rgba(2, 134, 151, 1)',
    iconBackgroundColor: 'rgba(230, 248, 251, 1)',
    isRequired: false,
    isDisabled: this.disabled(),
    executeMode: this.executeMode(),
    infoTooltip:
      'Set up safety measures and constraints to ensure responsible AI behavior',
  }));

  protected onAddClick(): void {
    this.addClicked.emit();
  }

  protected onRemoveGuardrail(guardrail: string): void {
    this.guardrailRemoved.emit(guardrail);
  }

  protected onInfoClick(): void {
    this.infoClicked.emit();
  }
}
