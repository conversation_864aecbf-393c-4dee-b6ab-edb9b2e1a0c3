<app-widget-card [data]="widgetCardData()" (infoClicked)="onInfoClick()">
  <!-- Guardrails List -->
  @if (data().guardrails.length > 0) {
    <div class="guardrails-tags">
      @for (guardrail of data().guardrails; track guardrail) {
        <aava-tag
          [label]="guardrail"
          color="default"
          size="md"
          [pill]="false"
          [removable]="!disabled()"
          (removed)="onRemoveGuardrail(guardrail)"
        ></aava-tag>
      }
    </div>
  }

  <div class="add-button">
    <aava-button
      label="Add"
      (userClick)="onAddClick()"
      variant="primary"
      size="sm"
      [outlined]="true"
      width="100%"
      [disabled]="disabled()"
    ></aava-button>
  </div>
</app-widget-card>
