import { AavaTextareaComponent, AavaButtonComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
} from '@angular/core';
import { FormsModule } from '@angular/forms';

import { WidgetCardComponent, WidgetCardData } from '../widget-card';

export interface BehaviourWidgetData {
  readonly goal: string;
  readonly backStory: string;
  readonly description: string;
  readonly expectedOutput: string;
  readonly agentRole: string;
}

@Component({
  selector: 'app-behaviour-widget',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    WidgetCardComponent,
    AavaTextareaComponent,
    AavaButtonComponent,
  ],
  templateUrl: './behaviour-widget.component.html',
  styleUrls: ['./behaviour-widget.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BehaviourWidgetComponent {
  // Signal inputs
  readonly data = input.required<BehaviourWidgetData>();
  readonly disabled = input<boolean>(false);
  readonly executeMode = input<boolean>(false);

  // Signal outputs
  readonly goalChanged = output<string>();
  readonly backStoryChanged = output<string>();
  readonly descriptionChanged = output<string>();
  readonly expectedOutputChanged = output<string>();
  readonly agentRoleChanged = output<string>();
  readonly improveClicked = output<void>();
  readonly infoClicked = output<void>();

  // Computed values
  readonly widgetCardData = computed<WidgetCardData>(() => ({
    title: 'Behaviour',
    titleColor: 'rgba(156, 39, 176, 1)',
    icon: 'user',
    iconColor: 'rgba(156, 39, 176, 1)',
    iconBackgroundColor: 'rgba(245, 233, 247, 1)',
    isRequired: true,
    isDisabled: this.disabled(),
    executeMode: this.executeMode(),
    infoTooltip: "Define your agent's behavior, goals, and expected outputs",
  }));

  protected onGoalChange(value: string): void {
    this.goalChanged.emit(value);
  }

  protected onBackStoryChange(value: string): void {
    this.backStoryChanged.emit(value);
  }

  protected onDescriptionChange(value: string): void {
    this.descriptionChanged.emit(value);
  }

  protected onAgentRoleChange(value: string): void {
    this.agentRoleChanged.emit(value);
  }
  protected onExpectedOutputChange(value: string): void {
    this.expectedOutputChanged.emit(value);
  }

  protected onImproveClick(): void {
    this.improveClicked.emit();
  }

  protected onInfoClick(): void {
    this.infoClicked.emit();
  }
}
