<app-widget-card [data]="widgetCardData()" (infoClicked)="onInfoClick()">
  <!-- Form Fields -->
  <div class="form-fields">
    <div class="form-field">
      <aava-textbox
        label="Agent Role"
        placeholder="What should it do?"
        [ngModel]="data().agentRole"
        [disabled]="disabled()"
        (ngModelChange)="onAgentRoleChange($event)"
        size="sm"
        variant="default"
        [required]="true"
      ></aava-textbox>
    </div>
    <div class="form-field">
      <aava-textarea
        label="Goal"
        placeholder="Enter the goal you wish to achieve"
        [ngModel]="data().goal"
        [disabled]="disabled()"
        (ngModelChange)="onGoalChange($event)"
        [rows]="3"
        size="sm"
        variant="default"
        [required]="true"
      ></aava-textarea>
    </div>

    <div class="form-field">
      <aava-textarea
        label="Back Story"
        placeholder="Enter the back story"
        [ngModel]="data().backStory"
        [disabled]="disabled()"
        (ngModelChange)="onBackStoryChange($event)"
        [rows]="7"
        size="sm"
        variant="default"
        [required]="true"
      ></aava-textarea>
    </div>

    <div class="form-field">
      <aava-textarea
        label="Description"
        placeholder="Enter the description"
        [ngModel]="data().description"
        [disabled]="disabled()"
        (ngModelChange)="onDescriptionChange($event)"
        [rows]="7"
        size="sm"
        variant="default"
        [required]="true"
      ></aava-textarea>
    </div>

    <div class="form-field">
      <aava-textarea
        label="Expected Output"
        placeholder="Enter the expected output"
        [ngModel]="data().expectedOutput"
        [disabled]="disabled()"
        (ngModelChange)="onExpectedOutputChange($event)"
        [rows]="7"
        size="sm"
        variant="default"
        [required]="true"
      ></aava-textarea>
    </div>

    <aava-button
      label="Improve"
      [disabled]="true"
      (userClick)="onImproveClick()"
      variant="primary"
      size="sm"
      [outlined]="true"
      width="100%"
    ></aava-button>
  </div>
</app-widget-card>
