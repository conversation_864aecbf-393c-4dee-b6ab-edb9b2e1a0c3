<app-widget-card [data]="widgetCardData()" (infoClicked)="onInfoClick()">
  <!-- Tools List -->
  @if (data().tools.length > 0) {
    <div class="tools-tags">
      @for (tool of data().tools; track tool) {
        <aava-tag
          [label]="tool"
          color="default"
          size="md"
          [pill]="false"
          [removable]="!disabled()"
          (removed)="onRemoveTool(tool)"
        ></aava-tag>
      }
    </div>
  }

  <div class="add-button">
    <aava-button
      label="Add"
      (userClick)="onAddClick()"
      variant="primary"
      size="sm"
      [outlined]="true"
      width="100%"
      [disabled]="disabled()"
    ></aava-button>
  </div>
</app-widget-card>
