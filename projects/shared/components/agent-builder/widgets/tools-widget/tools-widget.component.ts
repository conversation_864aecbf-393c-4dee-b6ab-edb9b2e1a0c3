import { AavaButtonComponent, AavaTagComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
} from '@angular/core';

import { WidgetCardComponent, WidgetCardData } from '../widget-card';

export interface ToolsWidgetData {
  readonly tools: string[];
}

@Component({
  selector: 'app-tools-widget',
  standalone: true,
  imports: [
    CommonModule,
    WidgetCardComponent,
    AavaButtonComponent,
    AavaTagComponent,
  ],
  templateUrl: './tools-widget.component.html',
  styleUrls: ['./tools-widget.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToolsWidgetComponent {
  // Signal inputs
  readonly data = input.required<ToolsWidgetData>();
  readonly disabled = input<boolean>(false);
  readonly executeMode = input<boolean>(false);
  // Signal outputs
  readonly addClicked = output<void>();
  readonly toolRemoved = output<string>();
  readonly infoClicked = output<void>();

  // Computed values
  readonly widgetCardData = computed<WidgetCardData>(() => ({
    title: 'Tools',
    titleColor: 'rgba(156, 39, 176, 1)',
    icon: 'wrench',
    iconColor: 'rgba(156, 39, 176, 1)',
    iconBackgroundColor: 'rgba(245, 233, 247, 1)',
    isRequired: false,
    isDisabled: this.disabled(),
    executeMode: this.executeMode(),
    infoTooltip:
      "Add tools and integrations to extend your agent's capabilities",
  }));

  protected onAddClick(): void {
    this.addClicked.emit();
  }

  protected onRemoveTool(tool: string): void {
    this.toolRemoved.emit(tool);
  }

  protected onInfoClick(): void {
    this.infoClicked.emit();
  }
}
