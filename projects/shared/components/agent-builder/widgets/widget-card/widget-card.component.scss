.widget-card {
  --border-color: var(--Brand-Neutral-n-100, #d1d3d8);
  --text-color: var(--Colors-Text-primary, #3b3f46);
  --interactive-color: var(--Brand-Primary-500, #007bff);
  --disabled-color: var(--Brand-Neutral-n-200, #e5e7eb);
  --required-color: var(--Brand-Error-500, #dc3545);

  background: none;
  color: var(--text-color);
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;

  // &.interactive:hover {
  //   border-color: var(--interactive-color);
  //   box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
  // }

  // &.disabled {
  //   opacity: 0.6;
  //   cursor: not-allowed;
  //   background: var(--disabled-color);
  // }

  // Section Header
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding-bottom: 0.75rem;

    .header-left {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      ::ng-deep #header-icon {
        svg {
          stroke-width: 1.5;
        }
      }

      .section-icon {
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--Brand-Neutral-n-50, #f8f9fa);
        border-radius: 0.375rem;
        border: 1px solid var(--border-color);
      }

      .section-icon-box {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.625rem;
        border-radius: 0.5rem;
        width: fit-content;
        height: fit-content;
      }

      .section-title {
        font-family: var(--Global-v1-Family-Body, Inter);
        font-size: var(--Global-v1-Size-16, 1rem);
        font-weight: 600;
        line-height: var(--Global-v1-Line-height-20, 1.25rem);
        color: var(--text-color);
        margin: 0;
      }

      .info-icon {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
        color: var(--Brand-Neutral-n-400, #9ca3af);
        padding: 0.25rem;
        border-radius: 0.25rem;
        transition: color 0.2s ease;

        &:hover:not(:disabled) {
          color: var(--interactive-color);
        }

        &:disabled {
          cursor: not-allowed;
          opacity: 0.5;
        }
      }
    }

    .required-badge {
      font-size: 10px;
      font-weight: 500;
    }

    .optional-badge {
      font-size: 10px;
      font-weight: 500;
    }
  }

  // Widget Content
  // Content projection area - styles will be inherited from projected content

  // Responsive design
  @media (max-width: 768px) {
    padding: 1rem;

    .section-header {
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;

      .header-left {
        gap: 0.5rem;

        .section-icon {
          font-size: 1rem;
          width: 1.75rem;
          height: 1.75rem;
        }

        .section-icon-box {
          padding: 0.5rem;
        }

        .section-title {
          font-size: 0.875rem;
        }
      }

      .required-badge {
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
      }

      .optional-badge {
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
      }
    }
  }
}
