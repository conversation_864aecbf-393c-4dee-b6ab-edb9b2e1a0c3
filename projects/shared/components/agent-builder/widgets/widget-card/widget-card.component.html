<aava-default-card>
  <article
    class="widget-card"
    [class.disabled]="data().isDisabled"
    [class.interactive]="isInteractive()"
    [attr.aria-label]="ariaLabel()"
    [attr.aria-disabled]="data().isDisabled"
    role="article"
    tabindex="0"
    (click)="onCardClick()"
    (keydown)="onKeyDown($event)"
    [ngStyle]="safeStyles()"
  >
    <!-- Section Header -->
    <header class="section-header">
      <div class="header-left">
        <!-- <div class="section-icon" [attr.aria-hidden]="true">
          {{ data().icon }}
        </div> -->
        <div
          class="section-icon-box"
          [style.background-color]="data().iconBackgroundColor"
        >
          <aava-icon
            id="header-icon"
            [iconSize]="16"
            [iconName]="data().icon"
            [iconColor]="data().iconColor"
          ></aava-icon>
        </div>
        <h2 class="section-title" [style.color]="data().titleColor">
          {{ data().title }}
        </h2>
        @if (data().infoTooltip && !data().executeMode) {
          <aava-icon
            iconName="info"
            iconColor="rgba(59, 63, 70, 1)"
            (click)="onInfoClick($event)"
            [attr.aria-label]="'More information about ' + data().title"
            [attr.title]="data().infoTooltip"
            [iconSize]="16"
          ></aava-icon>
          <!-- <button
            type="button"
            class="info-icon"
            [disabled]="data().isDisabled"
            (click)="onInfoClick($event)"
            [attr.aria-label]="'More information about ' + data().title"
            [attr.title]="data().infoTooltip"
          >
            ℹ️
          </button> -->
        }
      </div>
      @if (!data().executeMode) {
        @if (data().isRequired) {
          <label class="required-badge">Required</label>
          <!-- <aava-tag size="sm" label="Required" variant="outlined"></aava-tag> -->
        } @else {
          <label class="optional-badge">Optional</label>
          <!-- <aava-tag size="sm" label="Optional" variant="outlined"></aava-tag> -->
        }
      }
    </header>

    <!-- Content Projection -->
    <div class="widget-content">
      <ng-content></ng-content>
    </div>
  </article>
</aava-default-card>
