import {
  AavaDefaultCardComponent,
  AavaIconComponent,
  AavaTagComponent,
} from '@aava/play-core';
import { CommonModule, NgStyle } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
} from '@angular/core';

export interface WidgetCardData {
  readonly title: string;
  readonly titleColor: string;
  readonly icon: string;
  readonly iconColor: string;
  readonly iconBackgroundColor: string;
  readonly isRequired: boolean;
  readonly isDisabled?: boolean;
  readonly infoTooltip?: string;
  readonly executeMode?: boolean;
}

@Component({
  selector: 'app-widget-card',
  standalone: true,
  imports: [CommonModule, NgStyle, AavaDefaultCardComponent, AavaIconComponent],
  templateUrl: './widget-card.component.html',
  styleUrls: ['./widget-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WidgetCardComponent {
  // Signal inputs (Angular 17+)
  readonly data = input.required<WidgetCardData>();
  readonly customStyles = input<Record<string, string>>({});

  // Signal outputs (Angular 17+)
  readonly infoClicked = output<void>();
  readonly cardClicked = output<void>();

  // Computed values
  readonly isInteractive = computed(() => !this.data().isDisabled);
  readonly safeStyles = computed(() =>
    this.sanitizeStyles(this.customStyles())
  );
  readonly ariaLabel = computed(
    () =>
      `${this.data().title} widget card${this.data().isRequired ? ' (Required)' : ''}`
  );

  protected onInfoClick(event: Event): void {
    event.stopPropagation();
    if (this.isInteractive()) {
      this.infoClicked.emit();
    }
  }

  protected onCardClick(): void {
    if (this.isInteractive()) {
      this.cardClicked.emit();
    }
  }

  protected onKeyDown(event: KeyboardEvent): void {
    // Only handle keyboard events if they're not coming from input elements
    const target = event.target as HTMLElement;
    const isInputElement =
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.tagName === 'SELECT' ||
      target.contentEditable === 'true' ||
      target.closest('aava-textbox') ||
      target.closest('aava-textarea') ||
      target.closest('aava-select');

    if (!isInputElement && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      this.onCardClick();
    }
  }

  private sanitizeStyles(
    styles: Record<string, string>
  ): Record<string, string> {
    // Implement style validation logic
    const allowedProperties = [
      'color',
      'background-color',
      'border',
      'padding',
      'margin',
    ];

    return Object.entries(styles).reduce(
      (acc, [key, value]) => {
        if (allowedProperties.includes(key)) {
          acc[key] = value;
        }
        return acc;
      },
      {} as Record<string, string>
    );
  }
}
