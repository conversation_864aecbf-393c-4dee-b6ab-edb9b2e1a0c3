.agent-image-container {
  margin-bottom: 24px;
}

.agent-image {
  position: relative;
  width: 100%;
  height: 180px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  overflow: hidden;

  &.has-image {
    border: 2px solid #d1d5db;
    background-color: #ffffff;
  }
}

.agent-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

.agent-image-placeholder {
  width: 160px;
  height: 160px;
  object-fit: contain;
}

.robot-placeholder {
  font-size: 64px;
  color: #9ca3af;
}

.hidden-file-input {
  display: none;
}

.edit-image-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 32px;
  height: 32px;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  color: #6b7280;

  &:hover:not(:disabled) {
    background-color: #f9fafb;
    border-color: #9ca3af;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// Selected Tags Container
.selected-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 0;
}

// Form Fields
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

// Form input styles removed - now using aava-textbox and aava-select components

// Tag-related styles removed - now using aava-tag and aava-select components
