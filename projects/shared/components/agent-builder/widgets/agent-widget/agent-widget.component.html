<app-widget-card [data]="widgetCardData()" (infoClicked)="onInfoClick()">
  <!-- Agent Image -->
  <div class="agent-image-container">
    <div class="agent-image" [class.has-image]="data().agentImage">
      <!-- Display uploaded image or placeholder -->
      @if (data().agentImage) {
        <img
          [src]="data().agentImage"
          alt="Agent Image"
          class="agent-image-preview"
        />
      } @else {
        <img
          src="assets/bot.png"
          alt="Default Agent"
          class="agent-image-placeholder"
        />
      }
    </div>
  </div>

  <!-- Form Fields -->
  <div class="form-fields">
    <div class="form-field">
      <aava-textbox
        label="Agent Name"
        placeholder="Enter Agent name"
        [ngModel]="data().agentName"
        [disabled]="disabled()"
        (ngModelChange)="onAgentNameChange($event)"
        size="sm"
        variant="default"
        [required]="true"
        [error]="agentNameError()"
      ></aava-textbox>
    </div>

    <div class="form-field">
      <aava-textbox
        label="Agent Details"
        placeholder="Describe the agent's capabilities and characteristics"
        [ngModel]="data().agentDetails"
        [disabled]="disabled()"
        (ngModelChange)="onAgentDetailsChange($event)"
        size="sm"
        variant="default"
        [required]="true"
        [error]="agentDetailsError()"
      ></aava-textbox>
    </div>

    <div class="form-field">
      <aava-select
        size="sm"
        label="Practice Area"
        placeholder="Select a practice area"
        [ngModel]="data().practiceArea"
        [disabled]="disabled()"
        (selectionChange)="onPracticeAreaChange($event)"
        [required]="true"
      >
        <aava-select-option
          *ngFor="let option of practiceAreaOptions()"
          [value]="option.value"
        >
          {{ option.label }}
        </aava-select-option>
      </aava-select>
    </div>

    <div class="form-field good-at-field">
      <aava-select
        size="sm"
        [multiple]="true"
        label="Good At"
        placeholder="Select skills and technologies"
        [ngModel]="data().goodAtTags || []"
        [disabled]="disabled()"
        (selectionChange)="onGoodAtSelectionChange($event)"
        [required]="true"
      >
        <aava-select-option
          *ngFor="let option of goodAtOptions()"
          [value]="option.value"
        >
          <aava-checkbox
            size="sm"
            [isChecked]="isOptionSelected(option.value)"
          ></aava-checkbox>
          {{ option.label }}
        </aava-select-option>
      </aava-select>

      <!-- Selected Tags Display -->
      <div
        class="selected-tags-container"
        *ngIf="data().goodAtTags && data().goodAtTags.length > 0"
      >
        @for (tag of data().goodAtTags; track tag) {
          <aava-tag
            [label]="tag"
            size="sm"
            variant="outlined"
            [removable]="true"
            [disabled]="disabled()"
            (removed)="onGoodAtTagRemove(tag)"
          ></aava-tag>
        }
      </div>
    </div>
  </div>
</app-widget-card>
