import { AavaTextboxComponent } from '@aava/play-core';
import { AavaSelectComponent } from '@aava/play-core';
import { AavaSelectOptionComponent } from '@aava/play-core';
import { AavaTagComponent } from '@aava/play-core';
import { AavaCheckboxComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
  inject,
  OnInit,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { FormsModule } from '@angular/forms';

import { DropdownDataStore } from '../../../../stores/dropdown-data.store';
import { WidgetCardComponent, WidgetCardData } from '../widget-card';

export interface AgentWidgetData {
  readonly agentName: string;
  readonly agentDetails: string;
  readonly practiceArea: string;
  readonly goodAtTags: string[];
  readonly agentImage?: string; // Base64 image data
}

@Component({
  selector: 'app-agent-widget',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    WidgetCardComponent,
    AavaTextboxComponent,
    AavaSelectComponent,
    AavaSelectOptionComponent,
    AavaTagComponent,
    AavaCheckboxComponent,
  ],
  templateUrl: './agent-widget.component.html',
  styleUrls: ['./agent-widget.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgentWidgetComponent implements OnInit {
  // Store injection
  private readonly dropdownStore = inject(DropdownDataStore);

  // ViewChild for file input
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  // Signal inputs
  readonly data = input.required<AgentWidgetData>();
  readonly disabled = input<boolean>(false);
  readonly executeMode = input<boolean>(false);

  // Signal outputs
  readonly agentNameChanged = output<string>();
  readonly agentDetailsChanged = output<string>();
  readonly practiceAreaChanged = output<string>();
  readonly tagAdded = output<string>();
  readonly tagRemoved = output<string>();
  readonly goodAtSelectionChanged = output<string[]>();
  readonly imageChanged = output<string>(); // Base64 image data
  readonly imageEditClicked = output<void>();
  readonly infoClicked = output<void>();

  // Validation inputs from parent
  readonly agentNameError = input<string>('');
  readonly agentDetailsError = input<string>('');

  // Computed values
  readonly widgetCardData = computed<WidgetCardData>(() => ({
    title: 'Agent',
    titleColor: 'rgba(0, 132, 255, 1)',
    icon: 'bot',
    iconColor: 'rgba(0, 132, 255, 1)',
    iconBackgroundColor: 'rgba(230, 243, 255, 1)',
    isRequired: true,
    isDisabled: this.disabled(),
    executeMode: this.executeMode(),
    infoTooltip: "Configure your AI agent's basic information and capabilities",
  }));

  // Dynamic dropdown options from store
  readonly practiceAreaOptions = this.dropdownStore.practiceAreaOptions;
  readonly goodAtOptions = this.dropdownStore.goodAtOptions;

  // Store signals
  readonly loading = this.dropdownStore.loading;
  readonly error = this.dropdownStore.error;

  ngOnInit(): void {
    // Dropdown data is loaded by the parent component
    // No need to load it again here to avoid race conditions
  }

  protected onAgentNameChange(value: string): void {
    this.agentNameChanged.emit(value);
  }

  protected onAgentDetailsChange(value: string): void {
    this.agentDetailsChanged.emit(value);
  }

  protected onPracticeAreaChange(value: string): void {
    this.practiceAreaChanged.emit(value);
  }

  protected onTagInputKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      const target = event.target as HTMLInputElement;
      const value = target.value.trim();
      if (value) {
        this.tagAdded.emit(value);
        target.value = '';
      }
    }
  }

  protected onRemoveTag(tag: string): void {
    this.tagRemoved.emit(tag);
  }

  protected onGoodAtSelectionChange(selectedValues: string[]): void {
    // Emit the bulk selection change event
    this.goodAtSelectionChanged.emit(selectedValues);

    // Get current tags
    const currentTags = this.data().goodAtTags || [];

    // Find tags to remove (in current but not in selected)
    const tagsToRemove = currentTags.filter(
      tag => !selectedValues.includes(tag)
    );

    // Find tags to add (in selected but not in current)
    const tagsToAdd = selectedValues.filter(
      value => !currentTags.includes(value)
    );

    // Remove tags that are no longer selected
    tagsToRemove.forEach(tag => this.tagRemoved.emit(tag));

    // Add new tags
    tagsToAdd.forEach(value => this.tagAdded.emit(value));
  }

  protected onGoodAtTagRemove(tag: string): void {
    this.tagRemoved.emit(tag);
  }

  // Helper method to get current selected values for checkbox state
  protected isOptionSelected(optionValue: string): boolean {
    return this.data().goodAtTags?.includes(optionValue) || false;
  }

  protected onEditImageClick(): void {
    // Trigger file input click
    this.fileInput.nativeElement.click();
  }

  protected onImageUpload(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file.');
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        alert('Image size must be less than 5MB.');
        return;
      }

      this.convertToBase64(file);
    }
  }

  private convertToBase64(file: File): void {
    const reader = new FileReader();

    reader.onload = e => {
      const result = e.target?.result as string;
      if (result) {
        // Emit the base64 data
        this.imageChanged.emit(result);
        console.log(
          '🖼️ Image converted to base64:',
          `${result.substring(0, 50)}...`
        );
      }
    };

    reader.onerror = () => {
      console.error('❌ Error reading file');
      alert('Error reading the image file.');
    };

    reader.readAsDataURL(file);
  }

  protected onInfoClick(): void {
    this.infoClicked.emit();
  }
}
