import { AavaButtonComponent, AavaTagComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
} from '@angular/core';

import { WidgetCardComponent, WidgetCardData } from '../widget-card';

export interface KnowledgeBaseWidgetData {
  readonly knowledgeBases: string[];
}

@Component({
  selector: 'app-knowledge-base-widget',
  standalone: true,
  imports: [
    CommonModule,
    WidgetCardComponent,
    AavaButtonComponent,
    AavaTagComponent,
  ],
  templateUrl: './knowledge-base-widget.component.html',
  styleUrls: ['./knowledge-base-widget.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class KnowledgeBaseWidgetComponent {
  // Signal inputs
  readonly data = input.required<KnowledgeBaseWidgetData>();
  readonly disabled = input<boolean>(false);
  readonly executeMode = input<boolean>(false);
  // Signal outputs
  readonly addClicked = output<void>();
  readonly knowledgeBaseRemoved = output<string>();
  readonly infoClicked = output<void>();

  // Computed values
  readonly widgetCardData = computed<WidgetCardData>(() => ({
    title: 'Knowledge Base',
    titleColor: 'rgba(233, 30, 99, 1)',
    icon: 'book-open-text',
    iconColor: 'rgba(233, 30, 99, 1)',
    iconBackgroundColor: 'rgba(253, 233, 239, 1)',
    isRequired: false,
    isDisabled: this.disabled(),
    executeMode: this.executeMode(),
    infoTooltip:
      'Connect your agent to knowledge sources and documents for enhanced capabilities',
  }));

  protected onAddClick(): void {
    this.addClicked.emit();
  }

  protected onRemoveKnowledgeBase(knowledgeBase: string): void {
    this.knowledgeBaseRemoved.emit(knowledgeBase);
  }

  protected onInfoClick(): void {
    this.infoClicked.emit();
  }
}
