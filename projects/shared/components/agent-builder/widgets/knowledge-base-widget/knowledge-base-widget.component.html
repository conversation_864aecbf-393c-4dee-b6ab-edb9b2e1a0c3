<app-widget-card [data]="widgetCardData()" (infoClicked)="onInfoClick()">
  <!-- Knowledge Base Tags -->
  @if (data().knowledgeBases.length > 0) {
    <div class="knowledge-base-tags">
      @for (knowledgeBase of data().knowledgeBases; track knowledgeBase) {
        <aava-tag
          [label]="knowledgeBase"
          color="default"
          size="md"
          [pill]="false"
          [removable]="!disabled()"
          (removed)="onRemoveKnowledgeBase(knowledgeBase)"
        ></aava-tag>
      }
    </div>
  }
  <div class="add-button">
    <aava-button
      label="Add"
      (userClick)="onAddClick()"
      variant="primary"
      size="sm"
      [outlined]="true"
      width="100%"
      [disabled]="disabled()"
    ></aava-button>
  </div>
</app-widget-card>
