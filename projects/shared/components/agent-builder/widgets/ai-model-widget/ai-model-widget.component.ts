import {
  AavaTextboxComponent,
  AavaSelectComponent,
  AavaSelectOptionComponent,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  computed,
  inject,
  OnInit,
} from '@angular/core';
import { FormsModule } from '@angular/forms';

import { DropdownDataStore } from '../../../../stores/dropdown-data.store';
import { WidgetCardComponent, WidgetCardData } from '../widget-card';

export interface AIModelWidgetData {
  readonly selectedModel: string;
  readonly temperature: string;
  readonly maxToken: string;
  readonly topP: string;
  readonly maxIteration: string;
  readonly maxRpm: string;
  readonly maxExecutionTime: string;
}

@Component({
  selector: 'app-ai-model-widget',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    WidgetCardComponent,
    AavaTextboxComponent,
    AavaSelectComponent,
    AavaSelectOptionComponent,
  ],
  templateUrl: './ai-model-widget.component.html',
  styleUrls: ['./ai-model-widget.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AIModelWidgetComponent implements OnInit {
  // Store injection
  private readonly dropdownStore = inject(DropdownDataStore);
  // Signal inputs
  readonly data = input.required<AIModelWidgetData>();
  readonly disabled = input<boolean>(false);

  // Signal outputs
  readonly modelChanged = output<string>();
  readonly temperatureChanged = output<string>();
  readonly maxTokenChanged = output<string>();
  readonly topPChanged = output<string>();
  readonly maxIterationChanged = output<string>();
  readonly maxRpmChanged = output<string>();
  readonly maxExecutionTimeChanged = output<string>();
  readonly infoClicked = output<void>();

  // Dynamic dropdown options from store
  readonly modelOptions = this.dropdownStore.modelOptions;

  // Store signals
  readonly loading = this.dropdownStore.loading;
  readonly error = this.dropdownStore.error;

  ngOnInit(): void {
    // Load dropdown data when component initializes
    this.dropdownStore.loadAllData();
  }

  // Computed values
  readonly widgetCardData = computed<WidgetCardData>(() => ({
    title: 'AI Model',
    titleColor: 'rgba(103, 58, 183, 1)',
    icon: 'box',
    iconColor: 'rgba(103, 58, 183, 1)',
    iconBackgroundColor: 'rgba(242, 235, 253, 1)',
    isRequired: true,
    isDisabled: this.disabled(),
    infoTooltip: 'Configure the AI model parameters and performance settings',
  }));

  protected onModelChange(value: string): void {
    this.modelChanged.emit(value);
  }

  protected onTemperatureChange(value: string): void {
    this.temperatureChanged.emit(value);
  }

  protected onMaxTokenChange(value: string): void {
    this.maxTokenChanged.emit(value);
  }

  protected onTopPChange(value: string): void {
    this.topPChanged.emit(value);
  }

  protected onMaxIterationChange(value: string): void {
    this.maxIterationChanged.emit(value);
  }

  protected onMaxRpmChange(value: string): void {
    this.maxRpmChanged.emit(value);
  }

  protected onMaxExecutionTimeChange(value: string): void {
    this.maxExecutionTimeChanged.emit(value);
  }

  protected onInfoClick(): void {
    this.infoClicked.emit();
  }
}
