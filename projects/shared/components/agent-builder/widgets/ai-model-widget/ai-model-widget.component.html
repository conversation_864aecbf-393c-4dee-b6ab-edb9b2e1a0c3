<app-widget-card [data]="widgetCardData()" (infoClicked)="onInfoClick()">
  <!-- Form Fields -->
  <div class="form-fields">
    <div class="form-field">
      <aava-select
        size="sm"
        label="Model"
        placeholder="Select Model"
        [ngModel]="data().selectedModel"
        [disabled]="disabled()"
        (selectionChange)="onModelChange($event)"
        [required]="true"
      >
        <aava-select-option
          *ngFor="let option of modelOptions()"
          [value]="option.value"
        >
          {{ option.label }}
        </aava-select-option>
      </aava-select>
    </div>

    <div class="form-fields-grid">
      <div class="form-field">
        <aava-textbox
          label="Temperature"
          placeholder="Temperature (0.1-1.0)"
          [ngModel]="data().temperature"
          [disabled]="disabled()"
          (ngModelChange)="onTemperatureChange($event)"
          type="number"
          min="0.1"
          max="1.0"
          step="0.1"
          size="sm"
          variant="default"
          [required]="true"
        ></aava-textbox>
      </div>

      <div class="form-field">
        <aava-textbox
          label="Max Token"
          placeholder="Enter max tokens"
          [ngModel]="data().maxToken"
          [disabled]="disabled()"
          (ngModelChange)="onMaxTokenChange($event)"
          type="number"
          min="100"
          max="32000"
          step="100"
          size="sm"
          variant="default"
          [required]="true"
        ></aava-textbox>
      </div>

      <div class="form-field">
        <aava-textbox
          label="Top P"
          placeholder="Enter top P (0.1-1.0)"
          [ngModel]="data().topP"
          [disabled]="disabled()"
          (ngModelChange)="onTopPChange($event)"
          type="number"
          min="0.1"
          max="1.0"
          step="0.1"
          size="sm"
          variant="default"
          [required]="true"
        ></aava-textbox>
      </div>

      <div class="form-field">
        <aava-textbox
          label="Max Iteration"
          placeholder="Enter max iterations"
          [ngModel]="data().maxIteration"
          [disabled]="disabled()"
          (ngModelChange)="onMaxIterationChange($event)"
          type="number"
          min="1"
          max="100"
          step="1"
          size="sm"
          variant="default"
        ></aava-textbox>
      </div>

      <div class="form-field">
        <aava-textbox
          label="Max RPM"
          placeholder="Enter max RPM"
          [ngModel]="data().maxRpm"
          [disabled]="disabled()"
          (ngModelChange)="onMaxRpmChange($event)"
          type="number"
          min="1"
          max="10000"
          step="1"
          size="sm"
          variant="default"
        ></aava-textbox>
      </div>

      <div class="form-field">
        <aava-textbox
          label="Max Execution Time"
          placeholder="Enter max execution time (seconds)"
          [ngModel]="data().maxExecutionTime"
          [disabled]="disabled()"
          (ngModelChange)="onMaxExecutionTimeChange($event)"
          type="number"
          min="1"
          max="3600"
          step="1"
          size="sm"
          variant="default"
        ></aava-textbox>
      </div>
    </div>
  </div>
</app-widget-card>
