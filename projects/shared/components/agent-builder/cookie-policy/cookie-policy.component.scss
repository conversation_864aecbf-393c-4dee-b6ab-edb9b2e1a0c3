.cookie-consent-container {
  padding: 24px;
  border-radius: 8px;
  margin: 0 auto;
}

.cookie-content {
  margin-bottom: 24px;
}

.header-content {
  color: var(--color-text-primary, #3b3f46);
  font-family: var(--Global-v1-Family-Heading, Mulish);
  font-size: var(--Global-v1-Size-24, 24px);
  font-style: normal;
  font-weight: 700;
  line-height: var(--Global-v1-Line-height-28, 28px);
}

.content {
  align-self: stretch;
  color: var(--color-text-primary, #616874);
  font-family: var(--Global-v1-Family-Body, Inter);
  font-size: var(--Global-v1-Size-16, 16px);
  font-style: normal;
  font-weight: 400;
  line-height: var(--Global-v1-Line-height-20, 20px);
}

.cookie-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
  align-self: stretch;
}

.cookie-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
