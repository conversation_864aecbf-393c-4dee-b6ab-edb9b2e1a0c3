import {
  AavaButtonComponent,
  AavaIconComponent,
  AavaDialogService,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';

import { CookieService } from '../../../services/cookie.service';

@Component({
  selector: 'app-cookie-policy',
  imports: [CommonModule, AavaIconComponent, AavaButtonComponent],
  templateUrl: './cookie-policy.component.html',
  styleUrls: ['./cookie-policy.component.scss'],
})
export class CookiePolicyComponent {
  @Input() title = 'Cookie Policy';
  @Input() message =
    'We use cookies to enhance your browsing experience, serve personalized content, and analyze our traffic. By clicking "Accept All", you consent to our use of cookies.';

  constructor(
    private cookieService: CookieService,
    private dialogService: AavaDialogService
  ) {}

  /**
   * Handle accept all button click
   */
  onAcceptAll(): void {
    this.cookieService.acceptAll();
    this.dialogService.close();
  }

  /**
   * Handle reject all button click
   */
  onRejectAll(): void {
    this.cookieService.rejectAll();
    this.dialogService.close();
  }

  get computedIconColor(): string {
    return 'var(--color-text-primary)';
  }
}
