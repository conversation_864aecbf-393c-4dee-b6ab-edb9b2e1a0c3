import { CommonModule } from '@angular/common';
import { Component, ChangeDetectionStrategy } from '@angular/core';

import { AgentBuilderPageComponent } from './components';

@Component({
  selector: 'app-agent-builder',
  standalone: true,
  imports: [CommonModule, AgentBuilderPageComponent],
  templateUrl: './agent-builder.component.html',
  styleUrls: ['./agent-builder.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgentBuilderComponent {
  // This component now simply hosts the page component
}
