<app-libraries-main-layout [isThirdColumnVisible]="true">
  <app-guardrail-builder-header
    header
    [isFormValid]="isFormValid()"
    (saveClick)="onSaveGuardrail()"
    (runClick)="onRunGuardrail()"
  >
  </app-guardrail-builder-header>

  <app-guardrail-meta
    firstRow
    [data]="metadataData()"
    (dataChanged)="onMetadataChange($event)"
  >
  </app-guardrail-meta>
  <app-code-editor
    secondRow
    title="Colang Definition"
    language="python"
    theme="light"
    [value]="colangCode()"
    [showPrimaryButton]="false"
    height="100%"
    (valueChange)="onColangCodeChange($event)"
  >
  </app-code-editor>
  <app-code-editor
    thirdRow
    title="YAML Configuration"
    language="yaml"
    theme="light"
    [value]="yamlCode()"
    [showPrimaryButton]="false"
    height="100%"
    (valueChange)="onYamlCodeChange($event)"
  >
  </app-code-editor>
</app-libraries-main-layout>
