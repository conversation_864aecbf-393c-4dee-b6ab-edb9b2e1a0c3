/* eslint-disable @typescript-eslint/no-explicit-any */
import { AavaDialogService } from '@aava/play-core';
import {
  Component,
  computed,
  signal,
  inject,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LibrariesMainLayoutComponent } from '@shared';
import { Subject } from 'rxjs';

import { Guardrail } from '../../../../models/artifact.model';
import { GuardrailService } from '../../../../services/guardrail.service';
import { TeamIdService } from '../../../../services/team-id.service';
import { DropdownDataStore } from '../../../../stores/dropdown-data.store';
import { CodeEditorComponent } from '../../../code-editor/code-editor.component';
import {
  GuardrailMetaComponent,
  GuardrailMetaData,
} from '../../widgets/guardrail-meta/guardrail-meta.component';
import { GuardrailBuilderHeaderComponent } from '../guardrail-builder-header/guardrail-builder-header.component';
import { ToastWrapperService } from 'projects/shared/services/toast-wrapper.service';

@Component({
  selector: 'app-guardrail-builder',
  standalone: true,
  imports: [
    GuardrailBuilderHeaderComponent,
    CodeEditorComponent,
    GuardrailMetaComponent,
    LibrariesMainLayoutComponent,
  ],
  templateUrl: './guardrail-builder.component.html',
  styleUrl: './guardrail-builder.component.scss',
})
export class GuardrailBuilderComponent implements OnInit, OnDestroy {
  // Service injection
  private readonly dialogService = inject(AavaDialogService);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly guardrailService = inject(GuardrailService);
  private readonly teamIdService = inject(TeamIdService);
  private readonly dropdownStore = inject(DropdownDataStore);

  // Component lifecycle
  private readonly destroy$ = new Subject<void>();
  // Form state signals
  private readonly metadataSignal = signal<GuardrailMetaData>({
    name: '',
    description: '',
  });
  private readonly colangCodeSignal = signal<string>('');
  private readonly yamlCodeSignal = signal<string>('');
  // Practice area and tags tracking
  private readonly practiceAreaSignal = signal<string>('');
  private readonly practiceAreaIdSignal = signal<number>(1);
  private createdGuardrailId : number | null = null;
  private goodAtTagsSignal = signal<string[]>([]);
  private readonly goodAtTagIdsSignal = signal<number[]>([]);

  // Current guardrail data
  private readonly currentGuardrailSignal = signal<Guardrail | null>(null);

  // Computed properties
  readonly guardrail = computed(() => this.currentGuardrailSignal());
  readonly metadataData = computed(() => this.metadataSignal());
  readonly colangCode = computed(() => this.colangCodeSignal());
  readonly yamlCode = computed(() => this.yamlCodeSignal());
  readonly isFormValid = computed(() => {
    const metadata = this.metadataData();
    const colang = this.colangCode();
    const yaml = this.yamlCode();
    return (
      metadata.name.trim() !== '' &&
      metadata.description.trim() !== '' &&
      colang.trim() !== '' &&
      yaml.trim() !== ''
    );
  });

  readonly isEditMode = computed(() => {
    const currentGuardrail = this.guardrail();
    return currentGuardrail?.id !== undefined && currentGuardrail.id > 0;
  });

  constructor(private toastWrapper: ToastWrapperService){}

  ngOnInit(): void {
    this.loadDropdownData();
    this.initializeGuardrail();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private async loadDropdownData(): Promise<void> {
    try {
      await Promise.all([
        this.dropdownStore.loadPracticeAreas(),
        this.dropdownStore.loadGoodAtTags(),
      ]);
    } catch (error) {
      console.error('Failed to load dropdown data:', error);
    }
  }

  /**
   * Initialize guardrail based on route parameters
   */
  private initializeGuardrail(): void {
    const guardrailId = this.route.snapshot.paramMap.get('id');
    if (guardrailId) {
      this.loadExistingGuardrail(parseInt(guardrailId));
    }
  }

  /**
   * Load existing guardrail
   */
  private loadExistingGuardrail(id: number): void {
    this.guardrailService.getById(id).subscribe({
      next: (guardrail: Guardrail) => {
        this.currentGuardrailSignal.set(guardrail);
        this.metadataSignal.set({
          name: guardrail.name,
          description: guardrail.description,
        });
        this.colangCodeSignal.set(guardrail.content);
        this.yamlCodeSignal.set(guardrail.yamlContent);

        // Set practice area and tags if available
        if (guardrail.practiceArea) {
          this.practiceAreaIdSignal.set(guardrail.practiceArea);
          // Find practice area name from dropdown options
          const practiceAreaOption = this.dropdownStore
            .practiceAreaOptions()
            .find(option => option.id === guardrail.practiceArea);
          if (practiceAreaOption) {
            this.practiceAreaSignal.set(practiceAreaOption.value);
          }
        }

        if (guardrail.tags && guardrail.tags.length > 0) {
          this.goodAtTagIdsSignal.set(guardrail.tags);
          // Find tag names from dropdown options
          const tagNames = guardrail.tags
            .map(tagId => {
              const tagOption = this.dropdownStore
                .goodAtOptions()
                .find(option => option.id === tagId);
              return tagOption?.value || '';
            })
            .filter(name => name !== '');
          this.goodAtTagsSignal.set(tagNames);
        }
      },
      error: (error: any) => {
        console.error('Failed to load guardrail:', error);
      },
    });
  }

  // ============================================================================
  // FORM CHANGE HANDLERS
  // ============================================================================

  onMetadataChange(data: GuardrailMetaData): void {
    this.metadataSignal.set(data);

    // Handle practice area
    if (data.practiceArea) {
      this.practiceAreaSignal.set(data.practiceArea);
      // Get practice area ID from dropdown store
      const practiceAreaData = this.dropdownStore
        .practiceAreaOptions()
        .find(option => option.value === data.practiceArea);
      if (practiceAreaData?.id) {
        this.practiceAreaIdSignal.set(practiceAreaData.id);
      }
    }

    // Handle good at tags
    if (data.goodAtTags && data.goodAtTags.length > 0) {
      this.goodAtTagsSignal.set(data.goodAtTags);
      // Get good at tag IDs from dropdown store
      const tagIds = data.goodAtTags
        .map(tagName => {
          const tagData = this.dropdownStore
            .goodAtOptions()
            .find(option => option.value === tagName);
          return tagData?.id || 0;
        })
        .filter(id => id > 0);
      this.goodAtTagIdsSignal.set(tagIds);
    } else {
      this.goodAtTagsSignal.set([]);
      this.goodAtTagIdsSignal.set([]);
    }
  }

  onColangCodeChange(code: string): void {
    this.colangCodeSignal.set(code);
  }

  onYamlCodeChange(code: string): void {
    this.yamlCodeSignal.set(code);
  }

  // Removed unused createFeedbackConfig method

  async onSaveGuardrail(): Promise<void> {
    if (this.isFormValid()) {
      try {
        const result = await this.dialogService.confirmation({
          title: 'Save Guardrail',
          message: 'Are you sure you want to save the guardrail?',
          confirmButtonText: 'Save',
          cancelButtonText: 'Cancel',
        });

        if (result.confirmed) {
          await this.saveGuardrail();
        }
      } catch (error) {
        console.error('Error with confirmation dialog:', error);
      }
    }
  }

  async onRunGuardrail(): Promise<void> {
    if(this.createdGuardrailId){
      console.log(this.createdGuardrailId);
      this.router.navigate(['/build/guardrails/playground'], {
        queryParams: { type: 'guardrail', id: this.createdGuardrailId },
      });
    }
    else{
      this.toastWrapper.error("Please save the Guardrail configuration before proceeding");
    }
    // TODO: Implement run functionality later
  }

  /**
   * Save the guardrail
   */
  private async saveGuardrail(): Promise<void> {
    const currentGuardrail = this.guardrail();
    const metadata = this.metadataData();
    const colangCode = this.colangCode();
    const yamlCode = this.yamlCode();

    // Get team ID from cookies (same as agents)
    const teamId = this.teamIdService.getTeamIdFromCookies() || 23;

    // Get practice area ID
    const practiceAreaId = this.practiceAreaIdSignal() || 1;

    // Get good at tag IDs
    const tagIds = this.goodAtTagIdsSignal();

    const guardrailData: Omit<Guardrail, 'id'> = {
      name: metadata.name,
      description: metadata.description,
      content: colangCode,
      yamlContent: yamlCode,
      chatBot: false,
      version: 1,
      parentId: null,
      isDeleted: false,
      approvedBy: null,
      approvedAt: null,
      status: 'CREATED',
      teamId: teamId,
      changeSummary: 'Initial creation',
      comments: 'Looks good', // Hard-coded comment as requested
      configKey: `CFG-${Date.now()}`,
      isEditable: true,
      tags: tagIds.length > 0 ? tagIds : [], // Use actual good at tag IDs or empty array
      practiceArea: practiceAreaId, // Use actual practice area ID
      createdBy: 101, // TODO: Get from auth service
      modifiedBy: 101,
      createdAt: new Date().toISOString().slice(0, -1).padEnd(26, '0'),
      modifiedAt: new Date().toISOString().slice(0, -1).padEnd(26, '0'),
    };

    // Show loading dialog
    this.dialogService.loading({
      title: 'Saving Guardrail',
      message: 'Please wait while we save your guardrail...',
    });

    try {
      if (currentGuardrail?.id) {
        // Update existing guardrail
        const updatedGuardrail: Guardrail = {
          ...currentGuardrail,
          ...guardrailData,
          id: currentGuardrail.id,
        };

        this.guardrailService.update(updatedGuardrail).subscribe({
          next: async (result: any) => {
            this.dialogService.close();
            // Show success popup with API response
            await this.showSuccessWithResponse(
              'Guardrail Updated Successfully!',
              result
            );
          },
          error: async (error: any) => {
            console.error('❌ Error updating guardrail:', error);
            // Close loading dialog
            this.dialogService.close();
            await this.showErrorMessage(
              'Failed to update guardrail. Please try again.',
              error
            );
          },
        });
      } else {
        // Create new guardrail - THIS IS THE MAIN FLOW
        this.guardrailService.create(guardrailData).subscribe({
          next: async (result: any) => {
            this.dialogService.close();
            // Show success popup with API response
            await this.showSuccessWithResponse(
              'Guardrail Created Successfully!',
              result
            );
          },
          error: async (error: any) => {
            console.error('❌ Error creating guardrail via API:', error);
            // Close loading dialog
            this.dialogService.close();
            await this.showErrorMessage(
              'Failed to create guardrail. Please try again.',
              error
            );
          },
        });
      }
    } catch (error) {
      console.error('❌ Unexpected error in saveGuardrail:', error);
      this.dialogService.close();
      await this.showErrorMessage(
        'An unexpected error occurred. Please try again.',
        error
      );
    }
  }

  /**
   * Show success message with API response
   */
  private async showSuccessWithResponse(
    message: string,
    response: any
  ): Promise<void> {
    try {
      // Use API response message if available, otherwise use default
      let successMessage = message;
      if (response?.message) {
        successMessage = response.message;
      }

      // Add guardrail ID if available
      if (response?.guardrailId || response?.id) {
        this.createdGuardrailId = response?.guardrailId || response?.id;
        successMessage += `\n\nGuardrail ID: ${response.guardrailId || response.id}`;
      }

      await this.dialogService.success({
        title: 'Success',
        message: successMessage,
      });
    } catch (error) {
      console.error('Error showing success message:', error);
    }
  }

  /**
   * Show error message
   */
  private async showErrorMessage(message: string, error?: any): Promise<void> {
    try {
      let errorMessage = message;

      // Extract actual error message from API response
      // Handle pattern: { "message": "Exception while adding Guardrail..." }
      if (error?.error?.message) {
        errorMessage = error.error.message;
      } else if (error?.error?.data?.message) {
        errorMessage = error.error.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }
      errorMessage = this.cleanErrorMessage(errorMessage);

      await this.dialogService.error({
        title: 'Error',
        message: errorMessage,
      });
    } catch (dialogError) {
      console.error('Error showing error message:', dialogError);
    }
  }

  /**
   * Clean and format error messages for better user experience
   */
  private cleanErrorMessage(errorMessage: string): string {
    // Return original message as-is without any modifications
    return errorMessage;
  }
}
