:host {
  height: 100%;
  display: block;
}

.builder_container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 12px 12px 12px 12px;
  border: 2px solid var(--Brand-White-White, #FDFDFD);
  background: var(--Surface-Fill-Light-Surface-White-6, rgba(255, 255, 255, 0.60));
}

.tool-builder-content {
  display: flex;
  gap: 24px;
  padding: 16px 24px 16px 24px;
  flex: 1;
  min-height: 0; /* Allow flex child to shrink */
  overflow: hidden;

  .metadata-column {
    width: 20.9713%;
    display: flex;
    flex-direction: column;
    height: 100%;

    app-guardrail-meta {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .code-editor-column {
    width: 39.5142%;
    display: flex;
    flex-direction: column;
    height: 100%;

    app-code-editor {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .compiler-column {
    width: 39.5142%;
    display: flex;
    flex-direction: column;
    height: 100%;

    app-code-editor {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}