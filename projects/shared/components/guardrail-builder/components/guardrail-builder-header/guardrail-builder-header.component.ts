import { AavaButtonComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';

@Component({
  selector: 'app-guardrail-builder-header',
  imports: [CommonModule, AavaButtonComponent],
  templateUrl: './guardrail-builder-header.component.html',
  styleUrl: './guardrail-builder-header.component.scss',
})
export class GuardrailBuilderHeaderComponent {
  @Input() isFormValid: boolean = false;
  @Output() saveClick = new EventEmitter<void>();
  @Output() runClick = new EventEmitter<void>();

  // Computed values
  protected saveButtonAriaLabel = computed(() =>
    this.isFormValid
      ? 'Save guardrail'
      : 'Complete required fields to save guardrail'
  );

  protected runButtonAriaLabel = computed(() =>
    this.isFormValid
      ? 'Run guardrail'
      : 'Complete required fields to run guardrail'
  );

  protected onSaveClick(): void {
    this.saveClick.emit();
  }

  protected onRunClick(): void {
    this.runClick.emit();
  }
}
