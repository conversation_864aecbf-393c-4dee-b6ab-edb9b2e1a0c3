.tool-builder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;  
  height: 2.5rem;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.9);
  border-top-right-radius: 0.75rem;
  border-top-left-radius: 0.75rem;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  position: relative;
}

.header-spacer {
  flex: 1;
}

.header-actions {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.page-title {
  color: var(--Colors-Text-primary, #3b3f46);
  font-family: var(--Global-v1-Family-Body, Inter);
  font-size: var(--Global-v1-Size-16, 1rem);
  font-style: normal;
  font-weight: 500;
  line-height: var(--Global-v1-Line-height-20, 1.25rem);
  margin: 0;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  white-space: nowrap;
}

// Responsive design
@media (max-width: 768px) {
  .tool-builder-header {
    padding: 0.5rem 16px 0.5rem 16px;
  }
}