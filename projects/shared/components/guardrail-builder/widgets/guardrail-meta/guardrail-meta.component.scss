:host {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tool-metadata-card {
  display: flex;
  flex-direction: column;
  gap: var(--Global-V1-Spacing-Space8, 16px);
  padding: 16px;
  width: 100%;
  height: 100%;
  border-radius: var(--Global-V1-Radius-Rad7, 14px);
  border: 2px solid var(--Brand-White-White, #FDFDFD);
  background: var(--Surface-Fill-Light-Surface-White-6, rgba(255, 255, 255, 0.6));
  box-shadow: 0 2px 4px 0 var(--Brand-Neutral-n-100, #D1D3D8);
  box-sizing: border-box;

  .card-title {
    color: #000;
    font-family: var(--Global-v1-Family-Body, Inter);
    font-size: var(--Global-v1-Size-18, 18px);
    font-style: normal;
    font-weight: 500;
    flex-shrink: 0;
    margin: 0;
  }
}

// Selected Tags Container
.selected-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 0;
}

// Form Fields
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  color: #111827;
  background-color: #ffffff;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }

  &:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
  }
}

.form-select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;

  &:disabled {
    cursor: not-allowed;
  }
}

// Tag Input
.tag-input-container {
  position: relative;
}

.tag-input {
  padding-right: 40px;
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 16px;
  pointer-events: none;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #f3f4f6;
  border-radius: 16px;
  font-size: 14px;
}

.tag-text {
  color: #374151;
}

.tag-remove {
  width: 16px;
  height: 16px;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 12px;

  &:hover:not(:disabled) {
    background-color: #e5e7eb;
    color: #374151;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}