import { AavaTextboxComponent } from '@aava/play-core';
import { AavaSelectComponent } from '@aava/play-core';
import { AavaSelectOptionComponent } from '@aava/play-core';
import { AavaTagComponent } from '@aava/play-core';
import { AavaCheckboxComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  input,
  output,
  OnInit,
  OnDestroy,
  inject,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { DropdownDataStore } from '../../../../stores/dropdown-data.store';

export interface GuardrailMetaData {
  readonly name: string;
  readonly description: string;
  readonly practiceArea?: string;
  readonly goodAtTags?: string[];
}

@Component({
  selector: 'app-guardrail-meta',
  standalone: true,
  imports: [
    CommonModule,
    AavaTextboxComponent,
    ReactiveFormsModule,
    AavaSelectOptionComponent,
    AavaSelectComponent,
    AavaTagComponent,
    AavaCheckboxComponent,
  ],
  templateUrl: './guardrail-meta.component.html',
  styleUrl: './guardrail-meta.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GuardrailMetaComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  private dropdownStore = inject(DropdownDataStore);

  // Signal inputs
  readonly data = input<GuardrailMetaData>({ name: '', description: '' });
  readonly disabled = input<boolean>(false);

  // Signal outputs
  readonly dataChanged = output<GuardrailMetaData>();

  // Form
  metadataForm!: FormGroup;

  // Get options from dropdown store
  get practiceAreaOptions() {
    return this.dropdownStore.practiceAreaOptions();
  }

  get goodAtOptions() {
    return this.dropdownStore.goodAtOptions();
  }

  constructor(private fb: FormBuilder) {
    this.initializeForm();
  }

  ngOnInit(): void {
    // Update form when data changes
    this.updateFormFromData();

    // Listen to form changes
    this.metadataForm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.metadataForm.valid) {
          this.emitFormData();
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.metadataForm = this.fb.group({
      name: ['', [Validators.required]],
      description: ['', [Validators.required]],
      practiceArea: [''],
      goodAtTags: [[]],
    });
  }

  private updateFormFromData(): void {
    const currentData = this.data();
    this.metadataForm.patchValue(
      {
        name: currentData.name,
        description: currentData.description,
        practiceArea: currentData.practiceArea || '',
        goodAtTags: currentData.goodAtTags || [],
      },
      { emitEvent: false }
    );
  }

  private emitFormData(): void {
    const formValue = this.metadataForm.value;
    this.dataChanged.emit({
      name: formValue.name,
      description: formValue.description,
      practiceArea: formValue.practiceArea,
      goodAtTags: formValue.goodAtTags,
    });
  }

  onGoodAtSelectionChange(event: any): void {
    // Handle both direct array and event object with value property
    const selectedValues = Array.isArray(event) ? event : event.value || [];

    // Update form control and emit changes
    this.metadataForm.patchValue({
      goodAtTags: selectedValues,
    });
    this.emitFormData();
  }

  isOptionSelected(value: string): boolean {
    const formValue = this.metadataForm.get('goodAtTags')?.value || [];
    return formValue.includes(value);
  }

  onGoodAtTagRemove(tagToRemove: string): void {
    const currentTags = this.metadataForm.get('goodAtTags')?.value || [];
    const updatedTags = currentTags.filter(
      (tag: string) => tag !== tagToRemove
    );
    this.metadataForm.patchValue({
      goodAtTags: updatedTags,
    });
    this.emitFormData();
  }
}
