<div class="tool-metadata-card">
  <h2 class="card-title">Guardrail Metadata</h2>

  <form [formGroup]="metadataForm" class="form-fields">
    <!-- Guardrail Name -->
    <div class="form-field">
      <aava-textbox
        label="Guardrail Name"
        placeholder="Enter guardrail name"
        size="sm"
        [required]="true"
        variant="default"
        formControlName="name"
        [disabled]="disabled()">
      </aava-textbox>
    </div>

    <!-- Guardrail Description -->
    <div class="form-field">
      <aava-textbox
        label="Guardrail Description"
        placeholder="Enter guardrail description"
        size="sm"
        [required]="true"
        variant="default"
        formControlName="description"
        [disabled]="disabled()">
      </aava-textbox>
    </div>

    <!-- Practice Area -->
    <div class="form-field">
      <aava-select
        label="Practice Area"
        size="sm"
        variant="default"
        [required]="true"
        formControlName="practiceArea"
        [disabled]="disabled()">
        @for (option of practiceAreaOptions; track option.value) {
          <aava-select-option [value]="option.value">
            {{ option.label }}
          </aava-select-option>
        }
      </aava-select>
    </div>

    <!-- Good at Section -->
    <div class="form-field good-at-field">
      <aava-select
        size="sm"
        [multiple]="true"
        label="Good At"
        [required]="true"
        placeholder="Select skills and technologies"
        formControlName="goodAtTags"
        [disabled]="disabled()"
        (selectionChange)="onGoodAtSelectionChange($event)">
        <aava-select-option
          *ngFor="let option of goodAtOptions"
          [value]="option.value">
          <aava-checkbox
            size="sm"
            [isChecked]="isOptionSelected(option.value)">
          </aava-checkbox>
          {{ option.label }}
        </aava-select-option>
      </aava-select>

      <!-- Selected Tags Display -->
      <div
        class="selected-tags-container"
        *ngIf="metadataForm.get('goodAtTags')?.value && metadataForm.get('goodAtTags')?.value.length > 0"
      >
        @for (tag of metadataForm.get('goodAtTags')?.value; track tag) {
          <aava-tag
            [label]="tag"
            size="sm"
            variant="outlined"
            [removable]="true"
            [disabled]="disabled()"
            (removed)="onGoodAtTagRemove(tag)"
          ></aava-tag>
        }
      </div>
    </div>
  </form>
</div>
