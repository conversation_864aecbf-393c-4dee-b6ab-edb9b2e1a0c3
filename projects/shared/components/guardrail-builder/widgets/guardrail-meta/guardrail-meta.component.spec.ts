import { ComponentFixture, TestBed } from '@angular/core/testing';

import { GuardrailMetaComponent } from './guardrail-meta.component';

describe('GuardrailMetaComponent', () => {
  let component: GuardrailMetaComponent;
  let fixture: ComponentFixture<GuardrailMetaComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [GuardrailMetaComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(GuardrailMetaComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
