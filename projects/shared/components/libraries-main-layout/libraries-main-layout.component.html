<div class="libraries-main-layout-container me-3 ms-3">
  <div class="row g-3 library-container">
    <div class="col-12">
      <ng-content select="[header]"></ng-content>
    </div>
    <!-- Metadata Column (always present) -->
    <div class="col-3 metadata-column ps-3">
      <ng-content select="[firstRow]"></ng-content>
    </div>

    <!-- Code Editor Column (dynamic width) -->
    <div
      class="code-editor-column"
      [ngClass]="isThirdColumnVisible ? 'col-4' : 'col-9 pe-3'"
    >
      <ng-content select="[secondRow]"></ng-content>
    </div>

    <!-- Compiler Column (optional) -->
    <div class="col-5 pe-3 compiler-column" *ngIf="isThirdColumnVisible">
      <ng-content select="[thirdRow]"></ng-content>
    </div>
  </div>
</div>
