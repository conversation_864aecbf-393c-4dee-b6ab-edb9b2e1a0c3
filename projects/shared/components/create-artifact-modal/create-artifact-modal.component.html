<app-popup-modal
  [isVisible]="isVisible"
  title="Create"
  size="medium"
  (modalClosed)="onModalClose()"
>
  <div class="create-modal-content">
    <div class="artifact-types">
      <div
        *ngFor="let type of artifactTypes"
        class="artifact-type-card"
        (click)="onArtifactTypeClick(type)"
        (keydown.enter)="onArtifactTypeClick(type)"
        (keydown.space)="onArtifactTypeClick(type)"
        tabindex="0"
        role="button"
        [attr.aria-label]="'Create ' + type.name"
        [style.--card-color]="type.color"
      >
        <div class="artifact-icon">
          <aava-icon
            [iconName]="type.icon"
            [iconSize]="24"
            [iconColor]="type.color"
          ></aava-icon>
        </div>
        <h4>{{ type.name }}</h4>
      </div>
    </div>
  </div>
</app-popup-modal>
