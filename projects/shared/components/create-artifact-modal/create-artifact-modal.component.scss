// Override the modal body padding for a more compact design with equal spacing
:host ::ng-deep .modal-body {
  padding: 1rem 1rem 1rem 1rem !important;
}

// Override the modal header padding for a more sleek design
:host ::ng-deep .modal-header {
  padding: 1rem 1.5rem !important;
}

// Override the modal container for a more sleek design with glassmorphism
:host ::ng-deep .modal-container {
  border-radius: 12px !important;
  border: 1px solid #fff !important;
  background: var(
    --Surface-Fill-Light-Surface-White-8,
    rgba(255, 255, 255, 0.8)
  ) !important;
  /* Surface Blur/Blur 5 */
  backdrop-filter: blur(calc(var(--Blur-Blur---5, 25px) / 2)) !important;
}

.create-modal-content {
  padding: 0;
}

.artifact-types {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 16px;
  max-width: 540px;
  margin: 0 auto;
}

.artifact-type-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 16px;
  border-radius: 6px;
  background: var(--Surface-Fill-Light-Surface-White-10, #fff);
  border: 1px solid #f3f4f6;
  transition: all 0.2s ease;
  min-height: 80px;
  text-align: center;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    border-color: var(--card-color, #6366f1);
  }

  &:focus {
    outline: 2px solid var(--card-color, #6366f1);
    outline-offset: 2px;
  }

  .artifact-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    height: 24px;
  }

  h4 {
    margin: 0;
    color: var(--Global-colors-Neutral-n---500, #6B7280);
    text-align: center;
    font-family: Mulish;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}

@media (max-width: 768px) {
  .create-modal-content {
    padding: 0;
  }

  .artifact-types {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 12px;
  }

  .artifact-type-card {
    padding: 16px 12px;
    min-height: 70px;

    .artifact-icon {
      height: 20px;
      margin-bottom: 6px;
    }

    h4 {
      font-size: 12px;
    }
  }
}
