import { AavaIconComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';

import { PopupModalComponent } from '../popup-modal/popup-modal.component';

interface ArtifactType {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
}

@Component({
  selector: 'app-create-artifact-modal',
  standalone: true,
  imports: [CommonModule, PopupModalComponent, AavaIconComponent],
  templateUrl: './create-artifact-modal.component.html',
  styleUrls: ['./create-artifact-modal.component.scss'],
})
export class CreateArtifactModalComponent {
  @Input() isVisible: boolean = false;
  @Output() modalClosed = new EventEmitter<void>();
  @Output() artifactTypeSelected = new EventEmitter<ArtifactType>();

  artifactTypes: ArtifactType[] = [
    {
      id: 'agents',
      name: 'Agents',
      description:
        'Create a new AI agent with custom capabilities and knowledge',
      icon: 'Bot',
      color: '#8B5CF6',
    },
    {
      id: 'pipeline',
      name: 'Pipeline',
      description: 'Build a workflow pipeline to automate complex processes',
      icon: 'Workflow',
      color: '#3B82F6',
    },
    {
      id: 'tools',
      name: 'Tools',
      description: 'Develop a custom tool for specific tasks and integrations',
      icon: 'Wrench',
      color: '#EF4444',
    },
    {
      id: 'model',
      name: 'Model',
      description: 'Configure and deploy a machine learning model',
      icon: 'Box',
      color: '#10B981',
    },
    {
      id: 'guardrails',
      name: 'Guardrails',
      description: 'Set up safety and compliance guardrails for AI systems',
      icon: 'Shield',
      color: '#F59E0B',
    },
    {
      id: 'knowledgebases',
      name: 'Knowledgebases',
      description: 'Create and manage knowledge repositories for AI training',
      icon: 'BookText',
      color: '#EC4899',
    },
  ];

  onModalClose() {
    this.modalClosed.emit();
  }

  onArtifactTypeClick(artifactType: ArtifactType) {
    this.artifactTypeSelected.emit(artifactType);
    this.modalClosed.emit();
  }
}
