import {
  AavaSelectComponent,
  AavaSelectOptionComponent,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';

export interface SortOption {
  id: string;
  label: string;
  value: string;
}

@Component({
  selector: 'app-sort-filter',
  standalone: true,
  imports: [CommonModule, AavaSelectComponent, AavaSelectOptionComponent],
  templateUrl: './sort-filter.component.html',
  styleUrl: './sort-filter.component.scss',
})
export class SortFilterComponent {
  @Input() selectedSort: string = '';
  @Input() title = 'Sort by';
  @Input() disabled = false;
  @Output() sortChange = new EventEmitter<string>();

  // Sort options
  readonly sortOptions: SortOption[] = [
    { id: '1', label: 'Recently Created', value: 'Recently Created' },
    { id: '2', label: 'Most Viewed', value: 'Most Viewed' },
    { id: '3', label: 'Most Used', value: 'Most Used' },
    { id: '4', label: 'Highest Rated', value: 'Highest Rated' },
    { id: '5', label: 'A-Z', value: 'Alphabetical' },
  ];

  onSortChange(value: string): void {
    this.selectedSort = value;
    this.sortChange.emit(value);
  }

  trackByOptionId(index: number, option: SortOption): string {
    return option.id;
  }
}
