# Sort Filter Component

A reusable Angular component that provides a dropdown selector for sorting options.

## Features

- Dropdown select with predefined sort options
- Recently Created
- Most Viewed
- Most Used
- Highest Rated
- Emits sort change events
- Customizable title and disabled state

## Usage

```typescript
import { SortFilterComponent } from './sort-filter.component';

@Component({
  // ...
  imports: [SortFilterComponent],
})
export class YourComponent {
  selectedSort = '';

  onSortChange(sortValue: string) {
    console.log('Sort changed to:', sortValue);
  }
}
```

```html
<app-sort-filter
  [selectedSort]="selectedSort"
  [title]="'Sort by'"
  [disabled]="false"
  (sortChange)="onSortChange($event)"
>
</app-sort-filter>
```

## Inputs

- `selectedSort` (string): Currently selected sort value
- `title` (string): Title displayed above the dropdown (default: 'Sort by')
- `disabled` (boolean): Whether the dropdown is disabled (default: false)

## Outputs

- `sortChange` (EventEmitter<string>): Emitted when sort selection changes
