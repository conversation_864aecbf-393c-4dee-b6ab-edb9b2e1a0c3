<div class="sort-filter">
  <!-- Title -->
  <div class="filter-title">
    <h3>{{ title }}</h3>
  </div>

  <!-- Sort Options -->
  <div class="filter-options">
    <div class="form-field">
      <aava-select
        size="sm"
        [label]="title + ' *'"
        [placeholder]="'Select sort option'"
        [disabled]="disabled"
        (selectionChange)="onSortChange($event)"
      >
        <aava-select-option
          *ngFor="let option of sortOptions; trackBy: trackByOptionId"
          [value]="option.value"
        >
          {{ option.label }}
        </aava-select-option>
      </aava-select>
    </div>
  </div>
</div>
