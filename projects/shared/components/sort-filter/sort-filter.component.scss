.sort-filter {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: var(--color-background-primary, #ffffff);
  border-radius: 8px;
  max-width: 14.25rem; // 228px converted to rem
  width: 14.25rem; // 228px converted to rem

  .filter-title {
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--color-text-primary, #1f2937);
      line-height: 1.5;
    }
  }

  .filter-options {
    .form-field {
      width: 100%;
    }
  }
}

// Dark mode support
[data-theme='dark'] {
  .sort-filter {
    background: var(--neutral-50, #1f2937);

    .filter-title h3 {
      color: var(--neutral-900, #f9fafb);
    }
  }
}
