import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SortFilterComponent } from './sort-filter.component';

describe('SortFilterComponent', () => {
  let component: SortFilterComponent;
  let fixture: ComponentFixture<SortFilterComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SortFilterComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(SortFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default sort options', () => {
    expect(component.sortOptions.length).toBe(4);
    expect(component.sortOptions[0].label).toBe('Recently Created');
    expect(component.sortOptions[1].label).toBe('Most Viewed');
    expect(component.sortOptions[2].label).toBe('Most Used');
    expect(component.sortOptions[3].label).toBe('Highest Rated');
  });

  it('should emit sortChange when onSortChange is called', () => {
    spyOn(component.sortChange, 'emit');
    const testValue = 'most-viewed';

    component.onSortChange(testValue);

    expect(component.selectedSort).toBe(testValue);
    expect(component.sortChange.emit).toHaveBeenCalledWith(testValue);
  });
});
