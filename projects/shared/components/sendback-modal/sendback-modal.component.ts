import {
  AavaButtonComponent,
  AavaDialogService,
  AavaTextareaComponent,
} from '@aava/play-core';
import { AavaTextboxComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-sendback-artifact-modal',
  standalone: true,
  imports: [
    AavaButtonComponent,
    AavaTextboxComponent,
    AavaTextareaComponent,
    FormsModule,
    CommonModule,
  ],
  template: `
    <div class="modal-container">
      <div dialog-header class="modal-header flex-row justify-content-center">
        <div class="body-bold-default-400 header-content">
          Confirm Send back
        </div>
      </div>

      <div dialog-body>
        <div class="modal-body">
          <div class="text-box-container">
            <aava-textbox
              label="What went well"
              variant="default"
              placeholder="Enter your feedback"
              size="sm"
              [(ngModel)]="whatWentGood"
            ></aava-textbox>
          </div>
          <div class="text-box-container">
            <aava-textbox
              label="What didn't work"
              variant="default"
              placeholder="Enter your feedback"
              size="sm"
              [(ngModel)]="whatWentWrong"
            ></aava-textbox>
          </div>
          <div class="text-box-container">
            <aava-textarea
              label="Suggestion for improvements"
              variant="default"
              placeholder="Enter your feedback"
              size="sm"
              [(ngModel)]="improvements"
            ></aava-textarea>
          </div>
        </div>
      </div>

      <div dialog-footer class="dialog-footer">
        <aava-button
          label="Cancel"
          variant="secondary"
          size="sm"
          (userClick)="cancel()"
        ></aava-button>

        <aava-button
          label="Send back"
          variant="primary"
          size="sm"
          (userClick)="reject()"
        ></aava-button>
      </div>
    </div>
  `,
  styles: [
    `
      .modal-header {
        display: flex;
        padding: 0;
      }

      .modal-container {
        padding: 1.5rem;
      }

      .modal-header {
        color: #3b3f46;
      }

      .header-content {
        font-weight: 700;
      }

      .modal-body {
        padding: 1rem 0;
      }

      .text-box-container {
        margin: 0.5rem 0;
      }

      .dialog-footer {
        display: flex;
        width: 100%;
        gap: 0.75rem;
        justify-content: flex-end;
      }
    `,
  ],
})
export class SendbackArtifactModalComponent {
  @Output() closed = new EventEmitter<any | null>();
  whatWentWrong: string = '';
  whatWentGood: string = '';
  improvements: string = '';

  constructor(private dialogService: AavaDialogService) {}

  cancel() {
    this.closed.emit(null);
  }

  reject() {
    this.closed.emit({
      whatWentWrong: this.whatWentWrong,
      whatWentGood: this.whatWentGood,
      improvements: this.improvements,
    });
  }
}
