import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaDialogService, AavaToastService } from '@aava/play-core';

interface ListItem {
  id: string;
  name: string;
  short_description: string;
  owner?: string;
  realm?: string;
  created_at?: string;
}

@Component({
  selector: 'aava-list-modal',
  templateUrl: './list-modal.component.html',
  styleUrls: ['./list-modal.component.scss'],
  imports: [CommonModule],
  standalone: true,
})
export class ListModalComponent {
  // Array of list items with name and short_description

  constructor(private dialogService: AavaDialogService) {}
  private toastService = inject(AavaToastService);

  listItems: ListItem[] = [
    {
      id: 'l_1',
      name: 'My Test List',
      short_description: 'Artifacts for testing',
      owner: 'u_1',
      realm: 'h2',
      created_at: '2025-08-21',
    },
    {
      id: 'l_2',
      name: 'Product Requirements',
      short_description: 'Documents for product planning',
      owner: 'u_2',
      realm: 'h2',
      created_at: '2025-09-01',
    },
    {
      id: 'l_3',
      name: 'Design Assets',
      short_description: 'UI/UX resources and mockups',
      owner: 'u_3',
      realm: 'h2',
      created_at: '2025-09-05',
    },
  ];

  // Method to handle list item clicks
  onItemClick(item: ListItem): void {
    console.log('List item clicked:', item);
    // Display success toast message
    this.toastService.success({
      title: 'Successfully added!',
      message: 'Artifact added to list successfully!',
      duration: 4000,
      customWidth: '300px',
      design: 'modern',
      size: 'small',
      showCloseButton: false,
    });

    this.dialogService.close();
  }
}
