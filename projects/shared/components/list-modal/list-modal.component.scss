.list-modal {
  margin: 0 auto;

  &__header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--color-border-subtle);

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--color-text-primary);
    }
  }

  &__content {
    padding: 16px 24px;
  }
}

.list-items {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--color-border-subtle);
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--color-surface-subtle-hover);
  }

  &:last-child {
    border-bottom: none;
  }

  &__content {
    flex: 1;
  }

  &__name {
    margin: 0 0 4px;
    font-size: 16px;
    font-weight: 500;
    color: var(--color-text-primary);
  }

  &__description {
    margin: 0;
    font-size: 14px;
    color: var(--color-text-secondary);
  }

  /* Button styles removed as per requirements */
}
