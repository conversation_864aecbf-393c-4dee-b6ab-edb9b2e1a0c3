.create-card {
  background: var(--card-bg-color, #f1f5f9);
  color: var(--text-color, #64748b);
  height: var(--card-height, 100%) !important;
  min-height: var(--card-height, 208px);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue',
    Arial, sans-serif;

  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.create-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
}

.create-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--icon-wrapper-bg-color, rgba(255, 255, 255, 0.9));
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.plus-icon {
  font-size: 2.5rem;
  font-weight: 300;
  color: var(--icon-color, #3b82f6);
  line-height: 1;
  user-select: none;
}

.create-title {
  font-size: 1.25rem;
  font-weight: 500;
  text-align: center;
  margin: 0;
  color: var(--text-color, #64748b);
  letter-spacing: 0.025em;
}
