<aava-card-content>
  <article
    class="create-card"
    [class.disabled]="data().disabled"
    [class.interactive]="isInteractive()"
    [attr.aria-label]="ariaLabel()"
    [attr.aria-disabled]="data().disabled"
    role="button"
    tabindex="0"
    (click)="onCardClick()"
    (keydown)="onKeyDown($event)"
    [ngStyle]="safeStyles()"
  >
    <div class="create-icon-container">
      <div class="create-icon">
        <span class="plus-icon">+</span>
      </div>
    </div>
    <p class="create-title">{{ data().title }}</p>
  </article>
</aava-card-content>
