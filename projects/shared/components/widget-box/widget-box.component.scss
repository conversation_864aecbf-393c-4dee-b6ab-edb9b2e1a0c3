/* Widget Box Component Styles for AI Response Display */
.widget-box {
  display: block;
  /* Regular surface background instead of glass morphism */
  background-color: var(--color-background-primary, #ffffff);
  border-radius: 12px;
  border: 1px solid var(--color-border-default, rgba(209, 213, 219, 0.3));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;

  // Remove old theme variants since theme option was removed

  // Size variants
  &--compact {
    padding: 12px;
    margin: 4px 0;
    border-radius: 10px;
  }

  // Header section
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid
      var(--color-border-subtle, rgba(255, 255, 255, 0.1));
  }

  &__timestamp {
    font-size: 12px;
    color: var(--color-text-secondary, rgba(255, 255, 255, 0.8));
    font-weight: 500;
  }

  &__actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  &__action-btn {
    background: transparent;
    border: 1px solid var(--color-border-subtle, rgba(255, 255, 255, 0.2));
    border-radius: 6px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 14px;
    color: var(--color-text-primary, rgba(255, 255, 255, 0.9));
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 28px;

    &:hover:not(:disabled) {
      background: var(--color-surface-subtle-hover, rgba(255, 255, 255, 0.1));
      border-color: var(--color-border-default, rgba(255, 255, 255, 0.3));
      transform: translateY(-1px);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  &__copy-btn {
    &:hover:not(:disabled) {
      background: var(--success-800, #f0fff4);
      border-color: var(--success-500, #38a169);
    }
  }

  &__expand-btn {
    &:hover:not(:disabled) {
      background: var(--info-800, #ebf8ff);
      border-color: var(--info-500, #3182ce);
    }
  }

  // Content section
  &__content {
    line-height: 1.6;
    word-wrap: break-word;
    overflow-wrap: break-word;
    color: var(--color-text-primary, rgba(255, 255, 255, 0.9));

    &--text {
      white-space: pre-wrap;
      font-family: var(--font-family-body, 'Inter', sans-serif);
    }

    &--markdown {
      ::ng-deep {
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin-top: 0;
          margin-bottom: 8px;
          font-weight: 600;
          color: var(--color-text-primary, rgba(255, 255, 255, 0.95));
        }

        p {
          margin: 8px 0;
          color: var(--color-text-secondary, rgba(255, 255, 255, 0.85));
        }

        code {
          background: var(
            --color-surface-subtle-hover,
            rgba(255, 255, 255, 0.1)
          );
          color: var(--color-text-primary, rgba(255, 255, 255, 0.95));
          padding: 2px 4px;
          border-radius: 3px;
          font-family: var(--font-family-mono, 'Monaco', monospace);
          font-size: 0.9em;
        }

        pre {
          background: var(--neutral-100, rgba(0, 0, 0, 0.3));
          color: var(--color-text-primary, rgba(255, 255, 255, 0.9));
          padding: 12px;
          border-radius: 4px;
          overflow-x: auto;
          margin: 8px 0;

          code {
            background: transparent;
            padding: 0;
          }
        }

        ul,
        ol {
          margin: 8px 0;
          padding-left: 20px;
        }

        blockquote {
          border-left: 3px solid
            var(--color-border-default, rgba(255, 255, 255, 0.3));
          padding-left: 12px;
          margin: 8px 0;
          font-style: italic;
          color: var(--color-text-secondary, rgba(255, 255, 255, 0.8));
        }
      }
    }

    &--html {
      ::ng-deep {
        * {
          max-width: 100%;
        }
      }
    }

    &--json {
      pre {
        background: var(--neutral-100, rgba(0, 0, 0, 0.3));
        color: var(--color-text-primary, rgba(255, 255, 255, 0.9));
        padding: 12px;
        border-radius: 4px;
        overflow-x: auto;
        margin: 0;
        font-family: var(--font-family-mono, 'Monaco', monospace);
        font-size: 0.9em;
        white-space: pre-wrap;
      }
    }

    &--search-results {
      .widget-box__search-results {
        .widget-box__search-meta {
          margin-bottom: 16px;
          padding: 12px;
          background: transparent;
          border-radius: 8px;
          border: 1px solid var(--color-border-subtle, rgba(255, 255, 255, 0.1));

          .search-meta__header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;

            h4 {
              margin: 0;
              font-size: 16px;
              font-weight: 600;
              color: var(--color-text-primary, rgba(255, 255, 255, 0.95));
            }
            .search-meta__stats {
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              gap: 4px;
              font-size: 12px;
              color: var(--color-text-secondary, rgba(255, 255, 255, 0.7));
            }
          }

          .search-meta__query {
            font-size: 14px;
            color: var(--color-text-secondary, rgba(255, 255, 255, 0.8));
          }
        }

        .widget-box__results-list {
          display: flex;
          flex-direction: column;
          gap: 12px;
          max-height: 400px;
          overflow-y: auto;

          .search-result-item {
            padding: 12px;
            background: transparent;
            border: 1px solid
              var(--color-border-subtle, rgba(255, 255, 255, 0.1));
            border-radius: 8px;
            transition: all 0.2s ease;

            &:hover {
              background: var(
                --color-surface-subtle-hover,
                rgba(255, 255, 255, 0.05)
              );
              border-color: var(
                --color-border-default,
                rgba(255, 255, 255, 0.2)
              );
              transform: translateY(-1px);
            }

            .search-result__header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 8px;

              .search-result__name {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: var(--color-text-primary, rgba(255, 255, 255, 0.95));
                flex: 1;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                cursor: help;

                ::ng-deep em {
                  background: rgba(var(--effect-color-secondary), 0.15);
                  padding: 2px 4px;
                  border-radius: 3px;
                  font-style: normal;
                  font-weight: 700;
                }
              }

              .search-result__score {
                font-size: 12px;
                color: var(--color-text-secondary, rgba(255, 255, 255, 0.7));
                background: var(
                  --color-surface-subtle-hover,
                  rgba(255, 255, 255, 0.1)
                );
                padding: 2px 6px;
                border-radius: 12px;
                white-space: nowrap;
              }
            }

            .search-result__meta {
              display: flex;
              flex-wrap: wrap;
              gap: 6px;
              margin-bottom: 8px;

              .meta-tag {
                font-size: 11px;
                padding: 2px 8px;
                background: var(
                  --color-surface-subtle-hover,
                  rgba(255, 255, 255, 0.1)
                );
                border-radius: 12px;
                color: var(--color-text-secondary, rgba(255, 255, 255, 0.7));
                text-transform: capitalize;
              }
            }

            .search-result__description {
              margin-bottom: 8px;

              p {
                margin: 0;
                font-size: 14px;
                line-height: 1.5;
                color: var(--color-text-secondary, rgba(255, 255, 255, 0.85));

                ::ng-deep em {
                  background: rgba(var(--effect-color-secondary), 0.15);
                  padding: 2px 4px;
                  border-radius: 3px;
                  font-style: normal;
                  font-weight: 600;
                }
              }
            }

            .search-result__stats {
              display: flex;
              gap: 12px;
              margin-bottom: 8px;
              font-size: 12px;
              color: var(--color-text-placeholder, rgba(255, 255, 255, 0.6));
            }

            .search-result__actions {
              display: flex;
              gap: 8px;
              justify-content: flex-end;

              .result-action-btn {
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                border: 1px solid transparent;

                &--primary {
                  background: rgba(var(--effect-color-primary), 0.1);
                  color: var(--color-brand-primary, #3b82f6);
                  border-color: rgba(var(--effect-color-primary), 0.2);

                  &:hover {
                    background: rgba(var(--effect-color-primary), 0.2);
                    border-color: rgba(var(--effect-color-primary), 0.3);
                    transform: translateY(-1px);
                  }
                }

                &--secondary {
                  background: rgba(var(--effect-color-neutral), 0.1);
                  color: var(--color-text-secondary, #6b7280);
                  border-color: rgba(var(--effect-color-neutral), 0.2);

                  &:hover {
                    background: rgba(var(--effect-color-neutral), 0.2);
                    border-color: rgba(var(--effect-color-neutral), 0.3);
                    transform: translateY(-1px);
                  }
                }
              }
            }
          }
        }
      }
    }

    &--search-result {
      .widget-box__search-result {
        .search-result-item {
          padding: 12px;
          background: transparent;
          border: 1px solid var(--color-border-subtle, rgba(255, 255, 255, 0.1));
          border-radius: 8px;
          transition: all 0.2s ease;

          &:hover {
            background: var(
              --color-surface-subtle-hover,
              rgba(255, 255, 255, 0.05)
            );
            border-color: var(--color-border-default, rgba(255, 255, 255, 0.2));
            transform: translateY(-1px);
          }
          .search-result__header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;

            .search-result__name {
              margin: 0;
              font-size: 16px;
              font-weight: 600;
              color: var(--color-text-primary, rgba(255, 255, 255, 0.95));
              flex: 1;
              max-width: 200px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              cursor: help;

              ::ng-deep em {
                background: rgba(var(--effect-color-secondary), 0.15);
                padding: 2px 4px;
                border-radius: 3px;
                font-style: normal;
                font-weight: 700;
              }
            }

            .search-result__score {
              font-size: 12px;
              color: var(--color-text-secondary, rgba(255, 255, 255, 0.7));
              background: var(
                --color-surface-subtle-hover,
                rgba(255, 255, 255, 0.1)
              );
              padding: 2px 6px;
              border-radius: 12px;
              white-space: nowrap;
            }
          }

          .search-result__meta {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 8px;

            .meta-tag {
              font-size: 11px;
              padding: 2px 8px;
              background: var(
                --color-surface-subtle-hover,
                rgba(255, 255, 255, 0.1)
              );
              border-radius: 12px;
              color: var(--color-text-secondary, rgba(255, 255, 255, 0.7));
              text-transform: capitalize;
            }
          }

          .search-result__description {
            margin-bottom: 8px;

            p {
              margin: 0;
              font-size: 14px;
              line-height: 1.5;
              color: var(--color-text-secondary, rgba(255, 255, 255, 0.85));
              ::ng-deep em {
                background: rgba(255, 215, 0, 0.3);
                padding: 2px 4px;
                border-radius: 3px;
                font-style: normal;
                font-weight: 600;
              }
            }
          }

          .search-result__stats {
            display: flex;
            gap: 12px;
            margin-bottom: 8px;
            font-size: 12px;
            color: var(--color-text-placeholder, rgba(255, 255, 255, 0.6));
          }

          .search-result__actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;

            .result-action-btn {
              padding: 6px 12px;
              border-radius: 6px;
              font-size: 12px;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.2s ease;
              border: 1px solid transparent;

              &--primary {
                background: rgba(var(--effect-color-primary), 0.1);
                color: var(--color-brand-primary, #3b82f6);
                border-color: rgba(var(--effect-color-primary), 0.2);

                &:hover {
                  background: rgba(var(--effect-color-primary), 0.2);
                  border-color: rgba(var(--effect-color-primary), 0.3);
                  transform: translateY(-1px);
                }
              }

              &--secondary {
                background: rgba(var(--effect-color-neutral), 0.1);
                color: var(--color-text-secondary, #6b7280);
                border-color: rgba(var(--effect-color-neutral), 0.2);

                &:hover {
                  background: rgba(var(--effect-color-neutral), 0.2);
                  border-color: rgba(var(--effect-color-neutral), 0.3);
                  transform: translateY(-1px);
                }
              }
            }
          }
        }
      }
    }
  }

  // Loading state
  &__loading {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 20px 0;
    justify-content: center;
    color: var(--color-text-secondary, rgba(255, 255, 255, 0.7));
  }

  &__spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--color-border-subtle, rgba(255, 255, 255, 0.2));
    border-top: 2px solid var(--color-text-secondary, rgba(255, 255, 255, 0.7));
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  // Footer section
  &__footer {
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid var(--color-border-subtle, rgba(255, 255, 255, 0.1));
    text-align: center;
  }

  &__expand-btn--footer {
    background: transparent;
    border: 1px solid var(--color-border-subtle, rgba(255, 255, 255, 0.2));
    color: var(--color-text-primary, rgba(255, 255, 255, 0.9));
    cursor: pointer;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      background: var(--color-surface-subtle-hover, rgba(255, 255, 255, 0.1));
      border-color: var(--color-border-default, rgba(255, 255, 255, 0.3));
      transform: translateY(-1px);
    }
  }

  // Dark theme overrides
  &--dark {
    background-color: var(--color-background-primary, #2d3036);
    border-color: var(--color-border-default, #4a5568);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

    .widget-box__header {
      border-bottom-color: var(--color-border-subtle, #4a5568);
    }

    .widget-box__timestamp {
      color: var(--color-text-secondary, #a0aec0);
    }

    .widget-box__action-btn {
      border-color: var(--color-border-default, #4a5568);
      color: var(--color-text-primary, #f7fafc);

      &:hover:not(:disabled) {
        background: var(--color-surface-subtle-hover, #4a5568);
        border-color: var(--color-border-interactive, #718096);
      }
    }

    .widget-box__content {
      &--markdown ::ng-deep {
        code {
          background: var(--neutral-100, #2d3748);
          color: var(--color-text-primary, #f7fafc);
        }

        pre {
          background: var(--neutral-100, #2d3748);
          color: var(--color-text-primary, #f7fafc);
        }
      }

      &--json pre {
        background: var(--neutral-100, #2d3748);
        color: var(--color-text-primary, #f7fafc);
      }
    }

    .widget-box__loading {
      color: var(--color-text-secondary, #a0aec0);
    }

    .widget-box__footer {
      border-top-color: var(--color-border-subtle, #4a5568);
    }

    .widget-box__expand-btn--footer {
      color: var(--color-brand-primary, #63b3ed);

      &:hover {
        background: var(--color-surface-subtle-hover, #4a5568);
      }
    }
  }
}

// Animations
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive design
@media (max-width: 768px) {
  .widget-box {
    margin: 4px 0;
    padding: 12px;

    &--detailed {
      padding: 16px;
    }

    &__header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    &__actions {
      align-self: flex-end;
    }
  }
}
