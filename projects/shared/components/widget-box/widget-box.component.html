<!-- Widget Box Component Template for AI Response Display -->
<div
  class="widget-box"
  [ngClass]="{
    'widget-box--compact': variant === 'compact',
    'widget-box--detailed': variant === 'detailed',
  }"
  [style.max-height]="maxHeight"
  *ngIf="aiResponse && (displayText || searchResult)"
>
  <!-- Header with timestamp and actions -->
  <div
    class="widget-box__header"
    *ngIf="
      (variant === 'detailed' || showTimestamp || showCopyButton) &&
      responseType !== 'search-result'
    "
  >
    <div
      class="widget-box__timestamp"
      *ngIf="showTimestamp && aiResponse?.timestamp"
    >
      {{ aiResponse.timestamp | date: 'short' }}
    </div>
    <div class="widget-box__actions">
      <button
        *ngIf="shouldShowExpandButton"
        class="widget-box__action-btn widget-box__expand-btn"
        (click)="toggleExpand()"
        [title]="isExpanded ? 'Show less' : 'Show more'"
      >
        {{ isExpanded ? '▲' : '▼' }}
      </button>
    </div>
  </div>

  <!-- Content area -->
  <div
    class="widget-box__content"
    [ngClass]="'widget-box__content--' + responseType"
  >
    <!-- Loading state -->
    <div *ngIf="aiResponse?.isLoading" class="widget-box__loading">
      <div class="widget-box__spinner"></div>
      <span>AI is thinking...</span>
    </div>

    <!-- Search Result -->
    <div
      *ngIf="
        !aiResponse?.isLoading &&
        responseType === 'search-result' &&
        searchResult
      "
      class="widget-box__search-result"
    >
      <div class="search-result-item">
        <div class="search-result__header">
          <h5
            class="search-result__name"
            [innerHTML]="
              getHighlightedText(
                searchResult.highlights.name,
                searchResult.name
              )
            "
            [title]="searchResult.name"
          ></h5>
          <div class="search-result__score">
            Score: {{ searchResult.score | number: '1.1-1' }}
          </div>
        </div>

        <div class="search-result__meta">
          <span class="meta-tag">{{ searchResult.entity }}</span>
          <span class="meta-tag">{{ searchResult.organization }}</span>
          <span class="meta-tag">{{ searchResult.domain }}</span>
          <span class="meta-tag">{{ searchResult.team }}</span>
        </div>

        <div class="search-result__description">
          <p
            [innerHTML]="
              getHighlightedText(
                searchResult.highlights.short_description,
                searchResult.short_description
              )
            "
          ></p>
        </div>

        <div class="search-result__stats">
          <span><strong>Views:</strong> {{ searchResult.views }}</span>
          <span
            ><strong>Executions:</strong> {{ searchResult.executions }}</span
          >
          <span
            ><strong>Created:</strong>
            {{ searchResult.created_at | date: 'short' }}</span
          >
        </div>

        <div class="search-result__actions">
          <button
            class="result-action-btn result-action-btn--primary"
            (click)="navigateToMarketplace(searchResult)"
          >
            Show More
          </button>
        </div>
      </div>
    </div>

    <!-- Text content -->
    <div
      *ngIf="!aiResponse?.isLoading && responseType === 'text'"
      class="widget-box__text"
    >
      {{ truncatedText }}
    </div>

    <!-- Markdown content -->
    <div
      *ngIf="!aiResponse?.isLoading && responseType === 'markdown'"
      class="widget-box__markdown"
    >
      <markdown [data]="truncatedText"></markdown>
    </div>

    <!-- HTML content -->
    <div
      *ngIf="!aiResponse?.isLoading && responseType === 'html'"
      class="widget-box__html"
      [innerHTML]="truncatedText"
    ></div>

    <!-- JSON content -->
    <div
      *ngIf="!aiResponse?.isLoading && responseType === 'json'"
      class="widget-box__json"
    >
      <pre>{{ truncatedText }}</pre>
    </div>
  </div>
</div>
