import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { Router } from '@angular/router';
import { MarkdownModule } from 'ngx-markdown';

export interface SearchResult {
  id: number;
  name: string;
  organization: string;
  domain: string;
  project: string;
  team: string;
  views: string;
  executions: string;
  status: string | null;
  score: number;
  highlights: {
    name?: string[];
    short_description?: string[];
    long_description?: string[];
  };
  entity: string;
  short_description: string;
  long_description: string;
  created_by: string;
  created_at: string;
  modified_by: string;
  modified_at: string;
  efficiency_rating: string;
  approved_by: string | null;
  approved_at: string | null;
  is_deleted: string | null;
}

export interface AIResponseData {
  text?: string;
  type?: 'text' | 'markdown' | 'html' | 'json' | 'search-result';
  isLoading?: boolean;
  showActions?: boolean;
  timestamp?: Date;
  searchResult?: SearchResult;
}

@Component({
  selector: 'app-widget-box',
  standalone: true,
  imports: [CommonModule, MarkdownModule],
  templateUrl: './widget-box.component.html',
  styleUrls: ['./widget-box.component.scss'],
})
export class WidgetBoxComponent implements OnInit, OnChanges {
  @Input() aiResponse: AIResponseData | null = null;
  @Input() variant: 'default' | 'compact' | 'detailed' = 'default';
  @Input() showTimestamp: boolean = false;
  @Input() showCopyButton: boolean = true;
  @Input() maxHeight: string = 'auto';

  @Output() actionClicked = new EventEmitter<string>();
  @Output() textCopied = new EventEmitter<string>();

  // Internal state
  isExpanded: boolean = false;
  isCopying: boolean = false;
  displayText: string = '';
  searchResult: SearchResult | null = null;

  constructor(private router: Router) {}

  ngOnInit() {
    this.updateDisplayText();
    this.updateSearchData();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['aiResponse']) {
      this.updateDisplayText();
      this.updateSearchData();
    }
  }

  private updateDisplayText() {
    if (this.aiResponse) {
      this.displayText = this.aiResponse.text || '';
    } else {
      this.displayText = '';
    }
  }

  private updateSearchData() {
    if (this.aiResponse?.type === 'search-result') {
      this.searchResult = this.aiResponse.searchResult || null;
    } else {
      this.searchResult = null;
    }
  }

  onCopyText() {
    if (this.displayText) {
      this.isCopying = true;
      navigator.clipboard
        .writeText(this.displayText)
        .then(() => {
          this.textCopied.emit(this.displayText);
          setTimeout(() => {
            this.isCopying = false;
          }, 1000);
        })
        .catch(err => {
          console.error('Failed to copy text:', err);
          this.isCopying = false;
        });
    }
  }

  onActionClick(action: string) {
    this.actionClicked.emit(action);
  }

  toggleExpand() {
    this.isExpanded = !this.isExpanded;
  }

  get shouldShowExpandButton(): boolean {
    return this.displayText.length > 500 && this.variant !== 'compact';
  }

  get truncatedText(): string {
    if (this.isExpanded || this.displayText.length <= 500) {
      return this.displayText;
    }
    return `${this.displayText.substring(0, 500)}...`;
  }

  get responseType(): string {
    return this.aiResponse?.type || 'text';
  }

  // Get highlighted text with fallback to original text
  getHighlightedText(
    highlights: string[] | undefined,
    originalText: string
  ): string {
    if (highlights && highlights.length > 0) {
      return highlights[0];
    }
    return originalText;
  }

  // Handle search result actions
  onResultAction(action: string, result: SearchResult) {
    this.actionClicked.emit(`${action}:${result.id}`);
  }

  // Navigate to marketplace route
  navigateToMarketplace(result: SearchResult) {
    // Navigate to marketplace with the search result ID
    this.router.navigate(['/marketplace']);
  }
}
