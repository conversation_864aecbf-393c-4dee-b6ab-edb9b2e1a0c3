import {
  AavaButtonComponent,
  AavaCheckboxComponent,
  AavaDialogService,
  AavaDropdownComponent,
  AavaIconComponent,
  AavaTextareaComponent,
  DropdownOption,
} from '@aava/play-core';
import { AavaTextboxComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { SettingsService } from 'projects/console/src/app/services/settings.service';
import { TokenStorageService } from 'projects/shared/auth/services/token-storage.service';
import { ToastWrapperService } from 'projects/shared/services/toast-wrapper.service';

@Component({
  selector: 'app-create-configuration-modal',
  standalone: true,
  imports: [
    AavaButtonComponent,
    AavaTextboxComponent,
    AavaTextareaComponent,
    AavaDropdownComponent,
    AavaCheckboxComponent,
    AavaIconComponent,
    FormsModule,
    CommonModule,
  ],
  templateUrl: './create-configuration-modal.component.html',
  styleUrls: ['./create-configuration-modal.component.scss'],
})
export class CreateConfigurationModalComponent implements OnInit {
  @Output() closed = new EventEmitter<any | null>();
  @Input() data: any;
  @Input() isEditMode: boolean = false;
  @Input() categories: any[] = [];
  @Input() applications: any[] = [];
  applicationName: string = '';
  description: string = '';
  value: string = '';
  isEncrypted: boolean = false;
  isSensitive: boolean = false;
  useExternalSecret: boolean = false;
  reasonForChange: string = '';
  selectedDatatype: any = { name: 'String', value: 'STRING' };
  selectedApplication: any;
  selectedCategory: any;
  configKey: string = '';
  version: number = 1;
  selectedSecretProvider: any = { name: 'None - Local Storage', value: null };
  secretKey: string | null = null;
  basicDatatypeOptions: DropdownOption[] = [
    { name: 'String', value: 'STRING' },
    { name: 'Boolean', value: 'BOOLEAN' },
    { name: 'Integer', value: 'INTEGER' },
    { name: 'JSON', value: 'JSON' },
  ];
  secretProviderOptions: DropdownOption[] = [
    { name: 'None - Local Storage', value: 'null' },
    { name: 'Azure Key Vault', value: 'AZURE' },
    { name: 'AWS Secret Manager', value: 'AWS' },
    { name: 'Google Cloud Secret Manager', value: 'GCP' },
  ];

  constructor(
    private dialogService: AavaDialogService,
    private tokenService: TokenStorageService,
    private settingService: SettingsService,
    private toastService: ToastWrapperService
  ) {}

  ngOnInit() {
    console.log(this.isFormValid);
    if (this.data) {
      this.loadConfigData(this.data);
    }
  }

  get applicationsOptions(): DropdownOption[] {
    return this.applications.map(app => ({
      name: app.name,
      value: app.id,
    }));
  }

  get categoriesOptions(): DropdownOption[] {
    return this.categories.map(category => ({
      name: category.name,
      value: category.id,
    }));
  }

  get isFormValid() {
    return (
      !!this.selectedApplication?.value &&
      !!this.selectedCategory?.value &&
      !!this.selectedDatatype?.value &&
      this.value?.trim().length > 0 &&
      this.configKey?.trim().length > 0
    );
  }

  get enableEdit() {
    if (this.useExternalSecret && this.selectedSecretProvider.value !== null) {
      return this.secretKey !== null && this.secretKey?.trim().length > 0;
    }

    return true;
  }

  loadConfigData(data: any) {
    this.applicationName = data.applicationName || '';
    this.description = data.description || '';
    this.value = data.configValue || '';
    this.configKey = data.configKey || '';
    this.isEncrypted = data.isEncrypted ?? false;
    this.isSensitive = data.isSensitive ?? false;

    this.selectedDatatype = this.basicDatatypeOptions.find(
      option => option.value === data?.dataType
    );
    this.selectedApplication = this.applicationsOptions.find(
      option => option.name === data?.applicationName
    );
    this.selectedCategory = this.categoriesOptions.find(
      option => option.name === data?.categoryName
    );
  }

  cancel() {
    this.closed.emit(null);
  }

  save() {
    let config: any;

    if (this.data) {
      config = {
        description: this.description,
        configValue: this.value,
        configKey: this.configKey,
        dataType: this.selectedDatatype?.value ?? this.data.dataType,
        isEncrypted: this.isEncrypted,
        isSensitive: this.isSensitive,
        changeReason: this.reasonForChange,
        updatedBy: this.tokenService.getDaUsername() || 'admin',
      };
    } else {
      config = {
        configKey: this.configKey ?? null,
        configValue: this.value,
        dataType: this.selectedDatatype?.value ?? null,
        isEncrypted: this.isEncrypted,
        isSensitive: this.isSensitive,
        description: this.description,
        createdBy: this.tokenService.getDaUsername() || 'admin',
        secretProvider: this.selectedSecretProvider?.value ?? null,
        secretKey: this.secretKey,
      };
    }

    this.settingService
      .createConfig(
        this.selectedApplication.name,
        this.selectedCategory.name,
        config
      )
      .subscribe({
        next: (res: any) => {
          this.toastService.success(
            res?.message || 'Configuration created successfully!'
          );
          this.closed.emit({ action: 'save' });
        },
        error: (err: any) => {
          this.toastService.error(
            err?.error?.message || 'Something went wrong, please try again!'
          );
        },
      });
  }

  handleApplicationSelection(event: any) {
    console.log('selected application: ', event);
    this.selectedApplication = event?.selectedOptions[0];
  }

  handleCategorySelection(event: any) {
    console.log('selected data type: ', event);
    this.selectedCategory = event?.selectedOptions[0];
  }

  handleDataTypeSelection(event: any) {
    console.log('selected data type: ', event);
    this.selectedDatatype = event?.selectedOptions[0];
  }

  handleSecretProviderSelection(event: any) {
    console.log('selected provider: ', event);
    this.selectedSecretProvider = event?.selectedOptions[0];
  }

  handleIsEncrypted(event: boolean) {
    console.log('is encrypted checked: ', event);
    this.isEncrypted = event;
  }

  handleIsSensitive(event: boolean) {
    console.log('is sensitive checked: ', event);
    this.isSensitive = event;
  }

  handleUseExternalSecret(event: boolean) {
    console.log('is external secret checked: ', event);
    this.useExternalSecret = event;
    this.secretKey = null;
  }
}
