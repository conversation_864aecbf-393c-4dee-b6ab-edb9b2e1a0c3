  .modal-container {
      padding: 1.5rem;

      .modal-header {
          display: flex;
          padding: 0;
          color: #3b3f46;

          .header-content {
              font-weight: 700;
          }
      }

      .modal-body-container {
          overflow-y: auto;
          overflow-x: hidden;
          max-height: 60vh;

          .modal-body {
              padding: 1rem 0;

              .form-title{
                display: flex;
                gap: 0.5rem;
              }

              .basic-information {
                  display: flex;
                  margin: 1rem;
                  gap: 0.5rem;

                  .basic-info-textbox {
                      width: 100%;
                  }

                  .basic-info-dropdown {
                      display: flex;
                      gap: 1rem;
                  }
              }

              .configuration-value {
                  display: flex;
                  margin: 1rem;

                  .datatype-dropdown-wrapper {
                      display: flex;
                      width: 50%;
                  }
              }

              .security-settings {
                  display: flex;
                  margin: 1rem;
                  gap: 1rem;
              }

              .secret-management {
                  display: flex;
                  margin: 1rem;
                  gap: 0.5rem;

                  .secret-dropdowns {
                      display: flex;
                      gap: 1rem;
                  }

                  .secret-key-textbox {
                      width: 100%;
                  }
              }

              .additional-info {
                  display: flex;
                  gap: 0.5rem;
                  margin: 1rem;
              }
          }
      }

      .modal-body-container::-webkit-scrollbar {
          display: none;
      }

      .modal-body-container {
          scrollbar-width: none;
      }

      .modal-body-container {
          -ms-overflow-style: none;
      }

      .dialog-footer {
          display: flex;
          width: 100%;
          gap: 0.75rem;
      }
  }