<div class="modal-container">
    <div dialog-header class="modal-header flex-row justify-content-start">
        <div class="body-bold-default-400 header-content">
            @if (isEditMode) {
            Edit Configuration
            } @else {
            Create Configuration
            }
        </div>
    </div>

    <div class="modal-body-container" dialog-body>
        <div class="modal-body">
            <div>
                <div class="form-title flex-row align-items-center">
                    <aava-icon iconName="info" iconSize="16"></aava-icon>
                    <div class="body-medium-default-200">Basic Information</div>
                </div>
                <div class="basic-information flex-column">
                    <div class="basic-info-dropdown flex-row">
                        <aava-dropdown label="Application" dropdownTitle="Select Application" [required]="true"
                            [options]="applicationsOptions" [selectedValue]="selectedApplication?.name"
                            (selectionChange)="handleApplicationSelection($event)">
                        </aava-dropdown>
                        <aava-dropdown label="Category" dropdownTitle="Select Category" [required]="true"
                            [options]="categoriesOptions" [selectedValue]="selectedCategory?.name"
                            (selectionChange)="handleCategorySelection($event)">
                        </aava-dropdown>
                    </div>
                    <div class="basic-info-textbox">
                        <aava-textbox label="Configuration Key" [required]="true" variant="default"
                            placeholder="Enter configuration key" size="md" [(ngModel)]="configKey"></aava-textbox>
                    </div>
                </div>
            </div>

            <div>
                <div class="form-title flex-row align-items-center">
                    <aava-icon iconName="info" iconSize="16"></aava-icon>
                    <div class="body-medium-default-200">Configuration View</div>
                </div>
                <div class="configuration-value flex-row">
                    <div class="datatype-dropdown-wrapper">
                        <aava-textbox label="Value" [required]="true" variant="default"
                            placeholder="Enter value configuration" size="md" [(ngModel)]="value"></aava-textbox>
                    </div>
                    <div class="datatype-dropdown-wrapper">
                        <aava-dropdown label="Data Type" dropdownTitle="Select Data Type" [required]="true"
                            [options]="basicDatatypeOptions" [selectedValue]="selectedDatatype?.name"
                            (selectionChange)="handleDataTypeSelection($event)">
                        </aava-dropdown>
                    </div>
                </div>
            </div>

            <div>
                <div class="form-title flex-row align-items-center">
                    <aava-icon iconName="shield" iconSize="16"></aava-icon>
                    <div class="body-medium-default-200">Security Settings</div>
                </div>
                
                <div class="security-settings flex-column">
                    <aava-checkbox size="md" variant="with-bg" label="Encrypted" [isChecked]="isEncrypted"
                        (isCheckedChange)="handleIsEncrypted($event)"></aava-checkbox>
                    <aava-checkbox size="md" variant="with-bg" label="Sensitive" [isChecked]="isSensitive"
                        (isCheckedChange)="handleIsSensitive($event)"></aava-checkbox>
                </div>
            </div>

            <div>
                <div class="form-title flex-row align-items-center">
                    <aava-icon iconName="key-round" iconSize="16"></aava-icon>
                    <div class="body-medium-default-200">Secret Management</div>
                </div>
                <div class="secret-management flex-column">
                    <div class="secret-dropdowns flex-row justify-content-between align-items-center">
                        <div class="secret-dropdown-wrapper">
                            <aava-dropdown label="Secret Provider" dropdownTitle="Select a Secret Provider"
                                [selectedValue]="selectedSecretProvider?.name"
                                [options]="secretProviderOptions" (selectionChange)="handleSecretProviderSelection($event)">
                            </aava-dropdown>
                        </div>
                        <div class="secret-dropdown-wrapper">
                            <aava-checkbox size="md" variant="with-bg" label="User External Secret"
                                [isChecked]="useExternalSecret"
                                (isCheckedChange)="handleUseExternalSecret($event)"></aava-checkbox>
                        </div>
                    </div>
                    @if(useExternalSecret && selectedSecretProvider?.value){
                        <div class="secret-key-textbox">
                            <aava-textbox label="Secret Reference/Name" [required]="true" variant="default"
                                placeholder="Name/reference of the secret in the external provider" size="md" [(ngModel)]="secretKey"></aava-textbox>
                        </div>
                    }
                </div>
            </div>

            <div>
                <div class="form-title flex-row align-items-center">
                    <aava-icon iconName="info" iconSize="16"></aava-icon>
                    <div class="body-medium-default-200">Additional Information</div>
                </div>
                <div class="additional-info flex-column">
                    <aava-textarea variant="default" placeholder="Enter additonal information" size="md"
                        [(ngModel)]="description"></aava-textarea>
                    @if (isEditMode) {
                    <aava-textarea label="Reason for change" variant="default" placeholder="Enter reason for change"
                        size="md" [(ngModel)]="reasonForChange"></aava-textarea>
                    }
                </div>
            </div>
        </div>
    </div>

    <div dialog-footer class="dialog-footer justify-content-end">
        <aava-button label="Cancel" variant="secondary" size="sm" (userClick)="cancel()"></aava-button>

        <aava-button label="Save Configuration" variant="primary" size="sm" [disabled]="!isFormValid || !enableEdit"
            (userClick)="save()"></aava-button>
    </div>
</div>