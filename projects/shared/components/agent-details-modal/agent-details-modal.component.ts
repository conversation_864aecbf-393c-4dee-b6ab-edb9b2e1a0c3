import {
  AavaButtonComponent,
  AavaIconComponent,
  AavaTagComponent,
  AavaTextboxComponent,
  AavaTextareaComponent,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  Input,
  OnInit,
  inject,
} from '@angular/core';
import { Router } from '@angular/router';
import { AavaDialogService } from '@aava/play-core';
import { FormsModule } from '@angular/forms';

export interface AgentDetailsData {
  readonly id: string;
  readonly name: string;
  readonly role: string;
  readonly practiceArea: string;
  readonly goodAt: string[];
  readonly model: string;
  readonly temperature: number;
  readonly maxRpm: number;
  readonly maxToken: number;
  readonly topP: number;
  readonly maxIteration: number;
  readonly maxExecutionTime: number;
  readonly knowledgeBase: string[];
  readonly guardrails: string[];
  readonly tools: string[];
  readonly goal: string;
  readonly backStory: string;
  readonly description: string;
}

@Component({
  selector: 'app-agent-details-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AavaButtonComponent,
    AavaIconComponent,
    AavaTagComponent,
    AavaTextboxComponent,
    AavaTextareaComponent,
  ],
  templateUrl: './agent-details-modal.component.html',
  styleUrls: ['./agent-details-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgentDetailsModalComponent implements OnInit {
  private router = inject(Router);
  private dialogService = inject(AavaDialogService);

  // Input for agent data from dialog service (like marketplace component)
  @Input() data: AgentDetailsData | null = null;

  // Use the input data or fallback to mock data
  protected get displayData(): AgentDetailsData {
    return this.data || this.mockAgentData;
  }

  // Mock data as fallback
  private mockAgentData: AgentDetailsData = {
    id: '1',
    name: 'Agent Alpha',
    role: 'Testing Agent',
    practiceArea: 'Software Development',
    goodAt: ['Software Development', 'Management', 'Marketing'],
    model: 'GPT 3.5',
    temperature: 0.3,
    maxRpm: 100,
    maxToken: 4000,
    topP: 0.95,
    maxIteration: 5,
    maxExecutionTime: 300,
    knowledgeBase: ['KB File 1', 'KB File 2', 'KB File 3', 'KB File 4'],
    guardrails: ['Software Development', 'Management', 'Marketing'],
    tools: ['Serper Dev', 'Website Scraper', 'Test Tool'],
    goal: 'Design, scale, and maintain high-performance backend systems that can handle heavy workloads, complex workflows, and evolving business requirements.',
    backStory:
      "As applications grow, performance bottlenecks, database inefficiencies, and scalability issues often emerge. This role was shaped to tackle those challenges head-on, ensuring Ruby-based systems don't just work — they thrive under pressure.",
    description:
      'This Ruby Developer specializes in creating resilient architectures. They dive deep into profiling, debugging, and optimizing code to ensure systems can handle massive scale while maintaining clean, maintainable codebases.',
  };

  ngOnInit(): void {
    console.log('AgentDetailsModalComponent initialized with data:', this.data);
    console.log('Display data will be:', this.displayData);
    console.log('Agent data type:', typeof this.data);
    console.log(
      'Agent data keys:',
      this.data ? Object.keys(this.data) : 'null'
    );
  }

  // Method to set data from dialog service (like in marketplace component)
  setDialogData(agentData: AgentDetailsData): void {
    console.log('setDialogData called with:', agentData);
    this.data = agentData;
  }

  // Event handlers
  protected onCloseClick(): void {
    // Close the modal using AavaDialogService
    this.dialogService.close();
  }

  // Empty handler for disabled form fields
  protected onNoOp(): void {
    // No operation - used for disabled form fields that need ngModelChange
  }

  protected onEditClick(): void {
    // Navigate to agent builder page with agentId and close modal
    if (this.displayData?.id) {
      this.router.navigate(['/build/agent', this.displayData.id]);
      this.dialogService.close(); // Close the modal after navigation
    } else {
      console.warn('No agent ID available for navigation');
    }
  }

  protected onBackdropClick(event: MouseEvent): void {
    if (event.target === event.currentTarget) {
      this.onCloseClick();
    }
  }
}
