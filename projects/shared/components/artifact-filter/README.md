# Artifact Filter Component

A reusable Angular component for filtering artifacts by type with an integrated search functionality.

## Features

- Radio button selection for artifact types
- Integrated search bar with clear functionality
- Customizable options and labels
- TypeScript support with proper typing
- Responsive design
- Dark mode support
- Accessibility features

## Usage

### Basic Usage

```typescript
import { ArtifactFilterComponent } from '@shared/components/artifact-filter';

@Component({
  selector: 'app-example',
  standalone: true,
  imports: [ArtifactFilterComponent],
  template: `
    <app-artifact-filter
      [selectedType]="selectedArtifactType"
      [searchQuery]="searchQuery"
      (typeChange)="onTypeChange($event)"
      (searchChange)="onSearchChange($event)"
    >
    </app-artifact-filter>
  `,
})
export class ExampleComponent {
  selectedArtifactType: ArtifactType | 'all' = 'all';
  searchQuery = '';

  onTypeChange(type: ArtifactType | 'all') {
    this.selectedArtifactType = type;
    // Handle type change logic
  }

  onSearchChange(query: string) {
    this.searchQuery = query;
    // Handle search logic
  }
}
```

### With Custom Options

```typescript
import {
  ArtifactFilterComponent,
  ArtifactFilterOption,
} from '@shared/components/artifact-filter';

@Component({
  selector: 'app-custom-example',
  standalone: true,
  imports: [ArtifactFilterComponent],
  template: `
    <app-artifact-filter
      [customOptions]="customFilterOptions"
      [selectedType]="selectedType"
      (typeChange)="onTypeChange($event)"
    >
    </app-artifact-filter>
  `,
})
export class CustomExampleComponent {
  customFilterOptions: ArtifactFilterOption[] = [
    { id: 'all', label: 'All Items', value: 'all' },
    { id: 'agents', label: 'AI Agents', value: 'agent' },
    { id: 'tools', label: 'Custom Tools', value: 'tool' },
  ];

  selectedType: ArtifactType | 'all' = 'all';

  onTypeChange(type: ArtifactType | 'all') {
    this.selectedType = type;
  }
}
```

## API Reference

### Inputs

| Property            | Type                             | Default           | Description                               |
| ------------------- | -------------------------------- | ----------------- | ----------------------------------------- |
| `selectedType`      | `ArtifactType \| 'all'`          | `'all'`           | Currently selected artifact type          |
| `searchQuery`       | `string`                         | `''`              | Current search query                      |
| `searchPlaceholder` | `string`                         | `'Search'`        | Placeholder text for search input         |
| `title`             | `string`                         | `'Artifact Type'` | Title displayed above the filter options  |
| `showSearch`        | `boolean`                        | `true`            | Whether to show the search input          |
| `customOptions`     | `ArtifactFilterOption[] \| null` | `null`            | Custom filter options (overrides default) |

### Outputs

| Event          | Type                                  | Description                                  |
| -------------- | ------------------------------------- | -------------------------------------------- |
| `typeChange`   | `EventEmitter<ArtifactType \| 'all'>` | Emitted when artifact type selection changes |
| `searchChange` | `EventEmitter<string>`                | Emitted when search query changes            |

### Interfaces

```typescript
export interface ArtifactFilterOption {
  id: string;
  label: string;
  value: ArtifactType | 'all';
  icon?: string; // Future enhancement
}
```

## Default Artifact Types

The component includes the following default artifact types:

- All Artifacts
- Agent
- Pipeline
- Knowledge Base
- Tool
- Guardrail

## Styling

The component uses CSS custom properties and is designed to work with both light and dark themes. The styling is contained within the component and follows the design system patterns used throughout the application.

## Accessibility

- Full keyboard navigation support
- Screen reader friendly
- Focus indicators
- Semantic HTML structure
- ARIA labels where appropriate

## Examples

### Hide Search Bar

```typescript
<app-artifact-filter
  [showSearch]="false"
  [selectedType]="selectedType"
  (typeChange)="onTypeChange($event)">
</app-artifact-filter>
```

### Custom Title

```typescript
<app-artifact-filter
  title="Filter by Type"
  [selectedType]="selectedType"
  (typeChange)="onTypeChange($event)">
</app-artifact-filter>
```

### Custom Search Placeholder

```typescript
<app-artifact-filter
  searchPlaceholder="Search artifacts..."
  [searchQuery]="searchQuery"
  (searchChange)="onSearchChange($event)">
</app-artifact-filter>
```
