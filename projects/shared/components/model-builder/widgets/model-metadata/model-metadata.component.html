<div class="model-metadata-panel">
  <h3 class="panel-title">{{ labels.metadata_title }}</h3>
  <div class="form-fields">
    <div class="form-field">
      <aava-textbox 
        size="sm"
        [required]="true"
        [label]="labels.model_name"
        [placeholder]="placeholders.name"
        [ngModel]="modelData.name"
        (ngModelChange)="onNameChange($event)">
      </aava-textbox>
    </div>
    
    <div class="form-field">
      <aava-textbox 
        size="sm"
        [required]="true"
        [label]="labels.model_description"
        [placeholder]="placeholders.description"
        type="textarea"
        [ngModel]="modelData.description"
        (ngModelChange)="onDescriptionChange($event)">
      </aava-textbox>
    </div>

    <div class="form-field">
      <aava-select
        size="sm"
        [label]="labels.model_practice_area"
        [placeholder]="placeholders.practice_area" 
        [required]="true"
        variant="default"
        [ngModel]="modelData.practiceArea"
        (selectionChange)="onPracticeAreaChange($event)">
        @for (option of practiceAreaOptions(); track option.value) {
          <aava-select-option [value]="option.value">
            {{ option.label }}
          </aava-select-option>
        }
      </aava-select>
    </div>

    <!-- Good at Section -->
    <div class="form-field good-at-field">
      <aava-select
        size="sm"
        [multiple]="true"
        [label]="labels.model_good_at"
        [required]="true"
        [placeholder]="placeholders.good_at_tags"
        [ngModel]="selectedGoodAtTags"
        (selectionChange)="onGoodAtSelectionChange($event)">
        <aava-select-option
          *ngFor="let option of goodAtOptions()"
          [value]="option.value">
          <aava-checkbox
            size="sm"
            [isChecked]="isOptionSelected(option.value)">
          </aava-checkbox>
          {{ option.label }}
        </aava-select-option>
      </aava-select>

      <!-- Selected Tags Display -->
      <div class="selected-tags-container" *ngIf="selectedGoodAtTags.length > 0">
        @for (tag of selectedGoodAtTags; track tag) {
          <aava-tag
            [label]="tag"
            size="sm"
            variant="outlined"
            [removable]="true"
            (removed)="onGoodAtTagRemove(tag)">
          </aava-tag>
        }
      </div>
    </div>
  </div>
</div>
