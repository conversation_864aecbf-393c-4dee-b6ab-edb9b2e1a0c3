:host {
  height: 100%;
  display: block;
  padding:8px 8px 24px 12px;
}

.model-metadata-panel {
  display: flex;
  flex-direction: column;
  gap: var(--Global-V1-Spacing-Space8, 16px);
  padding: 16px;
  width: 100%;
  height: 100%;
  border-radius: var(--Global-V1-Radius-Rad7, 14px);
  border: 2px solid var(--<PERSON>-White-<PERSON>, #FDFDFD);
  background: var(--Surface-Fill-Light-Surface-White-6, rgba(255, 255, 255, 0.6));
  box-shadow: 0 2px 4px 0 var(--Brand-Neutral-n-100, #D1D3D8);
  box-sizing: border-box;

  .panel-title {
    color: #000;
    font-family: var(--Global-v1-Family-Body, Inter);
    font-size: var(--Global-v1-Size-18, 18px);
    font-style: medium;
    font-weight: 500;
    flex-shrink: 0;
    margin: 0;
  }
  .form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  .form-field {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  }
  .selected-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding-top: 8px;
  }
}
