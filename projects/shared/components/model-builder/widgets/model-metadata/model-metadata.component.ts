import {
  AavaSelectComponent,
  AavaSelectOptionComponent,
  AavaTagComponent,
  AavaTextboxComponent,
  AavaCheckboxComponent,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  inject,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DropdownDataStore } from 'projects/shared/stores/dropdown-data.store';
import modelText from '../../components/model-builder/models.json';

export interface ModelMetadataData {
  name: string;
  description: string;
  practiceArea: string;
  goodAtTags: string[];
}

@Component({
  selector: 'app-model-metadata',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AavaTextboxComponent,
    AavaSelectComponent,
    AavaSelectOptionComponent,
    AavaTagComponent,
    AavaCheckboxComponent,
  ],
  templateUrl: './model-metadata.component.html',
  styleUrl: './model-metadata.component.scss',
})
export class ModelMetadataComponent implements OnInit {
  @Input() modelData: ModelMetadataData = {
    name: '',
    description: '',
    practiceArea: '',
    goodAtTags: [],
  };

  @Output() dataChanged = new EventEmitter<ModelMetadataData>();

  selectedGoodAtTags: string[] = [];
  // Add labels and placeholders
  readonly labels = modelText.labels;
  readonly placeholders = modelText.placeholders;

  // Inject dropdown store
  private dropdownStore = inject(DropdownDataStore);

  // Get options from dropdown store
  readonly practiceAreaOptions = this.dropdownStore.practiceAreaOptions;
  readonly goodAtOptions = this.dropdownStore.goodAtOptions;

  ngOnInit() {
        this.loadDropdownData();
    this.selectedGoodAtTags = this.modelData.goodAtTags || [];
  }
    private async loadDropdownData(): Promise<void> {
    try {
      await Promise.all([
        this.dropdownStore.loadPracticeAreas(),
        this.dropdownStore.loadGoodAtTags(),
      ]);
    } catch (error) {
      console.error('Failed to load dropdown data:', error);
    }
  }
  onNameChange(name: string): void {
    this.modelData.name = name;
    this.emitChange();
  }

  onDescriptionChange(description: string): void {
    this.modelData.description = description;
    this.emitChange();
  }

  onPracticeAreaChange(practiceArea: string): void {
    this.modelData.practiceArea = practiceArea;
    this.emitChange();
  }

  isOptionSelected(value: string): boolean {
    return this.selectedGoodAtTags.includes(value);
  }

  onGoodAtSelectionChange(selectedValues: string[]): void {
    this.selectedGoodAtTags = selectedValues;
    this.modelData.goodAtTags = selectedValues;
    this.emitChange();
  }

  onGoodAtTagRemove(tagToRemove: string): void {
    this.selectedGoodAtTags = this.selectedGoodAtTags.filter(
      tag => tag !== tagToRemove
    );
    this.modelData.goodAtTags = this.selectedGoodAtTags;
    this.emitChange();
  }

  private emitChange(): void {
    this.dataChanged.emit({ ...this.modelData });
  }
}
