import {
  AavaImageCardComponent,
  AavaTabsComponent,
  AavaToggleComponent,
  ImageCardButton,
  ImageCardData,
  TabItem,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  OnInit,
  inject,
} from '@angular/core';

import {
  ModelsService,
  RefDataItem,
} from '../../../../services/models.service';
import modelText from '../../components/model-builder/models.json';
export interface EngineCard {
  id: string;
  title: string;
  description: string;
  image: string;
  selected: boolean;
  variant?: 'withActions' | 'withoutActions';
  layout?: 'vertical' | 'horizontal';
}

@Component({
  selector: 'app-model-types',
  standalone: true,
  imports: [AavaImageCardComponent, CommonModule,AavaTabsComponent],
  templateUrl: './model-types.component.html',
  styleUrl: './model-types.component.scss',
})
export class ModelTypesComponent implements OnInit, OnChanges {
  private modelsService = inject(ModelsService);

  @Input() selectedEngine: string = '';
  @Input() isEmbedding: boolean = false;
  @Input() engineCards: EngineCard[] = [];
   // Add labels
  readonly labels = modelText.labels;
  
  // Update tab configuration to use labels from JSON
  modelTypeTabs = [
    { id: 'generative', label: this.labels.tabs.generative },
    { id: 'embedding', label: this.labels.tabs.embedding }
  ];
   // Computed property for active tab
  get activeModelTypeTab(): string {
    return this.isEmbedding ? 'generative' : 'embedding';
  }

  // Add a computed property for image card data
  engineCardData: ImageCardData[] = [];

  @Output() engineSelected = new EventEmitter<string>();
  @Output() embeddingToggled = new EventEmitter<boolean>();
  
  onModelTypeTabChange(tab: TabItem): void {
    const isEmbedding = tab.id === 'generative';
    
    // Reset selected engine when tab changes
    this.selectedEngine = '';
    this.updateButtonStates();
    this.updateEngineCardData();
    
    // Emit the embedding toggle event
    this.embeddingToggled.emit(isEmbedding);
  }

  ngOnInit() {
    this.loadAIEngines();
  }

  ngOnChanges() {
    this.updateButtonStates();
    this.updateEngineCardData(); // Update card data when inputs change
  }

  private loadAIEngines() {
    // Static image mapping for AI engines
    const engineImageMap: { [key: string]: string } = {
     'AzureOpenAI': 'azureOpenAi.svg',
    'AmazonBedrock': 'amazonBedrock.svg',
    'GoogleAI': 'googleAi.svg',
    'DatabricksAI': 'dataBricks.svg',
    'BNY': 'azureOpenAi.svg',
    'DaOpenSourceAI': 'azureOpenAi.svg'
    };

    this.modelsService.getAIEngines().subscribe({
      next: (refDataItems: RefDataItem[]) => {
        if (refDataItems.length > 0) {
          const aiEnginesData = JSON.parse(refDataItems[0]['value']);
          const cards: EngineCard[] = aiEnginesData.map((engine: any) => {
            const engineId = engine.engineName.toLowerCase().replace(/\s+/g, '-');
            return {
              id: engineId,
              title: engine.label,
              description: engine.description,
              image: engineImageMap[engine.engineName] || 'default-engine.svg', // Use static image]]]]]
              selected: true,
              variant: 'withActions' as const,
              layout: 'vertical' as const
            };
          });
          
          this.engineCards = cards;
          this.updateButtonStates();
          this.updateEngineCardData(); // Update card data after loading
        }
      },
      error: error => {
        console.error('Failed to load AI engines:', error);
      },
    });
  }

  selectEngine(engineId: string) {
    this.engineSelected.emit(engineId);
  }

  onButtonClick(event: {
    action: string | undefined;
    button: ImageCardButton;
  }) {
    if (event.action) {
      this.engineSelected.emit(event.action);
    }
  }

  // onEmbeddingToggle(isEmbedding: boolean) {
  //   // Reset selected engine when toggle changes
  //   this.selectedEngine = '';
  //   this.updateButtonStates();
  //   this.updateEngineCardData();
    
  //   this.embeddingToggled.emit(isEmbedding);
  // }

  private updateButtonStates() {
    this.engineCards.forEach(card => {
      card.selected = this.selectedEngine === card.id;
    });
  }

  trackByCardId(index: number, card: EngineCard): string {
    return card.id || index.toString();
  }

  // New method to pre-compute all card data
  private updateEngineCardData() {
    this.engineCardData = this.engineCards.map(card => ({
      variant: 'withActions' as const,
      type: 'simple' as const,
      title: card.title,
      image: card.image,
      description: card.description,
      tags: [
        {
          label: card.title,
          size: 'xs' as const,
          color: 'primary' as const,
          shape: 'pill' as const
        }
      ],
      buttons: [
        {
          text: this.selectedEngine === card.id ? 'Selected' : 'Select',
          action: card.id,
          variant: 'secondary',
          size: 'sm' as const,
          width: '100%'
        }
      ],
      layout: {
        orientation: 'vertical' as const,
        imageGrid: 'top' as const,
        infoGrid: 'bottom' as const,
      },
    }));
  }

  // Keep the method for backward compatibility
  protected getImageCardData(card: EngineCard): ImageCardData {
    const index = this.engineCards.findIndex(c => c.id === card.id);
    return this.engineCardData[index] || this.createFallbackCardData(card);
  }

  private createFallbackCardData(card: EngineCard): ImageCardData {
    return {
      variant: 'withActions',
      type: 'simple',
      title: card.title,
      image: card.image,
      description: card.description,
      buttons: [
        {
          text: this.selectedEngine === card.id ? 'Selected' : 'Select',
          action: card.id,
          variant: 'secondary',
          size: 'xs',
        },
      ],
      layout: {
        orientation: 'vertical',
        imageGrid: 'top',
        infoGrid: 'bottom',
      },
    };
  }

  protected onEngineSelect(card: EngineCard): void {
    this.engineSelected.emit(card.id);
  }
}
