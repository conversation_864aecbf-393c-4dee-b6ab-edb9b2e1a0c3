<div class="artifact-type-selector-widget-panel">
  <div class="selector-header">
    <h2 class="selector-title">{{ labels.model_types_title }}</h2>
    <div class="toggle-container">
      <aava-tabs
        [tabs]="modelTypeTabs"
        [activeTabId]="activeModelTypeTab"
        variant="button"
        size="sm"
        buttonShape="pill"
        [showContentPanels]="false"
        [bordered]="false"
        (tabChange)="onModelTypeTabChange($event)">
      </aava-tabs>
    </div>
  </div>
  <div class="engine-grid">
    @for (card of engineCards; track card.id) {
      <aava-image-card
        [data]="getImageCardData(card)"
        (buttonClicked)="onButtonClick($event)"
        class="engine-card"
        [class.selected]="selectedEngine === card.id">
      </aava-image-card>
    }
  </div>
</div>
