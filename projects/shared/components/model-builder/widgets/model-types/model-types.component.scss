.artifact-type-selector-widget-panel {
  width: 100%;
  background: transparent;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.selector-title {
  color: #000;
    font-family: var(--Global-v1-Family-Body, Inter);
    font-size: var(--Global-v1-Size-16, 16px);
    font-style: normal;
    font-weight: 600;
    flex-shrink: 0;
    margin: 0;
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 12px;

  ::ng-deep .toggle-wrapper {
    background: rgba(var(--rgb-brand-primary)) !important;
  }
 
}

.toggle-label {
  font-size: 16px;
  font-weight: 400;
  color: #000000;
  
  &.active {
    color: rgba(var(--rgb-brand-primary));
    font-weight: 600;
  }
}

.engine-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  width: 100%;
}

@media (max-width: 1024px) {
  .engine-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .selector-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .engine-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .selector-title {
    font-size: 18px;
  }
}

.engine-card.selected {
  ::ng-deep .ava-button.secondary.ava-button--glass-10{
    background: #E6F3FF !important;
    border:none !important
  }

}
  ::ng-deep .ava-tabs .ava-tabs__tab--active {
   background: #E6F3FF !important;
}
