<div class="input-widget-panel">
  <h3 class="panel-title">{{ labels.model_inputs_title }}</h3>
  <div class="config-grid">
    <!-- Choose Model Dropdown -->
    <div class="form-field model-select-field">
      <aava-select
        size="sm"
        [label]="labels.choose_model"
        [placeholder]="placeholders.select_model"
        [ngModel]="configData.selectedModel"
        [disabled]="!selectedEngine || disabled"
        (selectionChange)="onModelChange($event)">
        <aava-select-option
          *ngFor="let option of modelOptions(); trackBy: trackByValue"
          [value]="option.value">
          {{ option.label }}
        </aava-select-option>
      </aava-select>
    </div>

    <div></div>

    <!-- Dynamic Fields based on selected model's engine -->
    <ng-container *ngIf="isModelSelected">
      <!-- AzureOpenAI Fields -->
      <div class="form-field" *ngIf="shouldShowField('baseUrl')">
        <aava-textbox 
          [label]="labels.base_url"
          [required]="true" 
          [placeholder]="placeholders.base_url"
          [ngModel]="configData.baseUrl"
          (ngModelChange)="onBaseUrlChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>

      <div class="form-field" *ngIf="shouldShowField('deploymentName')">
        <aava-textbox 
          [required]="true"
          [label]="labels.deployment_name"
          [placeholder]="placeholders.deployment_name"
          [ngModel]="configData.deploymentName"
          (ngModelChange)="onDeploymentNameChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>

      <div class="form-field" *ngIf="shouldShowField('apiKey')">
        <aava-textbox 
          [required]="true"
          [label]="labels.api_key"
          [placeholder]="placeholders.api_key"
          [ngModel]="configData.apiKey"
          (ngModelChange)="onApiKeyChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>

      <div class="form-field" *ngIf="shouldShowField('apiVersion')">
        <aava-select
          size="sm"
          [label]="labels.api_version"
          [placeholder]="placeholders.api_version"
          [ngModel]="configData.apiVersion"
          [disabled]="disabled"
          [required]="true"
          (selectionChange)="onApiVersionChange($event)">
          <aava-select-option
            *ngFor="let option of apiVersionOptions(); trackBy: trackByValue"
            [value]="option.value">
            {{ option.label }}
          </aava-select-option>
        </aava-select>
      </div>

      <!-- AmazonBedrock Fields -->
      <div class="form-field" *ngIf="shouldShowField('awsAccessKey')">
        <aava-textbox 
          [required]="true"
          [label]="labels.aws_access_key"
          [placeholder]="placeholders.aws_access_key"
          [ngModel]="configData.awsAccessKey"
          (ngModelChange)="onAwsAccessKeyChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>

      <div class="form-field" *ngIf="shouldShowField('awsSecretKey')">
        <aava-textbox 
          [required]="true"
          [label]="labels.aws_secret_key"
          [placeholder]="placeholders.aws_secret_key"
          [ngModel]="configData.awsSecretKey"
          (ngModelChange)="onAwsSecretKeyChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>

      <div class="form-field" *ngIf="shouldShowField('awsRegion')">
        <aava-textbox 
          [required]="true"
          [label]="labels.aws_region"
          [placeholder]="placeholders.aws_region"
          [ngModel]="configData.awsRegion"
          (ngModelChange)="onAwsRegionChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>

      <div class="form-field" *ngIf="shouldShowField('bedrockModelId')">
        <aava-textbox 
          [required]="true"
          [label]="labels.bedrock_model_id"
          [placeholder]="placeholders.bedrock_model_id"
          [ngModel]="configData.bedrockModelId"
          (ngModelChange)="onBedrockModelIdChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>

      <!-- GoogleAI Fields -->
      <div class="form-field" *ngIf="shouldShowField('gcpProjectId')">
        <aava-textbox 
          [required]="true"
          label="GCP Project ID"
          [placeholder]="placeholders.gcp_project_id"
          [ngModel]="configData.gcpProjectId"
          (ngModelChange)="onGcpProjectIdChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>

      <div class="form-field" *ngIf="shouldShowField('gcpLocation')">
        <aava-textbox 
          [required]="true"
          label="GCP Location"
          [placeholder]="placeholders.gcp_location"
          [ngModel]="configData.gcpLocation"
          (ngModelChange)="onGcpLocationChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>

      <div class="form-field" *ngIf="shouldShowField('vertexAIEndpoint')">
        <aava-textbox 
          [required]="true"
          label="Vertex AI Endpoint"
          [placeholder]="placeholders.vertex_ai_endpoint"
          [ngModel]="configData.vertexAIEndpoint"
          (ngModelChange)="onVertexAIEndpointChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>

      <!-- DaOpenSourceAI Fields -->
      <div class="form-field" *ngIf="shouldShowField('serviceUrl')">
        <aava-textbox 
          [required]="true"
          label="Service URL"
          [placeholder]="placeholders.service_url"
          [ngModel]="configData.serviceUrl"
          (ngModelChange)="onServiceUrlChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>

      <div class="form-field" *ngIf="shouldShowField('apiKeyEncoded')">
        <aava-textbox 
          [required]="true"
          [label]="labels.api_key"
          [placeholder]="placeholders.api_key"
          [ngModel]="configData.apiKeyEncoded"
          (ngModelChange)="onApiKeyEncodedChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>

      <div class="form-field" *ngIf="shouldShowField('headerName')">
        <aava-textbox 
          [required]="true"
          label="Header Name"
          [placeholder]="placeholders.header_name"
          [ngModel]="configData.headerName"
          (ngModelChange)="onHeaderNameChange($event)"
          [disabled]="disabled"
          size="sm">
        </aava-textbox>
      </div>
    </ng-container>
  </div>
</div>
