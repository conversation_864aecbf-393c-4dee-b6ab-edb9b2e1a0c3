.input-widget-panel {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
}

.panel-title {
    color: #3B3F46;
    font-family: var(--Global-v1-Family-Body, Inter);
    font-size: var(--Global-v1-Size-16, 16px);
    font-style: normal;
    font-weight: 600;
    flex-shrink: 0;
    margin: 0 0 20px 0;
}

.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.model-select-field {
  grid-column: 1;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

@media (max-width: 768px) {
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .model-select-field {
    grid-column: 1;
  }
}
