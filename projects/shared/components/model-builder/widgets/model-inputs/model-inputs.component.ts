import {
  AavaTextboxComponent,
  AavaSelectComponent,
  AavaSelectOptionComponent,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnChanges,
  inject,
  computed,
  SimpleChanges,
} from '@angular/core';
import { FormsModule } from '@angular/forms';

import {
  DropdownDataStore,
  DropdownOption,
} from '../../../../stores/dropdown-data.store';
import modelText from '../../components/model-builder/models.json';
import { ModelsService, RefDataItem } from 'projects/shared/services/models.service';

export interface ModelConfigData {
  selectedModel: string;
  selectedModelId: number | null;
  baseUrl: string;
  deploymentName: string;
  apiKey: string;
  apiVersion: string;
  // Additional fields based on AI engine
  awsAccessKey?: string;
  awsSecretKey?: string;
  awsRegion?: string;
  gcpProjectId?: string;
  gcpLocation?: string;
  vertexAIEndpoint?: string;
  serviceUrl?: string;
  headerName?: string;
  bedrockModelId?: string;
  apiKeyEncoded?: string;
}

@Component({
  selector: 'app-model-inputs',
  standalone: true,
  imports: [
    AavaTextboxComponent,
    AavaSelectComponent,
    AavaSelectOptionComponent,
    FormsModule,
    CommonModule,
  ],
  templateUrl: './model-inputs.component.html',
  styleUrl: './model-inputs.component.scss',
})
export class ModelInputsComponent implements OnInit, OnChanges {
  // Store injection
  private readonly dropdownStore = inject(DropdownDataStore);
  private readonly modelService = inject(ModelsService);

  @Input() configData: ModelConfigData = {
    selectedModel: '',
    selectedModelId: null,
    baseUrl: '',
    deploymentName: '',
    apiKey: '',
    apiVersion: '',
  };

  @Input() selectedEngine: string = '';
  @Input() isEmbedding: boolean = false;
  @Input() disabled: boolean = false;

  @Output() configChanged = new EventEmitter<ModelConfigData>();
  // Add labels and placeholders properties
  readonly labels = modelText.labels;
  readonly placeholders = modelText.placeholders;
  // Use computed to get filtered models based on current inputs
  readonly modelOptions = computed(() => {
    const allModels = this.dropdownStore.models();

    if (!this.selectedEngine) {
      return [];
    }

    const filteredModels = allModels.filter(model => {
      const engineMatch =
        model.aiEngine?.toLowerCase().replace(/\s+/g, '-') ===
        this.selectedEngine;
      const typeMatch = this.isEmbedding
        ? model.type === 'Embedding'
        : model.type === 'Generative';

      return engineMatch && typeMatch;
    });

    const options = filteredModels.map(model => ({
      value: model.name,
      label: model.name,
      id: model.id,
    }));

    return options;
  });

  readonly selectedModelDetails = computed(() => {
    if (!this.configData.selectedModelId) return null;
    return this.dropdownStore.getModelById(this.configData.selectedModelId);
  });
  readonly apiVersionOptions = computed(() => {
    return this.dropdownStore.apiVersionOptions();
  });


  ngOnInit(): void {
  this.loadApiVersions();
  }

  // Update filters when inputs change - but don't use them in computed
 ngOnChanges(changes: SimpleChanges): void {
    // Reload data when engine changes to ensure we have fresh data
    if (changes['selectedEngine']) {
      this.dropdownStore.loadModelsOnly();
    }
  }

  trackByValue(index: number, option: any): string {
    return option.value;
  }

  onModelChange(value: string): void {
    this.configData.selectedModel = value;

    // Find the selected model details with proper typing
    const modelOption = this.modelOptions().find(
      (option: DropdownOption) => option.value === value
    );
    this.configData.selectedModelId = modelOption?.id || null;

    // Pre-fill fields if model has existing data
    const modelDetails = this.selectedModelDetails();
    if (modelDetails) {
      this.configData.baseUrl = modelDetails.baseurl || '';
      this.configData.deploymentName = modelDetails.llmDeploymentName || '';
      this.configData.apiKey =
        modelDetails.apiKeyEncoded || modelDetails.apiKey || '';
      this.configData.apiVersion = modelDetails.apiVersion || '';

      // Engine-specific fields
      if (modelDetails.awsAccessKey)
        this.configData.awsAccessKey = modelDetails.awsAccessKey;
      if (modelDetails.awsSecretKey)
        this.configData.awsSecretKey = modelDetails.awsSecretKey;
      if (modelDetails.awsRegion)
        this.configData.awsRegion = modelDetails.awsRegion;
      if (modelDetails.gcpProjectId)
        this.configData.gcpProjectId = modelDetails.gcpProjectId;
      if (modelDetails.gcpLocation)
        this.configData.gcpLocation = modelDetails.gcpLocation;
      if (modelDetails.vertexAIEndpoint)
        this.configData.vertexAIEndpoint = modelDetails.vertexAIEndpoint;
      if (modelDetails.serviceUrl)
        this.configData.serviceUrl = modelDetails.serviceUrl;
      if (modelDetails.headerName)
        this.configData.headerName = modelDetails.headerName;
    }

    this.emitChange();
  }

  onBaseUrlChange(value: string): void {
    this.configData.baseUrl = value;
    this.emitChange();
  }

  onDeploymentNameChange(value: string): void {
    this.configData.deploymentName = value;
    this.emitChange();
  }

  onApiKeyChange(value: string): void {
    this.configData.apiKey = value;
    this.emitChange();
  }

  onApiVersionChange(value: string): void {
    this.configData.apiVersion = value;
    this.emitChange();
  }

  // Add methods for additional fields
  onAwsAccessKeyChange(value: string): void {
    this.configData.awsAccessKey = value;
    this.emitChange();
  }

  onAwsSecretKeyChange(value: string): void {
    this.configData.awsSecretKey = value;
    this.emitChange();
  }

  onAwsRegionChange(value: string): void {
    this.configData.awsRegion = value;
    this.emitChange();
  }

  private emitChange(): void {
    this.configChanged.emit({ ...this.configData });
  }

  // Add computed property to check if model is selected
  get isModelSelected(): boolean {
    return !!this.configData.selectedModel;
  }

  // Add computed property to get current engine
  get currentEngine(): string {
    const modelDetails = this.selectedModelDetails();
    return modelDetails?.aiEngine || '';
  }

  // Computed property to determine which fields to show based on model's AI engine
  readonly requiredFields = computed(() => {
    const modelDetails = this.selectedModelDetails();
    if (!modelDetails?.aiEngine) return [];

    const engine = modelDetails.aiEngine;
    
    // Define field requirements per engine 
    const engineFieldMap: Record<string, string[]> = {
      'AzureOpenAI': ['baseUrl', 'deploymentName', 'apiKey', 'apiVersion'],
      'AmazonBedrock': ['awsAccessKey', 'awsSecretKey', 'awsRegion', 'bedrockModelId'],
      'GoogleAI': ['gcpProjectId', 'gcpLocation', 'vertexAIEndpoint'],
      'DaOpenSourceAI': ['serviceUrl', 'apiKeyEncoded', 'headerName'],
      'BNY': [], // Add if needed
      'DatabricksAI': [] // Add if needed
    };

    return engineFieldMap[engine] || [];
  });

  // Helper methods to check if specific fields should be shown
  shouldShowField(fieldName: string): boolean {
    return this.requiredFields().includes(fieldName);
  }

  // Add missing methods for engine-specific fields
  onBedrockModelIdChange(value: string): void {
    this.configData.bedrockModelId = value;
    this.emitChange();
  }

  onApiKeyEncodedChange(value: string): void {
    this.configData.apiKeyEncoded = value;
    this.emitChange();
  }

  onGcpProjectIdChange(value: string): void {
    this.configData.gcpProjectId = value;
    this.emitChange();
  }

  onGcpLocationChange(value: string): void {
    this.configData.gcpLocation = value;
    this.emitChange();
  }

  onVertexAIEndpointChange(value: string): void {
    this.configData.vertexAIEndpoint = value;
    this.emitChange();
  }

  onServiceUrlChange(value: string): void {
    this.configData.serviceUrl = value;
    this.emitChange();
  }

  onHeaderNameChange(value: string): void {
    this.configData.headerName = value;
    this.emitChange();
  }
  loadApiVersions(): void {
    this.modelService.getRefData('Api Version').subscribe({
      next: (refDataItems: RefDataItem[]) => {
        if (refDataItems.length > 0) {
          this.dropdownStore.setApiVersions(refDataItems);
        }
      },
      error: error => {
        console.error('Failed to load API versions:', error);
      },
    });
  }
}
