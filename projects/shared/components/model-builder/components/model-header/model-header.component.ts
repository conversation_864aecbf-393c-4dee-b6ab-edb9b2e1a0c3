import { AavaButtonComponent } from '@aava/play-core';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  computed,
} from '@angular/core';
import modelText from '../../components/model-builder/models.json';

@Component({
  selector: 'app-model-header',
  standalone: true,
  imports: [AavaButtonComponent],
  templateUrl: './model-header.component.html',
  styleUrl: './model-header.component.scss',
})
export class ModelHeaderComponent {
  @Input() isFormValid: boolean = false;
  @Output() saveClicked = new EventEmitter<void>();
  // Add labels and placeholders properties
    readonly labels = modelText.labels;
    readonly placeholders = modelText.placeholders;
  onSaveClick(): void {
    this.saveClicked.emit();
  }
  protected saveButtonAriaLabel = computed(() =>
    this.isFormValid ? 'Save Model' : 'Complete required fields to save model'
  );
}
