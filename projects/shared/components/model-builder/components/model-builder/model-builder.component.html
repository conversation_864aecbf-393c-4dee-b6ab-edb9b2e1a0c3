<div class="model-builder-container">
  <app-libraries-main-layout [isThirdColumnVisible]="false">
    <!-- Header -->
    <app-model-header header [isFormValid]="isFormValid()" (saveClicked)="onSaveModel()">
    </app-model-header>

    <!-- Content Area -->
    <!-- Left Panel: Model Metadata Widget -->
    @if (isLoading()) {
    <div firstRow class="metadata-skeleton">
      <aava-skeleton  width="100%" height="60px" shape="rounded" animation="wave">
      </aava-skeleton>
      <aava-skeleton width="100%" height="40px" shape="rounded" animation="wave">
      </aava-skeleton>
      <aava-skeleton width="80%" height="40px" shape="rounded" animation="wave">
      </aava-skeleton>
            <aava-skeleton width="80%" height="40px" shape="rounded" animation="wave">
      </aava-skeleton>
    </div>
    } @else {
    <app-model-metadata firstRow [modelData]="modelMetadata()" (dataChanged)="onMetadataChange($event)">
    </app-model-metadata>
    }

    <!-- Right Panel: Main Content -->
    <main class="main-content" secondRow>
      @if (isLoading()) {
      <!-- Model Types Skeleton -->
      <div class="model-types-skeleton">
        <div class="skeleton-header">
          <aava-skeleton width="200px" height="24px" shape="rounded" animation="wave">
          </aava-skeleton>
          <aava-skeleton width="120px" height="32px" shape="rounded" animation="wave">
          </aava-skeleton>
        </div>
        <div class="skeleton-grid">
          @for (card of [1,2,3]; track card) {
          <div class="skeleton-card">
            <aava-skeleton width="100%" height="120px" shape="rounded" animation="wave">
            </aava-skeleton>
            <aava-skeleton width="80%" height="20px" shape="rounded" animation="wave">
            </aava-skeleton>
            <aava-skeleton width="100%" height="16px" shape="rounded" animation="wave">
            </aava-skeleton>
            <aava-skeleton width="60%" height="32px" shape="rounded" animation="wave">
            </aava-skeleton>
          </div>
          }
        </div>
      </div>

      <!-- Divider Skeleton -->
      <div class="skeleton-divider">
        <aava-skeleton width="100%" height="1px" shape="rectangle" animation="wave">
        </aava-skeleton>
      </div>

      <!-- Model Configuration Skeleton -->
      <div class="model-config-skeleton">
        <aava-skeleton width="150px" height="24px" shape="rounded" animation="wave">
        </aava-skeleton>
        <div class="skeleton-form-grid">
          @for (field of [1,2,3,4]; track field) {
          <div class="skeleton-form-field">
            <aava-skeleton width="100px" height="16px" shape="rounded" animation="wave">
            </aava-skeleton>
            <aava-skeleton width="100%" height="40px" shape="rounded" animation="wave">
            </aava-skeleton>
          </div>
          }
        </div>
      </div>
      } @else {
      <!-- Model Types Section -->
      <div class="model-types-section">
        <app-model-types [selectedEngine]="selectedEngine()" [isEmbedding]="toggleDisplayState()"
          [engineCards]="engineCards()" (engineSelected)="onEngineSelected($event)"
          (embeddingToggled)="onEmbeddingToggled($event)">
        </app-model-types>
      </div>

      <!-- Divider -->
      <div class="section-divider"></div>

      <!-- Model Configuration Section -->
      <div class="model-config-section">
        <app-model-inputs [configData]="modelConfig()" [selectedEngine]="selectedEngine()" [isEmbedding]="isEmbedding()"
          [disabled]="!selectedEngine()" (configChanged)="onConfigChange($event)">
        </app-model-inputs>
      </div>
      }
    </main>
  </app-libraries-main-layout>
</div>