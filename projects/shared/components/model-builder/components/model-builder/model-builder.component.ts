import { AavaDialogService, AavaSkeletonComponent, AavaToastService } from '@aava/play-core';
import { Component, inject, signal, OnInit, computed } from '@angular/core';
import { LibrariesMainLayoutComponent } from '@shared';
import { DropdownDataStore } from '../../../../stores/dropdown-data.store';

import {
  ModelsService,
  RefDataItem,
} from '../../../../services/models.service';
import {
  ModelInputsComponent,
  ModelConfigData,
} from '../../widgets/model-inputs/model-inputs.component';
import {
  ModelMetadataComponent,
  ModelMetadataData,
} from '../../widgets/model-metadata/model-metadata.component';
import {
  ModelTypesComponent,
  EngineCard,
} from '../../widgets/model-types/model-types.component';
import { ModelHeaderComponent } from '../model-header/model-header.component';
import { Router } from '@angular/router';

@Component({
  selector: 'app-model-builder',
  standalone: true,
  imports: [
    ModelHeaderComponent,
    ModelInputsComponent,
    ModelMetadataComponent,
    ModelTypesComponent,
    LibrariesMainLayoutComponent,
    AavaSkeletonComponent
  ],
  templateUrl: './model-builder.component.html',
  styleUrl: './model-builder.component.scss',
})
export class ModelBuilderComponent implements OnInit {
  private modelsService = inject(ModelsService);
  private toastService = inject(AavaToastService);
  private router = inject(Router);
  private dropdownStore = inject(DropdownDataStore);

  // Signals for reactive data
  modelMetadata = signal<ModelMetadataData>({
    name: '',
    description: '',
    practiceArea: '',
    goodAtTags: [],
  });

  selectedEngine = signal('');
  isEmbedding = signal(false);

  engineCards = signal<EngineCard[]>([]);

  modelConfig = signal<ModelConfigData>({
    selectedModel: '',
    selectedModelId: null,
    baseUrl: '',
    deploymentName: '',
    apiKey: '',
    apiVersion: '',
    // Add all optional fields
    awsAccessKey: '',
    awsSecretKey: '',
    awsRegion: '',
    gcpProjectId: '',
    gcpLocation: '',
    vertexAIEndpoint: '',
    serviceUrl: '',
    headerName: '',
    bedrockModelId: '',
    apiKeyEncoded: '',
  });

  isFormValid = signal(false);

  // Add a computed property for the toggle display
  readonly toggleDisplayState = computed(() => !this.isEmbedding());

  // Add loading state signal
  private _isLoading = signal(false);
  readonly isLoading = this._isLoading.asReadonly();

  // Add saving state signal
  private _isSaving = signal(false);
  readonly isSaving = this._isSaving.asReadonly();

  ngOnInit(): void {

  }

  private setLoading(loading: boolean): void {
    this._isLoading.set(loading);
  }


  onMetadataChange(data: ModelMetadataData): void {
    this.modelMetadata.set(data);
    this.validateForm();
  }

  onEngineSelected(engineId: string): void {
    this.selectedEngine.set(engineId);
    // Reset model selection when engine changes
    this.modelConfig.update(config => ({
      ...config,
      selectedModel: '',
      selectedModelId: null,
    }));
    this.validateForm();
  }

  onEmbeddingToggled(isEmbedding: boolean): void {
    const actualEmbeddingState = !isEmbedding;
    this.isEmbedding.set(actualEmbeddingState);
    // Reset engine and model selection when type changes
    this.selectedEngine.set('');
    this.modelConfig.update(config => ({
      ...config,
      selectedModel: '',
      selectedModelId: null,
    }));
    this.validateForm();
  }

  onConfigChange(config: ModelConfigData): void {
    this.modelConfig.set(config);
    this.validateForm();
  }

  async onSaveModel(): Promise<void> {
    if (this.isFormValid()) {
      try {
        // Set saving state
        this._isSaving.set(true);

        const modelData = this.buildModelPayload();
        this.modelsService.createModel(modelData).subscribe({
          next: async (response: any) => {
            // Clear saving state
            this._isSaving.set(false);
             // Navigate to marketplace after successful save
            this.router.navigate(['/marketplace']);
            // Show success toast
            await this.toastService.success({
              title: 'Success!',
              message: response?.message || 'Model created successfully!',
              duration: 2000,
              customWidth: '350px',
              design: 'modern',
              size: 'medium',
              showCloseButton: false
            });
          },
          error: async error => {
            console.error('Error saving model:', error);
            // Clear saving state
            this._isSaving.set(false);

            // Extract error message
            let errorMessage = 'Failed to create model. Please try again.';
            if (error?.error?.message) {
              errorMessage = error.error.message;
            } else if (error?.error?.data?.message) {
              errorMessage = error.error.data.message;
            } else if (error?.message) {
              errorMessage = error.message;
            }

            // Show error toast
            await this.toastService.error({
              title: 'Error',
              message: errorMessage,
              duration: 2000,
              customWidth: '350px',
              design: 'modern',
              size: 'medium',
              showCloseButton: false
            });
          },
        });
      } catch (error) {
        // Clear saving state
        this._isSaving.set(false);
        
        await this.toastService.error({
          title: 'Error',
          message: 'An unexpected error occurred. Please try again.',
          duration: 2000,
          customWidth: '350px',
          design: 'modern',
          size: 'medium',
          showCloseButton: false
        });
      }
    } else {
      // Show warning toast for invalid form
      await this.toastService.warning({
        title: 'Form Incomplete',
        message: 'Please fill in all required fields before saving.',
        duration: 2000,
        customWidth: '350px',
        design: 'modern',
        size: 'medium',
        showCloseButton: false
      });
    }
  }

 private buildModelPayload(): any {
  const metadata = this.modelMetadata();
  const config = this.modelConfig();
  const engineName = this.getEngineNameById(this.selectedEngine());

  // Base payload with common fields
  const payload = {
    name: metadata.name,
    description: metadata.description,
    model: config.selectedModel,
    type: this.isEmbedding() ? 'Embedding' : 'Generative',
    aiEngine: engineName,
    practiceArea: this.getPracticeAreaId(metadata.practiceArea),
    goodAt: this.getTagIds(metadata.goodAtTags),
  };

  // Add engine-specific fields based on selected engine
  switch (engineName) {
    case 'AzureOpenAI':
      return {
        ...payload,
        baseurl: config.baseUrl,
        llmDeploymentName: config.deploymentName,
        apiKeyEncoded: config.apiKey,
        apiVersion: config.apiVersion,
      };

    case 'AmazonBedrock':
      return {
        ...payload,
        awsAccessKey: config.awsAccessKey,
        awsSecretKey: config.awsSecretKey,
        awsRegion: config.awsRegion,
        bedrockModelId: config.bedrockModelId,
      };

    case 'GoogleAI':
      return {
        ...payload,
        gcpProjectId: config.gcpProjectId,
        gcpLocation: config.gcpLocation,
        vertexAIEndpoint: config.vertexAIEndpoint,
      };

    case 'DaOpenSourceAI':
      return {
        ...payload,
        serviceUrl: config.serviceUrl,
        apiKeyEncoded: config.apiKeyEncoded,
        headerName: config.headerName,
      };

    case 'DatabricksAI':
    case 'BNY':
      return {
        ...payload,
        serviceUrl: config.serviceUrl,
        apiKeyEncoded: config.apiKeyEncoded,
        headerName: config.headerName,
      };

    default:
      // Fallback to Azure fields for unknown engines
      return {
        ...payload,
        baseurl: config.baseUrl,
        llmDeploymentName: config.deploymentName,
        apiKeyEncoded: config.apiKey,
        apiVersion: config.apiVersion,
      };
  }
}

  private getEngineNameById(engineId: string): string {
    const engineMap: { [key: string]: string } = {
      databricksai: 'DatabricksAI',
      bny: 'BNY',
      amazonbedrock: 'AmazonBedrock',
      azureopenai: 'AzureOpenAI',
      googleai: 'GoogleAI',
      daopensourceai : 'DaOpenSourceAI',
    };
    const engineName = engineMap[engineId] || engineId;
    return engineName;
  }

  private getPracticeAreaId(practiceAreaName: string): number | undefined {
    const practiceArea = this.dropdownStore.practiceAreaOptions().find(
      (option: any) => option.value === practiceAreaName
    );
    return practiceArea?.id;
  }

  private getTagIds(tagNames: string[]): number[] {
    const tagOptions = this.dropdownStore.goodAtOptions();
    return tagNames
      .map(tagName => {
        const tag = tagOptions.find((option: any) => option.value === tagName);
        return tag?.id;
      })
      .filter((id): id is number => id !== undefined);
  }

  private validateForm(): void {
    const metadata = this.modelMetadata();
    const config = this.modelConfig();
    const hasEngine = this.selectedEngine();

    // Basic required fields
    const basicFieldsValid = !!(
      metadata.name &&
      metadata.description &&
      hasEngine &&
      config.selectedModel
    );

    if (!basicFieldsValid) {
      this.isFormValid.set(false);
      return;
    }

    // Engine-specific validation
    const engineName = this.getEngineNameById(hasEngine);
    let engineFieldsValid = true;

    switch (engineName) {
      case 'AzureOpenAI':
        engineFieldsValid = !!(
          config.baseUrl &&
          config.deploymentName &&
          config.apiKey &&
          config.apiVersion
        );
        break;
      
      case 'AmazonBedrock':
        engineFieldsValid = !!(
          config.awsAccessKey &&
          config.awsSecretKey &&
          config.awsRegion &&
          config.bedrockModelId
        );
        break;
      
      case 'GoogleAI':
        engineFieldsValid = !!(
          config.gcpProjectId &&
          config.gcpLocation &&
          config.vertexAIEndpoint
        );
        break;
        case 'DaOpenSourceAI':
        engineFieldsValid = !!(
          config.serviceUrl &&
          config.apiKeyEncoded &&
          config.headerName
        );
        break;
      
      case 'DatabricksAI':
      case 'BNY':
        engineFieldsValid = !!(
          config.serviceUrl &&
          config.apiKeyEncoded &&
          config.headerName
        );
        break;
      
      default:
        // For unknown engines, require basic fields
        engineFieldsValid = !!(
          config.baseUrl &&
          config.deploymentName &&
          config.apiKey &&
          config.apiVersion
        );
    }

    this.isFormValid.set(engineFieldsValid);
  }
}
