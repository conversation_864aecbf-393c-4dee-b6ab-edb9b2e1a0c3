.model-builder-container {
    box-shadow: #D1D3D8;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    scrollbar-width: none;
    -ms-overflow-style: none;
    border-radius: 16px;
  
  // Hide scrollbar for webkit browsers
  &::-webkit-scrollbar {
    display: none;
  }
  
  // Hide scrollbar for Firefox
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.main-content {
  flex: 1;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow-y: auto;
  padding: 24px;
  margin:8px 12px 24px 0px;
  
  // Hide scrollbar for webkit browsers
  &::-webkit-scrollbar {
    display: none;
  }
  
  // Hide scrollbar for Firefox
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.section-divider {
  height: 1px;
  background: #e2e8f0;
  margin: 32px 0;
  flex-shrink: 0;
}

// Skeleton loader styles
.metadata-skeleton {
  background: #ffffff;
  padding: 24px;
  margin: 8px 0px 24px 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.model-types-skeleton {
  margin-bottom: 24px;
  
  .skeleton-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }
  
  .skeleton-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    
    @media (max-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
  
  .skeleton-card {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
  }
}

.skeleton-divider {
  margin: 24px 0;
}

.model-config-skeleton {
  .skeleton-form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-top: 16px;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
  
  .skeleton-form-field {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}
