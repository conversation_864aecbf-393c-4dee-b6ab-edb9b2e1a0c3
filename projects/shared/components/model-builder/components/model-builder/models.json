{"labels": {"model_name": "Model Name", "model_description": "Model Description", "model_practice_area": "Practice Area", "model_good_at": "Good At", "main_heading": "Model Editor", "metadata_title": "<PERSON> <PERSON><PERSON><PERSON>", "model_types_title": "Select your AI Engine and Model Type", "model_inputs_title": "Model Configuration", "tabs": {"embedding": "Embedding", "generative": "Generative"}, "choose_model": "<PERSON><PERSON> Model", "base_url": "Base URL", "deployment_name": "LLM Deployment Name", "api_key": "API Key Encoded", "api_version": "API Version", "aws_access_key": "AWS Access Key", "aws_secret_key": "AWS Secret Key", "aws_region": "AWS Region", "bedrock_model_id": "Bedrock Model ID", "save": "Save", "cancel": "Cancel"}, "placeholders": {"name": "Enter model name", "description": "Enter model description", "practice_area": "Select practice area", "good_at_tags": "Select good at tags", "select_model": "Select model", "bedrock_model_id": "Enter Bedrock model ID", "base_url": "Enter base URL", "deployment_name": "Enter deployment name", "api_key": "Enter API key", "api_version": "Enter API version", "aws_access_key": "Enter AWS access key", "aws_secret_key": "Enter AWS secret key", "aws_region": "Enter AWS region", "gcp_project_id": "Enter GCP project ID", "gcp_location": "Enter GCP location", "vertex_ai_endpoint": "Enter Vertex AI endpoint", "service_url": "Enter service URL", "header_name": "Enter header name"}}