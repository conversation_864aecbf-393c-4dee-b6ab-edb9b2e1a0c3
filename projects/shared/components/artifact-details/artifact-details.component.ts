import {
  AavaButtonComponent,
  AavaDefaultCardComponent,
  AavaDialogService,
  AavaIconComponent,
  AavaTagComponent,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnDestroy,
} from '@angular/core';
import { ListModalComponent } from '@shared';

export interface ArtifactDetailsConfig {
  title?: string;
  entityType?: string;
  description?: string;
  status?: 'active' | 'inactive' | 'pending' | 'error';
  tags?: string[];
  metadata?: { [key: string]: string | number | boolean };
  icon?: string;
  imageUrl?: string;
  disabled?: boolean;
  variant?: 'default' | 'compact' | 'large';
  theme?: 'default' | 'outline' | 'minimal';
  showActions?: boolean;
}

export interface ArtifactAction {
  id: string;
  label: string;
  icon?: string;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'danger';
}

@Component({
  selector: 'app-artifact-details',
  standalone: true,
  imports: [
    CommonModule,
    AavaButtonComponent,
    AavaDefaultCardComponent,
    AavaIconComponent,
    AavaTagComponent,
  ],
  templateUrl: './artifact-details.component.html',
  styleUrls: ['./artifact-details.component.scss'],
})
export class ArtifactDetailsComponent implements OnDestroy {
  @Input() config: ArtifactDetailsConfig = {};
  @Input() title: string = 'Artifact Title';
  @Input() entityType: string = '';
  @Input() description: string = '';
  @Input() status: 'active' | 'inactive' | 'pending' | 'error' = 'active';
  @Input() tags: string[] = [];
  @Input() metadata: { [key: string]: string | number | boolean } = {};
  @Input() icon: string = 'box';
  @Input() imageUrl: string = '';
  @Input() disabled: boolean = false;
  @Input() variant: 'default' | 'compact' | 'large' = 'default';
  @Input() theme: 'default' | 'outline' | 'minimal' = 'default';
  @Input() showActions: boolean = true;
  @Input() actions: ArtifactAction[] = [];
  @Input() customClass: string = '';

  @Output() cardClicked = new EventEmitter<MouseEvent>();
  @Output() actionClicked = new EventEmitter<{
    action: ArtifactAction;
    event: MouseEvent;
  }>();
  @Output() tagClicked = new EventEmitter<{ tag: string; event: MouseEvent }>();
  @Output() actionPlayClick = new EventEmitter<Event>();
  @Output() menuClicked = new EventEmitter<MouseEvent>();

  // Modal state
  isModalVisible: boolean = false;

  // Context menu state
  isContextMenuVisible: boolean = false;
  contextMenuPosition = { top: 0, left: 0 };

  constructor(private dialogService: AavaDialogService) {}

  // New output events for context menu actions
  @Output() shareClicked = new EventEmitter<MouseEvent>();
  @Output() addToListClicked = new EventEmitter<MouseEvent>();

  // Allow configuration via config object or individual inputs
  get finalTitle(): string {
    return this.config.title || this.title;
  }

  get finalEntityType(): string {
    return this.config.entityType || this.entityType;
  }

  get finalDescription(): string {
    return this.config.description || this.description;
  }

  get finalStatus(): string {
    return this.config.status || this.status;
  }

  get finalTags(): string[] {
    return this.config.tags || this.tags;
  }

  get finalMetadata(): { [key: string]: string | number | boolean } {
    return this.config.metadata || this.metadata;
  }

  get finalIcon(): string {
    // If icon is explicitly set via config or input, use that
    const explicitIcon = this.config.icon || this.icon;
    if (explicitIcon !== 'box') {
      // 'box' is the default value
      return explicitIcon;
    }

    // Otherwise, determine icon based on entityType
    return this.getIconByEntityType();
  }

  get finalImageUrl(): string {
    return this.config.imageUrl || this.imageUrl;
  }

  /**
   * Determines the icon based on the entityType value
   */
  private getIconByEntityType(): string {
    const entityType = this.finalEntityType.toLowerCase();

    if (entityType.includes('agent')) {
      return 'bot';
    } else if (entityType.includes('pipeline')) {
      return 'workflow';
    } else if (entityType.includes('tools')) {
      return 'wrench';
    } else if (entityType.includes('model')) {
      return 'box';
    } else if (entityType.includes('guardrail')) {
      return 'shield';
    } else if (entityType.includes('knowledgebase')) {
      return 'book-text';
    }

    // Default icon if no condition matches
    return 'box';
  }

  /**
   * Determines the tag color based on the entityType value
   * - Pipeline: Global colors/Pink/P - 50
   * - Agent: Global colors/Deep purple/p - 50
   * - Model: Global colors/Royal blue/50
   * - Default: 'default'
   */
  getTagColorByEntityType():
    | 'error'
    | 'default'
    | 'primary'
    | 'success'
    | 'warning'
    | 'info'
    | 'custom' {
    const entityType = this.finalEntityType.toLowerCase();

    if (
      entityType.includes('pipeline') ||
      entityType.includes('agent') ||
      entityType.includes('model')
    ) {
      return 'custom';
    }

    // Default color
    return 'default';
  }

  /**
   * Provides custom styles for tags based on entityType
   * - Pipeline: Global colors/Pink/P - 50 (#FCE7F3)
   * - Agent: Global colors/Deep purple/p - 50 (#F3E8FF)
   * - Model: Global colors/Royal blue/50 (#EFF6FF)
   */
  getTagCustomStyle(): { background: string; color: string } {
    const entityType = this.finalEntityType.toLowerCase();

    if (entityType.includes('pipeline')) {
      return { background: '#FCE7F3', color: '#2D3036' }; // Global colors/Pink/P - 50
    } else if (entityType.includes('agent')) {
      return { background: '#F3E8FF', color: '#2D3036' }; // Global colors/Deep purple/p - 50
    } else if (entityType.includes('model')) {
      return { background: '#EFF6FF', color: '#2D3036' }; // Global colors/Royal blue/50
    }

    // Default - won't be used if color is 'default'
    return { background: '', color: '' };
  }

  get finalDisabled(): boolean {
    return this.config.disabled || this.disabled;
  }

  get finalVariant(): string {
    return this.config.variant || this.variant;
  }

  get finalTheme(): string {
    return this.config.theme || this.theme;
  }

  get finalShowActions(): boolean {
    return this.config.showActions !== undefined
      ? this.config.showActions
      : this.showActions;
  }

  get statusIcon(): string {
    switch (this.finalStatus) {
      case 'active':
        return 'check-circle';
      case 'pending':
        return 'clock';
      case 'error':
        return 'x-circle';
      case 'inactive':
        return 'pause-circle';
      default:
        return 'info-circle';
    }
  }

  get metadataEntries(): { key: string; value: string | number | boolean }[] {
    return Object.entries(this.finalMetadata).map(([key, value]) => ({
      key,
      value,
    }));
  }

  onCardClick(event: MouseEvent) {
    if (this.finalDisabled) {
      event.preventDefault();
      return;
    }

    // Open the modal
    this.isModalVisible = true;
    this.cardClicked.emit(event);
  }

  onModalClose() {
    this.isModalVisible = false;
  }

  onPlayClick(event: Event) {
    event.stopPropagation(); // Prevent card click
    this.actionPlayClick.emit(event);
  }

  onActionClick(action: ArtifactAction, event: MouseEvent) {
    if (this.finalDisabled || action.disabled) {
      event.preventDefault();
      event.stopPropagation();
      return;
    }

    event.stopPropagation();
    this.actionClicked.emit({ action, event });
  }

  onMenuClick(event: MouseEvent) {
    // Prevent the card click event from being triggered
    event.stopPropagation();
    event.preventDefault();

    // Toggle the context menu
    this.isContextMenuVisible = !this.isContextMenuVisible;

    if (this.isContextMenuVisible) {
      // Position the context menu next to the button that was clicked
      const buttonElement = event.currentTarget as HTMLElement;
      const rect = buttonElement.getBoundingClientRect();

      // Calculate position for the context menu
      this.contextMenuPosition = {
        top: 40, // Position below the button
        left: 0, // Align with the button's left edge
      };

      // Add click outside listener to close the menu when clicking elsewhere
      // Using setTimeout to ensure the event is registered after the current click event is processed
      setTimeout(() => {
        // Add the event listener to the document
        document.addEventListener('click', this.closeContextMenu);
      }, 0);
    } else {
      // If we're closing the menu, remove the event listener
      document.removeEventListener('click', this.closeContextMenu);
    }

    // Emit event for parent components to handle the menu click
    this.menuClicked.emit(event);
  }

  /**
   * Close the context menu when clicking outside
   * Using arrow function to preserve 'this' context
   */
  closeContextMenu = (event: MouseEvent) => {
    // Get the clicked element
    const clickedElement = event.target as HTMLElement;

    // Check if the clicked element is inside the context-menu or the button
    const isClickInsideMenu = clickedElement.closest('.context-menu');
    const isClickOnMenuButton = clickedElement.closest(
      '[aria-label="Open options menu"]'
    );

    // If clicking outside menu and not on the menu button, close the menu
    if (!isClickInsideMenu && !isClickOnMenuButton) {
      this.isContextMenuVisible = false;
      document.removeEventListener('click', this.closeContextMenu);
    }
  };

  /**
   * Handle context menu option clicks
   */
  onContextMenuOptionClick(option: string, event: MouseEvent) {
    // Stop event propagation to prevent the document click handler from being triggered
    event.stopPropagation();
    event.preventDefault();

    // Close the menu
    this.isContextMenuVisible = false;

    // Remove the document click listener since we're manually closing the menu
    document.removeEventListener('click', this.closeContextMenu);

    // Emit the appropriate event based on which option was clicked
    switch (option) {
      case 'share':
        this.shareClicked.emit(event);
        break;
      case 'addToList':
        this.dialogService.openModal(ListModalComponent, {
          width: '70vw',
          maxHeight: '100vh',
        });
        this.addToListClicked.emit(event);
        break;
      default:
        break;
    }
  }

  onTagClick(tag: string, event: MouseEvent) {
    if (this.finalDisabled) {
      event.preventDefault();
      event.stopPropagation();
      return;
    }

    event.stopPropagation();
    this.tagClicked.emit({ tag, event });
  }

  onKeyPress(event: KeyboardEvent) {
    if (this.finalDisabled) {
      return;
    }

    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.cardClicked.emit(event as unknown as MouseEvent);
    }
  }

  formatMetadataValue(value: string | number | boolean): string {
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  }

  /**
   * Clean up event listeners when component is destroyed
   */
  ngOnDestroy() {
    // Make sure the context menu is closed and event listeners are removed
    this.isContextMenuVisible = false;
    document.removeEventListener('click', this.closeContextMenu);
  }
}
