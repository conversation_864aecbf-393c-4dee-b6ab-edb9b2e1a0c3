# Artifact Details Component

A reusable Angular component for displaying detailed information about artifacts with support for various themes, variants, and interactive features.

## Features

- **Flexible Configuration**: Support for both individual props and configuration objects
- **Multiple Variants**: Compact, default, and large sizes
- **Theme Support**: Default, outline, and minimal themes
- **Status Indicators**: Active, pending, error, and inactive states with visual indicators
- **Interactive Elements**: Clickable tags, action buttons, and card interaction
- **Accessibility**: Full keyboard navigation and ARIA support
- **Responsive Design**: Mobile-friendly with adaptive layouts
- **Glass Morphism**: Modern design with backdrop blur effects

## Basic Usage

```typescript
import { ArtifactDetailsComponent } from '@shared/components/artifact-details';

@Component({
  selector: 'app-example',
  standalone: true,
  imports: [ArtifactDetailsComponent],
  template: `
    <app-artifact-details
      [title]="'My Document'"
      [entityType]="'PDF Document'"
      [description]="'A detailed document containing important information'"
      [status]="'active'"
      [tags]="['document', 'important', 'pdf']"
      [metadata]="metadata"
      [actions]="actions"
      (cardClicked)="onCardClick($event)"
      (actionClicked)="onActionClick($event)"
      (tagClicked)="onTagClick($event)"
    ></app-artifact-details>
  `,
})
export class ExampleComponent {
  metadata = {
    Created: '2024-01-15',
    Size: '2.5 MB',
    Pages: 24,
  };

  actions = [
    { id: 'view', label: 'View', variant: 'primary' as const },
    { id: 'edit', label: 'Edit', variant: 'secondary' as const },
    { id: 'delete', label: 'Delete', variant: 'danger' as const },
  ];

  onCardClick(event: MouseEvent) {
    console.log('Card clicked:', event);
  }

  onActionClick(event: { action: any; event: MouseEvent }) {
    console.log('Action clicked:', event.action.id);
  }

  onTagClick(event: { tag: string; event: MouseEvent }) {
    console.log('Tag clicked:', event.tag);
  }
}
```

## Configuration Object Usage

```typescript
import { ArtifactDetailsConfig } from '@shared/components/artifact-details';

@Component({
  template: `
    <app-artifact-details
      [config]="artifactConfig"
      [actions]="actions"
    ></app-artifact-details>
  `,
})
export class ConfigExampleComponent {
  artifactConfig: ArtifactDetailsConfig = {
    title: 'Project Report',
    entityType: 'Q4 2024 Analysis',
    description: 'Comprehensive analysis of project performance and metrics',
    status: 'active',
    tags: ['report', 'q4', 'analysis'],
    metadata: {
      Author: 'John Doe',
      Department: 'Analytics',
      Reviewed: true,
    },
    icon: 'document',
    variant: 'large',
    theme: 'outline',
    showActions: true,
  };

  actions = [
    {
      id: 'download',
      label: 'Download',
      icon: '⬇️',
      variant: 'primary' as const,
    },
    { id: 'share', label: 'Share', icon: '🔗', variant: 'secondary' as const },
  ];
}
```

## Variants

### Size Variants

```html
<!-- Compact variant -->
<app-artifact-details
  variant="compact"
  [title]="'Compact Card'"
  [entityType]="'Small size'"
></app-artifact-details>

<!-- Default variant -->
<app-artifact-details
  variant="default"
  [title]="'Default Card'"
  [entityType]="'Medium size'"
></app-artifact-details>

<!-- Large variant -->
<app-artifact-details
  variant="large"
  [title]="'Large Card'"
  [entityType]="'Big size'"
></app-artifact-details>
```

### Theme Variants

```html
<!-- Default theme (glass morphism) -->
<app-artifact-details
  theme="default"
  [title]="'Glass Effect'"
></app-artifact-details>

<!-- Outline theme -->
<app-artifact-details
  theme="outline"
  [title]="'Outline Style'"
></app-artifact-details>

<!-- Minimal theme -->
<app-artifact-details
  theme="minimal"
  [title]="'Minimal Style'"
></app-artifact-details>
```

### Status Variants

```html
<!-- Active status (green) -->
<app-artifact-details
  status="active"
  [title]="'Active Artifact'"
></app-artifact-details>

<!-- Pending status (yellow) -->
<app-artifact-details
  status="pending"
  [title]="'Pending Approval'"
></app-artifact-details>

<!-- Error status (red) -->
<app-artifact-details
  status="error"
  [title]="'Failed Processing'"
></app-artifact-details>

<!-- Inactive status (gray) -->
<app-artifact-details
  status="inactive"
  [title]="'Archived Artifact'"
></app-artifact-details>
```

## Props

### Required Props

None - all props have sensible defaults.

### Optional Props

| Prop          | Type                                             | Default            | Description                                       |
| ------------- | ------------------------------------------------ | ------------------ | ------------------------------------------------- |
| `config`      | `ArtifactDetailsConfig`                          | `{}`               | Configuration object for all settings             |
| `title`       | `string`                                         | `'Artifact Title'` | Main title of the artifact                        |
| `entityType`  | `string`                                         | `''`               | Entity type or category                           |
| `description` | `string`                                         | `''`               | Detailed description                              |
| `status`      | `'active' \| 'inactive' \| 'pending' \| 'error'` | `'active'`         | Status indicator                                  |
| `tags`        | `string[]`                                       | `[]`               | Array of tags to display                          |
| `metadata`    | `{ [key: string]: string \| number \| boolean }` | `{}`               | Key-value metadata                                |
| `icon`        | `string`                                         | `'document'`       | Icon identifier ('document', 'folder', or custom) |
| `imageUrl`    | `string`                                         | `''`               | URL for custom image (overrides icon)             |
| `disabled`    | `boolean`                                        | `false`            | Disable all interactions                          |
| `variant`     | `'default' \| 'compact' \| 'large'`              | `'default'`        | Size variant                                      |
| `theme`       | `'default' \| 'outline' \| 'minimal'`            | `'default'`        | Visual theme                                      |
| `showActions` | `boolean`                                        | `true`             | Show action buttons                               |
| `actions`     | `ArtifactAction[]`                               | `[]`               | Array of action buttons                           |
| `customClass` | `string`                                         | `''`               | Additional CSS classes                            |

### Action Object Structure

```typescript
interface ArtifactAction {
  id: string; // Unique identifier
  label: string; // Button text
  icon?: string; // Optional icon
  disabled?: boolean; // Disable this action
  variant?: 'primary' | 'secondary' | 'danger'; // Button style
}
```

## Events

| Event           | Payload                                         | Description                              |
| --------------- | ----------------------------------------------- | ---------------------------------------- |
| `cardClicked`   | `MouseEvent`                                    | Emitted when the card is clicked         |
| `actionClicked` | `{ action: ArtifactAction, event: MouseEvent }` | Emitted when an action button is clicked |
| `tagClicked`    | `{ tag: string, event: MouseEvent }`            | Emitted when a tag is clicked            |

## Accessibility

- Full keyboard navigation support
- ARIA labels and roles
- Screen reader compatible
- High contrast mode support
- Reduced motion support

## Styling

The component uses CSS custom properties for theming:

```css
:root {
  --color-primary: #6366f1;
  --color-primary-light: #8b5cf6;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-neutral: #6b7280;
  --color-text-primary: #ffffff;
  --color-text-secondary: rgba(255, 255, 255, 0.8);
  --color-text-tertiary: rgba(255, 255, 255, 0.6);
  --glass-background-color: rgba(17, 25, 40, 0.51);
  --glass-border-color: rgba(255, 255, 255, 0.125);
}
```

## Examples

### File Management

```typescript
const fileArtifact = {
  title: 'presentation.pptx',
  entityType: 'PowerPoint Presentation',
  description: 'Q4 2024 Business Review Presentation',
  status: 'active' as const,
  tags: ['presentation', 'business', 'q4'],
  metadata: {
    Size: '15.2 MB',
    Modified: '2024-01-15',
    Slides: 42,
  },
  icon: 'document',
};

const fileActions = [
  { id: 'open', label: 'Open', variant: 'primary' as const },
  { id: 'download', label: 'Download', variant: 'secondary' as const },
  { id: 'share', label: 'Share', variant: 'secondary' as const },
  { id: 'delete', label: 'Delete', variant: 'danger' as const },
];
```

### Project Status

```typescript
const projectArtifact = {
  title: 'Website Redesign',
  entityType: 'Frontend Development',
  description: 'Complete redesign of the company website with modern UI/UX',
  status: 'pending' as const,
  tags: ['frontend', 'design', 'website'],
  metadata: {
    Progress: '75%',
    'Team Size': 5,
    Deadline: '2024-02-28',
  },
  variant: 'large' as const,
  theme: 'outline' as const,
};
```

### Data Analysis

```typescript
const dataArtifact = {
  title: 'Customer Analytics Report',
  entityType: 'Data Science',
  description: 'Comprehensive analysis of customer behavior and trends',
  status: 'active' as const,
  tags: ['analytics', 'customers', 'data-science'],
  metadata: {
    Records: '1.2M',
    Accuracy: '94.5%',
    Runtime: '2.3s',
  },
  icon: 'folder',
};
```
