.complex-card {
  height: var(--card-height, 215px);
  min-height: 215px;
  max-height: 215px;
  width: 411px;
  max-width: 411px;
  min-width: 411px;
  box-sizing: border-box;

  .menu-button {
    padding: 0;
    border: none;
    background: none;
    cursor: pointer;
  }

  &.clickable-card {
    cursor: pointer;
    transition:
      transform 0.2s ease,
      box-shadow 0.2s ease;

    &:hover {
      transform: none; /* removing scale to maintain exact width */
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
    }

    &:focus {
      outline: 2px solid #6366f1;
      outline-offset: 2px;
    }

    &:active {
      transform: translateY(0);
    }
  }

  .card-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 100%;
    justify-content: space-between;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
    overflow: hidden;
  }

  .icon-container {
    display: flex;
    width: 36px;
    height: 36px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 8px;
    background: #e7e7e7;
    flex-shrink: 0;
  }

  .card-title {
    color: #3b3f46;
    font-family: Inter;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    margin: 0;

    /* Truncate text with ellipsis */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    min-width: 0;
    max-width: 100%;
    width: 150px;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
    margin-left: 8px;

    .menu-container {
      position: relative;
    }

    .context-menu {
      position: absolute;
      top: 100%;
      right: 0;
      z-index: 9999; // Higher z-index to ensure it appears on top
      min-width: 160px;
      padding: 8px 0;
      margin: 0;
      background-color: #ffffff;
      border-radius: 6px;
      box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.15);
      list-style: none;

      .menu-item {
        display: flex;
        align-items: center;
        padding: 10px 16px;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        color: #3b3f46;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }

  .rating-text {
    color: #3b3f46;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 16px;
    letter-spacing: 0.028px;
  }

  .card-description {
    overflow: hidden;
    color: #3b3f46;
    text-overflow: ellipsis;
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin: 0;
    min-height: 40px;
    max-height: 40px;

    /* Truncate after 2 lines */
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: normal;
  }

  .tags-section {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    max-height: 32px;
    overflow: hidden;
  }

  .meta-info-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .meta-left,
  .meta-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .meta-text {
    color: #898e99;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;

    /* Truncate at 1st line */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .action-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .action-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .action-number {
    color: #898e99;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }

  .action-right {
    display: flex;
    align-items: center;
    gap: 24px;

    .play-button {
      display: flex;
      min-width: 36px;
      padding: 10px 12px;
      justify-content: center;
      align-items: center;
      gap: 8px;
      flex: 1 0 0;
      border-radius: 6px;
      background: linear-gradient(90deg, #e91e63 0%, #d41b5a 100%);
    }
  }
}

// Modal styles
.empty-modal-content {
  padding: 20px 0;

  .empty-card {
    width: 100%;
    min-height: 150px;

    .empty-card-content {
      padding: 40px;
      text-align: center;
      color: #6b7280;
      font-family: Inter;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
    }
  }
}
