<aava-default-card
  class="complex-card clickable-card"
  (click)="onCardClick($event)"
  tabindex="0"
  role="button"
  aria-label="View artifact details"
  (keydown)="onKeyPress($event)"
>
  <div class="card-content">
    <!-- Header Section -->
    <div class="card-header">
      <div class="header-left">
        <aava-icon
          iconName="{{ finalIcon }}"
          [iconSize]="24"
          iconColor="#000000"
        ></aava-icon>

        <h2 class="card-title">{{ title }}</h2>
      </div>
      <div class="header-right">
        <div class="menu-container">
          <aava-button
            iconName="ellipsis-vertical"
            iconPosition="only"
            variant="tertiary"
            size="xs"
            [outlined]="false"
            (click)="onMenuClick($event)"
            aria-label="Open options menu"
            class="menu-button"
          >
          </aava-button>
          <!-- Context Menu -->
          <div class="context-menu" *ngIf="isContextMenuVisible">
            <div
              class="menu-item"
              (click)="onContextMenuOptionClick('share', $event)"
            >
              <span>Share</span>
            </div>
            <div
              class="menu-item"
              (click)="onContextMenuOptionClick('addToList', $event)"
            >
              <span>Add to List</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Description -->
    <p class="card-description">
      {{ description }}
    </p>

    <!-- Tags Section -->
    <div class="tags-section">
      <aava-tag
        [label]="entityType"
        [color]="getTagColorByEntityType()"
        [customStyle]="getTagCustomStyle()"
        size="lg"
        [pill]="false"
      ></aava-tag>
    </div>

    <!-- Action Section -->
    <div class="action-section">
      <div class="action-left">
        <aava-icon
          iconName="user"
          [iconSize]="16"
          iconColor="#898E99"
        ></aava-icon>
        <span class="action-number">{{ metadata['Author'] }}</span>
      </div>
      <div class="action-right">
        <aava-button
          iconName="play"
          iconPosition="only"
          variant="secondary"
          [outlined]="true"
          size="xs"
          [pill]="false"
          (click)="onPlayClick($event)"
        >
        </aava-button>
      </div>
    </div>
  </div>
</aava-default-card>
