<div [class]="containerClasses">
  <!-- Input Container -->
  <div class="siri-text-box__container">
    <!-- Left Icon (Search) -->

    <img
      *ngIf="mergedConfig.showSearchIcon"
      class="siri-text-box__search-logo"
      src="revelioLogo.svg"
      alt="Search icon"
    />

    <!-- Input Field -->
    <input
      [class]="inputClasses"
      type="text"
      [placeholder]="mergedConfig.placeholder"
      [disabled]="mergedConfig.disabled"
      [readonly]="mergedConfig.readonly"
      [maxLength]="mergedConfig.maxLength"
      [value]="value"
      (input)="onInputChange($event)"
      (focus)="onInputFocus($event)"
      (blur)="onInputBlur($event)"
      (keydown)="onKeydown($event)"
      autocomplete="off"
    />

    <!-- Clear Button -->
    <aava-button
      *ngIf="showClearButton"
      iconName="eraser"
      iconPosition="only"
      clearable="true"
      iconColor="#fff"
      (click)="onClearClick($event)"
    ></aava-button>

    <!-- Right Icon (Microphone) -->
  </div>
</div>
