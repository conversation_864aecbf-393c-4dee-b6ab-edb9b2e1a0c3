/* Siri Text Box Component Styles */
.siri-text-box {
  display: block;
  width: 100%;
  position: relative;

  /* Input Container */
  &__container {
    position: relative;
    display: flex;
    align-items: center;
    background-image: url('../../public/White_Revelio.svg');
    background-repeat: no-repeat;
    background-position: center;
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--siri-border-radius, 50px);
    min-height: var(--siri-height, 60px);
    width: var(--siri-width, 100%);
    padding: 0 24px 0 20px;
    overflow: visible;
  }

  /* Input Field */
  &__input {
    flex: 1;
    width: 100%;
    border: none;
    outline: none;
    background: transparent;
    color: rgba(0, 0, 0, 0.8);
    font-size: 17px;
    font-weight: 400;
    line-height: 1.4;
    padding: 18px 0;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial,
      sans-serif;
    letter-spacing: 0.01em;
    z-index: 1;
    position: relative;

    &::placeholder {
      color: rgba(0, 0, 0, 0.5);
      font-weight: 400;
    }

    &:focus::placeholder {
      color: rgba(0, 0, 0, 0.3);
    }

    &--disabled {
      color: rgba(0, 0, 0, 0.3);
      cursor: not-allowed;

      &::placeholder {
        color: rgba(0, 0, 0, 0.2);
      }
    }

    &--readonly {
      cursor: default;
    }

    /* Remove browser styles */
    &::-webkit-search-decoration,
    &::-webkit-search-cancel-button,
    &::-webkit-search-results-button,
    &::-webkit-search-results-decoration {
      -webkit-appearance: none;
    }
  }

  /* Search Icon Image */
  img {
    width: 24px;
    height: 24px;
    opacity: 0.6;
  }

  /* Search Logo */
  &__search-logo {
    margin-right: 12px;
  }

  /* Icon Styles */
  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: transparent;
    border: none;
    color: rgba(0, 0, 0, 0.6);
    cursor: pointer;
    padding: 0;
    border-radius: 50%;
    z-index: 2;
    position: relative;

    &:hover {
      color: rgba(0, 0, 0, 0.8);
      background: rgba(0, 0, 0, 0.05);
    }

    &--search {
      margin-right: 16px;
    }

    &--mic {
      margin-left: 16px;

      &.siri-text-box__icon--listening {
        color: #ff6b6b;
        background: rgba(255, 107, 107, 0.1);
      }
    }
    svg {
      width: 100%;
      height: 100%;
      filter: none;
    }
  }

  /* Clear Button */
  &__clear-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 0, 0.1);
    border: none;
    color: rgba(0, 0, 0, 0.6);
    cursor: pointer;
    padding: 0;
    border-radius: 50%;
    margin-left: 12px;
    z-index: 2;
    position: relative;

    &:hover {
      background: rgba(0, 0, 0, 0.15);
      color: rgba(0, 0, 0, 0.8);
    }

    svg {
      width: 12px;
      height: 12px;
    }
  }

  /* Listening Indicator */
  &__listening-indicator {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 3;
  }

  &__wave {
    width: 4px;
    height: 4px;
    background: rgba(var(--rgb-rose, 255, 107, 107), 0.8);
    border-radius: 50%;
  }

  /* Focus State */
  &--focused &__container {
    border-color: var(--color-border-focus);
  }

  /* Disabled State */
  &__container--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;

    .siri-text-box__icon {
      cursor: not-allowed;

      &:hover {
        background: transparent;
      }
    }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    &__container {
      min-height: 56px;
      padding: 0 20px;
      border-radius: 28px;
    }

    &__input {
      font-size: 16px; /* Prevent zoom on iOS */
      padding: 16px 0;
    }

    &__icon {
      width: 24px;
      height: 24px;

      &--search {
        margin-right: 12px;
      }

      &--mic {
        margin-left: 12px;
      }
    }
  }

  @media (max-width: 480px) {
    &__container {
      min-height: 50px;
      padding: 0 16px;
      border-radius: 25px;
    }

    &__input {
      padding: 14px 0;
      font-size: 16px;
    }

    &__icon {
      width: 22px;
      height: 22px;

      &--search {
        margin-right: 10px;
      }

      &--mic {
        margin-left: 10px;
      }
    }
  }

  /* Accessibility */
  @media (prefers-contrast: high) {
    &__container {
      border-width: 2px;
    }
  }

  /* Focus visible support */
  &__input:focus-visible {
    outline: none;
  }

  &__icon:focus-visible,
  &__clear-btn:focus-visible {
    outline: 2px solid rgba(0, 122, 255, 0.8);
    outline-offset: 2px;
  }
}
