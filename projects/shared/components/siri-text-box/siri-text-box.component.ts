import { AavaButtonComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  forwardRef,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

export interface SiriTextBoxConfig {
  placeholder?: string;
  disabled?: boolean;
  readonly?: boolean;
  maxLength?: number;
  showMicIcon?: boolean;
  showSearchIcon?: boolean;
  clearable?: boolean;
  animated?: boolean;
}

@Component({
  selector: 'app-siri-text-box',
  standalone: true,
  imports: [CommonModule, AavaButtonComponent],
  templateUrl: './siri-text-box.component.html',
  styleUrls: ['./siri-text-box.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SiriTextBoxComponent),
      multi: true,
    },
  ],
})
export class SiriTextBoxComponent implements ControlValueAccessor {
  @Input() config: SiriTextBoxConfig = {};
  @Input() theme: 'light' | 'dark' = 'dark';
  @Input() autofocus: boolean = false;

  @Output() valueChange = new EventEmitter<string>();
  @Output() focusEvent = new EventEmitter<FocusEvent>();
  @Output() blurEvent = new EventEmitter<FocusEvent>();
  @Output() enter = new EventEmitter<KeyboardEvent>();
  @Output() micClick = new EventEmitter<MouseEvent | KeyboardEvent>();
  @Output() searchClick = new EventEmitter<MouseEvent | KeyboardEvent>();
  @Output() clearClick = new EventEmitter<MouseEvent>();

  // Internal state
  value: string = '';
  isFocused: boolean = true;
  isListening: boolean = true;

  // ControlValueAccessor callbacks
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private onChange = (_value: string) => {};
  private onTouched = () => {};

  // Default configuration
  get mergedConfig(): Required<SiriTextBoxConfig> {
    return {
      placeholder: 'Type to Siri',
      disabled: false,
      readonly: false,
      maxLength: 500,
      showMicIcon: true,
      showSearchIcon: false,
      clearable: true,
      animated: true,
      ...this.config,
    };
  }

  get inputClasses(): string {
    const classes = ['siri-text-box__input'];

    if (this.mergedConfig.disabled) {
      classes.push('siri-text-box__input--disabled');
    }

    if (this.mergedConfig.readonly) {
      classes.push('siri-text-box__input--readonly');
    }

    if (this.isFocused) {
      classes.push('siri-text-box__input--focused');
    }

    return classes.join(' ');
  }

  get containerClasses(): string {
    const classes = ['siri-text-box'];

    if (this.theme) {
      classes.push(`siri-text-box--${this.theme}`);
    }

    if (this.isFocused) {
      classes.push('siri-text-box--focused');
    }

    if (this.isListening) {
      classes.push('siri-text-box--listening');
    }

    if (this.mergedConfig.animated) {
      classes.push('siri-text-box--animated');
    }

    return classes.join(' ');
  }

  get showClearButton(): boolean {
    return (
      this.mergedConfig.clearable &&
      this.value.length > 0 &&
      !this.mergedConfig.disabled &&
      !this.mergedConfig.readonly
    );
  }

  // ControlValueAccessor implementation
  writeValue(value: string): void {
    this.value = value || '';
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.config = { ...this.config, disabled: isDisabled };
  }

  // Event handlers
  onInputChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.value = target.value;
    this.onChange(this.value);
    this.valueChange.emit(this.value);
  }

  onInputFocus(event: FocusEvent): void {
    this.isFocused = true;
    this.focusEvent.emit(event);
  }

  onInputBlur(event: FocusEvent): void {
    this.isFocused = false;
    this.onTouched();
    this.blurEvent.emit(event);
  }

  onKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.enter.emit(event);
    }
  }

  onMicClick(event: MouseEvent | KeyboardEvent): void {
    this.isListening = !this.isListening;
    this.micClick.emit(event);
  }

  onClearClick(event: MouseEvent): void {
    event.stopPropagation();
    this.value = '';
    this.onChange(this.value);
    this.valueChange.emit(this.value);
    this.clearClick.emit(event);
  }
}
