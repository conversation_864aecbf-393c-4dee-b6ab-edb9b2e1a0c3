<div
  class="modal-backdrop"
  *ngIf="isVisible"
  (click)="onBackdropClick($event)"
  (keydown.escape)="onClose()"
>
  <div class="modal-container" [ngClass]="'modal-' + size">
    <div class="modal-header" *ngIf="title || showCloseButton">
      <h3 class="modal-title" *ngIf="title">{{ title }}</h3>
      <aava-icon
        *ngIf="showCloseButton"
        class="modal-close-btn"
        iconName="X"
        [iconSize]="20"
        iconColor="#6b7280"
        (click)="onClose()"
        tabindex="0"
        role="button"
        aria-label="Close modal"
        (keydown.enter)="onClose()"
        (keydown.space)="onClose()"
      ></aava-icon>
    </div>

    <div class="modal-body">
      <ng-content></ng-content>
    </div>

    <div class="modal-footer">
      <ng-content select="[slot=footer]"></ng-content>
    </div>
  </div>
</div>
