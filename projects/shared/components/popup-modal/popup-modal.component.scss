.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 90vh;
  overflow-y: auto;
  position: relative;

  &.modal-small {
    width: 400px;
    max-width: 90vw;
  }

  &.modal-medium {
    width: 600px;
    max-width: 90vw;
  }

  &.modal-large {
    width: 800px;
    max-width: 90vw;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--Brand-Neutral-n-100, #d1d3d8);

  .modal-title {
    margin: 0;
    color: var(--input-field-input-text-color, #3b3f46);
    font-family: Mulish;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }

  .modal-close-btn {
    cursor: pointer;
    padding: 4px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    &:focus {
      outline: 2px solid #6366f1;
      outline-offset: 2px;
    }
  }
}

.modal-body {
  padding: 1.5rem 1.5rem 1.5rem 1.5rem;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;

  &:empty {
    display: none;
  }
}
