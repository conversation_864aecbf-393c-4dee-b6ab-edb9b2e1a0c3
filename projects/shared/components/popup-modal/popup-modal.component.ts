import { AavaIconComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-popup-modal',
  standalone: true,
  imports: [CommonModule, AavaIconComponent],
  templateUrl: './popup-modal.component.html',
  styleUrls: ['./popup-modal.component.scss'],
})
export class PopupModalComponent {
  @Input() isVisible: boolean = false;
  @Input() title: string = '';
  @Input() showCloseButton: boolean = true;
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() backdropClose: boolean = true;

  @Output() modalClosed = new EventEmitter<void>();
  @Output() modalOpened = new EventEmitter<void>();

  constructor() {}

  onClose() {
    this.isVisible = false;
    this.modalClosed.emit();
  }

  onBackdropClick(event: MouseEvent) {
    if (this.backdropClose && event.target === event.currentTarget) {
      this.onClose();
    }
  }

  onModalOpen() {
    this.modalOpened.emit();
  }
}
