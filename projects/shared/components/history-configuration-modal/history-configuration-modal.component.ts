import {
  AavaButtonComponent,
  AavaDataGridComponent,
  AavaDialogService,
  AavaTagComponent,
  AvaColumnDefDirective,
  AvaHeaderCellDefDirective,
  AvaCellDefDirective,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Output } from '@angular/core';

@Component({
  selector: 'app-history-configuration-modal',
  standalone: true,
  imports: [
    AavaButtonComponent,
    AavaTagComponent,
    CommonModule,
    AavaDataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
  ],
  templateUrl: './history-configuration-modal.component.html',
  styles: [
    `
      .meta-info-modal {
        padding: 1.5rem;
        max-width: 100%;
        margin: 0 auto;

        .meta-modal-container {
          gap: 2rem;
        }

        .meta-modal-header {
          display: flex;
          gap: 0.75rem;
        }

        .meta-header {
          font-weight: 600;
        }

        .meta-body-container {
          display: flex;
          margin: 0.5rem 0;
          max-height: 50vh;
          overflow: auto;
          gap: 0.75rem;

          .meta-realm {
            display: flex;
            gap: 0.5rem;

            .meta-realm-field {
              display: flex;
              color: #898e99;
              gap: 0.75rem;

              .meta-realm-title {
                font-weight: 600;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }

          .history-table {
            width: 100%;
          }
        }

        .meta-body-container::-webkit-scrollbar {
          display: none;
        }

        .meta-body-container {
          scrollbar-width: none;
        }

        .meta-body-container {
          -ms-overflow-style: none;
        }

        .meta-body {
          color: #6b7280;
          margin: 0.5rem 0;
        }

        .meta-footer {
          display: flex;
          gap: 0.75rem;
        }

        // Responsive breakpoints
        @media (max-width: 820px) {
          width: calc(100% - 40px);
          margin: 0 20px;
        }

        @media (max-width: 480px) {
          width: calc(100% - 20px);
          margin: 0 10px;
        }
      }
    `,
  ],
})
export class HistoryConfigurationModalComponent {
  @Output() closed = new EventEmitter<any | null>();
  data: any = {};
  displayedColumns = [
    'version',
    'value',
    'changed by',
    'change time',
    'change type',
  ];

  constructor(private dialogService: AavaDialogService) {
    this.data = (this.dialogService as any).getModalData?.();
  }

  onClick() {
    this.closed.emit(null);
  }
}
