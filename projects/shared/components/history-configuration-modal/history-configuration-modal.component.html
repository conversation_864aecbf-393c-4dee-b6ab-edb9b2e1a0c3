<div class="meta-info-modal">
    <div class="meta-modal-container">
        <div dialog-header class="meta-modal-header align-items-center align-self-stretch">
            <div class="meta-header body-bold-default-400">Version History</div>
        </div>
        <div class="meta-body-container flex-column" dialog-body>
            <div class="meta-realm flex-column">
                <div class="meta-realm-field flex-row">
                    <div
                        class="meta-realm-title align-content-center justify-content-center body-bold-default-100-inter">
                        Configuration:
                    </div>
                    <div
                        class="align-content-center justify-content-center body-regular-default-100-inter">
                        {{data[0].configKey}}
                    </div>
                </div>
                <div class="meta-realm-field flex-row">
                    <div
                        class="meta-realm-title align-content-center justify-content-center body-bold-default-100-inter">
                        Application Service:
                    </div>
                    <div
                        class="align-content-center justify-content-center body-regular-default-100-inter">
                        {{data[0].applicationName}}
                    </div>
                </div>
                <div class="meta-realm-field flex-row">
                    <div
                        class="meta-realm-title align-content-center justify-content-center body-bold-default-100-inter">
                        Category:
                    </div>
                    <div
                        class="align-content-center justify-content-center body-regular-default-100-inter">
                        {{data[0].categoryName}}
                    </div>
                </div>
            </div>
            <div class="history-table">
                <aava-data-grid [dataSource]="data" [displayedColumns]="displayedColumns" class="styled-data-grid">
                    <ng-container avaColumnDef="version">
                        <ng-container *avaHeaderCellDef>
                            <div class="header-cell">
                                <span class="header-text">Version</span>
                            </div>
                        </ng-container>
                        <ng-container *avaCellDef="let row">
                            <div class="data-cell name-cell">
                                <span class="employee-name">{{ row.version }}</span>
                            </div>
                        </ng-container>
                    </ng-container>

                    <ng-container avaColumnDef="value">
                        <ng-container *avaHeaderCellDef>
                            <div class="header-cell">
                                <span class="header-text">Value</span>
                            </div>
                        </ng-container>
                        <ng-container *avaCellDef="let row">
                            <div class="data-cell email-cell">
                                <span class="email-text">{{ row.newValue }}</span>
                            </div>
                        </ng-container>
                    </ng-container>

                    <ng-container avaColumnDef="changed by">
                        <ng-container *avaHeaderCellDef>
                            <div class="header-cell">
                                <span class="header-text">Changed By</span>
                            </div>
                        </ng-container>
                        <ng-container *avaCellDef="let row">
                            <div class="data-cell department-cell">
                                <span class="department-badge">{{ row.changedBy }}</span>
                            </div>
                        </ng-container>
                    </ng-container>

                    <ng-container avaColumnDef="change time">
                        <ng-container *avaHeaderCellDef>
                            <div class="header-cell">
                                <span class="header-text">Change Time</span>
                            </div>
                        </ng-container>
                        <ng-container *avaCellDef="let row">
                            <div class="data-cell department-cell">
                                <span class="department-badge">{{ row.createdAt | date:'d/M/yyyy, h:mm:ss a' }}</span>
                            </div>
                        </ng-container>
                    </ng-container>

                    <ng-container avaColumnDef="change type">
                        <ng-container *avaHeaderCellDef>
                            <div class="header-cell">
                                <span class="header-text">Changed Type</span>
                            </div>
                        </ng-container>
                        <ng-container *avaCellDef="let row">
                            <div class="data-cell department-cell">
                                <span class="department-badge">{{ row.changeType }}</span>
                            </div>
                        </ng-container>
                    </ng-container>

                </aava-data-grid>
            </div>
        </div>
        <div dialog-footer class="meta-footer justify-content-end align-items-end">
            <aava-button label="Close" variant="primary" iconName="chevron-right" iconPosition="right"
                iconColor="#FFFFFF" [iconSize]="16" size="sm" (userClick)="onClick()"></aava-button>
        </div>
    </div>
</div>