import { Injectable, computed, effect, signal } from '@angular/core';
import { BehaviorSubject, Observable, tap, Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

import { UserTool, ToolConfig } from '../models/artifact.model';
import { ToolService } from '../services/tool.service';

export interface ToolFilters {
  teamId?: number;
  status?: string;
  createdBy?: number;
  page?: number;
  limit?: number;
  toolType?: string;
}

export interface ToolState {
  tools: UserTool[];
  currentTool: UserTool | null;
  loading: boolean;
  error: string | null;
  filters: ToolFilters;
  totalCount: number;
  categories: string[];
}

@Injectable({ providedIn: 'root' })
export class ToolStore {
  // Private state signals
  private readonly _tools = signal<UserTool[]>([]);
  private readonly _currentTool = signal<UserTool | null>(null);
  private readonly _loading = signal<boolean>(false);
  private readonly _error = signal<string | null>(null);
  private readonly _filters = signal<ToolFilters>({});
  private readonly _totalCount = signal<number>(0);
  private readonly _categories = signal<string[]>([]);
  private readonly _isDirty = signal<boolean>(false);
  private readonly _autosaveLoading = signal<boolean>(false);

  // Public readonly signals
  public readonly tools = this._tools.asReadonly();
  public readonly currentTool = this._currentTool.asReadonly();
  public readonly loading = this._loading.asReadonly();
  public readonly error = this._error.asReadonly();
  public readonly filters = this._filters.asReadonly();
  public readonly totalCount = this._totalCount.asReadonly();
  public readonly categories = this._categories.asReadonly();
  public readonly isDirty = this._isDirty.asReadonly();
  public readonly autosaveLoading = this._autosaveLoading.asReadonly();

  // Computed signals
  public readonly approvedTools = computed(() =>
    this._tools().filter(tool => tool.status === 'APPROVED')
  );

  public readonly draftTools = computed(() =>
    this._tools().filter(tool => tool.status === 'DRAFTED')
  );

  public readonly reviewTools = computed(() =>
    this._tools().filter(tool => tool.status === 'IN_REVIEW')
  );

  public readonly hasTools = computed(() => this._tools().length > 0);
  public readonly isLoading = computed(() => this._loading());
  public readonly hasError = computed(() => this._error() !== null);
  public readonly hasCategories = computed(() => this._categories().length > 0);

  // Observable streams for reactive components
  private readonly _toolsSubject = new BehaviorSubject<UserTool[]>([]);
  public readonly tools$ = this._toolsSubject.asObservable();

  private readonly _currentToolSubject = new BehaviorSubject<UserTool | null>(
    null
  );
  public readonly currentTool$ = this._currentToolSubject.asObservable();

  private readonly _loadingSubject = new BehaviorSubject<boolean>(false);
  public readonly loading$ = this._loadingSubject.asObservable();

  private readonly _errorSubject = new BehaviorSubject<string | null>(null);
  public readonly error$ = this._errorSubject.asObservable();

  private readonly _categoriesSubject = new BehaviorSubject<string[]>([]);
  public readonly categories$ = this._categoriesSubject.asObservable();

  // Autosave functionality
  private readonly saveSubject = new Subject<{
    id: number;
    updates: Partial<UserTool>;
  }>();

  constructor(private toolService: ToolService) {
    // Sync signals with subjects for backward compatibility
    effect(() => {
      this._toolsSubject.next(this._tools());
    });

    effect(() => {
      this._currentToolSubject.next(this._currentTool());
    });

    effect(() => {
      this._loadingSubject.next(this._loading());
    });

    effect(() => {
      this._errorSubject.next(this._error());
    });

    effect(() => {
      this._categoriesSubject.next(this._categories());
    });

    // Setup autosave with debouncing
    this.setupAutosave();

    // Load categories on initialization
    this.loadCategories();
  }

  // ==================== AUTOSAVE FUNCTIONALITY ====================

  /**
   * Setup autosave with debouncing
   */
  private setupAutosave(): void {
    this.saveSubject.pipe(debounceTime(2000)).subscribe(({ id, updates }) => {
      this.performAutosave(id, updates);
    });
  }

  /**
   * Trigger autosave for local changes
   * @param id - Tool ID (use -1 for new tools that haven't been created yet)
   * @param updates - Partial updates to save
   */
  onLocalChange(id: number, updates: Partial<UserTool>): void {
    this._isDirty.set(true);
    this.saveSubject.next({ id, updates });
  }

  /**
   * Perform the actual autosave operation
   */
  private performAutosave(id: number, updates: Partial<UserTool>): void {
    this.setAutosaveLoading(true);

    // Check if this is a new tool (id === -1) or an existing tool
    if (id === -1) {
      // This is a new tool that needs to be created
      this.createToolForAutosave(updates);
    } else {
      // This is an existing tool that needs to be updated
      this.updateToolForAutosave(id, updates);
    }
  }

  /**
   * Create a new tool during autosave
   */
  private createToolForAutosave(updates: Partial<UserTool>): void {
    // Convert partial updates to a complete tool object for creation
    const toolData: Omit<UserTool, 'id'> = {
      toolName: updates.toolName || 'Untitled Tool',
      toolDescription: updates.toolDescription || '',
      toolConfig: updates.toolConfig || {
        runtime: 'Python 3.10',
        libraries: [],
        maxRows: 1000,
      },
      version: updates.version || 1,
      status: updates.status || 'CREATED',
      teamId: updates.teamId || 1, // Default team ID, should be set properly
      ...updates,
    };

    this.toolService.create(toolData).subscribe({
      next: newTool => {
        // Add to tools list
        const currentTools = this._tools();
        this.updateTools([...currentTools, newTool]);

        // Set as current tool
        this.updateCurrentTool(newTool);

        // Clear dirty state
        this._isDirty.set(false);
        this.setAutosaveLoading(false);

        console.log('Tool created via autosave:', newTool);
      },
      error: error => {
        console.error('Autosave create failed for tool:', error);
        // Keep dirty state for retry - user can manually save
        this.setAutosaveLoading(false);
      },
    });
  }

  /**
   * Update an existing tool during autosave
   */
  private updateToolForAutosave(id: number, updates: Partial<UserTool>): void {
    this.toolService.update(id, updates).subscribe({
      next: updatedTool => {
        // Update in tools list
        const currentTools = this._tools();
        const updatedTools = currentTools.map(tool =>
          tool.id === id ? updatedTool : tool
        );
        this.updateTools(updatedTools);

        // Update current tool if it's the one being updated
        const currentTool = this._currentTool();
        if (currentTool && currentTool.id === id) {
          this.updateCurrentTool(updatedTool);
        }

        // Clear dirty state
        this._isDirty.set(false);
        this.setAutosaveLoading(false);
      },
      error: error => {
        console.error('Autosave update failed for tool:', error);
        // Keep dirty state for retry - user can manually save
        this.setAutosaveLoading(false);
      },
    });
  }

  // ==================== STATE MANAGEMENT ====================

  /**
   * Set loading state
   */
  private setLoading(loading: boolean): void {
    this._loading.set(loading);
  }

  /**
   * Set autosave loading state
   */
  private setAutosaveLoading(loading: boolean): void {
    this._autosaveLoading.set(loading);
  }

  /**
   * Set error state
   */
  private setError(error: string | null): void {
    this._error.set(error);
  }

  /**
   * Clear error state
   */
  private clearError(): void {
    this._error.set(null);
  }

  /**
   * Update tools list
   */
  private updateTools(tools: UserTool[]): void {
    this._tools.set(tools);
  }

  /**
   * Update current tool
   */
  private updateCurrentTool(tool: UserTool | null): void {
    this._currentTool.set(tool);
  }

  /**
   * Update filters
   */
  private updateFilters(filters: ToolFilters): void {
    this._filters.set(filters);
  }

  /**
   * Update total count
   */
  private updateTotalCount(count: number): void {
    this._totalCount.set(count);
  }

  /**
   * Update categories
   */
  private updateCategories(categories: string[]): void {
    this._categories.set(categories);
  }

  // ==================== CRUD OPERATIONS ====================

  /**
   * Load all tools with optional filtering
   */
  loadTools(filters?: ToolFilters): Observable<UserTool[]> {
    this.setLoading(true);
    this.clearError();

    if (filters) {
      this.updateFilters(filters);
    }

    return this.toolService.getAll(filters || this._filters()).pipe(
      tap({
        next: tools => {
          this.updateTools(tools);
          this.updateTotalCount(tools.length);
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to load tools');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Load tool by ID
   */
  loadToolById(id: number): Observable<UserTool> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.getById(id).pipe(
      tap({
        next: tool => {
          this.updateCurrentTool(tool);
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to load tool');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Create new tool
   */
  createTool(toolData: Omit<UserTool, 'id'>): Observable<UserTool> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.create(toolData).pipe(
      tap({
        next: newTool => {
          // Add to tools list
          const currentTools = this._tools();
          this.updateTools([...currentTools, newTool]);

          // Set as current tool
          this.updateCurrentTool(newTool);

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to create tool');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Create a new tool with temporary ID for autosave
   * This creates a tool in memory with id: -1 for autosave purposes
   */
  createNewToolForEditing(toolData: Partial<UserTool> = {}): UserTool {
    const newTool: UserTool = {
      id: -1, // Temporary ID for new tools
      toolName: toolData.toolName || 'Untitled Tool',
      toolDescription: toolData.toolDescription || '',
      toolConfig: toolData.toolConfig || {
        runtime: 'Python 3.10',
        libraries: [],
        maxRows: 1000,
      },
      version: toolData.version || 1,
      status: toolData.status || 'CREATED',
      teamId: toolData.teamId || 1,
      ...toolData,
    };

    // Set as current tool for editing
    this.updateCurrentTool(newTool);

    return newTool;
  }

  /**
   * Update existing tool
   */
  updateTool(id: number, updates: Partial<UserTool>): Observable<UserTool> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.update(id, updates).pipe(
      tap({
        next: updatedTool => {
          // Update in tools list
          const currentTools = this._tools();
          const updatedTools = currentTools.map(tool =>
            tool.id === id ? updatedTool : tool
          );
          this.updateTools(updatedTools);

          // Update current tool if it's the one being updated
          const currentTool = this._currentTool();
          if (currentTool && currentTool.id === id) {
            this.updateCurrentTool(updatedTool);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to update tool');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Delete tool
   */
  deleteTool(id: number): Observable<void> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.delete(id).pipe(
      tap({
        next: () => {
          // Remove from tools list
          const currentTools = this._tools();
          const filteredTools = currentTools.filter(tool => tool.id !== id);
          this.updateTools(filteredTools);

          // Clear current tool if it's the one being deleted
          const currentTool = this._currentTool();
          if (currentTool && currentTool.id === id) {
            this.updateCurrentTool(null);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to delete tool');
          this.setLoading(false);
        },
      })
    );
  }

  // ==================== LIFECYCLE OPERATIONS ====================

  /**
   * Submit tool for review
   */
  submitForReview(id: number): Observable<UserTool> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.submitForReview(id).pipe(
      tap({
        next: updatedTool => {
          // Update in tools list
          const currentTools = this._tools();
          const updatedTools = currentTools.map(tool =>
            tool.id === id ? updatedTool : tool
          );
          this.updateTools(updatedTools);

          // Update current tool if it's the one being updated
          const currentTool = this._currentTool();
          if (currentTool && currentTool.id === id) {
            this.updateCurrentTool(updatedTool);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to submit tool for review');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Approve tool
   */
  approveTool(id: number): Observable<UserTool> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.approve(id).pipe(
      tap({
        next: updatedTool => {
          // Update in tools list
          const currentTools = this._tools();
          const updatedTools = currentTools.map(tool =>
            tool.id === id ? updatedTool : tool
          );
          this.updateTools(updatedTools);

          // Update current tool if it's the one being updated
          const currentTool = this._currentTool();
          if (currentTool && currentTool.id === id) {
            this.updateCurrentTool(updatedTool);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to approve tool');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Reject tool
   */
  rejectTool(id: number): Observable<UserTool> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.reject(id).pipe(
      tap({
        next: updatedTool => {
          // Update in tools list
          const currentTools = this._tools();
          const updatedTools = currentTools.map(tool =>
            tool.id === id ? updatedTool : tool
          );
          this.updateTools(updatedTools);

          // Update current tool if it's the one being updated
          const currentTool = this._currentTool();
          if (currentTool && currentTool.id === id) {
            this.updateCurrentTool(updatedTool);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to reject tool');
          this.setLoading(false);
        },
      })
    );
  }

  // ==================== EXECUTION OPERATIONS ====================

  /**
   * Test tool execution
   */
  testTool(id: number, userInputs?: any): Observable<any> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.test(id, userInputs).pipe(
      tap({
        next: () => {
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Tool test failed');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Run tool in playground
   */
  playgroundRun(id: number, inputs?: any): Observable<any> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.playgroundRun(id, inputs).pipe(
      tap({
        next: () => {
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Playground execution failed');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Validate tool configuration
   */
  validateTool(id: number): Observable<any> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.validate(id).pipe(
      tap({
        next: () => {
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Tool validation failed');
          this.setLoading(false);
        },
      })
    );
  }

  // ==================== CATEGORY OPERATIONS ====================

  /**
   * Load available tool categories
   */
  loadCategories(): Observable<string[]> {
    return this.toolService.getCategories().pipe(
      tap({
        next: categories => {
          this.updateCategories(categories);
        },
        error: error => {
          console.error('Failed to load tool categories:', error);
        },
      })
    );
  }

  /**
   * Get tools by category
   */
  getToolsByCategory(category: string): Observable<UserTool[]> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.getByCategory(category).pipe(
      tap({
        next: tools => {
          this.updateTools(tools);
          this.updateTotalCount(tools.length);
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to load tools by category');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Search tools by name or description
   */
  searchTools(
    query: string,
    params?: {
      teamId?: number;
      status?: string;
      page?: number;
      limit?: number;
    }
  ): Observable<UserTool[]> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.search(query, params).pipe(
      tap({
        next: tools => {
          this.updateTools(tools);
          this.updateTotalCount(tools.length);
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Tool search failed');
          this.setLoading(false);
        },
      })
    );
  }

  // ==================== UTILITY OPERATIONS ====================

  /**
   * Get tool configuration template
   */
  getConfigTemplate(): Partial<ToolConfig> {
    return this.toolService.getConfigTemplate();
  }

  /**
   * Clone tool
   */
  cloneTool(id: number, newName: string): Observable<UserTool> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.clone(id, newName).pipe(
      tap({
        next: clonedTool => {
          // Add to tools list
          const currentTools = this._tools();
          this.updateTools([...currentTools, clonedTool]);

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to clone tool');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Export tool configuration
   */
  exportTool(id: number): Observable<any> {
    return this.toolService.export(id);
  }

  /**
   * Import tool configuration
   */
  importTool(config: any): Observable<UserTool> {
    this.setLoading(true);
    this.clearError();

    return this.toolService.import(config).pipe(
      tap({
        next: importedTool => {
          // Add to tools list
          const currentTools = this._tools();
          this.updateTools([...currentTools, importedTool]);

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to import tool');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Get tool statistics
   */
  getToolStats(id: number): Observable<any> {
    return this.toolService.getStats(id);
  }

  /**
   * Get tool execution history
   */
  getExecutionHistory(
    id: number,
    params?: {
      page?: number;
      limit?: number;
      startDate?: string;
      endDate?: string;
    }
  ): Observable<any[]> {
    return this.toolService.getExecutionHistory(id, params);
  }

  // ==================== STATE RESET ====================

  /**
   * Reset store state
   */
  reset(): void {
    this._tools.set([]);
    this._currentTool.set(null);
    this._loading.set(false);
    this._error.set(null);
    this._filters.set({});
    this._totalCount.set(0);
    this._isDirty.set(false);
    this._autosaveLoading.set(false);
    // Don't reset categories as they're loaded on initialization
  }

  /**
   * Clear current tool
   */
  clearCurrentTool(): void {
    this._currentTool.set(null);
  }

  /**
   * Clear error
   */
  clearErrorState(): void {
    this._error.set(null);
  }

  /**
   * Clear dirty state
   */
  clearDirtyState(): void {
    this._isDirty.set(false);
  }
}
