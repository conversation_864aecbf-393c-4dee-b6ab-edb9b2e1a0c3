# Autosave Implementation in New Stores

This document explains how the autosave functionality has been implemented in the new specialized stores (AgentStore, ToolStore, WorkflowStore) and how it replaces the deprecated LifecycleStore.

## Overview

The autosave functionality provides automatic saving of user changes with a 2-second debounce delay, ensuring that frequent changes don't overwhelm the backend while maintaining a smooth user experience.

## Architecture

### Before: LifecycleStore (Deprecated)
```typescript
// Old implementation in lifecycle.store.ts
export class LifecycleStore {
  private saveSubject = new Subject<{ id: string; payload: { type: ArtifactType; metadata: any } }>();

  constructor() {
    // Debounced autosave with 2-second delay
    this.saveSubject.pipe(debounceTime(2000)).subscribe((s) => {
      this.saveToServer(s.id, s.payload);
    });
  }

  onLocalChange(id: string, payload: { type: ArtifactType; metadata: any }) {
    this._isDirty.set(true);
    this.saveSubject.next({ id, payload });
  }
}
```

### After: Specialized Stores (Current)
```typescript
// New implementation in each store
export class AgentStore {
  private saveSubject = new Subject<{ id: number; updates: Partial<Agent> }>();
  private readonly _isDirty = signal<boolean>(false);
  public readonly isDirty = this._isDirty.asReadonly();

  constructor(private agentService: AgentService) {
    this.setupAutosave();
  }

  private setupAutosave(): void {
    this.saveSubject.pipe(debounceTime(2000)).subscribe(({ id, updates }) => {
      this.performAutosave(id, updates);
    });
  }

  onLocalChange(id: number, updates: Partial<Agent>): void {
    this._isDirty.set(true);
    this.saveSubject.next({ id, updates });
  }
}
```

## Key Features

### 1. **Debounced Autosave**
- **2-second delay** prevents excessive API calls
- **Automatic cleanup** of pending saves when new changes occur
- **Efficient** - only saves the latest version of changes

### 2. **Dirty State Tracking**
- **Real-time** dirty state indication
- **Visual feedback** for users about unsaved changes
- **Automatic clearing** when autosave succeeds

### 3. **Error Handling**
- **Graceful degradation** when autosave fails
- **Dirty state preservation** for retry attempts
- **User notification** of autosave failures

### 4. **State Synchronization**
- **Automatic updates** of local store state
- **Consistent data** across all components
- **Real-time UI updates** after successful saves

## Implementation Details

### Store Structure

Each store now includes:

```typescript
export class AgentStore {
  // Autosave state
  private readonly _isDirty = signal<boolean>(false);
  public readonly isDirty = this._isDirty.asReadonly();

  // Autosave subject for debouncing
  private readonly saveSubject = new Subject<{ id: number; updates: Partial<Agent> }>();

  // Autosave methods
  private setupAutosave(): void { /* ... */ }
  onLocalChange(id: number, updates: Partial<Agent>): void { /* ... */ }
  private performAutosave(id: number, updates: Partial<Agent>): void { /* ... */ }
}
```

### Autosave Flow

```mermaid
graph TD
    A[User makes change] --> B[onLocalChange called]
    B --> C[Set dirty state = true]
    B --> D[Add to saveSubject]
    D --> E[Debounce 2 seconds]
    E --> F[performAutosave]
    F --> G[Call service.update]
    G --> H{Success?}
    H -->|Yes| I[Update local state]
    H -->|Yes| J[Clear dirty state]
    H -->|No| K[Keep dirty state]
    H -->|No| L[Log error]
```

## Usage Examples

### 1. **Basic Autosave Usage**

```typescript
import { Component } from '@angular/core';
import { AgentStore } from '@shared/stores/agent.store';

@Component({
  selector: 'app-agent-editor',
  template: `
    <div class="editor">
      <!-- Dirty state indicator -->
      <div *ngIf="agentStore.isDirty()" class="dirty-indicator">
        ⚠️ Unsaved changes
      </div>

      <!-- Form fields -->
      <input 
        [value]="agent.name"
        (input)="onNameChange($event.target.value)"
        placeholder="Agent Name">
      
      <textarea 
        [value]="agent.description"
        (input)="onDescriptionChange($event.target.value)"
        placeholder="Description">
      </textarea>
    </div>
  `
})
export class AgentEditorComponent {
  constructor(public agentStore: AgentStore) {}

  onNameChange(name: string): void {
    // Trigger autosave
    this.agentStore.onLocalChange(this.agent.id!, { name });
  }

  onDescriptionChange(description: string): void {
    // Trigger autosave
    this.agentStore.onLocalChange(this.agent.id!, { description });
  }
}
```

### 2. **Advanced Autosave with Multiple Fields**

```typescript
export class AdvancedAgentEditorComponent {
  private pendingChanges: Partial<Agent> = {};

  onFieldChange(field: keyof Agent, value: any): void {
    // Accumulate changes
    this.pendingChanges[field] = value;
    
    // Trigger autosave with all pending changes
    this.agentStore.onLocalChange(this.agent.id!, this.pendingChanges);
  }

  // Clear pending changes after successful save
  onAutosaveSuccess(): void {
    this.pendingChanges = {};
  }
}
```

### 3. **Autosave with Validation**

```typescript
export class ValidatedAgentEditorComponent {
  onFieldChange(field: keyof Agent, value: any): void {
    // Validate before autosave
    if (this.validateField(field, value)) {
      this.agentStore.onLocalChange(this.agent.id!, { [field]: value });
    } else {
      // Don't autosave invalid data
      console.warn(`Invalid value for ${field}:`, value);
    }
  }

  private validateField(field: keyof Agent, value: any): boolean {
    // Implement validation logic
    return true;
  }
}
```

## Migration from LifecycleStore

### Old Way (Deprecated)
```typescript
// ❌ Old LifecycleStore usage
constructor(private lifecycleStore: LifecycleStore) {}

onChange(): void {
  this.lifecycleStore.onLocalChange('123', {
    type: 'agent',
    metadata: { name: 'New Name' }
  });
}

// Check dirty state
const isDirty = this.lifecycleStore.isDirty();
```

### New Way (Recommended)
```typescript
// ✅ New specialized store usage
constructor(public agentStore: AgentStore) {}

onChange(): void {
  this.agentStore.onLocalChange(123, { name: 'New Name' });
}

// Check dirty state
const isDirty = this.agentStore.isDirty();
```

## Benefits of New Implementation

### 1. **Type Safety**
```typescript
// Before: Generic payload with 'any' type
onLocalChange(id: string, payload: { type: ArtifactType; metadata: any })

// After: Type-safe updates
onLocalChange(id: number, updates: Partial<Agent>)
```

### 2. **Better Performance**
- **Direct service calls** instead of type switching
- **Optimized state updates** with Angular signals
- **Reduced memory overhead** from generic payloads

### 3. **Cleaner Architecture**
- **Single responsibility** - each store handles its own type
- **No type switching** or generic handling
- **Easier testing** and maintenance

### 4. **Enhanced User Experience**
- **Real-time dirty state** with signals
- **Immediate feedback** on changes
- **Consistent behavior** across all stores

## Error Handling and Recovery

### Autosave Failure Scenarios

```typescript
private performAutosave(id: number, updates: Partial<Agent>): void {
  this.agentService.update(id, updates).subscribe({
    next: (updatedAgent) => {
      // Success: Update state and clear dirty flag
      this.updateAgentInList(updatedAgent);
      this._isDirty.set(false);
    },
    error: (error) => {
      // Failure: Keep dirty state for retry
      console.error('Autosave failed:', error);
      
      // Optionally show user notification
      this.showAutosaveError(error);
      
      // Keep dirty state so user can manually save
    }
  });
}
```

### User Recovery Options

```typescript
export class AgentEditorComponent {
  // Manual save when autosave fails
  manualSave(): void {
    if (this.agentStore.isDirty()) {
      this.agentStore.updateAgent(this.agent.id!, this.getCurrentChanges())
        .subscribe({
          next: () => {
            this.showSuccessMessage('Changes saved successfully');
          },
          error: (error) => {
            this.showErrorMessage('Failed to save changes');
          }
        });
    }
  }

  // Retry autosave
  retryAutosave(): void {
    if (this.agentStore.isDirty()) {
      // Trigger autosave again
      this.agentStore.onLocalChange(this.agent.id!, this.getCurrentChanges());
    }
  }
}
```

## Configuration Options

### Customizing Debounce Time

```typescript
export class CustomDebounceStore {
  private readonly DEBOUNCE_TIME = 5000; // 5 seconds

  private setupAutosave(): void {
    this.saveSubject.pipe(debounceTime(this.DEBOUNCE_TIME))
      .subscribe(({ id, updates }) => {
        this.performAutosave(id, updates);
      });
  }
}
```

### Conditional Autosave

```typescript
export class ConditionalAutosaveStore {
  private autosaveEnabled = true;

  onLocalChange(id: number, updates: Partial<Agent>): void {
    if (this.autosaveEnabled) {
      this._isDirty.set(true);
      this.saveSubject.next({ id, updates });
    }
  }

  toggleAutosave(enabled: boolean): void {
    this.autosaveEnabled = enabled;
  }
}
```

## Testing Autosave

### Unit Testing

```typescript
describe('AgentStore Autosave', () => {
  let store: AgentStore;
  let service: jasmine.SpyObj<AgentService>;

  beforeEach(() => {
    service = jasmine.createSpyObj('AgentService', ['update']);
    store = new AgentStore(service);
  });

  it('should trigger autosave after debounce', fakeAsync(() => {
    // Arrange
    const updates = { name: 'New Name' };
    service.update.and.returnValue(of({ id: 1, name: 'New Name' }));

    // Act
    store.onLocalChange(1, updates);
    tick(2000); // Advance past debounce time

    // Assert
    expect(service.update).toHaveBeenCalledWith(1, updates);
    expect(store.isDirty()).toBe(false);
  }));

  it('should maintain dirty state on autosave failure', fakeAsync(() => {
    // Arrange
    const updates = { name: 'New Name' };
    service.update.and.returnValue(throwError(() => new Error('Network error')));

    // Act
    store.onLocalChange(1, updates);
    tick(2000);

    // Assert
    expect(store.isDirty()).toBe(true);
  }));
});
```

### Integration Testing

```typescript
describe('Agent Editor Autosave Integration', () => {
  it('should show dirty indicator during editing', async () => {
    // Navigate to editor
    await page.goto('/agents/1/edit');

    // Make a change
    await page.fill('input[name="name"]', 'New Name');

    // Verify dirty indicator appears
    await expect(page.locator('.dirty-indicator')).toBeVisible();

    // Wait for autosave
    await page.waitForTimeout(2500);

    // Verify dirty indicator disappears
    await expect(page.locator('.dirty-indicator')).not.toBeVisible();
  });
});
```

## Best Practices

### 1. **Always Check Dirty State**
```typescript
// Show save button only when needed
<button 
  *ngIf="agentStore.isDirty()"
  (click)="manualSave()"
  class="btn-save">
  Save Changes
</button>
```

### 2. **Handle Autosave Failures Gracefully**
```typescript
// Provide user feedback and recovery options
if (this.agentStore.isDirty()) {
  this.showWarning('You have unsaved changes. Please save before leaving.');
}
```

### 3. **Use Appropriate Debounce Times**
```typescript
// Short debounce for text input
private readonly TEXT_DEBOUNCE = 1000; // 1 second

// Longer debounce for complex forms
private readonly FORM_DEBOUNCE = 3000; // 3 seconds
```

### 4. **Clear Dirty State Appropriately**
```typescript
// Clear dirty state after successful operations
this.agentStore.updateAgent(id, updates).subscribe({
  next: () => {
    this.agentStore.clearDirtyState();
  }
});
```

## Conclusion

The new autosave implementation provides:

- **Better type safety** with specialized stores
- **Improved performance** with Angular signals
- **Enhanced user experience** with real-time dirty state
- **Robust error handling** and recovery options
- **Easier testing** and maintenance

The LifecycleStore can now be safely removed as all its functionality has been properly implemented in the new specialized stores with improved architecture and user experience.
