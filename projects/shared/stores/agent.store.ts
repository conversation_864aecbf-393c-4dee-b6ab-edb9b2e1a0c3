/* eslint-disable complexity */
import { Injectable, computed, effect, signal } from '@angular/core';
import { BehaviorSubject, Observable, tap, Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

import { Agent, AgentConfig } from '../models/artifact.model';
import { AgentService } from '../services/agent.service';

export interface AgentFilters {
  teamId?: number;
  status?: string;
  createdBy?: number;
  page?: number;
  limit?: number;
}

export interface AgentState {
  agents: Agent[];
  currentAgent: Agent | null;
  loading: boolean;
  error: string | null;
  filters: AgentFilters;
  totalCount: number;
}

@Injectable({ providedIn: 'root' })
export class AgentStore {
  // Private state signals
  private readonly _agents = signal<Agent[]>([]);
  private readonly _currentAgent = signal<Agent | null>(null);
  private readonly _loading = signal<boolean>(false);
  private readonly _error = signal<string | null>(null);
  private readonly _filters = signal<AgentFilters>({});
  private readonly _totalCount = signal<number>(0);
  private readonly _isDirty = signal<boolean>(false);
  private readonly _autosaveLoading = signal<boolean>(false);

  // Public readonly signals
  public readonly agents = this._agents.asReadonly();
  public readonly currentAgent = this._currentAgent.asReadonly();
  public readonly loading = this._loading.asReadonly();
  public readonly error = this._error.asReadonly();
  public readonly filters = this._filters.asReadonly();
  public readonly totalCount = this._totalCount.asReadonly();
  public readonly isDirty = this._isDirty.asReadonly();
  public readonly autosaveLoading = this._autosaveLoading.asReadonly();

  // Computed signals
  public readonly approvedAgents = computed(() =>
    this._agents().filter(agent => agent.status === 'APPROVED')
  );

  public readonly draftAgents = computed(() =>
    this._agents().filter(agent => agent.status === 'DRAFTED')
  );

  public readonly reviewAgents = computed(() =>
    this._agents().filter(agent => agent.status === 'IN_REVIEW')
  );

  public readonly hasAgents = computed(() => this._agents().length > 0);
  public readonly isLoading = computed(() => this._loading());
  public readonly hasError = computed(() => this._error() !== null);

  // Observable streams for reactive components
  private readonly _agentsSubject = new BehaviorSubject<Agent[]>([]);
  public readonly agents$ = this._agentsSubject.asObservable();

  private readonly _currentAgentSubject = new BehaviorSubject<Agent | null>(
    null
  );
  public readonly currentAgent$ = this._currentAgentSubject.asObservable();

  private readonly _loadingSubject = new BehaviorSubject<boolean>(false);
  public readonly loading$ = this._loadingSubject.asObservable();

  private readonly _errorSubject = new BehaviorSubject<string | null>(null);
  public readonly error$ = this._errorSubject.asObservable();

  // Autosave functionality
  private readonly saveSubject = new Subject<{
    id: number;
    updates: Partial<Agent>;
  }>();

  constructor(private agentService: AgentService) {
    // Sync signals with subjects for backward compatibility
    effect(() => {
      this._agentsSubject.next(this._agents());
    });

    effect(() => {
      this._currentAgentSubject.next(this._currentAgent());
    });

    effect(() => {
      this._loadingSubject.next(this._loading());
    });

    effect(() => {
      this._errorSubject.next(this._error());
    });

    // Setup autosave with debouncing
    this.setupAutosave();
  }

  // ==================== AUTOSAVE FUNCTIONALITY ====================

  /**
   * Setup autosave with debouncing
   */
  private setupAutosave(): void {
    this.saveSubject.pipe(debounceTime(2000)).subscribe(({ id, updates }) => {
      this.performAutosave(id, updates);
    });
  }

  /**
   * Trigger autosave for local changes
   * @param id - Agent ID (use -1 for new agents that haven't been created yet)
   * @param updates - Partial updates to save
   */
  onLocalChange(id: number, updates: Partial<Agent>): void {
    this._isDirty.set(true);
    this.saveSubject.next({ id, updates });
  }

  /**
   * Perform the actual autosave operation
   */
  private performAutosave(id: number, updates: Partial<Agent>): void {
    this.setAutosaveLoading(true);

    // Check if this is a new agent (id === -1) or an existing agent
    if (id === -1) {
      // This is a new agent that needs to be created
      this.createAgentForAutosave(updates);
    } else {
      // This is an existing agent that needs to be updated
      this.updateAgentForAutosave(id, updates);
    }
  }

  /**
   * Create a new agent during autosave
   */
  private createAgentForAutosave(updates: Partial<Agent>): void {
    // Convert partial updates to a complete agent object for creation
    const agentData: Omit<Agent, 'id'> = {
      agentDetails: updates.agentDetails || 'New_Agent',
      name: updates.name || 'Untitled Agent',
      role: updates.role || 'Assistant',
      goal: updates.goal || 'Help users with their tasks',
      backstory: updates.backstory || 'You are a helpful assistant.',
      description: updates.description || 'A new agent',
      expectedOutput: updates.expectedOutput || 'Helpful responses',
      userTools: updates.userTools || [],
      tools: updates.tools || [],
      kbIds: updates.kbIds || [],
      modelId: updates.modelId || 1,
      agentConfigs: updates.agentConfigs || {
        temperature: 0.7,
        topP: 0.9,
        maxToken: '2000',
        levelId: 1,
        maxRpm: 10,
        maxExecutionTime: 5,
        allowDelegation: false,
        allowCodeExecution: false,
        isSafeCodeExecution: true,
        toolRef: [],
      },
      teamId: updates.teamId || 1,
      status: updates.status || 'CREATED',
      tags: updates.tags || [],
      practiceArea: updates.practiceArea || 1,
      ...updates,
    };

    this.agentService.create(agentData).subscribe({
      next: newAgent => {
        // Add to agents list
        const currentAgents = this._agents();
        this.updateAgents([...currentAgents, newAgent]);

        // Set as current agent
        this.updateCurrentAgent(newAgent);

        // Clear dirty state
        this._isDirty.set(false);
        this.setAutosaveLoading(false);

        console.log('Agent created via autosave:', newAgent);
      },
      error: error => {
        console.error('Autosave create failed for agent:', error);
        // Keep dirty state for retry - user can manually save
        this.setAutosaveLoading(false);
      },
    });
  }

  /**
   * Update an existing agent during autosave
   */
  private updateAgentForAutosave(id: number, updates: Partial<Agent>): void {
    this.agentService.update(id, updates).subscribe({
      next: updatedAgent => {
        // Update in agents list
        const currentAgents = this._agents();
        const updatedAgents = currentAgents.map(agent =>
          agent.id === id ? updatedAgent : agent
        );
        this.updateAgents(updatedAgents);

        // Update current agent if it's the one being updated
        const currentAgent = this._currentAgent();
        if (currentAgent && currentAgent.id === id) {
          this.updateCurrentAgent(updatedAgent);
        }

        // Clear dirty state
        this._isDirty.set(false);
        this.setAutosaveLoading(false);
      },
      error: error => {
        console.error('Autosave update failed for agent:', error);
        // Keep dirty state for retry - user can manually save
        this.setAutosaveLoading(false);
      },
    });
  }

  // ==================== STATE MANAGEMENT ====================

  /**
   * Set loading state
   */
  private setLoading(loading: boolean): void {
    this._loading.set(loading);
  }

  /**
   * Set autosave loading state
   */
  private setAutosaveLoading(loading: boolean): void {
    this._autosaveLoading.set(loading);
  }

  /**
   * Set error state
   */
  private setError(error: string | null): void {
    this._error.set(error);
  }

  /**
   * Clear error state
   */
  private clearError(): void {
    this._error.set(null);
  }

  /**
   * Update agents list
   */
  private updateAgents(agents: Agent[]): void {
    this._agents.set(agents);
  }

  /**
   * Update current agent
   */
  private updateCurrentAgent(agent: Agent | null): void {
    this._currentAgent.set(agent);
  }

  /**
   * Update filters
   */
  private updateFilters(filters: AgentFilters): void {
    this._filters.set(filters);
  }

  /**
   * Update total count
   */
  private updateTotalCount(count: number): void {
    this._totalCount.set(count);
  }

  // ==================== CRUD OPERATIONS ====================

  /**
   * Load all agents with optional filtering
   */
  loadAgents(filters?: AgentFilters): Observable<Agent[]> {
    this.setLoading(true);
    this.clearError();

    if (filters) {
      this.updateFilters(filters);
    }

    return this.agentService.getAll(filters || this._filters()).pipe(
      tap({
        next: agents => {
          this.updateAgents(agents);
          this.updateTotalCount(agents.length);
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to load agents');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Load agent by ID
   */
  loadAgentById(id: number): Observable<Agent> {
    this.setLoading(true);
    this.clearError();

    return this.agentService.getById(id).pipe(
      tap({
        next: agent => {
          this.updateCurrentAgent(agent);
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to load agent');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Create new agent
   */
  createAgent(agentData: Omit<Agent, 'id'>): Observable<Agent> {
    this.setLoading(true);
    this.clearError();

    return this.agentService.create(agentData).pipe(
      tap({
        next: newAgent => {
          // Add to agents list
          const currentAgents = this._agents();
          this.updateAgents([...currentAgents, newAgent]);

          // Set as current agent
          this.updateCurrentAgent(newAgent);

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to create agent');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Create a new agent with temporary ID for autosave
   * This creates an agent in memory with id: -1 for autosave purposes
   */
  createNewAgentForEditing(agentData: Partial<Agent> = {}): Agent {
    const newAgent: Agent = {
      id: -1, // Temporary ID for new agents
      agentDetails: agentData.agentDetails || 'New_Agent',
      name: agentData.name || 'Untitled Agent',
      role: agentData.role || 'Assistant',
      goal: agentData.goal || 'Help users with their tasks',
      backstory: agentData.backstory || 'You are a helpful assistant.',
      description: agentData.description || 'A new agent',
      expectedOutput: agentData.expectedOutput || 'Helpful responses',
      userTools: agentData.userTools || [],
      tools: agentData.tools || [],
      kbIds: agentData.kbIds || [],
      modelId: agentData.modelId || 1,
      agentConfigs: agentData.agentConfigs || {
        temperature: 0.7,
        topP: 0.9,
        maxToken: '2000',
        levelId: 1,
        maxRpm: 10,
        maxExecutionTime: 5,
        allowDelegation: false,
        allowCodeExecution: false,
        isSafeCodeExecution: true,
        toolRef: [],
      },
      teamId: agentData.teamId || 1,
      status: agentData.status || 'CREATED',
      tags: agentData.tags || [],
      practiceArea: agentData.practiceArea || 1,
      ...agentData,
    };

    // Set as current agent for editing
    this.updateCurrentAgent(newAgent);

    return newAgent;
  }

  /**
   * Update existing agent
   */
  updateAgent(id: number, updates: Partial<Agent>): Observable<Agent> {
    this.setLoading(true);
    this.clearError();

    return this.agentService.update(id, updates).pipe(
      tap({
        next: updatedAgent => {
          // Update in agents list
          const currentAgents = this._agents();
          const updatedAgents = currentAgents.map(agent =>
            agent.id === id ? updatedAgent : agent
          );
          this.updateAgents(updatedAgents);

          // Update current agent if it's the one being updated
          const currentAgent = this._currentAgent();
          if (currentAgent && currentAgent.id === id) {
            this.updateCurrentAgent(updatedAgent);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to update agent');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Delete agent
   */
  deleteAgent(id: number): Observable<void> {
    this.setLoading(true);
    this.clearError();

    return this.agentService.delete(id).pipe(
      tap({
        next: () => {
          // Remove from agents list
          const currentAgents = this._agents();
          const filteredAgents = currentAgents.filter(agent => agent.id !== id);
          this.updateAgents(filteredAgents);

          // Clear current agent if it's the one being deleted
          const currentAgent = this._currentAgent();
          if (currentAgent && currentAgent.id === id) {
            this.updateCurrentAgent(null);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to delete agent');
          this.setLoading(false);
        },
      })
    );
  }

  // ==================== LIFECYCLE OPERATIONS ====================

  /**
   * Submit agent for review
   */
  submitForReview(id: number): Observable<Agent> {
    this.setLoading(true);
    this.clearError();

    return this.agentService.submitForReview(id).pipe(
      tap({
        next: updatedAgent => {
          // Update in agents list
          const currentAgents = this._agents();
          const updatedAgents = currentAgents.map(agent =>
            agent.id === id ? updatedAgent : agent
          );
          this.updateAgents(updatedAgents);

          // Update current agent if it's the one being updated
          const currentAgent = this._currentAgent();
          if (currentAgent && currentAgent.id === id) {
            this.updateCurrentAgent(updatedAgent);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to submit agent for review');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Approve agent
   */
  approveAgent(id: number): Observable<Agent> {
    this.setLoading(true);
    this.clearError();

    return this.agentService.approve(id).pipe(
      tap({
        next: updatedAgent => {
          // Update in agents list
          const currentAgents = this._agents();
          const updatedAgents = currentAgents.map(agent =>
            agent.id === id ? updatedAgent : agent
          );
          this.updateAgents(updatedAgents);

          // Update current agent if it's the one being updated
          const currentAgent = this._currentAgent();
          if (currentAgent && currentAgent.id === id) {
            this.updateCurrentAgent(updatedAgent);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to approve agent');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Reject agent
   */
  rejectAgent(id: number): Observable<Agent> {
    this.setLoading(true);
    this.clearError();

    return this.agentService.reject(id).pipe(
      tap({
        next: updatedAgent => {
          // Update in agents list
          const currentAgents = this._agents();
          const updatedAgents = currentAgents.map(agent =>
            agent.id === id ? updatedAgent : agent
          );
          this.updateAgents(updatedAgents);

          // Update current agent if it's the one being updated
          const currentAgent = this._currentAgent();
          if (currentAgent && currentAgent.id === id) {
            this.updateCurrentAgent(updatedAgent);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to reject agent');
          this.setLoading(false);
        },
      })
    );
  }

  // ==================== EXECUTION OPERATIONS ====================

  /**
   * Test agent execution
   */
  testAgent(id: number, userInputs?: any): Observable<any> {
    this.setLoading(true);
    this.clearError();

    return this.agentService.test(id, userInputs).pipe(
      tap({
        next: () => {
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Agent test failed');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Run agent in playground
   */
  playgroundRun(id: number, inputs?: any): Observable<any> {
    this.setLoading(true);
    this.clearError();

    return this.agentService.playgroundRun(id, inputs).pipe(
      tap({
        next: () => {
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Playground execution failed');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Validate agent configuration
   */
  validateAgent(id: number): Observable<any> {
    this.setLoading(true);
    this.clearError();

    return this.agentService.validate(id).pipe(
      tap({
        next: () => {
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Agent validation failed');
          this.setLoading(false);
        },
      })
    );
  }

  // ==================== UTILITY OPERATIONS ====================

  /**
   * Get agent configuration template
   */
  getConfigTemplate(): Partial<AgentConfig> {
    return this.agentService.getConfigTemplate();
  }

  /**
   * Clone agent
   */
  cloneAgent(id: number, newName: string): Observable<Agent> {
    this.setLoading(true);
    this.clearError();

    return this.agentService.clone(id, newName).pipe(
      tap({
        next: clonedAgent => {
          // Add to agents list
          const currentAgents = this._agents();
          this.updateAgents([...currentAgents, clonedAgent]);

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to clone agent');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Export agent configuration
   */
  exportAgent(id: number): Observable<any> {
    return this.agentService.export(id);
  }

  /**
   * Import agent configuration
   */
  importAgent(config: any): Observable<Agent> {
    this.setLoading(true);
    this.clearError();

    return this.agentService.import(config).pipe(
      tap({
        next: importedAgent => {
          // Add to agents list
          const currentAgents = this._agents();
          this.updateAgents([...currentAgents, importedAgent]);

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to import agent');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Get agent statistics
   */
  getAgentStats(id: number): Observable<any> {
    return this.agentService.getStats(id);
  }

  /**
   * Get agent execution history
   */
  getExecutionHistory(
    id: number,
    params?: {
      page?: number;
      limit?: number;
      startDate?: string;
      endDate?: string;
    }
  ): Observable<any[]> {
    return this.agentService.getExecutionHistory(id, params);
  }

  // ==================== STATE RESET ====================

  /**
   * Reset store state
   */
  reset(): void {
    this._agents.set([]);
    this._currentAgent.set(null);
    this._loading.set(false);
    this._error.set(null);
    this._filters.set({});
    this._totalCount.set(0);
    this._isDirty.set(false);
    this._autosaveLoading.set(false);
  }

  /**
   * Clear current agent
   */
  clearCurrentAgent(): void {
    this._currentAgent.set(null);
  }

  /**
   * Clear error
   */
  clearErrorState(): void {
    this._error.set(null);
  }

  /**
   * Clear dirty state
   */
  clearDirtyState(): void {
    this._isDirty.set(false);
  }
}
