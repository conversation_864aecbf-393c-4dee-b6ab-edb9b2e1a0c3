// stores/lifecycle.store.ts
import { Injectable, effect, signal } from '@angular/core';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

import { ArtifactFlag } from '../models/artifact-flags';
import { Artifact, ArtifactType } from '../models/artifact.model';
import { AgentService } from '../services/agent.service';
import { ToolService } from '../services/tool.service';
import { WorkflowService } from '../services/workflow.service';

import { canTransition } from './state-machine';

@Injectable({ providedIn: 'root' })
export class LifecycleStore {
  private _artifact = signal<Artifact | null>(null);
  artifact = this._artifact.asReadonly();

  private _isDirty = signal(false);
  isDirty = this._isDirty.asReadonly();

  private saveSubject = new Subject<{
    id: string;
    payload: { type: ArtifactType; metadata: any };
  }>();

  constructor(
    private agentService: AgentService,
    private toolService: ToolService,
    private workflowService: WorkflowService
  ) {
    this.saveSubject.pipe(debounceTime(2000)).subscribe(s => {
      this.saveToServer(s.id, s.payload);
    });

    effect(() => {
      const a = this._artifact();
      if (a) this._isDirty.set(false);
    });
  }

  /**
   * Load artifact by ID and type
   * @deprecated Use specific service stores (AgentStore, ToolStore, WorkflowStore) instead
   */
  loadArtifact(id: string, type: ArtifactType) {
    const numericId = parseInt(id);

    switch (type) {
      case 'agent':
        this.agentService.getById(numericId).subscribe({
          next: agent => {
            this._artifact.set(this.mapToArtifact(agent, 'agent'));
          },
          error: err => console.error('Failed to load agent:', err),
        });
        break;
      case 'tool':
        this.toolService.getById(numericId).subscribe({
          next: tool => {
            this._artifact.set(this.mapToArtifact(tool, 'tool'));
          },
          error: err => console.error('Failed to load tool:', err),
        });
        break;
      case 'workflow':
        this.workflowService.getById(numericId).subscribe({
          next: workflow => {
            this._artifact.set(this.mapToArtifact(workflow, 'workflow'));
          },
          error: err => console.error('Failed to load workflow:', err),
        });
        break;
      default:
        console.warn(`Unsupported artifact type: ${type}`);
    }
  }

  /**
   * Create new artifact
   * @deprecated Use specific service stores (AgentStore, ToolStore, WorkflowStore) instead
   */
  createArtifact(payload: { type: ArtifactType; metadata: any }) {
    switch (payload.type) {
      case 'agent':
        this.agentService.create(payload.metadata).subscribe({
          next: agent => {
            this._artifact.set(this.mapToArtifact(agent, 'agent'));
          },
          error: err => console.error('Failed to create agent:', err),
        });
        break;
      case 'tool':
        this.toolService.create(payload.metadata).subscribe({
          next: tool => {
            this._artifact.set(this.mapToArtifact(tool, 'tool'));
          },
          error: err => console.error('Failed to create tool:', err),
        });
        break;
      case 'workflow':
        this.workflowService.create(payload.metadata).subscribe({
          next: workflow => {
            this._artifact.set(this.mapToArtifact(workflow, 'workflow'));
          },
          error: err => console.error('Failed to create workflow:', err),
        });
        break;
      default:
        console.warn(`Unsupported artifact type: ${payload.type}`);
    }
  }

  /**
   * Handle local changes and trigger autosave
   * @deprecated Use specific service stores (AgentStore, ToolStore, WorkflowStore) instead
   */
  onLocalChange(id: string, payload: { type: ArtifactType; metadata: any }) {
    this._isDirty.set(true);
    this.saveSubject.next({ id, payload });
  }

  /**
   * Save changes to server
   * @deprecated Use specific service stores (AgentStore, ToolStore, WorkflowStore) instead
   */
  private saveToServer(
    id: string,
    payload: { type: ArtifactType; metadata: any }
  ) {
    const numericId = parseInt(id);

    switch (payload.type) {
      case 'agent':
        this.agentService.update(numericId, payload.metadata).subscribe({
          next: updated => {
            this._artifact.set(this.mapToArtifact(updated, 'agent'));
          },
          error: err => console.error('Autosave failed for agent:', err),
        });
        break;
      case 'tool':
        this.toolService.update(numericId, payload.metadata).subscribe({
          next: updated => {
            this._artifact.set(this.mapToArtifact(updated, 'tool'));
          },
          error: err => console.error('Autosave failed for tool:', err),
        });
        break;
      case 'workflow':
        this.workflowService.update(numericId, payload.metadata).subscribe({
          next: updated => {
            this._artifact.set(this.mapToArtifact(updated, 'workflow'));
          },
          error: err => console.error('Autosave failed for workflow:', err),
        });
        break;
      default:
        console.warn(`Unsupported artifact type for autosave: ${payload.type}`);
    }
  }

  /**
   * Transition artifact to new state
   */
  transitionTo(next: ArtifactFlag) {
    const a = this._artifact();
    if (!a) return;
    if (!canTransition(a.state, next)) {
      console.warn(`Invalid transition from ${a.state} → ${next}`);
      return;
    }
    this._artifact.set({ ...a, state: next });
  }

  /**
   * Validate artifact
   * @deprecated Use specific service stores (AgentStore, ToolStore, WorkflowStore) instead
   */
  async validate(id: string, type: ArtifactType) {
    const numericId = parseInt(id);

    switch (type) {
      case 'agent':
        return this.agentService.validate(numericId).toPromise();
      case 'tool':
        return this.toolService.validate(numericId).toPromise();
      case 'workflow':
        return this.workflowService.validate(numericId).toPromise();
      default:
        console.warn(`Unsupported artifact type for validation: ${type}`);
        return Promise.reject(new Error(`Unsupported artifact type: ${type}`));
    }
  }

  /**
   * Test artifact execution
   * @deprecated Use specific service stores (AgentStore, ToolStore, WorkflowStore) instead
   */
  async runTest(id: string, type: ArtifactType, inputs?: any) {
    const numericId = parseInt(id);

    switch (type) {
      case 'agent':
        return this.agentService.test(numericId, inputs).toPromise();
      case 'tool':
        return this.toolService.test(numericId, inputs).toPromise();
      case 'workflow':
        return this.workflowService.test(numericId, inputs).toPromise();
      default:
        console.warn(`Unsupported artifact type for testing: ${type}`);
        return Promise.reject(new Error(`Unsupported artifact type: ${type}`));
    }
  }

  /**
   * Submit artifact for approval
   * @deprecated Use specific service stores (AgentStore, ToolStore, WorkflowStore) instead
   */
  async submitForApproval(id: string, type: ArtifactType) {
    const numericId = parseInt(id);

    switch (type) {
      case 'agent':
        return this.agentService.submitForReview(numericId).toPromise();
      case 'tool':
        return this.toolService.submitForReview(numericId).toPromise();
      case 'workflow':
        return this.workflowService.submitForReview(numericId).toPromise();
      default:
        console.warn(`Unsupported artifact type for submission: ${type}`);
        return Promise.reject(new Error(`Unsupported artifact type: ${type}`));
    }
  }

  /**
   * Approve artifact
   * @deprecated Use specific service stores (AgentStore, ToolStore, WorkflowStore) instead
   */
  async approve(id: string, type: ArtifactType) {
    const numericId = parseInt(id);

    switch (type) {
      case 'agent':
        return this.agentService.approve(numericId).toPromise();
      case 'tool':
        return this.toolService.approve(numericId).toPromise();
      case 'workflow':
        return this.workflowService.approve(numericId).toPromise();
      default:
        console.warn(`Unsupported artifact type for approval: ${type}`);
        return Promise.reject(new Error(`Unsupported artifact type: ${type}`));
    }
  }

  /**
   * Reject artifact
   * @deprecated Use specific service stores (AgentStore, ToolStore, WorkflowStore) instead
   */
  async reject(id: string, type: ArtifactType) {
    const numericId = parseInt(id);

    switch (type) {
      case 'agent':
        return this.agentService.reject(numericId).toPromise();
      case 'tool':
        return this.toolService.reject(numericId).toPromise();
      case 'workflow':
        return this.workflowService.reject(numericId).toPromise();
      default:
        console.warn(`Unsupported artifact type for rejection: ${type}`);
        return Promise.reject(new Error(`Unsupported artifact type: ${type}`));
    }
  }

  /**
   * Run artifact in playground
   * @deprecated Use specific service stores (AgentStore, ToolStore, WorkflowStore) instead
   */
  async playgroundRun(id: string, type: ArtifactType) {
    const numericId = parseInt(id);

    switch (type) {
      case 'agent':
        return this.agentService.playgroundRun(numericId).toPromise();
      case 'tool':
        return this.toolService.playgroundRun(numericId).toPromise();
      case 'workflow':
        return this.workflowService.playgroundRun(numericId).toPromise();
      default:
        console.warn(`Unsupported artifact type for playground: ${type}`);
        return Promise.reject(new Error(`Unsupported artifact type: ${type}`));
    }
  }

  /**
   * Map specific artifact types to generic Artifact interface
   */
  private mapToArtifact<T>(data: T, type: ArtifactType): Artifact {
    return {
      id: (data as any).id?.toString() || '',
      type,
      metadata: data,
      state: this.mapStatusToFlag((data as any).status),
      createdAt: (data as any).createdAt,
      updatedAt: (data as any).modifiedAt || (data as any).updatedAt,
    };
  }

  /**
   * Map status strings to ArtifactFlag enum
   */
  private mapStatusToFlag(status: string): ArtifactFlag {
    const statusMap: Record<string, ArtifactFlag> = {
      CREATED: ArtifactFlag.CREATED,
      DRAFTED: ArtifactFlag.DRAFTED,
      IN_REVIEW: ArtifactFlag.IN_REVIEW,
      APPROVED: ArtifactFlag.APPROVED,
      REJECTED: ArtifactFlag.REJECTED,
      SUBMITTED: ArtifactFlag.SUBMITTED,
      EXECUTING: ArtifactFlag.EXECUTING,
      EXECUTION_COMPLETED: ArtifactFlag.EXECUTION_COMPLETED,
      EXECUTION_FAILED: ArtifactFlag.EXECUTION_FAILED,
      EXECUTION_CANCELLED: ArtifactFlag.EXECUTION_CANCELLED,
    };

    return statusMap[status] || ArtifactFlag.DRAFTED;
  }

  /**
   * Clear current artifact
   */
  clearArtifact(): void {
    this._artifact.set(null);
    this._isDirty.set(false);
  }

  /**
   * Reset store state
   */
  reset(): void {
    this._artifact.set(null);
    this._isDirty.set(false);
  }
}
