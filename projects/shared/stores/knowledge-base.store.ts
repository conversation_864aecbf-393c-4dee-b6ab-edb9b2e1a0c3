import { Injectable, signal, computed, inject } from '@angular/core';
import { KnowledgeBaseItem, KnowledgeBaseService } from '@shared';
import { firstValueFrom } from 'rxjs';

import { LibraryItem } from '../components/interfaces/library-item.interface';

@Injectable({
  providedIn: 'root',
})
export class KnowledgeBaseStore {
  private readonly knowledgeBaseService = inject(KnowledgeBaseService);

  // 🔹 State signals
  private readonly knowledgeBasesSignal = signal<KnowledgeBaseItem[]>([]);
  private readonly loadingSignal = signal<boolean>(false);
  private readonly errorSignal = signal<string | null>(null);

  // 🔹 Public signals (readonly)
  readonly knowledgeBases = this.knowledgeBasesSignal.asReadonly();
  readonly loading = this.loadingSignal.asReadonly();
  readonly error = this.errorSignal.asReadonly();

  // 🔹 Derived computed signal
  readonly libraryItems = computed(() =>
    this.mapToLibraryItems(this.knowledgeBasesSignal())
  );

  /**
   * Load all knowledge bases from API
   */
  async loadKnowledgeBases(): Promise<void> {
    this.loadingSignal.set(true);
    this.errorSignal.set(null);

    try {
      const knowledgeBases = await firstValueFrom(
        this.knowledgeBaseService.getAllKnowledgeBases()
      );

      this.knowledgeBasesSignal.set(knowledgeBases);
    } catch (error) {
      this.errorSignal.set(
        error instanceof Error
          ? error.message
          : 'Failed to load knowledge bases'
      );
    } finally {
      this.loadingSignal.set(false);
    }
  }

  /**
   * Map KnowledgeBaseItem[] → LibraryItem[]
   */
  private mapToLibraryItems(
    knowledgeBases: KnowledgeBaseItem[]
  ): LibraryItem[] {
    return knowledgeBases.map(kb => ({
      id: kb.id.toString(),
      title: kb.name,
      description: kb.description,
      category: this.getCategoryFromPracticeArea(kb.practiceArea),
      tags: this.getTagsFromGoodAt(kb.goodAt),
      isActive: kb.isactive,
      usageCount: 0, // API doesn’t provide this
      icon: 'book-open-text',
      iconColor: '#000',
      createdAt: new Date(kb.createdDate),
      updatedAt: new Date(kb.createdDate),
    }));
  }

  /**
   * Convert practice area → category
   */
  private getCategoryFromPracticeArea(practiceArea?: number): string {
    const categories: Record<number, string> = {
      1: 'Product Management',
      2: 'Experience Design',
      3: 'UI Engineering',
      4: 'API & Integration',
      5: 'Backend Engineering',
      6: 'Data Engineering',
      7: 'Cloud & DevOps',
      8: 'Quality Engineering',
      9: 'AI/ML',
      10: 'Security & Compliance',
      11: 'Legacy Modernization',
      12: 'Business Operations',
      13: 'Cross-Functional',
    };

    return categories[practiceArea ?? -1] ?? 'General';
  }

  /**
   * Parse JSON goodAt → tag strings
   */
  private getTagsFromGoodAt(goodAt?: string): string[] {
    if (!goodAt) return [];

    try {
      const tagIds: number[] = JSON.parse(goodAt);
      const tagNames: Record<number, string> = {
        1: 'automation',
        2: 'integration',
        3: 'validation',
        4: 'generation',
        5: 'UX-testing',
        6: 'review',
        7: 'instruction',
        8: 'parser',
        9: 'exporter',
        10: 'regex-filter',
        11: 'monitoring',
        12: 'conversion',
        13: 'embedding',
        14: 'summarization',
        15: 'orchestration',
        16: 'classification',
        17: 'retrieval',
        18: 'refactor',
        19: 'scheduling',
        20: 'qa',
      };

      return Array.isArray(tagIds)
        ? tagIds.map(id => tagNames[id] ?? `tag-${id}`)
        : [];
    } catch {
      return [];
    }
  }

  /**
   * Get KB by ID
   */
  getKnowledgeBaseById(id: number): KnowledgeBaseItem | undefined {
    return this.knowledgeBasesSignal().find(kb => kb.id === id);
  }

  /**
   * Get LibraryItem by ID
   */
  getLibraryItemById(id: string): LibraryItem | undefined {
    return this.libraryItems().find(item => item.id === id);
  }

  /**
   * Reset state
   */
  clearData(): void {
    this.knowledgeBasesSignal.set([]);
    this.loadingSignal.set(false);
    this.errorSignal.set(null);
  }
}
