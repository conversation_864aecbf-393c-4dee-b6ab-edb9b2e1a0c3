import { Injectable, signal, computed, inject } from '@angular/core';

import { GoodAtTagsService, GoodAtTag } from '../services/good-at-tags.service';
import { ModelsService, Model } from '../services/models.service';
import {
  PracticeAreasService,
  PracticeArea,
} from '../services/practice-areas.service';

export interface DropdownOption {
  value: string;
  label: string;
  id: number;
}

export interface ModelDropdownOption extends DropdownOption {
  aiEngine?: string;
  type?: string;
}

@Injectable({
  providedIn: 'root',
})
export class DropdownDataStore {
  // Services
  private goodAtTagsService = inject(GoodAtTagsService);
  private practiceAreasService = inject(PracticeAreasService);
  private modelsService = inject(ModelsService);

  // State signals
  private _goodAtTags = signal<GoodAtTag[]>([]);
  private _practiceAreas = signal<PracticeArea[]>([]);
  private _models = signal<Model[]>([]);
  private _schemes = signal<any[]>([]);
  private _loading = signal<boolean>(false);
  private _error = signal<string | null>(null);

  // Public signals
  readonly goodAtTags = this._goodAtTags.asReadonly();
  readonly practiceAreas = this._practiceAreas.asReadonly();
  readonly models = this._models.asReadonly();
  readonly schemes = this._schemes.asReadonly();
  readonly loading = this._loading.asReadonly();
  readonly error = this._error.asReadonly();

  // Computed signals for dropdown options
  readonly goodAtOptions = computed<DropdownOption[]>(() => {
    return this._goodAtTags().map(tag => ({
      value: tag.name,
      label: tag.name,
      id: tag.id,
    }));
  });

  readonly practiceAreaOptions = computed<DropdownOption[]>(() => {
    return this._practiceAreas().map(area => ({
      value: area.name,
      label: area.name,
      id: area.id,
    }));
  });

  readonly schemeOptions = computed<DropdownOption[]>(() => {
    const schemes = this._schemes();
    if (!schemes || schemes.length === 0) return [];
    
    try {
      // Parse the JSON string from the value field
      const parsedValue = JSON.parse(schemes[0].value);
      return Object.values(parsedValue).map((scheme, index) => ({
        value: scheme as string,
        label: scheme as string,
        id: index + 1,
      }));
    } catch (error) {
      console.error('Error parsing schemes:', error);
      return [];
    }
  });

  // Keep the existing modelOptions for backward compatibility
  readonly modelOptions = computed(() => {
    return this._models().map(model => ({
      value: model.name,
      label: model.name,
      id: model.id,
    }));
  });

  // Add computed signals for filtered models
  readonly filteredModelOptions = computed(() => {
    const allModels = this._models();
    const selectedEngine = this.selectedEngine();
    const isEmbedding = this.isEmbedding();

    if (!selectedEngine) return [];

    return allModels
      .filter(model => {
        const engineMatch =
          model.aiEngine?.toLowerCase().replace(/\s+/g, '-') === selectedEngine;
        const typeMatch = isEmbedding
          ? model.type === 'Embedding'
          : model.type === 'Generative';
        return engineMatch && typeMatch;
      })
      .map(model => ({
        value: model.name,
        label: model.name,
        id: model.id,
      }));
  });

  // Add signals for engine and embedding selection
  readonly selectedEngine = signal<string>('');
  readonly isEmbedding = signal<boolean>(false);

  // Add signals for API versions for Models
  private _apiVersions = signal<any[]>([]);
  readonly apiVersions = this._apiVersions.asReadonly();

  readonly apiVersionOptions = computed<DropdownOption[]>(() => {
    const versions = this._apiVersions();
    if (!versions || versions.length === 0) return [];
    
    try {
      const parsedValue = JSON.parse(versions[0].value);
      return Object.entries(parsedValue).map(([key, value], index) => ({
        value: value as string,
        label: key, 
        id: index + 1,
      }));
    } catch (error) {
      console.error('Error parsing API versions:', error);
      return [];
    }
  });

  /**
   * Set API versions data
   */
  setApiVersions(versions: any[]): void {
    this._apiVersions.set(versions || []);
  }

  /**
   * Load all dropdown data
   */
  async loadAllData(): Promise<void> {
    console.log('🔄 Starting to load dropdown data...');
    this._loading.set(true);
    this._error.set(null);

    try {
      const [goodAtTags, practiceAreas, models] = await Promise.all([
        this.goodAtTagsService.getGoodAtTags().toPromise(),
        this.practiceAreasService.getPracticeAreas().toPromise(),
        this.modelsService.getModels().toPromise(),
      ]);

      console.log('📊 Good At Tags loaded:', goodAtTags);
      console.log('📊 Practice Areas loaded:', practiceAreas);
      console.log('📊 Models loaded:', models);
      console.log(
        '📊 Models with engines:',
        models?.map(m => ({ name: m.name, aiEngine: m.aiEngine, type: m.type }))
      );

      this._goodAtTags.set(goodAtTags || []);
      this._practiceAreas.set(practiceAreas || []);
      this._models.set(models || []);

      console.log('✅ Dropdown data set in store');
    } catch (error) {
      console.error('❌ Error loading dropdown data:', error);
      this._error.set('Failed to load dropdown data');
    } finally {
      this._loading.set(false);
      console.log('🏁 Dropdown data loading completed');
    }
  }

  /**
   * Load only good at tags
   */
  async loadGoodAtTags(): Promise<void> {
    this._loading.set(true);
    this._error.set(null);

    try {
      const tags = await this.goodAtTagsService.getGoodAtTags().toPromise();
      this._goodAtTags.set(tags || []);
    } catch (error) {
      console.error('Error loading good at tags:', error);
      this._error.set('Failed to load good at tags');
    } finally {
      this._loading.set(false);
    }
  }

  /**
   * Load only practice areas
   */
  async loadPracticeAreas(): Promise<void> {
    this._loading.set(true);
    this._error.set(null);

    try {
      const areas = await this.practiceAreasService
        .getPracticeAreas()
        .toPromise();
      this._practiceAreas.set(areas || []);
    } catch (error) {
      console.error('Error loading practice areas:', error);
      this._error.set('Failed to load practice areas');
    } finally {
      this._loading.set(false);
    }
  }

  /**
   * Load only schemes
   */
  async loadSchemes(): Promise<void> {
    this._loading.set(true);
    this._error.set(null);

    try {
      const schemes = await this.modelsService.getRefData('Scheme').toPromise();
      this._schemes.set(schemes || []);
    } catch (error) {
      console.error('Error loading schemes:', error);
      this._error.set('Failed to load schemes');
    } finally {
      this._loading.set(false);
    }
  }

  /**
   * Get good at tag by ID
   */
  getGoodAtTagById(id: number): GoodAtTag | undefined {
    const tags = this._goodAtTags();
    console.log(`🔍 Looking for good at tag ID ${id} in:`, tags);
    const result = tags.find(tag => tag.id === id);
    console.log(`📋 Found good at tag:`, result);
    return result;
  }

  /**
   * Get practice area by ID
   */
  getPracticeAreaById(id: number): PracticeArea | undefined {
    const areas = this._practiceAreas();
    console.log(`🔍 Looking for practice area ID ${id} in:`, areas);
    const result = areas.find(area => area.id === id);
    console.log(`📋 Found practice area:`, result);
    return result;
  }

  /**
   * Get good at tags by IDs
   */
  getGoodAtTagsByIds(ids: number[]): GoodAtTag[] {
    const tags = this._goodAtTags();
    console.log(`🔍 Looking for good at tag IDs ${ids} in:`, tags);
    const result = tags.filter(tag => ids.includes(tag.id));
    console.log(`📋 Found good at tags:`, result);
    return result;
  }

  /**
   * Get practice areas by IDs
   */
  getPracticeAreasByIds(ids: number[]): PracticeArea[] {
    const areas = this._practiceAreas();
    console.log(`🔍 Looking for practice area IDs ${ids} in:`, areas);
    const result = areas.filter(area => ids.includes(area.id));
    console.log(`📋 Found practice areas:`, result);
    return result;
  }

  /**
   * Get model by ID from the original models array
   */
  getModelById(id: number): Model | undefined {
    const models = this._models();
    console.log(`🔍 Looking for model ID ${id} in:`, models);
    const result = models.find(model => model.id === id);
    console.log(`📋 Found model:`, result);
    return result;
  }

  /**
   * Get model by name
   */
  getModelByName(name: string): Model | undefined {
    const models = this._models();
    console.log(`🔍 Looking for model name "${name}" in:`, models);
    const result = models.find(model => model.name === name);
    console.log(`📋 Found model:`, result);
    return result;
  }

  /**
   * Map Revelio response IDs to names
   */
  mapRevelioIdsToNames(
    practiceAreaId: number,
    goodAtTagIds: number[]
  ): {
    practiceAreaName: string;
    goodAtTagNames: string[];
  } {
    const practiceArea = this.getPracticeAreaById(practiceAreaId);
    const goodAtTags = this.getGoodAtTagsByIds(goodAtTagIds);

    return {
      practiceAreaName: practiceArea?.name || '',
      goodAtTagNames: goodAtTags.map(tag => tag.name),
    };
  }

  // Add methods to update filters
  setEngineFilter(engine: string): void {
    this.selectedEngine.set(engine);
  }

  setEmbeddingFilter(isEmbedding: boolean): void {
    this.isEmbedding.set(isEmbedding);
  }

  /**
   * Load only models
   */
  async loadModelsOnly(): Promise<void> {
    this._loading.set(true);
    this._error.set(null);

    try {
      const models = await this.modelsService.getModels().toPromise();
      this._models.set(models || []);

    } catch (error) {
      this._error.set('Failed to load models');
    } finally {
      this._loading.set(false);
    }
  }
}
