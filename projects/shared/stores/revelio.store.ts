import { Injectable, computed, signal } from '@angular/core';
import { Observable, tap, map } from 'rxjs';

import {
  RevelioService,
  RevelioResponse,
  ParsedAgentData,
} from '../services/revelio.service';

export interface RevelioState {
  loading: boolean;
  error: string | null;
  lastQuery: string | null;
  lastResponse: RevelioResponse | null;
  parsedAgentData: ParsedAgentData | null;
  promptType: 'agent' | 'tool' | null;
}

@Injectable({ providedIn: 'root' })
export class RevelioStore {
  // Private state signals
  private readonly _loading = signal<boolean>(false);
  private readonly _error = signal<string | null>(null);
  private readonly _lastQuery = signal<string | null>(null);
  private readonly _lastResponse = signal<RevelioResponse | null>(null);
  private readonly _parsedAgentData = signal<ParsedAgentData | null>(null);
  private readonly _prompt = signal<string | null>(null);
  private readonly _promptType = signal<'agent' | 'tool' | null>(null);

  // Public readonly signals
  public readonly loading = this._loading.asReadonly();
  public readonly error = this._error.asReadonly();
  public readonly lastQuery = this._lastQuery.asReadonly();
  public readonly lastResponse = this._lastResponse.asReadonly();
  public readonly parsedAgentData = this._parsedAgentData.asReadonly();
  public readonly prompt = this._prompt.asReadonly();
  public readonly promptType = this._promptType.asReadonly();

  // Computed signals
  public readonly hasAgentData = computed(
    () => this._parsedAgentData() !== null
  );
  public readonly canCreateAgent = computed(
    () => this.hasAgentData() && !this._loading() && !this._error()
  );

  constructor(private revelioService: RevelioService) {}

  /**
   * Search for agent creation help
   */
  searchAgentCreation(query: string): Observable<ParsedAgentData | null> {
    this._loading.set(true);
    this._error.set(null);
    this._lastQuery.set(query);

    return this.revelioService.searchAgentCreation(query).pipe(
      tap({
        next: response => {
          console.log('🌐 Revelio API Raw Response:', response);
          console.log(
            '📝 Message Output (JSON String):',
            response.messageOutput
          );

          this._lastResponse.set(response);
          const parsedData = this.revelioService.parseAgentData(response);
          console.log('🔧 Parsed Agent Data:', parsedData);

          this._parsedAgentData.set(parsedData);
          this._loading.set(false);
        },
        error: error => {
          this._error.set(
            error.message || 'Failed to search for agent creation help'
          );
          this._loading.set(false);
        },
      }),
      map(response => {
        const parsedData = this.revelioService.parseAgentData(response);
        this._parsedAgentData.set(parsedData);
        return parsedData;
      })
    );
  }

  /**
   * Set the prompt for agent or tool creation
   */
  setPrompt(prompt: string, type: 'agent' | 'tool' = 'agent'): void {
    this._prompt.set(prompt);
    this._promptType.set(type);
  }

  /**
   * Clear the current search results
   */
  clearResults(): void {
    this._error.set(null);
    this._lastQuery.set(null);
    this._lastResponse.set(null);
    this._parsedAgentData.set(null);
    this._prompt.set(null);
    this._promptType.set(null);
  }

  /**
   * Get the current state
   */
  getState(): RevelioState {
    return {
      loading: this._loading(),
      error: this._error(),
      lastQuery: this._lastQuery(),
      lastResponse: this._lastResponse(),
      parsedAgentData: this._parsedAgentData(),
      promptType: this._promptType(),
    };
  }
}
