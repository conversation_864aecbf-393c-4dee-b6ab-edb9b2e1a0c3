# Loading State Management in Stores

This document explains how loading states are managed in the specialized stores (AgentStore, ToolStore, WorkflowStore) to provide optimal user experience.

## 🎯 Overview

The stores implement **dual loading state management** to distinguish between:

1. **User-initiated operations** - Show loading indicators
2. **Autosave operations** - Silent background operations

## 🏗️ Architecture

### **Loading State Signals**

Each store maintains two separate loading states:

```typescript
export class AgentStore {
  // Main loading state for user operations
  private readonly _loading = signal<boolean>(false);
  public readonly loading = this._loading.asReadonly();

  // Autosave loading state for background operations
  private readonly _autosaveLoading = signal<boolean>(false);
  public readonly autosaveLoading = this._autosaveLoading.asReadonly();
}
```

### **State Management Methods**

```typescript
export class AgentStore {
  // Set main loading state
  private setLoading(loading: boolean): void {
    this._loading.set(loading);
  }

  // Set autosave loading state
  private setAutosaveLoading(loading: boolean): void {
    this._autosaveLoading.set(loading);
  }
}
```

## 📊 Loading State Usage

### **1. User-Initiated Operations (Show Loading)**

```typescript
// ✅ These operations show loading indicators
loadAgents(filters?: AgentFilters): Observable<Agent[]> {
  this.setLoading(true);        // Show loading
  this.clearError();

  return this.agentService.getAll(filters || this._filters()).pipe(
    tap({
      next: (agents) => {
        this.updateAgents(agents);
        this.updateTotalCount(agents.length);
        this.setLoading(false);  // Hide loading
      },
      error: (error) => {
        this.setError(error.message || 'Failed to load agents');
        this.setLoading(false);  // Hide loading
      }
    })
  );
}

createAgent(agentData: Omit<Agent, 'id'>): Observable<Agent> {
  this.setLoading(true);        // Show loading
  this.clearError();

  return this.agentService.create(agentData).pipe(
    tap({
      next: (newAgent) => {
        // Add to agents list
        const currentAgents = this._agents();
        this.updateAgents([...currentAgents, newAgent]);
        
        this.setLoading(false);  // Hide loading
      },
      error: (error) => {
        this.setError(error.message || 'Failed to create agent');
        this.setLoading(false);  // Hide loading
      }
    })
  );
}
```

### **2. Autosave Operations (No Loading Indicator)**

```typescript
// ✅ These operations don't show loading indicators
private performAutosave(id: number, updates: Partial<Agent>): void {
  this.setAutosaveLoading(true);  // Internal state only
  
  this.agentService.update(id, updates).subscribe({
    next: (updatedAgent) => {
      // Update in agents list
      const currentAgents = this._agents();
      const updatedAgents = currentAgents.map(agent => 
        agent.id === id ? updatedAgent : agent
      );
      this.updateAgents(updatedAgents);

      // Update current agent if it's the one being updated
      const currentAgent = this._currentAgent();
      if (currentAgent && currentAgent.id === id) {
        this.updateCurrentAgent(updatedAgent);
      }

      // Clear dirty state
      this._isDirty.set(false);
      this.setAutosaveLoading(false);  // Clear internal state
    },
    error: (error) => {
      console.error('Autosave failed for agent:', error);
      // Keep dirty state for retry - user can manually save
      this.setAutosaveLoading(false);  // Clear internal state
    }
  });
}
```

## 🎨 UI Implementation

### **Component Template**

```typescript
@Component({
  selector: 'app-agent-manager',
  template: `
    <!-- Main loading indicator for user operations -->
    <div *ngIf="agentStore.loading()" class="loading-overlay">
      <div class="spinner">Loading...</div>
    </div>

    <!-- Autosave indicator (optional, subtle) -->
    <div *ngIf="agentStore.autosaveLoading()" class="autosave-indicator">
      <small class="text-muted">Saving...</small>
    </div>

    <!-- Error display -->
    <div *ngIf="agentStore.error()" class="error-alert">
      {{ agentStore.error() }}
    </div>

    <!-- Main content -->
    <div class="agent-list">
      <div *ngFor="let agent of agentStore.agents()">
        {{ agent.name }}
        <button (click)="editAgent(agent.id)">Edit</button>
      </div>
    </div>

    <!-- Dirty state indicator -->
    <div *ngIf="agentStore.isDirty()" class="dirty-indicator">
      ⚠️ Unsaved changes
    </div>
  `
})
export class AgentManagerComponent {
  constructor(public agentStore: AgentStore) {}
}
```

### **CSS Styling**

```scss
// Main loading overlay (prominent)
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .spinner {
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// Autosave indicator (subtle)
.autosave-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 12px;
  color: #6c757d;
  z-index: 100;
}

// Dirty state indicator
.dirty-indicator {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 8px 12px;
  border-radius: 4px;
  margin: 10px 0;
}
```

## 🔄 State Flow Examples

### **User Creates Agent**

```mermaid
sequenceDiagram
    participant U as User
    participant C as Component
    participant S as Store
    participant API as API

    U->>C: Click "Create Agent"
    C->>S: createAgent(agentData)
    S->>S: setLoading(true)
    S->>API: POST /agents
    API-->>S: Agent created
    S->>S: updateAgents()
    S->>S: setLoading(false)
    S-->>C: Success
    C->>U: Show success message
```

### **Autosave Triggers**

```mermaid
sequenceDiagram
    participant U as User
    participant C as Component
    participant S as Store
    participant API as API

    U->>C: Type in input field
    C->>S: onLocalChange(id, updates)
    S->>S: setDirty(true)
    S->>S: Debounce 2 seconds
    S->>S: setAutosaveLoading(true)
    S->>API: PUT /agents/{id}
    API-->>S: Agent updated
    S->>S: updateAgentInList()
    S->>S: setDirty(false)
    S->>S: setAutosaveLoading(false)
    S-->>C: Autosave complete
```

## 🎯 Best Practices

### **1. Use Appropriate Loading States**

```typescript
// ✅ For user operations - show loading
this.agentStore.loadAgents().subscribe();

// ✅ For autosave - no loading indicator
this.agentStore.onLocalChange(id, updates);

// ✅ Check loading states appropriately
if (this.agentStore.loading()) {
  // Show main loading indicator
}

if (this.agentStore.autosaveLoading()) {
  // Show subtle autosave indicator (optional)
}
```

### **2. Handle Loading States in Templates**

```typescript
// ✅ Show loading for user operations
<div *ngIf="agentStore.loading()" class="loading-spinner">
  Loading agents...
</div>

// ✅ Show autosave status (optional)
<div *ngIf="agentStore.autosaveLoading()" class="autosave-status">
  Saving changes...
</div>

// ✅ Disable forms during loading
<button [disabled]="agentStore.loading()" (click)="saveAgent()">
  Save Agent
</button>
```

### **3. Combine Loading States When Needed**

```typescript
// ✅ Combined loading state for complex operations
get combinedLoading() {
  return this.agentStore.loading() || this.agentStore.autosaveLoading();
}

// ✅ Use in template
<div *ngIf="combinedLoading" class="any-loading">
  Processing...
</div>
```

## 🚨 Common Pitfalls

### **❌ Don't Show Loading for Autosave**

```typescript
// ❌ Wrong: Don't show main loading for autosave
if (this.agentStore.loading() || this.agentStore.autosaveLoading()) {
  this.showLoadingSpinner(); // This will flicker during autosave
}

// ✅ Correct: Only show loading for user operations
if (this.agentStore.loading()) {
  this.showLoadingSpinner();
}
```

### **❌ Don't Block UI During Autosave**

```typescript
// ❌ Wrong: Disable form during autosave
<button [disabled]="agentStore.loading() || agentStore.autosaveLoading()">
  Save
</button>

// ✅ Correct: Only disable during user operations
<button [disabled]="agentStore.loading()">
  Save
</button>
```

## 🔧 Advanced Usage

### **Custom Loading States**

```typescript
export class AgentStore {
  // Custom loading states for specific operations
  private readonly _searchLoading = signal<boolean>(false);
  private readonly _exportLoading = signal<boolean>(false);

  public readonly searchLoading = this._searchLoading.asReadonly();
  public readonly exportLoading = this._exportLoading.asReadonly();

  searchAgents(query: string): Observable<Agent[]> {
    this.setSearchLoading(true);
    // ... search logic
  }

  exportAgent(id: number): Observable<any> {
    this.setExportLoading(true);
    // ... export logic
  }
}
```

### **Loading State Composition**

```typescript
export class AgentStore {
  // Computed loading states
  public readonly anyLoading = computed(() => 
    this._loading() || 
    this._searchLoading() || 
    this._exportLoading()
  );

  public readonly userOperationLoading = computed(() => 
    this._loading() || 
    this._searchLoading() || 
    this._exportLoading()
  );

  public readonly backgroundOperationLoading = computed(() => 
    this._autosaveLoading()
  );
}
```

## 📱 Mobile Considerations

### **Responsive Loading Indicators**

```scss
// Mobile-friendly loading states
@media (max-width: 768px) {
  .loading-overlay {
    .spinner {
      padding: 15px;
      font-size: 14px;
    }
  }

  .autosave-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    top: auto;
  }
}
```

### **Touch-Friendly Loading States**

```typescript
// Prevent touch events during loading
@Component({
  template: `
    <div 
      [class.loading]="agentStore.loading()"
      [class.touch-disabled]="agentStore.loading()"
      class="agent-form">
      <!-- Form content -->
    </div>
  `
})
export class AgentFormComponent {
  // Touch events are disabled during loading
}
```

## 🧪 Testing Loading States

### **Unit Tests**

```typescript
describe('AgentStore Loading States', () => {
  it('should show loading for user operations', () => {
    store.loadAgents();
    expect(store.loading()).toBe(true);
  });

  it('should not show loading for autosave', () => {
    store.onLocalChange(1, { name: 'New Name' });
    expect(store.loading()).toBe(false);
    expect(store.autosaveLoading()).toBe(true);
  });

  it('should clear loading states on completion', fakeAsync(() => {
    store.loadAgents();
    expect(store.loading()).toBe(true);
    
    tick(1000); // Simulate API response
    expect(store.loading()).toBe(false);
  }));
});
```

### **Integration Tests**

```typescript
describe('Agent Manager Loading States', () => {
  it('should show loading spinner during agent creation', async () => {
    await page.goto('/agents/create');
    await page.fill('input[name="name"]', 'Test Agent');
    await page.click('button[type="submit"]');
    
    // Should show loading spinner
    await expect(page.locator('.loading-overlay')).toBeVisible();
    
    // Wait for completion
    await expect(page.locator('.loading-overlay')).not.toBeVisible();
  });

  it('should show autosave indicator during typing', async () => {
    await page.goto('/agents/1/edit');
    await page.fill('input[name="name"]', 'Updated Name');
    
    // Should show autosave indicator
    await expect(page.locator('.autosave-indicator')).toBeVisible();
    
    // Wait for autosave completion
    await page.waitForTimeout(2500);
    await expect(page.locator('.autosave-indicator')).not.toBeVisible();
  });
});
```

## 🏁 Conclusion

The dual loading state management system provides:

- **Clear user feedback** for intentional operations
- **Silent background operations** for autosave
- **Better user experience** without UI flickering
- **Flexible state management** for different operation types
- **Consistent behavior** across all stores

By properly implementing these loading states, you can create a smooth, professional user experience that clearly communicates what's happening without being intrusive.
