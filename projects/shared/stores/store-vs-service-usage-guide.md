# Store vs Service Usage Guide

This document provides clear guidance on when to use **stores** vs **services** for agents, tools, and workflows in your Angular application.

## 🎯 Quick Decision Tree

```
Is this a user-initiated operation that needs state management?
├─ YES → Use STORE
│   ├─ CRUD operations (Create, Read, Update, Delete)
│   ├─ List management and data synchronization
│   ├─ UI state management (loading, error, dirty states)
│   ├─ Reactive data binding
│   └─ Caching and data persistence
│
└─ NO → Use SERVICE directly
    ├─ Export/Import operations
    ├─ Validation and business logic
    ├─ Testing and debugging
    ├─ External integrations
    └─ One-time operations
```

## 🏗️ Architecture Overview

### **Stores (State Management Layer)**
- **Purpose**: Manage application state, cache data, synchronize UI
- **State**: Stateful - hold and manage data between operations
- **Lifecycle**: Long-lived, persist throughout component lifecycle
- **Usage**: Primary interface for most operations

### **Services (Data Layer)**
- **Purpose**: API communication, data transformation, business logic
- **State**: Stateless - don't hold data between calls
- **Lifecycle**: Short-lived, created/destroyed per operation
- **Usage**: Direct API access for specialized operations

## 📋 Detailed Usage Guidelines

### **✅ Use STORES For:**

#### **1. CRUD Operations**
```typescript
// ✅ RECOMMENDED: Use stores for CRUD
export class AgentManagerComponent {
  constructor(private agentStore: AgentStore) {}

  // Create agent with state management
  createAgent(agentData: Omit<Agent, 'id'>): void {
    this.agentStore.createAgent(agentData).subscribe();
    // Store automatically:
    // - Updates loading state
    // - Refreshes agent list
    // - Handles errors
    // - Updates UI
  }

  // Update agent with state management
  updateAgent(id: number, updates: Partial<Agent>): void {
    this.agentStore.updateAgent(id, updates).subscribe();
    // Store automatically updates local state
  }

  // Delete agent with state management
  deleteAgent(id: number): void {
    this.agentStore.deleteAgent(id).subscribe();
    // Store automatically removes from list
  }
}
```

#### **2. Data Loading and Management**
```typescript
// ✅ RECOMMENDED: Use stores for data loading
export class AgentListComponent {
  constructor(private agentStore: AgentStore) {}

  ngOnInit(): void {
    // Load agents with automatic state management
    this.agentStore.loadAgents({ teamId: 23, status: 'APPROVED' });
  }

  // Reactive data binding
  agents$ = this.agentStore.agents$;
  loading$ = this.agentStore.loading$;
  error$ = this.agentStore.error$;
  totalCount$ = this.agentStore.totalCount$;
}
```

#### **3. UI State Management**
```typescript
// ✅ RECOMMENDED: Use stores for UI state
export class AgentEditorComponent {
  constructor(private agentStore: AgentStore) {}

  // Reactive state management
  loading$ = this.agentStore.loading$;
  error$ = this.agentStore.error$;
  isDirty$ = this.agentStore.isDirty$;

  // Autosave functionality
  onFieldChange(field: keyof Agent, value: any): void {
    this.agentStore.onLocalChange(this.agent.id!, { [field]: value });
    // Store handles:
    // - Debounced autosave
    // - Dirty state tracking
    // - Background updates
  }
}
```

#### **4. Search and Filtering**
```typescript
// ✅ RECOMMENDED: Use stores for search/filter
export class AgentSearchComponent {
  constructor(private agentStore: AgentStore) {}

  searchAgents(query: string): void {
    this.agentStore.searchAgents(query, { teamId: 23 });
    // Store handles:
    // - Search state management
    // - Result caching
    // - Loading states
    // - Error handling
  }

  // Reactive search results
  searchResults$ = this.agentStore.agents$;
  searchLoading$ = this.agentStore.loading$;
}
```

#### **5. Lifecycle Operations**
```typescript
// ✅ RECOMMENDED: Use stores for lifecycle operations
export class AgentWorkflowComponent {
  constructor(private agentStore: AgentStore) {}

  submitForReview(agentId: number): void {
    this.agentStore.submitForReview(agentId).subscribe();
    // Store automatically:
    // - Updates agent status
    // - Refreshes agent list
    // - Updates UI state
  }

  approveAgent(agentId: number): void {
    this.agentStore.approveAgent(agentId).subscribe();
  }

  rejectAgent(agentId: number): void {
    this.agentStore.rejectAgent(agentId).subscribe();
  }
}
```

### **✅ Use SERVICES Directly For:**

#### **1. Export/Import Operations**
```typescript
// ✅ RECOMMENDED: Use service directly for export/import
export class AgentExportComponent {
  constructor(private agentService: AgentService) {}

  exportAgent(id: number): void {
    this.agentService.export(id).subscribe(config => {
      // Handle download
      this.downloadFile(config);
    });
    // No state management needed
    // No UI updates required
    // One-time operation
  }

  importAgent(config: any): void {
    this.agentService.import(config).subscribe(agent => {
      // Handle import result
      this.showImportSuccess(agent);
    });
  }
}
```

#### **2. Validation and Business Logic**
```typescript
// ✅ RECOMMENDED: Use service directly for validation
export class AgentValidationComponent {
  constructor(private agentService: AgentService) {}

  validateAgentConfig(config: AgentConfig): void {
    this.agentService.validateConfig(config).subscribe(result => {
      // Handle validation result
      this.showValidationResult(result);
    });
    // No state management needed
    // Business logic only
  }

  testAgent(id: number, inputs: any): void {
    this.agentService.test(id, inputs).subscribe(result => {
      // Handle test result
      this.showTestResult(result);
    });
  }
}
```

#### **3. Testing and Debugging**
```typescript
// ✅ RECOMMENDED: Use service directly for testing
export class AgentTestingComponent {
  constructor(private agentService: AgentService) {}

  debugAgent(id: number): void {
    this.agentService.debug(id).subscribe(debugInfo => {
      // Show debug information
      console.log('Debug info:', debugInfo);
    });
    // No state management needed
    // Development/testing only
  }

  getAgentLogs(id: number): void {
    this.agentService.getLogs(id).subscribe(logs => {
      // Display logs
      this.displayLogs(logs);
    });
  }
}
```

#### **4. External Integrations**
```typescript
// ✅ RECOMMENDED: Use service directly for external integrations
export class AgentIntegrationComponent {
  constructor(private agentService: AgentService) {}

  syncWithExternalSystem(agentId: number): void {
    this.agentService.syncExternal(agentId).subscribe(result => {
      // Handle sync result
      this.showSyncStatus(result);
    });
    // No internal state management needed
    // External system integration
  }

  webhookCallback(data: any): void {
    this.agentService.processWebhook(data).subscribe(result => {
      // Handle webhook processing
      this.processWebhookResult(result);
    });
  }
}
```

## 🔄 Hybrid Approach (Recommended)

### **Primary: Store-First Pattern**
```typescript
// ✅ RECOMMENDED: Primary pattern
export class AgentManagerComponent {
  constructor(
    private agentStore: AgentStore,        // Primary: State management
    private agentService: AgentService     // Secondary: Specialized operations
  ) {}

  // Primary operations through store
  createAgent(agentData: Omit<Agent, 'id'>): void {
    this.agentStore.createAgent(agentData).subscribe();
  }

  // Specialized operations through service
  exportAgent(id: number): void {
    this.agentService.export(id).subscribe(config => {
      this.downloadFile(config);
    });
  }
}
```

### **Fallback: Service-Only Pattern**
```typescript
// ✅ Use when no state management is needed
export class AgentUtilityComponent {
  constructor(private agentService: AgentService) {}

  // Utility operations only
  validateAgent(id: number): void {
    this.agentService.validate(id).subscribe(result => {
      this.showValidationResult(result);
    });
  }

  getAgentMetadata(id: number): void {
    this.agentService.getMetadata(id).subscribe(metadata => {
      this.displayMetadata(metadata);
    });
  }
}
```

## 📊 Decision Matrix

| Operation Type | Use Store | Use Service | Reasoning |
|----------------|-----------|-------------|-----------|
| **Create Agent** | ✅ | ❌ | Needs state management, UI updates |
| **Update Agent** | ✅ | ❌ | Needs state management, UI updates |
| **Delete Agent** | ✅ | ❌ | Needs state management, UI updates |
| **Load Agent List** | ✅ | ❌ | Needs caching, loading states |
| **Search Agents** | ✅ | ❌ | Needs result caching, loading states |
| **Submit for Review** | ✅ | ❌ | Needs state management, UI updates |
| **Approve/Reject** | ✅ | ❌ | Needs state management, UI updates |
| **Export Agent** | ❌ | ✅ | One-time operation, no state needed |
| **Import Agent** | ❌ | ✅ | One-time operation, no state needed |
| **Validate Agent** | ❌ | ✅ | Business logic, no state needed |
| **Test Agent** | ❌ | ✅ | One-time operation, no state needed |
| **Get Agent Logs** | ❌ | ✅ | One-time operation, no state needed |
| **External Sync** | ❌ | ✅ | External integration, no state needed |

## 🎨 Component Implementation Patterns

### **Pattern 1: Store-Only Component**
```typescript
// ✅ Use when component only needs state management
@Component({
  selector: 'app-agent-list',
  template: `
    <div *ngIf="agentStore.loading()">Loading...</div>
    <div *ngFor="let agent of agentStore.agents()">
      {{ agent.name }}
      <button (click)="editAgent(agent.id)">Edit</button>
    </div>
  `
})
export class AgentListComponent {
  constructor(private agentStore: AgentStore) {}

  editAgent(id: number): void {
    this.agentStore.loadAgentById(id).subscribe();
  }
}
```

### **Pattern 2: Service-Only Component**
```typescript
// ✅ Use when component only needs specialized operations
@Component({
  selector: 'app-agent-export',
  template: `
    <button (click)="exportAgent(123)">Export Agent</button>
    <button (click)="validateAgent(123)">Validate Agent</button>
  `
})
export class AgentExportComponent {
  constructor(private agentService: AgentService) {}

  exportAgent(id: number): void {
    this.agentService.export(id).subscribe(config => {
      this.downloadFile(config);
    });
  }

  validateAgent(id: number): void {
    this.agentService.validate(id).subscribe(result => {
      this.showValidationResult(result);
    });
  }
}
```

### **Pattern 3: Hybrid Component (Recommended)**
```typescript
// ✅ RECOMMENDED: Use when component needs both
@Component({
  selector: 'app-agent-manager',
  template: `
    <div *ngIf="agentStore.loading()">Loading...</div>
    <div *ngFor="let agent of agentStore.agents()">
      {{ agent.name }}
      <button (click)="editAgent(agent.id)">Edit</button>
      <button (click)="exportAgent(agent.id)">Export</button>
    </div>
  `
})
export class AgentManagerComponent {
  constructor(
    private agentStore: AgentStore,        // Primary: State management
    private agentService: AgentService     // Secondary: Specialized operations
  ) {}

  // Primary operations through store
  editAgent(id: number): void {
    this.agentStore.loadAgentById(id).subscribe();
  }

  // Specialized operations through service
  exportAgent(id: number): void {
    this.agentService.export(id).subscribe(config => {
      this.downloadFile(config);
    });
  }
}
```

## 🚨 Common Anti-Patterns

### **❌ Don't Mix State Management**
```typescript
// ❌ WRONG: Mixing store and service for same operation
export class BadComponent {
  constructor(
    private agentStore: AgentStore,
    private agentService: AgentService
  ) {}

  // ❌ Wrong: Using service for state-managed operation
  createAgent(agentData: any): void {
    this.agentService.create(agentData).subscribe(agent => {
      // Manual state management - error prone!
      this.agents.push(agent);
      this.loading = false;
    });
  }
}
```

### **❌ Don't Duplicate Functionality**
```typescript
// ❌ WRONG: Duplicating store functionality in service
export class BadService {
  // ❌ Wrong: Service shouldn't manage state
  private agents: Agent[] = [];
  
  create(agentData: any): Observable<Agent> {
    return this.http.post('/agents', agentData).pipe(
      tap(agent => {
        // ❌ Wrong: Service managing state
        this.agents.push(agent);
      })
    );
  }
}
```

### **❌ Don't Use Store for One-Time Operations**
```typescript
// ❌ WRONG: Using store for one-time operation
export class BadComponent {
  constructor(private agentStore: AgentStore) {}

  // ❌ Wrong: Store not needed for export
  exportAgent(id: number): void {
    this.agentStore.exportAgent(id).subscribe(); // Unnecessary complexity
  }
}
```

## 🧪 Testing Strategies

### **Testing Store Operations**
```typescript
describe('AgentManagerComponent', () => {
  let component: AgentManagerComponent;
  let agentStore: jasmine.SpyObj<AgentStore>;

  beforeEach(() => {
    agentStore = jasmine.createSpyObj('AgentStore', ['createAgent', 'agents']);
    component = new AgentManagerComponent(agentStore);
  });

  it('should create agent through store', () => {
    agentStore.createAgent.and.returnValue(of(mockAgent));
    component.createAgent(mockAgentData);
    expect(agentStore.createAgent).toHaveBeenCalledWith(mockAgentData);
  });
});
```

### **Testing Service Operations**
```typescript
describe('AgentExportComponent', () => {
  let component: AgentExportComponent;
  let agentService: jasmine.SpyObj<AgentService>;

  beforeEach(() => {
    agentService = jasmine.createSpyObj('AgentService', ['export']);
    component = new AgentExportComponent(agentService);
  });

  it('should export agent through service', () => {
    agentService.export.and.returnValue(of(mockConfig));
    component.exportAgent(123);
    expect(agentService.export).toHaveBeenCalledWith(123);
  });
});
```

## 🔮 Future Considerations

### **Performance Optimization**
```typescript
// Future: Lazy loading of stores
export class LazyAgentComponent {
  constructor() {}

  async ngOnInit(): Promise<void> {
    // Load store only when needed
    const { AgentStore } = await import('@shared/stores/agent.store');
    this.agentStore = new AgentStore(this.agentService);
  }
}
```

### **Advanced State Management**
```typescript
// Future: Computed state composition
export class AdvancedAgentStore {
  // Computed loading states
  public readonly anyLoading = computed(() => 
    this._loading() || 
    this._searchLoading() || 
    this._exportLoading()
  );

  // Computed data states
  public readonly dataState = computed(() => ({
    loading: this._loading(),
    error: this._error(),
    hasData: this._agents().length > 0,
    isEmpty: this._agents().length === 0
  }));
}
```

## 🏁 Summary

### **Use STORES When You Need:**
- ✅ **State management** and data persistence
- ✅ **UI synchronization** and reactive updates
- ✅ **Caching** and data optimization
- ✅ **Loading states** and error handling
- ✅ **Autosave** and background operations
- ✅ **CRUD operations** with UI updates

### **Use SERVICES Directly When You Need:**
- ✅ **One-time operations** (export, import)
- ✅ **Business logic** (validation, testing)
- ✅ **External integrations** (webhooks, sync)
- ✅ **Utility functions** (debugging, logging)
- ✅ **No state management** required

### **Recommended Pattern:**
1. **Start with stores** for most operations
2. **Fall back to services** for specialized operations
3. **Use hybrid approach** when both are needed
4. **Keep stores focused** on state management
5. **Keep services focused** on data operations

By following these guidelines, you'll create a clean, maintainable architecture that provides the best user experience while keeping your code organized and efficient.
