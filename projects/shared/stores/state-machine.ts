// stores/state-machine.ts
import { ArtifactFlag } from '../models/artifact-flags';

/**
 * State transition rules for artifacts
 * Each state can transition to the states listed in its array
 */
export const STATE_TRANSITIONS: Record<ArtifactFlag, ArtifactFlag[]> = {
  // Initial states
  [ArtifactFlag.CREATED]: [
    ArtifactFlag.DRAFTED,
    ArtifactFlag.READY_FOR_TEST,
    ArtifactFlag.READY_FOR_SUBMIT,
  ],

  [ArtifactFlag.DRAFTED]: [
    ArtifactFlag.DIRTY,
    ArtifactFlag.READY_FOR_TEST,
    ArtifactFlag.READY_FOR_SUBMIT,
    ArtifactFlag.ARCHIVED,
  ],

  // Work in progress states
  [ArtifactFlag.DIRTY]: [ArtifactFlag.DRAFTED, ArtifactFlag.ARCHIVED],

  [ArtifactFlag.IN_REVIEW]: [
    ArtifactFlag.APPROVED,
    ArtifactFlag.REJECTED,
    ArtifactFlag.SENT_FOR_REVISION,
  ],

  // Testing and validation states
  [ArtifactFlag.READY_FOR_TEST]: [
    ArtifactFlag.TESTING,
    ArtifactFlag.DRAFTED,
    ArtifactFlag.ARCHIVED,
  ],

  [ArtifactFlag.TESTING]: [
    ArtifactFlag.HAS_SUCCESSFUL_EXECUTION,
    ArtifactFlag.READY_FOR_TEST,
    ArtifactFlag.DRAFTED,
  ],

  [ArtifactFlag.HAS_SUCCESSFUL_EXECUTION]: [
    ArtifactFlag.READY_FOR_SUBMIT,
    ArtifactFlag.READY_FOR_TEST,
    ArtifactFlag.DRAFTED,
  ],

  // Submission and approval states
  [ArtifactFlag.READY_FOR_SUBMIT]: [
    ArtifactFlag.SUBMITTED,
    ArtifactFlag.DRAFTED,
    ArtifactFlag.ARCHIVED,
  ],

  [ArtifactFlag.SUBMITTED]: [ArtifactFlag.IN_REVIEW, ArtifactFlag.DRAFTED],

  [ArtifactFlag.APPROVED]: [
    ArtifactFlag.ARCHIVED,
    ArtifactFlag.EXECUTING, // For workflows
    ArtifactFlag.DRAFTED, // Allow modifications after approval
  ],

  [ArtifactFlag.REJECTED]: [ArtifactFlag.DRAFTED, ArtifactFlag.ARCHIVED],

  // Revision and archival states
  [ArtifactFlag.SENT_FOR_REVISION]: [
    ArtifactFlag.DRAFTED,
    ArtifactFlag.ARCHIVED,
  ],

  [ArtifactFlag.ARCHIVED]: [
    ArtifactFlag.DRAFTED, // Allow restoration from archive
  ],

  // Execution states (for workflows)
  [ArtifactFlag.EXECUTING]: [
    ArtifactFlag.EXECUTION_COMPLETED,
    ArtifactFlag.EXECUTION_FAILED,
    ArtifactFlag.EXECUTION_CANCELLED,
    ArtifactFlag.APPROVED, // Return to approved state if execution is interrupted
  ],

  [ArtifactFlag.EXECUTION_COMPLETED]: [
    ArtifactFlag.APPROVED,
    ArtifactFlag.ARCHIVED,
  ],

  [ArtifactFlag.EXECUTION_FAILED]: [
    ArtifactFlag.APPROVED,
    ArtifactFlag.DRAFTED,
    ArtifactFlag.ARCHIVED,
  ],

  [ArtifactFlag.EXECUTION_CANCELLED]: [
    ArtifactFlag.APPROVED,
    ArtifactFlag.DRAFTED,
    ArtifactFlag.ARCHIVED,
  ],
};

/**
 * Check if a transition from one state to another is valid
 * @param from - Current state
 * @param to - Target state
 * @returns true if transition is valid, false otherwise
 */
export function canTransition(from: ArtifactFlag, to: ArtifactFlag): boolean {
  return STATE_TRANSITIONS[from]?.includes(to) || false;
}

/**
 * Get all possible next states for a given current state
 * @param currentState - Current artifact state
 * @returns Array of possible next states
 */
export function getPossibleTransitions(
  currentState: ArtifactFlag
): ArtifactFlag[] {
  return STATE_TRANSITIONS[currentState] || [];
}

/**
 * Check if an artifact can be edited in its current state
 * @param state - Current artifact state
 * @returns true if artifact can be edited, false otherwise
 */
export function canEdit(state: ArtifactFlag): boolean {
  return [
    ArtifactFlag.CREATED,
    ArtifactFlag.DRAFTED,
    ArtifactFlag.DIRTY,
    ArtifactFlag.REJECTED,
    ArtifactFlag.SENT_FOR_REVISION,
  ].includes(state);
}

/**
 * Check if an artifact can be submitted for review
 * @param state - Current artifact state
 * @returns true if artifact can be submitted, false otherwise
 */
export function canSubmitForReview(state: ArtifactFlag): boolean {
  return [
    ArtifactFlag.CREATED,
    ArtifactFlag.DRAFTED,
    ArtifactFlag.READY_FOR_SUBMIT,
    ArtifactFlag.HAS_SUCCESSFUL_EXECUTION,
  ].includes(state);
}

/**
 * Check if an artifact can be tested
 * @param state - Current artifact state
 * @returns true if artifact can be tested, false otherwise
 */
export function canTest(state: ArtifactFlag): boolean {
  return [
    ArtifactFlag.CREATED,
    ArtifactFlag.DRAFTED,
    ArtifactFlag.READY_FOR_TEST,
    ArtifactFlag.APPROVED,
  ].includes(state);
}

/**
 * Check if an artifact can be executed (for workflows)
 * @param state - Current artifact state
 * @returns true if artifact can be executed, false otherwise
 */
export function canExecute(state: ArtifactFlag): boolean {
  return [ArtifactFlag.APPROVED].includes(state);
}

/**
 * Check if an artifact can be approved
 * @param state - Current artifact state
 * @returns true if artifact can be approved, false otherwise
 */
export function canApprove(state: ArtifactFlag): boolean {
  return [ArtifactFlag.IN_REVIEW].includes(state);
}

/**
 * Check if an artifact can be rejected
 * @param state - Current artifact state
 * @returns true if artifact can be rejected, false otherwise
 */
export function canReject(state: ArtifactFlag): boolean {
  return [ArtifactFlag.IN_REVIEW].includes(state);
}

/**
 * Check if an artifact can be archived
 * @param state - Current artifact state
 * @returns true if artifact can be archived, false otherwise
 */
export function canArchive(state: ArtifactFlag): boolean {
  return [
    ArtifactFlag.CREATED,
    ArtifactFlag.DRAFTED,
    ArtifactFlag.APPROVED,
    ArtifactFlag.REJECTED,
    ArtifactFlag.EXECUTION_COMPLETED,
    ArtifactFlag.EXECUTION_FAILED,
    ArtifactFlag.EXECUTION_CANCELLED,
  ].includes(state);
}

/**
 * Get the display name for a state
 * @param state - Artifact state
 * @returns Human-readable state name
 */
export function getStateDisplayName(state: ArtifactFlag): string {
  const displayNames: Record<ArtifactFlag, string> = {
    [ArtifactFlag.CREATED]: 'Created',
    [ArtifactFlag.DRAFTED]: 'Draft',
    [ArtifactFlag.DIRTY]: 'Modified',
    [ArtifactFlag.IN_REVIEW]: 'In Review',
    [ArtifactFlag.READY_FOR_TEST]: 'Ready for Testing',
    [ArtifactFlag.TESTING]: 'Testing',
    [ArtifactFlag.HAS_SUCCESSFUL_EXECUTION]: 'Tested Successfully',
    [ArtifactFlag.READY_FOR_SUBMIT]: 'Ready for Submission',
    [ArtifactFlag.SUBMITTED]: 'Submitted',
    [ArtifactFlag.APPROVED]: 'Approved',
    [ArtifactFlag.REJECTED]: 'Rejected',
    [ArtifactFlag.SENT_FOR_REVISION]: 'Sent for Revision',
    [ArtifactFlag.ARCHIVED]: 'Archived',
    [ArtifactFlag.EXECUTING]: 'Executing',
    [ArtifactFlag.EXECUTION_COMPLETED]: 'Execution Completed',
    [ArtifactFlag.EXECUTION_FAILED]: 'Execution Failed',
    [ArtifactFlag.EXECUTION_CANCELLED]: 'Execution Cancelled',
  };

  return displayNames[state] || state;
}

/**
 * Get the CSS class for styling a state
 * @param state - Artifact state
 * @returns CSS class name for styling
 */
export function getStateCssClass(state: ArtifactFlag): string {
  const cssClasses: Record<ArtifactFlag, string> = {
    [ArtifactFlag.CREATED]: 'state-created',
    [ArtifactFlag.DRAFTED]: 'state-draft',
    [ArtifactFlag.DIRTY]: 'state-dirty',
    [ArtifactFlag.IN_REVIEW]: 'state-review',
    [ArtifactFlag.READY_FOR_TEST]: 'state-ready-test',
    [ArtifactFlag.TESTING]: 'state-testing',
    [ArtifactFlag.HAS_SUCCESSFUL_EXECUTION]: 'state-success',
    [ArtifactFlag.READY_FOR_SUBMIT]: 'state-ready-submit',
    [ArtifactFlag.SUBMITTED]: 'state-submitted',
    [ArtifactFlag.APPROVED]: 'state-approved',
    [ArtifactFlag.REJECTED]: 'state-rejected',
    [ArtifactFlag.SENT_FOR_REVISION]: 'state-revision',
    [ArtifactFlag.ARCHIVED]: 'state-archived',
    [ArtifactFlag.EXECUTING]: 'state-executing',
    [ArtifactFlag.EXECUTION_COMPLETED]: 'state-completed',
    [ArtifactFlag.EXECUTION_FAILED]: 'state-failed',
    [ArtifactFlag.EXECUTION_CANCELLED]: 'state-cancelled',
  };

  return cssClasses[state] || 'state-default';
}
