# State Machine for Artifact Lifecycle Management

This document describes the comprehensive state machine system for managing artifact lifecycles in the AAVA system. The state machine provides a robust framework for controlling how artifacts (agents, tools, and workflows) can transition between different states.

## Overview

The state machine ensures that artifacts follow proper lifecycle workflows and prevents invalid state transitions. It provides utility functions to check what actions are allowed in each state and helps maintain data integrity across the system.

## Artifact States

### Initial States
- **`CREATED`**: Artifact has been created but not yet configured
- **`DRAFTED`**: <PERSON><PERSON><PERSON> is in draft mode and can be edited

### Work in Progress States
- **`DIRTY`**: <PERSON>ifact has unsaved changes
- **`IN_REVIEW`**: Artifact is under review by approvers

### Testing and Validation States
- **`READY_FOR_TEST`**: Artifact is ready for testing
- **`TESTING`**: Artifact is currently being tested
- **`HAS_SUCCESSFUL_EXECUTION`**: Artifact has passed testing

### Submission and Approval States
- **`READY_FOR_SUBMIT`**: Artifact is ready to be submitted for approval
- **`SUBMITTED`**: Artifact has been submitted for approval
- **`APPROVED`**: Artifact has been approved and can be used
- **`REJECTED`**: Artifact has been rejected and needs revision

### Revision and Archival States
- **`SENT_FOR_REVISION`**: Artifact has been sent back for revision
- **`ARCHIVED`**: Artifact has been archived (can be restored)

### Execution States (Workflows)
- **`EXECUTING`**: Workflow is currently running
- **`EXECUTION_COMPLETED`**: Workflow execution finished successfully
- **`EXECUTION_FAILED`**: Workflow execution failed
- **`EXECUTION_CANCELLED`**: Workflow execution was cancelled

## State Transitions

### Valid Transition Rules

```typescript
// Example: What states can a DRAFTED artifact transition to?
const possibleStates = getPossibleTransitions(ArtifactFlag.DRAFTED);
// Returns: [DIRTY, READY_FOR_TEST, READY_FOR_SUBMIT, ARCHIVED]
```

### Transition Matrix

| Current State | Can Transition To |
|---------------|-------------------|
| `CREATED` | `DRAFTED`, `READY_FOR_TEST`, `READY_FOR_SUBMIT` |
| `DRAFTED` | `DIRTY`, `READY_FOR_TEST`, `READY_FOR_SUBMIT`, `ARCHIVED` |
| `DIRTY` | `DRAFTED`, `ARCHIVED` |
| `IN_REVIEW` | `APPROVED`, `REJECTED`, `SENT_FOR_REVISION` |
| `READY_FOR_TEST` | `TESTING`, `DRAFTED`, `ARCHIVED` |
| `TESTING` | `HAS_SUCCESSFUL_EXECUTION`, `READY_FOR_TEST`, `DRAFTED` |
| `HAS_SUCCESSFUL_EXECUTION` | `READY_FOR_SUBMIT`, `READY_FOR_TEST`, `DRAFTED` |
| `READY_FOR_SUBMIT` | `SUBMITTED`, `DRAFTED`, `ARCHIVED` |
| `SUBMITTED` | `IN_REVIEW`, `DRAFTED` |
| `APPROVED` | `ARCHIVED`, `EXECUTING`, `DRAFTED` |
| `REJECTED` | `DRAFTED`, `ARCHIVED` |
| `SENT_FOR_REVISION` | `DRAFTED`, `ARCHIVED` |
| `ARCHIVED` | `DRAFTED` |
| `EXECUTING` | `EXECUTION_COMPLETED`, `EXECUTION_FAILED`, `EXECUTION_CANCELLED`, `APPROVED` |
| `EXECUTION_COMPLETED` | `APPROVED`, `ARCHIVED` |
| `EXECUTION_FAILED` | `APPROVED`, `DRAFTED`, `ARCHIVED` |
| `EXECUTION_CANCELLED` | `APPROVED`, `DRAFTED`, `ARCHIVED` |

## Utility Functions

### State Transition Validation

```typescript
import { canTransition, getPossibleTransitions } from '@shared/stores/state-machine';

// Check if a transition is valid
const isValid = canTransition(ArtifactFlag.DRAFTED, ArtifactFlag.READY_FOR_TEST);
// Returns: true

// Get all possible next states
const nextStates = getPossibleTransitions(ArtifactFlag.DRAFTED);
// Returns: [DIRTY, READY_FOR_TEST, READY_FOR_SUBMIT, ARCHIVED]
```

### Action Permission Checks

```typescript
import { 
  canEdit, 
  canSubmitForReview, 
  canTest, 
  canExecute, 
  canApprove, 
  canReject, 
  canArchive 
} from '@shared/stores/state-machine';

// Check if artifact can be edited
const editable = canEdit(ArtifactFlag.DRAFTED);
// Returns: true

// Check if artifact can be submitted for review
const submittable = canSubmitForReview(ArtifactFlag.READY_FOR_SUBMIT);
// Returns: true

// Check if artifact can be tested
const testable = canTest(ArtifactFlag.APPROVED);
// Returns: true

// Check if workflow can be executed
const executable = canExecute(ArtifactFlag.APPROVED);
// Returns: true

// Check if artifact can be approved
const approvable = canApprove(ArtifactFlag.IN_REVIEW);
// Returns: true

// Check if artifact can be rejected
const rejectable = canReject(ArtifactFlag.IN_REVIEW);
// Returns: true

// Check if artifact can be archived
const archivable = canArchive(ArtifactFlag.APPROVED);
// Returns: true
```

### Display and Styling

```typescript
import { getStateDisplayName, getStateCssClass } from '@shared/stores/state-machine';

// Get human-readable state name
const displayName = getStateDisplayName(ArtifactFlag.IN_REVIEW);
// Returns: "In Review"

// Get CSS class for styling
const cssClass = getStateCssClass(ArtifactFlag.APPROVED);
// Returns: "state-approved"
```

## Usage Examples

### Component State Management

```typescript
import { Component } from '@angular/core';
import { 
  ArtifactFlag, 
  canTransition, 
  getPossibleTransitions,
  canEdit,
  canSubmitForReview 
} from '@shared/stores/state-machine';

@Component({
  selector: 'app-artifact-manager',
  template: `
    <div class="artifact-actions">
      <!-- Edit button - only show if editing is allowed -->
      <button 
        *ngIf="canEditArtifact()"
        (click)="editArtifact()"
        class="btn-edit">
        Edit
      </button>

      <!-- Submit button - only show if submission is allowed -->
      <button 
        *ngIf="canSubmitArtifact()"
        (click)="submitForReview()"
        class="btn-submit">
        Submit for Review
      </button>

      <!-- State transition buttons -->
      <div class="state-transitions">
        <button 
          *ngFor="let nextState of getNextPossibleStates()"
          (click)="transitionTo(nextState)"
          [class]="'btn-transition-' + nextState.toLowerCase()">
          {{ getStateDisplayName(nextState) }}
        </button>
      </div>
    </div>
  `
})
export class ArtifactManagerComponent {
  currentState: ArtifactFlag = ArtifactFlag.DRAFTED;

  canEditArtifact(): boolean {
    return canEdit(this.currentState);
  }

  canSubmitArtifact(): boolean {
    return canSubmitForReview(this.currentState);
  }

  getNextPossibleStates(): ArtifactFlag[] {
    return getPossibleTransitions(this.currentState);
  }

  transitionTo(newState: ArtifactFlag): void {
    if (canTransition(this.currentState, newState)) {
      this.currentState = newState;
      // Update artifact in backend
      this.updateArtifactState(newState);
    } else {
      console.warn(`Invalid transition from ${this.currentState} to ${newState}`);
    }
  }

  private updateArtifactState(newState: ArtifactFlag): void {
    // Implementation for updating artifact state
  }
}
```

### Store Integration

```typescript
import { Injectable } from '@angular/core';
import { 
  ArtifactFlag, 
  canTransition,
  canEdit,
  canSubmitForReview 
} from '@shared/stores/state-machine';

@Injectable({ providedIn: 'root' })
export class ArtifactStore {
  private _currentState = signal<ArtifactFlag>(ArtifactFlag.DRAFTED);

  // Check if transition is valid before updating
  transitionTo(newState: ArtifactFlag): boolean {
    const currentState = this._currentState();
    
    if (canTransition(currentState, newState)) {
      this._currentState.set(newState);
      return true;
    } else {
      console.warn(`Invalid transition from ${currentState} to ${newState}`);
      return false;
    }
  }

  // Check permissions before allowing actions
  canEdit(): boolean {
    return canEdit(this._currentState());
  }

  canSubmitForReview(): boolean {
    return canSubmitForReview(this._currentState());
  }

  // Get current state
  getCurrentState(): ArtifactFlag {
    return this._currentState();
  }
}
```

### Template Usage

```html
<!-- Display current state with proper styling -->
<div class="artifact-status">
  <span [class]="getStateCssClass(artifact.state)">
    {{ getStateDisplayName(artifact.state) }}
  </span>
</div>

<!-- Show/hide buttons based on state permissions -->
<div class="action-buttons">
  <button 
    *ngIf="canEdit(artifact.state)"
    (click)="editArtifact()"
    class="btn-edit">
    Edit
  </button>

  <button 
    *ngIf="canSubmitForReview(artifact.state)"
    (click)="submitForReview()"
    class="btn-submit">
    Submit for Review
  </button>

  <button 
    *ngIf="canTest(artifact.state)"
    (click)="testArtifact()"
    class="btn-test">
    Test
  </button>

  <button 
    *ngIf="canExecute(artifact.state)"
    (click)="executeArtifact()"
    class="btn-execute">
    Execute
  </button>
</div>

<!-- State transition options -->
<div class="state-transitions">
  <h4>Available Actions:</h4>
  <div class="transition-options">
    <button 
      *ngFor="let nextState of getPossibleTransitions(artifact.state)"
      (click)="transitionTo(nextState)"
      [class]="'btn-' + nextState.toLowerCase()">
      {{ getStateDisplayName(nextState) }}
    </button>
  </div>
</div>
```

## CSS Styling

The state machine provides CSS classes for styling different states:

```css
/* State-specific styling */
.state-created { background-color: #e3f2fd; color: #1976d2; }
.state-draft { background-color: #fff3e0; color: #f57c00; }
.state-dirty { background-color: #ffebee; color: #d32f2f; }
.state-review { background-color: #fff8e1; color: #fbc02d; }
.state-ready-test { background-color: #e8f5e8; color: #388e3c; }
.state-testing { background-color: #f3e5f5; color: #7b1fa2; }
.state-success { background-color: #e8f5e8; color: #388e3c; }
.state-ready-submit { background-color: #e1f5fe; color: #0277bd; }
.state-submitted { background-color: #fce4ec; color: #c2185b; }
.state-approved { background-color: #e8f5e8; color: #388e3c; }
.state-rejected { background-color: #ffebee; color: #d32f2f; }
.state-revision { background-color: #fff3e0; color: #f57c00; }
.state-archived { background-color: #fafafa; color: #757575; }
.state-executing { background-color: #e3f2fd; color: #1976d2; }
.state-completed { background-color: #e8f5e8; color: #388e3c; }
.state-failed { background-color: #ffebee; color: #d32f2f; }
.state-cancelled { background-color: #fafafa; color: #757575; }
```

## Best Practices

### 1. Always Validate Transitions
```typescript
// ❌ Don't do this
this.currentState = newState;

// ✅ Do this
if (canTransition(this.currentState, newState)) {
  this.currentState = newState;
} else {
  throw new Error(`Invalid transition from ${this.currentState} to ${newState}`);
}
```

### 2. Use Permission Checks
```typescript
// ❌ Don't do this
if (this.currentState === 'DRAFTED') {
  // Show edit button
}

// ✅ Do this
if (canEdit(this.currentState)) {
  // Show edit button
}
```

### 3. Provide User Feedback
```typescript
// Show available actions to users
const availableActions = getPossibleTransitions(this.currentState);
this.showActionButtons(availableActions);
```

### 4. Handle Invalid States
```typescript
// Always provide a fallback
const displayName = getStateDisplayName(state) || 'Unknown State';
const cssClass = getStateCssClass(state) || 'state-default';
```

## Migration from Old State Machine

### Old vs New Enum Values

| Old Value | New Value |
|-----------|-----------|
| `IS_NEW` | `CREATED` |
| `IS_DRAFT` | `DRAFTED` |
| `IS_DIRTY` | `DIRTY` |
| `IS_READY_FOR_TEST` | `READY_FOR_TEST` |
| `HAS_SUCCESSFUL_EXECUTION` | `HAS_SUCCESSFUL_EXECUTION` |
| `IS_READY_FOR_SUBMIT` | `READY_FOR_SUBMIT` |
| `IS_SUBMITTED` | `SUBMITTED` |
| `IS_APPROVED` | `APPROVED` |
| `IS_SENT_FOR_REVISION` | `SENT_FOR_REVISION` |
| `IS_REJECTED` | `REJECTED` |
| `IS_ARCHIVED` | `ARCHIVED` |

### Update Your Code

```typescript
// ❌ Old way
import { ArtifactFlag } from '@shared/models/artifact-flags';

if (state === ArtifactFlag.IS_DRAFT) {
  // Handle draft state
}

// ✅ New way
import { ArtifactFlag } from '@shared/models/artifact-flags';

if (state === ArtifactFlag.DRAFTED) {
  // Handle draft state
}
```

## Conclusion

The new state machine provides a robust, type-safe, and comprehensive system for managing artifact lifecycles. It ensures data integrity, provides clear permission checks, and makes it easy to build user interfaces that respect business rules.

Key benefits:
- **Type Safety**: Full TypeScript support
- **Business Logic**: Enforces proper workflow rules
- **User Experience**: Clear feedback on available actions
- **Maintainability**: Centralized state transition logic
- **Extensibility**: Easy to add new states and transitions

Use the utility functions provided by the state machine to build robust, user-friendly interfaces that respect the artifact lifecycle rules.
