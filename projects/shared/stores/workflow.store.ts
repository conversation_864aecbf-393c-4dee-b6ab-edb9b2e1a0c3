import { Injectable, computed, effect, signal } from '@angular/core';
import { BehaviorSubject, Observable, tap, Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

import { Workflow, WorkflowConfig, ManagerLLM } from '../models/artifact.model';
import { WorkflowService } from '../services/workflow.service';

export interface WorkflowFilters {
  teamId?: number;
  status?: string;
  createdBy?: number;
  page?: number;
  limit?: number;
  name?: string;
}

export interface WorkflowState {
  workflows: Workflow[];
  currentWorkflow: Workflow | null;
  loading: boolean;
  error: string | null;
  filters: WorkflowFilters;
  totalCount: number;
  availableLLMModels: ManagerLLM[];
}

@Injectable({ providedIn: 'root' })
export class WorkflowStore {
  // Private state signals
  private readonly _workflows = signal<Workflow[]>([]);
  private readonly _currentWorkflow = signal<Workflow | null>(null);
  private readonly _loading = signal<boolean>(false);
  private readonly _error = signal<string | null>(null);
  private readonly _filters = signal<WorkflowFilters>({});
  private readonly _totalCount = signal<number>(0);
  private readonly _availableLLMModels = signal<ManagerLLM[]>([]);
  private readonly _isDirty = signal<boolean>(false);
  private readonly _autosaveLoading = signal<boolean>(false);

  // Public readonly signals
  public readonly workflows = this._workflows.asReadonly();
  public readonly currentWorkflow = this._currentWorkflow.asReadonly();
  public readonly loading = this._loading.asReadonly();
  public readonly error = this._error.asReadonly();
  public readonly filters = this._filters.asReadonly();
  public readonly totalCount = this._totalCount.asReadonly();
  public readonly availableLLMModels = this._availableLLMModels.asReadonly();
  public readonly isDirty = this._isDirty.asReadonly();
  public readonly autosaveLoading = this._autosaveLoading.asReadonly();

  // Computed signals
  public readonly approvedWorkflows = computed(() =>
    this._workflows().filter(workflow => workflow.status === 'APPROVED')
  );

  public readonly draftWorkflows = computed(() =>
    this._workflows().filter(workflow => workflow.status === 'DRAFTED')
  );

  public readonly reviewWorkflows = computed(() =>
    this._workflows().filter(workflow => workflow.status === 'IN_REVIEW')
  );

  public readonly hasWorkflows = computed(() => this._workflows().length > 0);
  public readonly isLoading = computed(() => this._loading());
  public readonly hasError = computed(() => this._error() !== null);
  public readonly hasLLMModels = computed(
    () => this._availableLLMModels().length > 0
  );

  // Observable streams for reactive components
  private readonly _workflowsSubject = new BehaviorSubject<Workflow[]>([]);
  public readonly workflows$ = this._workflowsSubject.asObservable();

  private readonly _currentWorkflowSubject =
    new BehaviorSubject<Workflow | null>(null);
  public readonly currentWorkflow$ =
    this._currentWorkflowSubject.asObservable();

  private readonly _loadingSubject = new BehaviorSubject<boolean>(false);
  public readonly loading$ = this._loadingSubject.asObservable();

  private readonly _errorSubject = new BehaviorSubject<string | null>(null);
  public readonly error$ = this._errorSubject.asObservable();

  private readonly _availableLLMModelsSubject = new BehaviorSubject<
    ManagerLLM[]
  >([]);
  public readonly availableLLMModels$ =
    this._availableLLMModelsSubject.asObservable();

  // Autosave functionality
  private readonly saveSubject = new Subject<{
    id: number;
    updates: Partial<Workflow>;
  }>();

  constructor(private workflowService: WorkflowService) {
    // Sync signals with subjects for backward compatibility
    effect(() => {
      this._workflowsSubject.next(this._workflows());
    });

    effect(() => {
      this._currentWorkflowSubject.next(this._currentWorkflow());
    });

    effect(() => {
      this._loadingSubject.next(this._loading());
    });

    effect(() => {
      this._errorSubject.next(this._error());
    });

    effect(() => {
      this._availableLLMModelsSubject.next(this._availableLLMModels());
    });

    // Setup autosave with debouncing
    this.setupAutosave();

    // Load available LLM models on initialization
    this.loadAvailableLLMModels();
  }

  // ==================== AUTOSAVE FUNCTIONALITY ====================

  /**
   * Setup autosave with debouncing
   */
  private setupAutosave(): void {
    this.saveSubject.pipe(debounceTime(2000)).subscribe(({ id, updates }) => {
      this.performAutosave(id, updates);
    });
  }

  /**
   * Trigger autosave for local changes
   * @param id - Workflow ID (use -1 for new workflows that haven't been created yet)
   * @param updates - Partial updates to save
   */
  onLocalChange(id: number, updates: Partial<Workflow>): void {
    this._isDirty.set(true);
    this.saveSubject.next({ id, updates });
  }

  /**
   * Perform the actual autosave operation
   */
  private performAutosave(id: number, updates: Partial<Workflow>): void {
    this.setAutosaveLoading(true);

    // Check if this is a new workflow (id === -1) or an existing workflow
    if (id === -1) {
      // This is a new workflow that needs to be created
      this.createWorkflowForAutosave(updates);
    } else {
      // This is an existing workflow that needs to be updated
      this.updateWorkflowForAutosave(id, updates);
    }
  }

  /**
   * Create a new workflow during autosave
   */
  private createWorkflowForAutosave(updates: Partial<Workflow>): void {
    // Convert partial updates to a complete workflow object for creation
    const workflowData: Omit<Workflow, 'id'> = {
      name: updates.name || 'Untitled Workflow',
      description: updates.description || 'A new workflow',
      workflowAgents: updates.workflowAgents || [],
      workflowConfig: updates.workflowConfig || {
        managerLlm: [],
        topP: 0.9,
        maxToken: 2000,
        temperature: 0.7,
        enableAgenticMemory: false,
      },
      createdBy: updates.createdBy || 1,
      modifiedBy: updates.modifiedBy || 1,
      teamId: updates.teamId || 1,
      status: updates.status || 'CREATED',
      ...updates,
    };

    this.workflowService.create(workflowData).subscribe({
      next: newWorkflow => {
        // Add to workflows list
        const currentWorkflows = this._workflows();
        this.updateWorkflows([...currentWorkflows, newWorkflow]);

        // Set as current workflow
        this.updateCurrentWorkflow(newWorkflow);

        // Clear dirty state
        this._isDirty.set(false);
        this.setAutosaveLoading(false);

        console.log('Workflow created via autosave:', newWorkflow);
      },
      error: error => {
        console.error('Autosave create failed for workflow:', error);
        // Keep dirty state for retry - user can manually save
        this.setAutosaveLoading(false);
      },
    });
  }

  /**
   * Update an existing workflow during autosave
   */
  private updateWorkflowForAutosave(
    id: number,
    updates: Partial<Workflow>
  ): void {
    this.workflowService.update(id, updates).subscribe({
      next: updatedWorkflow => {
        // Update in workflows list
        const currentWorkflows = this._workflows();
        const updatedWorkflows = currentWorkflows.map(workflow =>
          workflow.id === id ? updatedWorkflow : workflow
        );
        this.updateWorkflows(updatedWorkflows);

        // Update current workflow if it's the one being updated
        const currentWorkflow = this._currentWorkflow();
        if (currentWorkflow && currentWorkflow.id === id) {
          this.updateCurrentWorkflow(updatedWorkflow);
        }

        // Clear dirty state
        this._isDirty.set(false);
        this.setAutosaveLoading(false);
      },
      error: error => {
        console.error('Autosave update failed for workflow:', error);
        // Keep dirty state for retry - user can manually save
        this.setAutosaveLoading(false);
      },
    });
  }

  // ==================== STATE MANAGEMENT ====================

  /**
   * Set loading state
   */
  private setLoading(loading: boolean): void {
    this._loading.set(loading);
  }

  /**
   * Set autosave loading state
   */
  private setAutosaveLoading(loading: boolean): void {
    this._autosaveLoading.set(loading);
  }

  /**
   * Set error state
   */
  private setError(error: string | null): void {
    this._error.set(error);
  }

  /**
   * Clear error state
   */
  private clearError(): void {
    this._error.set(null);
  }

  /**
   * Update workflows list
   */
  private updateWorkflows(workflows: Workflow[]): void {
    this._workflows.set(workflows);
  }

  /**
   * Update current workflow
   */
  private updateCurrentWorkflow(workflow: Workflow | null): void {
    this._currentWorkflow.set(workflow);
  }

  /**
   * Update filters
   */
  private updateFilters(filters: WorkflowFilters): void {
    this._filters.set(filters);
  }

  /**
   * Update total count
   */
  private updateTotalCount(count: number): void {
    this._totalCount.set(count);
  }

  /**
   * Update available LLM models
   */
  private updateAvailableLLMModels(models: ManagerLLM[]): void {
    this._availableLLMModels.set(models);
  }

  // ==================== CRUD OPERATIONS ====================

  /**
   * Load all workflows with optional filtering
   */
  loadWorkflows(filters?: WorkflowFilters): Observable<Workflow[]> {
    this.setLoading(true);
    this.clearError();

    if (filters) {
      this.updateFilters(filters);
    }

    return this.workflowService.getAll(filters || this._filters()).pipe(
      tap({
        next: workflows => {
          this.updateWorkflows(workflows);
          this.updateTotalCount(workflows.length);
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to load workflows');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Load workflow by ID
   */
  loadWorkflowById(id: number): Observable<Workflow> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.getById(id).pipe(
      tap({
        next: workflow => {
          this.updateCurrentWorkflow(workflow);
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to load workflow');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Create new workflow
   */
  createWorkflow(workflowData: Omit<Workflow, 'id'>): Observable<Workflow> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.create(workflowData).pipe(
      tap({
        next: newWorkflow => {
          // Add to workflows list
          const currentWorkflows = this._workflows();
          this.updateWorkflows([...currentWorkflows, newWorkflow]);

          // Set as current workflow
          this.updateCurrentWorkflow(newWorkflow);

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to create workflow');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Create a new workflow with temporary ID for autosave
   * This creates a workflow in memory with id: -1 for autosave purposes
   */
  createNewWorkflowForEditing(workflowData: Partial<Workflow> = {}): Workflow {
    const newWorkflow: Workflow = {
      id: -1, // Temporary ID for new workflows
      name: workflowData.name || 'Untitled Workflow',
      description: workflowData.description || 'A new workflow',
      workflowAgents: workflowData.workflowAgents || [],
      workflowConfig: workflowData.workflowConfig || {
        managerLlm: [],
        topP: 0.9,
        maxToken: 2000,
        temperature: 0.7,
        enableAgenticMemory: false,
      },
      createdBy: workflowData.createdBy || 1,
      modifiedBy: workflowData.modifiedBy || 1,
      teamId: workflowData.teamId || 1,
      status: workflowData.status || 'CREATED',
      ...workflowData,
    };

    // Set as current workflow for editing
    this.updateCurrentWorkflow(newWorkflow);

    return newWorkflow;
  }

  /**
   * Update existing workflow
   */
  updateWorkflow(id: number, updates: Partial<Workflow>): Observable<Workflow> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.update(id, updates).pipe(
      tap({
        next: updatedWorkflow => {
          // Update in workflows list
          const currentWorkflows = this._workflows();
          const updatedWorkflows = currentWorkflows.map(workflow =>
            workflow.id === id ? updatedWorkflow : workflow
          );
          this.updateWorkflows(updatedWorkflows);

          // Update current workflow if it's the one being updated
          const currentWorkflow = this._currentWorkflow();
          if (currentWorkflow && currentWorkflow.id === id) {
            this.updateCurrentWorkflow(updatedWorkflow);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to update workflow');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Delete workflow
   */
  deleteWorkflow(id: number): Observable<void> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.delete(id).pipe(
      tap({
        next: () => {
          // Remove from workflows list
          const currentWorkflows = this._workflows();
          const filteredWorkflows = currentWorkflows.filter(
            workflow => workflow.id !== id
          );
          this.updateWorkflows(filteredWorkflows);

          // Clear current workflow if it's the one being deleted
          const currentWorkflow = this._currentWorkflow();
          if (currentWorkflow && currentWorkflow.id === id) {
            this.updateCurrentWorkflow(null);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to delete workflow');
          this.setLoading(false);
        },
      })
    );
  }

  // ==================== LIFECYCLE OPERATIONS ====================

  /**
   * Submit workflow for review
   */
  submitForReview(id: number): Observable<Workflow> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.submitForReview(id).pipe(
      tap({
        next: updatedWorkflow => {
          // Update in workflows list
          const currentWorkflows = this._workflows();
          const updatedWorkflows = currentWorkflows.map(workflow =>
            workflow.id === id ? updatedWorkflow : workflow
          );
          this.updateWorkflows(updatedWorkflows);

          // Update current workflow if it's the one being updated
          const currentWorkflow = this._currentWorkflow();
          if (currentWorkflow && currentWorkflow.id === id) {
            this.updateCurrentWorkflow(updatedWorkflow);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(
            error.message || 'Failed to submit workflow for review'
          );
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Approve workflow
   */
  approveWorkflow(id: number): Observable<Workflow> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.approve(id).pipe(
      tap({
        next: updatedWorkflow => {
          // Update in workflows list
          const currentWorkflows = this._workflows();
          const updatedWorkflows = currentWorkflows.map(workflow =>
            workflow.id === id ? updatedWorkflow : workflow
          );
          this.updateWorkflows(updatedWorkflows);

          // Update current workflow if it's the one being updated
          const currentWorkflow = this._currentWorkflow();
          if (currentWorkflow && currentWorkflow.id === id) {
            this.updateCurrentWorkflow(updatedWorkflow);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to approve workflow');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Reject workflow
   */
  rejectWorkflow(id: number): Observable<Workflow> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.reject(id).pipe(
      tap({
        next: updatedWorkflow => {
          // Update in workflows list
          const currentWorkflows = this._workflows();
          const updatedWorkflows = currentWorkflows.map(workflow =>
            workflow.id === id ? updatedWorkflow : workflow
          );
          this.updateWorkflows(updatedWorkflows);

          // Update current workflow if it's the one being updated
          const currentWorkflow = this._currentWorkflow();
          if (currentWorkflow && currentWorkflow.id === id) {
            this.updateCurrentWorkflow(updatedWorkflow);
          }

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to reject workflow');
          this.setLoading(false);
        },
      })
    );
  }

  // ==================== EXECUTION OPERATIONS ====================

  /**
   * Test workflow execution
   */
  testWorkflow(id: number, userInputs?: any): Observable<any> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.test(id, userInputs).pipe(
      tap({
        next: () => {
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Workflow test failed');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Execute workflow
   */
  executeWorkflow(id: number, inputs?: any): Observable<any> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.execute(id, inputs).pipe(
      tap({
        next: () => {
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Workflow execution failed');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Run workflow in playground
   */
  playgroundRun(id: number, inputs?: any): Observable<any> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.playgroundRun(id, inputs).pipe(
      tap({
        next: () => {
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Playground execution failed');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Validate workflow configuration
   */
  validateWorkflow(id: number): Observable<any> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.validate(id).pipe(
      tap({
        next: () => {
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Workflow validation failed');
          this.setLoading(false);
        },
      })
    );
  }

  // ==================== EXECUTION MONITORING ====================

  /**
   * Get workflow execution status
   */
  getExecutionStatus(executionId: string): Observable<any> {
    return this.workflowService.getExecutionStatus(executionId);
  }

  /**
   * Get workflow execution logs
   */
  getExecutionLogs(executionId: string): Observable<any[]> {
    return this.workflowService.getExecutionLogs(executionId);
  }

  /**
   * Cancel workflow execution
   */
  cancelExecution(executionId: string): Observable<any> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.cancelExecution(executionId).pipe(
      tap({
        next: () => {
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to cancel execution');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Get workflow execution history
   */
  getExecutionHistory(
    id: number,
    params?: {
      page?: number;
      limit?: number;
      startDate?: string;
      endDate?: string;
      status?: string;
    }
  ): Observable<any[]> {
    return this.workflowService.getExecutionHistory(id, params);
  }

  // ==================== CONFIGURATION OPERATIONS ====================

  /**
   * Load available LLM models for workflow configuration
   */
  loadAvailableLLMModels(): Observable<ManagerLLM[]> {
    return this.workflowService.getAvailableLLMModels().pipe(
      tap({
        next: models => {
          this.updateAvailableLLMModels(models);
        },
        error: error => {
          console.error('Failed to load LLM models:', error);
        },
      })
    );
  }

  /**
   * Get workflow configuration template
   */
  getConfigTemplate(): Partial<WorkflowConfig> {
    return this.workflowService.getConfigTemplate();
  }

  /**
   * Get workflow dependencies
   */
  getDependencies(id: number): Observable<any> {
    return this.workflowService.getDependencies(id);
  }

  /**
   * Validate workflow dependencies
   */
  validateDependencies(id: number): Observable<any> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.validateDependencies(id).pipe(
      tap({
        next: () => {
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Dependency validation failed');
          this.setLoading(false);
        },
      })
    );
  }

  // ==================== UTILITY OPERATIONS ====================

  /**
   * Clone workflow
   */
  cloneWorkflow(id: number, newName: string): Observable<Workflow> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.clone(id, newName).pipe(
      tap({
        next: clonedWorkflow => {
          // Add to workflows list
          const currentWorkflows = this._workflows();
          this.updateWorkflows([...currentWorkflows, clonedWorkflow]);

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to clone workflow');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Export workflow configuration
   */
  exportWorkflow(id: number): Observable<any> {
    return this.workflowService.export(id);
  }

  /**
   * Import workflow configuration
   */
  importWorkflow(config: any): Observable<Workflow> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.import(config).pipe(
      tap({
        next: importedWorkflow => {
          // Add to workflows list
          const currentWorkflows = this._workflows();
          this.updateWorkflows([...currentWorkflows, importedWorkflow]);

          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Failed to import workflow');
          this.setLoading(false);
        },
      })
    );
  }

  /**
   * Get workflow statistics
   */
  getWorkflowStats(id: number): Observable<any> {
    return this.workflowService.getStats(id);
  }

  /**
   * Search workflows by name or description
   */
  searchWorkflows(
    query: string,
    params?: {
      teamId?: number;
      status?: string;
      page?: number;
      limit?: number;
    }
  ): Observable<Workflow[]> {
    this.setLoading(true);
    this.clearError();

    return this.workflowService.search(query, params).pipe(
      tap({
        next: workflows => {
          this.updateWorkflows(workflows);
          this.updateTotalCount(workflows.length);
          this.setLoading(false);
        },
        error: error => {
          this.setError(error.message || 'Workflow search failed');
          this.setLoading(false);
        },
      })
    );
  }

  // ==================== STATE RESET ====================

  /**
   * Reset store state
   */
  reset(): void {
    this._workflows.set([]);
    this._currentWorkflow.set(null);
    this._loading.set(false);
    this._autosaveLoading.set(false);
    this._error.set(null);
    this._filters.set({});
    this._totalCount.set(0);
    this._isDirty.set(false);
    // Don't reset LLM models as they're loaded on initialization
  }

  /**
   * Clear current workflow
   */
  clearCurrentWorkflow(): void {
    this._currentWorkflow.set(null);
  }

  /**
   * Clear error
   */
  clearErrorState(): void {
    this._error.set(null);
  }

  /**
   * Clear dirty state
   */
  clearDirtyState(): void {
    this._isDirty.set(false);
  }
}
