# Store Usage Examples

This document provides comprehensive examples of how to use the new specialized stores for managing agent, tool, and workflow state in your Angular components.

## Table of Contents
1. [AgentStore Usage](#agentstore-usage)
2. [ToolStore Usage](#toolstore-usage)
3. [WorkflowStore Usage](#workflowstore-usage)
4. [Best Practices](#best-practices)
5. [Migration Guide](#migration-guide)

## AgentStore Usage

### Basic Component Setup

```typescript
import { Component, OnInit, OnDestroy } from '@angular/core';
import { AgentStore, AgentFilters } from '@shared/stores/agent.store';
import { Agent } from '@shared/models/artifact.model';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-agent-manager',
  template: `
    <div class="agent-manager">
      <!-- Loading State -->
      <div *ngIf="agentStore.loading()" class="loading">
        Loading agents...
      </div>

      <!-- Error State -->
      <div *ngIf="agentStore.error()" class="error">
        {{ agentStore.error() }}
      </div>

      <!-- Agents List -->
      <div *ngIf="agentStore.hasAgents()" class="agents-list">
        <div *ngFor="let agent of agentStore.agents()" class="agent-item">
          <h3>{{ agent.name }}</h3>
          <p>{{ agent.description }}</p>
          <span class="status">{{ agent.status }}</span>
          
          <button (click)="editAgent(agent.id!)">Edit</button>
          <button (click)="deleteAgent(agent.id!)">Delete</button>
        </div>
      </div>

      <!-- Current Agent Details -->
      <div *ngIf="agentStore.currentAgent()" class="current-agent">
        <h2>Current Agent: {{ agentStore.currentAgent()?.name }}</h2>
        <p>Role: {{ agentStore.currentAgent()?.role }}</p>
        <p>Goal: {{ agentStore.currentAgent()?.goal }}</p>
        
        <button (click)="submitForReview(agentStore.currentAgent()?.id!)">
          Submit for Review
        </button>
      </div>

      <!-- Create New Agent -->
      <button (click)="createNewAgent()">Create New Agent</button>
    </div>
  `
})
export class AgentManagerComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  constructor(public agentStore: AgentStore) {}

  ngOnInit(): void {
    // Load agents on component initialization
    this.loadAgents();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Load all agents with filtering
  loadAgents(): void {
    const filters: AgentFilters = {
      teamId: 23,
      status: 'APPROVED',
      page: 1,
      limit: 20
    };

    this.agentStore.loadAgents(filters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (agents) => {
          console.log(`Loaded ${agents.length} agents`);
        },
        error: (error) => {
          console.error('Failed to load agents:', error);
        }
      });
  }

  // Load specific agent by ID
  loadAgentById(id: number): void {
    this.agentStore.loadAgentById(id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (agent) => {
          console.log('Loaded agent:', agent);
        },
        error: (error) => {
          console.error('Failed to load agent:', error);
        }
      });
  }

  // Create new agent
  createNewAgent(): void {
    const agentData = {
      agentDetails: 'Python_Data_Analyst',
      name: 'Data Analysis Agent',
      role: 'Python Developer',
      goal: 'Analyze data and provide insights using Python',
      backstory: 'You are an experienced data analyst with strong Python skills.',
      description: 'A specialized agent for data analysis tasks',
      expectedOutput: 'Comprehensive data analysis reports with insights',
      userTools: [{ toolId: 1 }, { toolId: 2 }],
      kbIds: [2, 3],
      modelId: 2,
      agentConfigs: {
        temperature: 0.3,
        topP: 0.95,
        maxToken: '4000',
        levelId: 4,
        maxRpm: 5,
        maxExecutionTime: 5,
        allowDelegation: true,
        allowCodeExecution: true,
        isSafeCodeExecution: true,
        toolRef: [3, 5]
      },
      teamId: 23,
      status: 'CREATED' as const
    };

    this.agentStore.createAgent(agentData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (agent) => {
          console.log('Agent created successfully:', agent);
        },
        error: (error) => {
          console.error('Failed to create agent:', error);
        }
      });
  }

  // Edit existing agent
  editAgent(id: number): void {
    const updates = {
      name: 'Updated Data Analysis Agent',
      description: 'Updated description for the agent'
    };

    this.agentStore.updateAgent(id, updates)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedAgent) => {
          console.log('Agent updated successfully:', updatedAgent);
        },
        error: (error) => {
          console.error('Failed to update agent:', error);
        }
      });
  }

  // Delete agent
  deleteAgent(id: number): void {
    if (confirm('Are you sure you want to delete this agent?')) {
      this.agentStore.deleteAgent(id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            console.log('Agent deleted successfully');
          },
          error: (error) => {
            console.error('Failed to delete agent:', error);
          }
        });
    }
  }

  // Submit agent for review
  submitForReview(id: number): void {
    this.agentStore.submitForReview(id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (agent) => {
          console.log('Agent submitted for review:', agent);
        },
        error: (error) => {
          console.error('Failed to submit agent for review:', error);
        }
      });
  }

  // Test agent execution
  testAgent(id: number): void {
    const userInputs = { input: 'test data for analysis' };
    
    this.agentStore.testAgent(id, userInputs)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => {
          console.log('Agent test result:', result);
        },
        error: (error) => {
          console.error('Agent test failed:', error);
        }
      });
  }

  // Run agent in playground
  runInPlayground(id: number): void {
    const inputs = { input: 'playground test data' };
    
    this.agentStore.playgroundRun(id, inputs)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => {
          console.log('Playground execution result:', result);
        },
        error: (error) => {
          console.error('Playground execution failed:', error);
        }
      });
  }
}
```

### Using Computed Signals

```typescript
@Component({
  selector: 'app-agent-dashboard',
  template: `
    <div class="dashboard">
      <!-- Status Summary -->
      <div class="status-summary">
        <div class="status-card">
          <h3>Approved Agents</h3>
          <span class="count">{{ agentStore.approvedAgents().length }}</span>
        </div>
        
        <div class="status-card">
          <h3>Draft Agents</h3>
          <span class="count">{{ agentStore.draftAgents().length }}</span>
        </div>
        
        <div class="status-card">
          <h3>In Review</h3>
          <span class="count">{{ agentStore.reviewAgents().length }}</span>
        </div>
      </div>

      <!-- Loading and Error States -->
      <div *ngIf="agentStore.isLoading()" class="loading-overlay">
        <mat-spinner></mat-spinner>
      </div>

      <div *ngIf="agentStore.hasError()" class="error-banner">
        {{ agentStore.error() }}
        <button (click)="agentStore.clearErrorState()">Dismiss</button>
      </div>
    </div>
  `
})
export class AgentDashboardComponent {
  constructor(public agentStore: AgentStore) {}
}
```

## ToolStore Usage

### Basic Component Setup

```typescript
import { Component, OnInit, OnDestroy } from '@angular/core';
import { ToolStore, ToolFilters } from '@shared/stores/tool.store';
import { UserTool } from '@shared/models/artifact.model';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-tool-manager',
  template: `
    <div class="tool-manager">
      <!-- Tool Categories -->
      <div class="categories" *ngIf="toolStore.hasCategories()">
        <h3>Tool Categories</h3>
        <div class="category-list">
          <button 
            *ngFor="let category of toolStore.categories()"
            (click)="loadToolsByCategory(category)"
            class="category-btn">
            {{ category }}
          </button>
        </div>
      </div>

      <!-- Search Tools -->
      <div class="search-section">
        <input 
          #searchInput
          type="text" 
          placeholder="Search tools..."
          (input)="searchTools(searchInput.value)">
      </div>

      <!-- Tools List -->
      <div *ngIf="toolStore.hasTools()" class="tools-list">
        <div *ngFor="let tool of toolStore.tools()" class="tool-item">
          <h3>{{ tool.toolName }}</h3>
          <p>{{ tool.toolDescription }}</p>
          <span class="status">{{ tool.status }}</span>
          <span class="runtime">{{ tool.toolConfig.runtime }}</span>
          
          <button (click)="editTool(tool.id!)">Edit</button>
          <button (click)="testTool(tool.id!)">Test</button>
        </div>
      </div>

      <!-- Current Tool Details -->
      <div *ngIf="toolStore.currentTool()" class="current-tool">
        <h2>Current Tool: {{ toolStore.currentTool()?.toolName }}</h2>
        <p>Description: {{ toolStore.currentTool()?.toolDescription }}</p>
        <p>Runtime: {{ toolStore.currentTool()?.toolConfig.runtime }}</p>
        <p>Libraries: {{ toolStore.currentTool()?.toolConfig.libraries.join(', ') }}</p>
      </div>
    </div>
  `
})
export class ToolManagerComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  constructor(public toolStore: ToolStore) {}

  ngOnInit(): void {
    this.loadTools();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Load all tools
  loadTools(): void {
    const filters: ToolFilters = {
      teamId: 23,
      status: 'APPROVED'
    };

    this.toolStore.loadTools(filters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (tools) => {
          console.log(`Loaded ${tools.length} tools`);
        },
        error: (error) => {
          console.error('Failed to load tools:', error);
        }
      });
  }

  // Load tools by category
  loadToolsByCategory(category: string): void {
    this.toolStore.getToolsByCategory(category)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (tools) => {
          console.log(`Loaded ${tools.length} tools in category: ${category}`);
        },
        error: (error) => {
          console.error('Failed to load tools by category:', error);
        }
      });
  }

  // Search tools
  searchTools(query: string): void {
    if (query.trim().length > 0) {
      this.toolStore.searchTools(query, { teamId: 23 })
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (tools) => {
            console.log(`Found ${tools.length} tools matching: ${query}`);
          },
          error: (error) => {
            console.error('Tool search failed:', error);
          }
        });
    } else {
      // If search is empty, load all tools
      this.loadTools();
    }
  }

  // Create new tool
  createTool(): void {
    const toolData = {
      toolName: 'Advanced Data Analyzer',
      toolDescription: 'Advanced data analysis tool with visualization capabilities',
      toolConfig: {
        runtime: 'Python 3.10',
        libraries: ['pandas', 'numpy', 'matplotlib'],
        maxRows: 10000
      },
      version: 1,
      status: 'CREATED' as const,
      teamId: 23
    };

    this.toolStore.createTool(toolData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (tool) => {
          console.log('Tool created successfully:', tool);
        },
        error: (error) => {
          console.error('Failed to create tool:', error);
        }
      });
  }

  // Edit tool
  editTool(id: number): void {
    const updates = {
      toolName: 'Updated Tool Name',
      toolDescription: 'Updated tool description'
    };

    this.toolStore.updateTool(id, updates)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedTool) => {
          console.log('Tool updated successfully:', updatedTool);
        },
        error: (error) => {
          console.error('Failed to update tool:', error);
        }
      });
  }

  // Test tool
  testTool(id: number): void {
    const userInputs = { input: 'test data for tool execution' };
    
    this.toolStore.testTool(id, userInputs)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => {
          console.log('Tool test result:', result);
        },
        error: (error) => {
          console.error('Tool test failed:', error);
        }
      });
  }
}
```

## WorkflowStore Usage

### Basic Component Setup

```typescript
import { Component, OnInit, OnDestroy } from '@angular/core';
import { WorkflowStore, WorkflowFilters } from '@shared/stores/workflow.store';
import { Workflow, WorkflowConfig, WorkflowAgent, ManagerLLM } from '@shared/models/artifact.model';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-workflow-manager',
  template: `
    <div class="workflow-manager">
      <!-- Workflows List -->
      <div *ngIf="workflowStore.hasWorkflows()" class="workflows-list">
        <div *ngFor="let workflow of workflowStore.workflows()" class="workflow-item">
          <h3>{{ workflow.name }}</h3>
          <p>{{ workflow.description }}</p>
          <span class="status">{{ workflow.status }}</span>
          <span class="agent-count">{{ workflow.workflowAgents.length }} agents</span>
          
          <button (click)="editWorkflow(workflow.id!)">Edit</button>
          <button (click)="executeWorkflow(workflow.id!)">Execute</button>
          <button (click)="viewExecutionHistory(workflow.id!)">History</button>
        </div>
      </div>

      <!-- Current Workflow Details -->
      <div *ngIf="workflowStore.currentWorkflow()" class="current-workflow">
        <h2>Current Workflow: {{ workflowStore.currentWorkflow()?.name }}</h2>
        <p>Description: {{ workflowStore.currentWorkflow()?.description }}</p>
        <p>Status: {{ workflowStore.currentWorkflow()?.status }}</p>
        
        <h3>Workflow Agents</h3>
        <div *ngFor="let agent of workflowStore.currentWorkflow()?.workflowAgents" class="agent-info">
          <span>Serial: {{ agent.serial }}</span>
          <span>Agent ID: {{ agent.agentId }}</span>
        </div>
      </div>

      <!-- LLM Models -->
      <div *ngIf="workflowStore.hasLLMModels()" class="llm-models">
        <h3>Available LLM Models</h3>
        <div *ngFor="let model of workflowStore.availableLLMModels()" class="model-item">
          <span>{{ model.modelDeploymentName }}</span>
          <span>Temperature: {{ model.temperature }}</span>
          <span>Max Tokens: {{ model.maxToken }}</span>
        </div>
      </div>
    </div>
  `
})
export class WorkflowManagerComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  constructor(public workflowStore: WorkflowStore) {}

  ngOnInit(): void {
    this.loadWorkflows();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Load all workflows
  loadWorkflows(): void {
    const filters: WorkflowFilters = {
      teamId: 23,
      status: 'APPROVED'
    };

    this.workflowStore.loadWorkflows(filters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (workflows) => {
          console.log(`Loaded ${workflows.length} workflows`);
        },
        error: (error) => {
          console.error('Failed to load workflows:', error);
        }
      });
  }

  // Create new workflow
  createWorkflow(): void {
    const managerLLM: ManagerLLM = {
      id: 40,
      modelDeploymentName: 'correct-anthropic.claude-3-sonnet-2',
      topP: 0.95,
      maxToken: 1500,
      temperature: 0.3
    };

    const workflowConfig: WorkflowConfig = {
      managerLlm: [managerLLM],
      topP: 0.95,
      maxToken: 1500,
      temperature: 0.3,
      enableAgenticMemory: false
    };

    const workflowAgents: WorkflowAgent[] = [
      { serial: 1, agentId: 24 },
      { serial: 2, agentId: 784 }
    ];

    const workflowData = {
      name: 'Data Analysis Workflow',
      description: 'End-to-end data analysis workflow using multiple agents',
      workflowAgents: workflowAgents,
      workflowConfig: workflowConfig,
      createdBy: 101,
      modifiedBy: 101,
      teamId: 23,
      status: 'CREATED' as const
    };

    this.workflowStore.createWorkflow(workflowData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (workflow) => {
          console.log('Workflow created successfully:', workflow);
        },
        error: (error) => {
          console.error('Failed to create workflow:', error);
        }
      });
  }

  // Execute workflow
  executeWorkflow(id: number): void {
    const inputs = { input: 'workflow execution data' };
    
    this.workflowStore.executeWorkflow(id, inputs)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => {
          console.log('Workflow execution started:', result);
        },
        error: (error) => {
          console.error('Workflow execution failed:', error);
        }
      });
  }

  // View execution history
  viewExecutionHistory(id: number): void {
    this.workflowStore.getExecutionHistory(id, { page: 1, limit: 10 })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (history) => {
          console.log('Execution history:', history);
        },
        error: (error) => {
          console.error('Failed to get execution history:', error);
        }
      });
  }

  // Validate workflow dependencies
  validateDependencies(id: number): void {
    this.workflowStore.validateDependencies(id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => {
          console.log('Dependency validation result:', result);
        },
        error: (error) => {
          console.error('Dependency validation failed:', error);
        }
      });
  }
}
```

## Best Practices

### 1. Component Lifecycle Management

```typescript
export class MyComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  constructor(public agentStore: AgentStore) {}

  ngOnInit(): void {
    // Load data on initialization
    this.loadData();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadData(): void {
    this.agentStore.loadAgents()
      .pipe(takeUntil(this.destroy$))
      .subscribe();
  }
}
```

### 2. Error Handling

```typescript
// Always handle errors in your subscriptions
this.agentStore.createAgent(agentData)
  .pipe(takeUntil(this.destroy$))
  .subscribe({
    next: (agent) => {
      // Success handling
      this.showSuccessMessage('Agent created successfully');
    },
    error: (error) => {
      // Error handling
      this.showErrorMessage(error.message || 'Failed to create agent');
    }
  });
```

### 3. Loading States

```typescript
// Use loading states for better UX
<button 
  [disabled]="agentStore.loading()"
  (click)="createAgent()">
  <span *ngIf="agentStore.loading()">Creating...</span>
  <span *ngIf="!agentStore.loading()">Create Agent</span>
</button>
```

### 4. Computed Signals

```typescript
// Use computed signals for derived state
public readonly hasApprovedAgents = computed(() => 
  this.agentStore.approvedAgents().length > 0
);

public readonly totalAgentCount = computed(() => 
  this.agentStore.agents().length
);
```

### 5. State Reset

```typescript
// Reset store state when needed
ngOnDestroy(): void {
  this.agentStore.reset();
  this.destroy$.next();
  this.destroy$.complete();
}
```

### 6. Filtering and Pagination

```typescript
// Use filters for better performance
loadAgentsWithPagination(page: number, limit: number): void {
  const filters: AgentFilters = {
    teamId: 23,
    status: 'APPROVED',
    page,
    limit
  };

  this.agentStore.loadAgents(filters)
    .pipe(takeUntil(this.destroy$))
    .subscribe();
}
```

### 7. Autosave Functionality

```typescript
// Use autosave for better user experience
export class AgentEditorComponent {
  constructor(public agentStore: AgentStore) {}

  onFieldChange(field: keyof Agent, value: any): void {
    // Trigger autosave with debouncing
    this.agentStore.onLocalChange(this.agent.id!, { [field]: value });
  }

  // Check if there are unsaved changes
  get hasUnsavedChanges(): boolean {
    return this.agentStore.isDirty();
  }
}
```

### 8. Reactive State Management

```typescript
// Use reactive state for automatic UI updates
export class AgentListComponent {
  constructor(public agentStore: AgentStore) {}

  // Reactive data binding
  agents$ = this.agentStore.agents$;
  loading$ = this.agentStore.loading$;
  error$ = this.agentStore.error$;
  totalCount$ = this.agentStore.totalCount$;

  // Computed reactive values
  approvedAgents$ = this.agentStore.approvedAgents$;
  hasAgents$ = this.agentStore.hasAgents$;
}
```

## Migration Guide

### From Generic Services to Specialized Stores

```typescript
// Old way (generic artifact service)
this.artifactService.get('123');
this.artifactService.create({ type: 'agent', metadata: agentData });

// New way (specialized stores)
this.agentStore.getById(123);
this.agentStore.createAgent(agentData);
```

### Benefits of New Stores

1. **Type Safety**: Full TypeScript support with proper interfaces
2. **Performance**: Optimized state management with Angular signals
3. **Maintainability**: Clear separation of concerns
4. **Reactivity**: Automatic UI updates when state changes
5. **Error Handling**: Centralized error management
6. **Computed Values**: Efficient derived state calculations
7. **Autosave**: Built-in autosave functionality with dirty state tracking
8. **Loading States**: Separate loading states for user operations vs autosave

### Migration Checklist

- [ ] Replace generic service calls with specialized store methods
- [ ] Update component constructors to inject appropriate stores
- [ ] Replace manual state management with store state
- [ ] Update templates to use store signals and computed values
- [ ] Implement proper error handling using store error states
- [ ] Add loading states for better user experience
- [ ] Implement autosave functionality where appropriate
- [ ] Update unit tests to mock stores instead of services
- [ ] Remove unused service imports and dependencies

## Conclusion

The new specialized stores provide a modern, type-safe, and performant way to manage artifact state in your Angular applications. They offer better developer experience, improved performance, and clearer architecture compared to generic services.

Key benefits include:
- **Type Safety**: Full TypeScript support with proper interfaces
- **State Management**: Centralized state management with Angular signals
- **Performance**: Optimized state updates and caching
- **User Experience**: Built-in loading states, error handling, and autosave
- **Maintainability**: Clear separation of concerns and single responsibility
- **Reactivity**: Automatic UI updates when state changes

Start using these stores today to build more robust, maintainable, and user-friendly applications.
