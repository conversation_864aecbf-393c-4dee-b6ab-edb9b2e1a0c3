import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../environments/environment';

export interface GoodAtTag {
  id: number;
  name: string;
}

export interface GoodAtTagsResponse {
  message: string;
  tagList: GoodAtTag[];
}

@Injectable({
  providedIn: 'root',
})
export class GoodAtTagsService {
  private readonly apiUrl = `${environment.consoleApiAuthUrl}/goodAt-tags`;

  constructor(private http: HttpClient) {}

  /**
   * Get all good at tags
   */
  getGoodAtTags(): Observable<GoodAtTag[]> {
    return this.http.get<GoodAtTagsResponse>(this.apiUrl).pipe(
      map(response => response.tagList),
      catchError(error => {
        console.error('Error fetching good at tags:', error);
        throw error;
      })
    );
  }

  /**
   * Get good at tag by ID
   */
  getGoodAtTagById(id: number): Observable<GoodAtTag | undefined> {
    return this.getGoodAtTags().pipe(
      map(tags => tags.find(tag => tag.id === id))
    );
  }

  /**
   * Get good at tags by IDs
   */
  getGoodAtTagsByIds(ids: number[]): Observable<GoodAtTag[]> {
    return this.getGoodAtTags().pipe(
      map(tags => tags.filter(tag => ids.includes(tag.id)))
    );
  }
}
