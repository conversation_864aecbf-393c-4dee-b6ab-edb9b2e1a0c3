import { Injectable } from '@angular/core';
import {
  AavaToastService,
  ToastResult,
  SuccessToastConfig,
  ErrorToastConfig,
  WarningToastConfig,
  InfoToastConfig
} from '@aava/play-core';

@Injectable({ providedIn: 'root' })
export class ToastWrapperService {
  private defaultConfig = {
    duration: 3000,
    customWidth: '350px',
    design: 'modern',
    size: 'medium',
    showCloseButton: false,
  };

  constructor(private toast: AavaToastService) { }

  success(
    message: string = 'Operation successful',
    title: string = 'Success'
  ): Promise<ToastResult> {
    return this.toast.success({ ...this.defaultConfig, message, title } as Partial<SuccessToastConfig>);
  }

  error(
    message: string = 'Something went wrong',
    title: string = 'Error'
  ): Promise<ToastResult> {
    return this.toast.error({ ...this.defaultConfig, message, title } as Partial<ErrorToastConfig>);
  }

  warning(
    message: string = 'Warning Message',
    title: string = 'Warning'
  ): Promise<ToastResult> {
    return this.toast.warning({ ...this.defaultConfig, message, title } as Partial<WarningToastConfig>);
  }

  info(
    message: string = 'Here is some information',
    title: string = 'Info'
  ): Promise<ToastResult> {
    return this.toast.info({ ...this.defaultConfig, message, title } as Partial<InfoToastConfig>);
  }
}
