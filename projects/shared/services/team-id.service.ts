import { Injectable } from '@angular/core';
import { TokenStorageService } from '@shared';

@Injectable({
  providedIn: 'root',
})
export class TeamIdService {
  /**
   * Extract team ID from org_path cookie
   * @param orgPath - The org_path cookie value (e.g., "ascendion@core@genai@aava::201@202@203@204")
   * @returns The team ID (last number after ::)
   */
  extractTeamId(orgPath: string): number | null {
    try {
      // Split by :: and get the last part
      const parts = orgPath.split('::');
      if (parts.length < 2) {
        console.warn('Invalid org_path format:', orgPath);
        return null;
      }

      const numbersPart = parts[parts.length - 1];
      const numbers = numbersPart.split('@');

      if (numbers.length === 0) {
        console.warn('No numbers found in org_path:', orgPath);
        return null;
      }

      // Get the last number
      const lastNumber = numbers[numbers.length - 1];
      const teamId = parseInt(lastNumber, 10);

      if (isNaN(teamId)) {
        console.warn('Invalid team ID:', lastNumber);
        return null;
      }

      return teamId;
    } catch (error) {
      console.error('Error extracting team ID from org_path:', error);
      return null;
    }
  }

  /**
   * Get team ID from cookies
   * @returns The team ID or null if not found
   */
  getTeamIdFromCookies(): number | null {
    const orgPath = this.tokenStorage.getCookie('org_path');
    if (!orgPath) {
      console.warn('org_path cookie not found');
      return null;
    }

    return this.extractTeamId(orgPath);
  }

  constructor(private readonly tokenStorage: TokenStorageService) {}
}
