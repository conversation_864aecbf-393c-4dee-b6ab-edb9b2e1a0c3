import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { EnvironmentService } from './environment.service';

export interface ArtifactResponse {
  totalItems: number;
  pageNumber: number;
  totalPages: number;
  pageSize: number;
  items: Artifact[];
}

export interface Artifact {
  id: string;
  name: string;
  shortDescription: string;
  entityType: string; // Keeping for backward compatibility
  type: string; // New field that replaces entityType
  realm: string | null;
  createdBy: string;
  createdAt: string;
  status: string;
  views: number;
  executions: number;
  efficiencyRating: number;
  tags: string[];
  practiceArea: string;
}

@Injectable({
  providedIn: 'root',
})
export class MarketplaceService {
  private apiUrl: string;

  constructor(
    private http: HttpClient,
    private environmentService: EnvironmentService
  ) {
    this.apiUrl = `${this.environmentService.baseUrl}/v1/api/admin/marketplace/artifacts`;
  }

  /**
   * Get artifacts with filtering options
   * @param pageNumber - The page number to fetch (for pagination)
   * @param pageSize - The number of items per page
   * @param params - Additional filtering parameters
   *        - search: string - Search query
   *        - type: string - Artifact type filter
   *        - status: string - Status filter
   *        - tags: string[] - Tags filter for capabilities
   *        - realms: string[] - Realm filter for realm capabilities
   *        - practiceArea: string - Practice area filter
   *        - isSample: boolean - Sample file filter
   *        - sort: string - Sort parameter
   * @returns Observable of ArtifactResponse
   */
  getArtifacts(
    pageNumber?: number,
    pageSize?: number,
    params?: {
      [key: string]:
        | string
        | number
        | boolean
        | ReadonlyArray<string | number | boolean>;
    }
  ): Observable<ArtifactResponse> {
    // Create a new params object that includes pageNumber and pageSize if provided
    const queryParams: {
      [key: string]:
        | string
        | number
        | boolean
        | ReadonlyArray<string | number | boolean>;
    } = { ...(params || {}) }; // Ensure params is not null or undefined

    if (pageNumber !== undefined) {
      queryParams['pageNumber'] = pageNumber;
    }

    if (pageSize !== undefined) {
      queryParams['pageSize'] = pageSize;
    }

    // Debug log to see what parameters are being sent
    // eslint-disable-next-line no-console
    console.log(
      'MarketplaceService getArtifacts params:',
      JSON.stringify(queryParams, null, 2)
    );

    // Create HttpParams from the queryParams object
    let httpParams = new HttpParams();

    // Add all parameters to HttpParams, handling arrays specially
    Object.entries(queryParams).forEach(([key, value]) => {
      if (value === undefined || value === null) {
        return; // Skip undefined or null values
      }

      if (Array.isArray(value)) {
        // For arrays, combine values into a comma-separated string
        // Skip empty arrays
        if (value.length > 0) {
          const filteredValues = value.filter(
            item => item !== undefined && item !== null
          );
          if (filteredValues.length > 0) {
            const joinedValue = filteredValues
              .map(item => item.toString())
              .join(', ');
            httpParams = httpParams.set(key, joinedValue);
          }
        }
      } else {
        // For non-arrays, add as a single parameter
        httpParams = httpParams.set(key, value.toString());
      }
    });

    return this.http.get<ArtifactResponse>(this.apiUrl, {
      params: httpParams,
    });
  }
}
