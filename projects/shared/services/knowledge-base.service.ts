import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { environment } from '../environments/environment';

export interface KnowledgeBaseItem {
  id: number;
  name: string;
  description: string;
  splitSize: string;
  modelRef: number;
  vectorDb: string;
  createdDate: string;
  createdBy?: string;
  isactive: boolean;
  practiceArea?: number;
  goodAt?: string;
}

@Injectable({
  providedIn: 'root',
})
export class KnowledgeBaseService {
  private readonly baseUrl = environment.baseUrl;

  constructor(private readonly http: HttpClient) {}

  getAllKnowledgeBases(params?: {
    status?: string;
    practiceArea?: string;
    tags?: string[];
    search?: string;
  }): Observable<KnowledgeBaseItem[]> {
    let httpParams = new HttpParams();

    if (params?.status) httpParams = httpParams.set('status', params.status);
    if (params?.practiceArea)
      httpParams = httpParams.set('practiceArea', params.practiceArea);
    if (params?.tags && params.tags.length > 0) {
      params.tags.forEach(tag => {
        httpParams = httpParams.append('tags', tag);
      });
    }
    if (params?.search) httpParams = httpParams.set('search', params.search);

    return this.http
      .get<
        KnowledgeBaseItem[]
      >(`${this.baseUrl}/embedding/knowledge`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  private handleError(error: unknown): Observable<never> {
    console.error('Knowledge Base API Error:', error);
    return throwError(() => error);
  }
}
