import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, tap, map } from 'rxjs/operators';

import { environment } from '../environments/environment';
import { Guardrail } from '../models/artifact.model';

@Injectable({
  providedIn: 'root',
})
export class GuardrailService {
  private readonly baseUrl = environment.baseUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get all guardrails with optional filtering
   */
  getAll(params?: {
    guardrailId?: number;
    type?: string;
    page?: number;
    records?: number;
    isDeleted?: boolean;
  }): Observable<Guardrail[]> {
    // For now, use a simple GET request. The FormData approach from Postman
    // might be a special case or different endpoint
    return this.http
      .get<Guardrail[]>(`${this.baseUrl}/guardrails`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Search guardrails with FormData (based on Postman collection)
   */
  search(params?: {
    guardrailId?: number;
    type?: string;
    page?: number;
    records?: number;
    isDeleted?: boolean;
  }): Observable<Guardrail[]> {
    const formData = new FormData();

    if (params?.guardrailId)
      formData.append('guardrailId', params.guardrailId.toString());
    if (params?.type) formData.append('type', params.type);
    if (params?.page) formData.append('page', params.page.toString());
    if (params?.records) formData.append('records', params.records.toString());
    if (params?.isDeleted !== undefined)
      formData.append('isDeleted', params.isDeleted.toString());

    // The Postman collection shows GET with FormData body, which is unusual
    // We'll implement this as a custom request
    return this.http
      .request<Guardrail[]>('GET', `${this.baseUrl}/guardrails`, {
        body: formData,
      })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get guardrail by ID
   */
  getById(id: number): Observable<Guardrail> {
    return this.search({ guardrailId: id }).pipe(
      map((guardrails: Guardrail[]) => {
        if (guardrails.length === 0) {
          throw new Error(`Guardrail with ID ${id} not found`);
        }
        return guardrails[0];
      }),
      catchError(this.handleError)
    );
  }

  getGuardrailDetailsById(id: number): Observable<any> {
    const payload = {
      guardrailId: id,
      isDeleted: false,
    };
    return this.http.get<any>(`${this.baseUrl}/guardrails`, { params: payload }).pipe(catchError(this.handleError));
  }

  /**
   * Create a new guardrail
   */
  create(guardrail: Omit<Guardrail, 'id'>): Observable<Guardrail> {
    console.log('🔥 GuardrailService.create() called with payload:', guardrail);
    console.log('🔥 API URL:', `${this.baseUrl}/guardrails`);

    return this.http
      .post<Guardrail>(`${this.baseUrl}/guardrails`, guardrail)
      .pipe(
        tap(response => {
          console.log('✅ GuardrailService.create() API response:', response);
        }),
        catchError(error => {
          console.error('❌ GuardrailService.create() API error:', error);
          return this.handleError(error);
        })
      );
  }

  /**
   * Update an existing guardrail
   */
  update(guardrail: Guardrail): Observable<Guardrail> {
    return this.http
      .put<Guardrail>(`${this.baseUrl}/guardrails`, guardrail)
      .pipe(catchError(this.handleError));
  }

  /**
   * Submit guardrail for review
   */
  submitForReview(guardrailId: number): Observable<any> {
    return this.http
      .put(`${this.baseUrl}/guardrails/review?guardrail-id=${guardrailId}`, {})
      .pipe(catchError(this.handleError));
  }

  /**
   * Approve guardrail
   */
  approve(id: number, status: string): Observable<any> {
    return this.http
      .put(`${this.baseUrl}/guardrails/approval`, { id, status })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get guardrail metrics
   */
  getMetrics(): Observable<any> {
    return this.http
      .get(`${this.baseUrl}/guardrails/metrics`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Handle HTTP errors
   */
  private handleError(error: any): Observable<never> {
    console.error('GuardrailService error:', error);
    return throwError(() => error);
  }
}
