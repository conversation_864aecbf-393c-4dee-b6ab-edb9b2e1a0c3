import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../environments/environment';

export interface Realm {
  realmId: number;
  realmName: string;
  orgId: number;
  orgName: string;
  domainId: number;
  domainName: string;
  projectId: number;
  projectName: string;
  teamId: number;
  teamName: string;
  message: string;
}

@Injectable({
  providedIn: 'root',
})
export class RealmService {
  private readonly baseUrl = environment.baseUrl;
  private readonly apiUrl = `${this.baseUrl}/api/auth/realms`;

  constructor(private http: HttpClient) {}

  /**
   * Get all realms
   */
  getRealms(): Observable<Realm[]> {
    return this.http.get<Realm[]>(this.apiUrl).pipe(
      catchError(error => {
        console.error('Error fetching realms:', error);
        throw error;
      })
    );
  }

  /**
   * Get realm by ID
   */
  getRealmById(id: number): Observable<Realm | undefined> {
    return this.getRealms().pipe(
      map(realms => realms.find(realm => realm.realmId === id))
    );
  }

  /**
   * Get realms by name (case-insensitive partial match)
   */
  getRealmsByName(name: string): Observable<Realm[]> {
    return this.getRealms().pipe(
      map(realms =>
        realms.filter(realm =>
          realm.realmName.toLowerCase().includes(name.toLowerCase())
        )
      )
    );
  }

  /**
   * Get realms by organization ID
   */
  getRealmsByOrgId(orgId: number): Observable<Realm[]> {
    return this.getRealms().pipe(
      map(realms => realms.filter(realm => realm.orgId === orgId))
    );
  }

  /**
   * Get realms by domain ID
   */
  getRealmsByDomainId(domainId: number): Observable<Realm[]> {
    return this.getRealms().pipe(
      map(realms => realms.filter(realm => realm.domainId === domainId))
    );
  }

  /**
   * Get realms by project ID
   */
  getRealmsByProjectId(projectId: number): Observable<Realm[]> {
    return this.getRealms().pipe(
      map(realms => realms.filter(realm => realm.projectId === projectId))
    );
  }

  /**
   * Get realms by team ID
   */
  getRealmsByTeamId(teamId: number): Observable<Realm[]> {
    return this.getRealms().pipe(
      map(realms => realms.filter(realm => realm.teamId === teamId))
    );
  }
}
