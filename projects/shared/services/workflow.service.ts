import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { environment } from '../environments/environment';
import { Workflow, WorkflowConfig, ManagerLLM } from '../models/artifact.model';
import { webSocket, WebSocketSubject } from 'rxjs/webSocket';

@Injectable({ providedIn: 'root' })
export class WorkflowService {
  private readonly baseUrl = environment.baseUrl;
  public socket$!: WebSocketSubject<any>;

  constructor(private http: HttpClient) {}

  /**
   * Get workflow by ID
   */
  getById(id: number): Observable<Workflow> {
    return this.http
      .get<Workflow>(`${this.baseUrl}/workflows?workFlowId=${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get all workflows with optional filtering
   */
  getAll(params?: {
    teamId?: number;
    status?: string;
    createdBy?: number;
    page?: number;
    limit?: number;
    name?: string;
  }): Observable<Workflow[]> {
    let httpParams = new HttpParams();

    if (params?.teamId)
      httpParams = httpParams.set('teamId', params.teamId.toString());
    if (params?.status) httpParams = httpParams.set('status', params.status);
    if (params?.createdBy)
      httpParams = httpParams.set('createdBy', params.createdBy.toString());
    if (params?.page)
      httpParams = httpParams.set('page', params.page.toString());
    if (params?.limit)
      httpParams = httpParams.set('limit', params.limit.toString());
    if (params?.name) httpParams = httpParams.set('name', params.name);

    return this.http
      .get<Workflow[]>(`${this.baseUrl}/workflows`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  /**
   * Create a new workflow
   */
  create(workflow: Omit<Workflow, 'id'>): Observable<Workflow> {
    return this.http
      .post<Workflow>(`${this.baseUrl}/workflows`, workflow)
      .pipe(catchError(this.handleError));
  }

  /**
   * Update an existing workflow
   */
  update(id: number, workflow: Partial<Workflow>): Observable<Workflow> {
    return this.http
      .put<Workflow>(`${this.baseUrl}/workflows`, { id, ...workflow })
      .pipe(catchError(this.handleError));
  }

  /**
   * Delete a workflow
   */
  delete(id: number): Observable<void> {
    return this.http
      .delete<void>(`${this.baseUrl}/workflows/${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Submit workflow for review
   */
  submitForReview(id: number): Observable<Workflow> {
    return this.http
      .put<Workflow>(
        `${this.baseUrl}/workflows/IN_REVIEW?workflow-id=${id}`,
        {}
      )
      .pipe(catchError(this.handleError));
  }

  /**
   * Approve workflow
   */
  approve(id: number): Observable<Workflow> {
    return this.http
      .put<Workflow>(`${this.baseUrl}/workflows/APPROVED?workflow-id=${id}`, {})
      .pipe(catchError(this.handleError));
  }

  /**
   * Reject workflow
   */
  reject(id: number): Observable<Workflow> {
    return this.http
      .put<Workflow>(`${this.baseUrl}/workflows/REJECTED?workflow-id=${id}`, {})
      .pipe(catchError(this.handleError));
  }

  /**
   * Test workflow execution
   */
  test(id: number, userInputs?: any): Observable<any> {
    const payload = {
      executionId: '',
      id,
      type: 'workflow',
      user: 'current',
      userInputs,
    };

    return this.http
      .post(`${this.baseUrl}/workflows/${id}/test`, payload)
      .pipe(catchError(this.handleError));
  }

  /**
   * Validate workflow configuration
   */
  validate(id: number): Observable<any> {
    return this.http
      .post(`${this.baseUrl}/workflows/${id}/validate`, {})
      .pipe(catchError(this.handleError));
  }

  /**
   * Run workflow in playground
   */
  playgroundRun(id: number, inputs?: any): Observable<any> {
    const payload = { inputs: { id, type: 'workflow', ...inputs } };
    return this.http
      .post(`${this.baseUrl}/playground/workflows/run`, payload)
      .pipe(catchError(this.handleError));
  }

  /**
   * Execute workflow
   */
  execute(id: number, inputs?: any): Observable<any> {
    const payload = { inputs: { id, type: 'workflow', ...inputs } };
    return this.http
      .post(`${this.baseUrl}/workflows/${id}/execute`, payload)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get workflow configuration template
   */
  getConfigTemplate(): Partial<WorkflowConfig> {
    return {
      managerLlm: [
        {
          id: 40,
          modelDeploymentName: 'correct-anthropic.claude-3-sonnet-2',
          topP: 0.95,
          maxToken: 1500,
          temperature: 0.3,
        },
      ],
      topP: 0.95,
      maxToken: 1500,
      temperature: 0.3,
      enableAgenticMemory: false,
    };
  }

  /**
   * Clone workflow
   */
  clone(id: number, newName: string): Observable<Workflow> {
    return this.http
      .post<Workflow>(`${this.baseUrl}/workflows/${id}/clone`, {
        name: newName,
      })
      .pipe(
        catchError(this.handleError),
        catchError(() =>
          throwError(() => new Error('Failed to clone workflow'))
        )
      );
  }

  /**
   * Export workflow configuration
   */
  export(id: number): Observable<any> {
    return this.http
      .get(`${this.baseUrl}/workflows/${id}/export`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Import workflow configuration
   */
  import(config: any): Observable<Workflow> {
    return this.http
      .post<Workflow>(`${this.baseUrl}/workflows/import`, config)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get workflow statistics
   */
  getStats(id: number): Observable<any> {
    return this.http
      .get(`${this.baseUrl}/workflows/${id}/stats`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get workflow execution history
   */
  getExecutionHistory(
    id: number,
    params?: {
      page?: number;
      limit?: number;
      startDate?: string;
      endDate?: string;
      status?: string;
    }
  ): Observable<any[]> {
    let httpParams = new HttpParams();

    if (params?.page)
      httpParams = httpParams.set('page', params.page.toString());
    if (params?.limit)
      httpParams = httpParams.set('limit', params.limit.toString());
    if (params?.startDate)
      httpParams = httpParams.set('startDate', params.startDate);
    if (params?.endDate) httpParams = httpParams.set('endDate', params.endDate);
    if (params?.status) httpParams = httpParams.set('status', params.status);

    return this.http
      .get<
        any[]
      >(`${this.baseUrl}/workflows/${id}/executions`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get workflow execution status
   */
  getExecutionStatus(executionId: string): Observable<any> {
    return this.http
      .get(`${this.baseUrl}/workflows/executions/${executionId}/status`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Cancel workflow execution
   */
  cancelExecution(executionId: string): Observable<any> {
    return this.http
      .post(`${this.baseUrl}/workflows/executions/${executionId}/cancel`, {})
      .pipe(catchError(this.handleError));
  }

  /**
   * Get workflow execution logs
   */
  getExecutionLogs(executionId: string): Observable<any[]> {
    return this.http
      .get<any[]>(`${this.baseUrl}/workflows/executions/${executionId}/logs`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get available LLM models for workflow configuration
   */
  getAvailableLLMModels(): Observable<ManagerLLM[]> {
    return this.http
      .get<ManagerLLM[]>(`${this.baseUrl}/workflows/llm-models`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Search workflows by name or description
   */
  search(
    query: string,
    params?: {
      teamId?: number;
      status?: string;
      page?: number;
      limit?: number;
    }
  ): Observable<Workflow[]> {
    let httpParams = new HttpParams().set('q', query);

    if (params?.teamId)
      httpParams = httpParams.set('teamId', params.teamId.toString());
    if (params?.status) httpParams = httpParams.set('status', params.status);
    if (params?.page)
      httpParams = httpParams.set('page', params.page.toString());
    if (params?.limit)
      httpParams = httpParams.set('limit', params.limit.toString());

    return this.http
      .get<
        Workflow[]
      >(`${this.baseUrl}/workflows/search`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get workflow dependencies
   */
  getDependencies(id: number): Observable<any> {
    return this.http
      .get(`${this.baseUrl}/workflows/${id}/dependencies`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Validate workflow dependencies
   */
  validateDependencies(id: number): Observable<any> {
    return this.http
      .post(`${this.baseUrl}/workflows/${id}/validate-dependencies`, {})
      .pipe(catchError(this.handleError));
  }

  /**
   * Centralized error handling
   */
  private handleError(error: any): Observable<never> {
    let errorMessage = 'An error occurred while processing workflow operation';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.status) {
      errorMessage = `HTTP ${error.status}: ${error.statusText}`;
    }

    console.error('WorkflowService Error:', error);
    return throwError(() => new Error(errorMessage));
  }

  
  // Initialize WebSocket connection
  public workflowLogConnect(executionId: string): Observable<any> {
    const wsUrl = environment['logStreamingApiUrl'];
    const fullWsUrl = `${wsUrl}?executionId=${executionId}`;
    console.log('Full WebSocket URL:', fullWsUrl);

    this.socket$ = webSocket(fullWsUrl);
    return this.socket$;
  }

  // Send a message to the WebSocket server
  public sendMessage(message: any): void {
    if (this.socket$) {
      this.socket$.next(message);
    }
  }

  // Disconnect from the WebSocket server
  public workflowLogDisconnect(): void {
    if (this.socket$) {
      this.socket$.complete();
    }
  }
}
