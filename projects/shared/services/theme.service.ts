import { isPlatformBrowser } from '@angular/common';
import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export type Theme = 'light' | 'dark';

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  private currentThemeSubject = new BehaviorSubject<Theme>('light');
  public currentTheme$ = this.currentThemeSubject.asObservable();

  constructor(@Inject(PLATFORM_ID) private platformId: object) {
    this.initializeTheme();
  }

  /**
   * Initialize theme on service creation
   */
  private initializeTheme(): void {
    if (isPlatformBrowser(this.platformId)) {
      const defaultTheme = this.getDefaultThemeForPath();
      this.setTheme(defaultTheme);
    }
  }

  /**
   * Detect current system theme preference
   */
  private detectSystemTheme(): void {
    // no-op in simplified service
  }

  /**
   * Setup listener for system theme changes
   */
  private setupSystemThemeListener(): void {
    if (isPlatformBrowser(this.platformId)) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      const handleChange = () => {};

      // Modern browsers
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', handleChange);
      } else {
        // Fallback for older browsers
        mediaQuery.addListener(handleChange);
      }
    }
  }

  /**
   * Apply the appropriate theme based on user preference and system theme
   */
  private applyTheme(theme: Theme): void {
    this.setTheme(theme);
  }

  /**
   * Set the current theme
   */
  private setTheme(theme: Theme): void {
    this.currentThemeSubject.next(theme);

    // Apply theme to document
    if (isPlatformBrowser(this.platformId)) {
      document.documentElement.setAttribute('data-theme', theme);
    }
  }

  /**
   * Set user preference for theme
   */
  public setUserPreference(preference: 'system' | 'light' | 'dark'): void {
    // 'system' collapses to light by default for now
    const theme: Theme = preference === 'dark' ? 'dark' : 'light';
    this.applyTheme(theme);
  }

  /**
   * Get current theme
   */
  public getCurrentTheme(): Theme {
    return this.currentThemeSubject.value;
  }

  /**
   * Get system theme
   */
  public getSystemTheme(): Theme {
    return 'light';
  }

  /**
   * Get user preference
   */
  public getUserPreference(): 'system' | 'light' | 'dark' {
    return this.getCurrentTheme();
  }

  /**
   * Toggle between light and dark themes (ignores system preference)
   */
  public toggleTheme(): void {
    const currentTheme = this.getCurrentTheme();
    const newTheme: Theme = currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(newTheme);
  }

  /**
   * Reset to light theme (no system preference)
   */
  public resetToSystem(): void {
    this.applyTheme('light');
  }

  /**
   * Check if current theme is dark
   */
  public isDarkTheme(): boolean {
    return this.getCurrentTheme() === 'dark';
  }

  /**
   * Check if current theme is light
   */
  public isLightTheme(): boolean {
    return this.getCurrentTheme() === 'light';
  }

  /**
   * Check if using system preference (always returns false now)
   */
  public isUsingSystemPreference(): boolean {
    return false; // System preference detection is disabled
  }

  private getDefaultThemeForPath(): Theme {
    const path = isPlatformBrowser(this.platformId)
      ? window.location.pathname
      : '';
    // Dark only for marketing; light for everything else
    return path.startsWith('/marketing/') ? 'dark' : 'light';
  }
}

//Old Code

// import { isPlatformBrowser } from '@angular/common';
// import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
// import { BehaviorSubject } from 'rxjs';

// export type Theme = 'light' | 'dark';

// @Injectable({
//   providedIn: 'root',
// })
// export class ThemeService {
//   private readonly THEME_STORAGE_KEY = 'app-theme';
//   private readonly THEME_PREFERENCE_KEY = 'theme-preference';

//   private currentThemeSubject = new BehaviorSubject<Theme>('light');
//   public currentTheme$ = this.currentThemeSubject.asObservable();

//   private systemThemeSubject = new BehaviorSubject<Theme>('light');
//   public systemTheme$ = this.systemThemeSubject.asObservable();

//   private userPreferenceSubject = new BehaviorSubject<
//     'system' | 'light' | 'dark'
//   >('system');
//   public userPreference$ = this.userPreferenceSubject.asObservable();

//   constructor(@Inject(PLATFORM_ID) private platformId: object) {
//     this.initializeTheme();
//   }

//   /**
//    * Initialize theme on service creation
//    */
//   private initializeTheme(): void {
//     if (isPlatformBrowser(this.platformId)) {
//       // Load user preference from localStorage
//       const savedPreference = this.getStoredUserPreference();
//       this.userPreferenceSubject.next(savedPreference);

//       // Detect system theme
//       this.detectSystemTheme();

//       // Apply appropriate theme
//       this.applyTheme();

//       // Listen for system theme changes
//       this.setupSystemThemeListener();
//     }
//   }

//   /**
//    * Get stored user preference from localStorage
//    */
//   private getStoredUserPreference(): 'system' | 'light' | 'dark' {
//     try {
//       const stored = localStorage.getItem(this.THEME_PREFERENCE_KEY);
//       return (stored as 'system' | 'light' | 'dark') || 'system';
//     } catch {
//       return 'system';
//     }
//   }

//   /**
//    * Detect current system theme preference
//    */
//   private detectSystemTheme(): void {
//     if (isPlatformBrowser(this.platformId)) {
//       const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
//       const systemTheme: Theme = isDark ? 'dark' : 'light';
//       this.systemThemeSubject.next(systemTheme);
//     }
//   }

//   /**
//    * Setup listener for system theme changes
//    */
//   private setupSystemThemeListener(): void {
//     if (isPlatformBrowser(this.platformId)) {
//       const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

//       const handleChange = (e: MediaQueryListEvent) => {
//         const newSystemTheme: Theme = e.matches ? 'dark' : 'light';
//         this.systemThemeSubject.next(newSystemTheme);

//         // If user preference is 'system', apply the new system theme
//         if (this.userPreferenceSubject.value === 'system') {
//           this.applyTheme();
//         }
//       };

//       // Modern browsers
//       if (mediaQuery.addEventListener) {
//         mediaQuery.addEventListener('change', handleChange);
//       } else {
//         // Fallback for older browsers
//         mediaQuery.addListener(handleChange);
//       }
//     }
//   }

//   /**
//    * Apply the appropriate theme based on user preference and system theme
//    */
//   private applyTheme(): void {
//     const userPreference = this.userPreferenceSubject.value;
//     let themeToApply: Theme;

//     if (userPreference === 'system') {
//       themeToApply = this.systemThemeSubject.value;
//     } else {
//       themeToApply = userPreference;
//     }

//     this.setTheme(themeToApply);
//   }

//   /**
//    * Set the current theme
//    */
//   private setTheme(theme: Theme): void {
//     this.currentThemeSubject.next(theme);

//     // Apply theme to document
//     if (isPlatformBrowser(this.platformId)) {
//       document.documentElement.setAttribute('data-theme', theme);

//       // Store the actual applied theme
//       try {
//         localStorage.setItem(this.THEME_STORAGE_KEY, theme);
//       } catch {
//         // Ignore localStorage errors
//       }
//     }
//   }

//   /**
//    * Set user preference for theme
//    */
//   public setUserPreference(preference: 'system' | 'light' | 'dark'): void {
//     this.userPreferenceSubject.next(preference);

//     // Store user preference
//     if (isPlatformBrowser(this.platformId)) {
//       try {
//         localStorage.setItem(this.THEME_PREFERENCE_KEY, preference);
//       } catch {
//         // Ignore localStorage errors
//       }
//     }

//     // Apply the appropriate theme
//     this.applyTheme();
//   }

//   /**
//    * Get current theme
//    */
//   public getCurrentTheme(): Theme {
//     return this.currentThemeSubject.value;
//   }

//   /**
//    * Get system theme
//    */
//   public getSystemTheme(): Theme {
//     return this.systemThemeSubject.value;
//   }

//   /**
//    * Get user preference
//    */
//   public getUserPreference(): 'system' | 'light' | 'dark' {
//     return this.userPreferenceSubject.value;
//   }

//   /**
//    * Toggle between light and dark themes (ignores system preference)
//    */
//   public toggleTheme(): void {
//     const currentTheme = this.getCurrentTheme();
//     const newTheme: Theme = currentTheme === 'light' ? 'dark' : 'light';

//     // Set explicit preference (not system)
//     this.setUserPreference(newTheme);
//   }

//   /**
//    * Reset to system preference
//    */
//   public resetToSystem(): void {
//     this.setUserPreference('system');
//   }

//   /**
//    * Check if current theme is dark
//    */
//   public isDarkTheme(): boolean {
//     return this.getCurrentTheme() === 'dark';
//   }

//   /**
//    * Check if current theme is light
//    */
//   public isLightTheme(): boolean {
//     return this.getCurrentTheme() === 'light';
//   }

//   /**
//    * Check if using system preference
//    */
//   public isUsingSystemPreference(): boolean {
//     return this.getUserPreference() === 'system';
//   }
// }
