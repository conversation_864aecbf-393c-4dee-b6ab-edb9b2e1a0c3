import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  preferences: boolean;
  timestamp: number;
}

export interface CookieType {
  id: string;
  name: string;
  description: string;
  necessary: boolean;
  category: 'necessary' | 'analytics' | 'marketing' | 'preferences';
}

@Injectable({
  providedIn: 'root'
})
export class CookieService {
  private readonly COOKIE_CONSENT_KEY = 'cookie_consent';
  private readonly COOKIE_PREFERENCES_KEY = 'cookie_preferences';
  private readonly COOKIE_CONSENT_EXPIRY_DAYS = 365;

  private cookieConsentSubject = new BehaviorSubject<boolean>(this.hasConsent());
  public cookieConsent$ = this.cookieConsentSubject.asObservable();

  private cookiePreferencesSubject = new BehaviorSubject<CookiePreferences | null>(this.getStoredPreferences());
  public cookiePreferences$ = this.cookiePreferencesSubject.asObservable();

  public readonly cookieTypes: CookieType[] = [
    {
      id: 'necessary',
      name: 'Necessary Cookies',
      description: 'These cookies are essential for the website to function properly. They cannot be disabled.',
      necessary: true,
      category: 'necessary'
    },
    {
      id: 'analytics',
      name: 'Analytics Cookies',
      description: 'These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously.',
      necessary: false,
      category: 'analytics'
    },
    {
      id: 'marketing',
      name: 'Marketing Cookies',
      description: 'These cookies are used to track visitors across websites to display relevant and engaging advertisements.',
      necessary: false,
      category: 'marketing'
    },
    {
      id: 'preferences',
      name: 'Preference Cookies',
      description: 'These cookies remember your choices and preferences to provide a more personalized experience.',
      necessary: false,
      category: 'preferences'
    }
  ];

  constructor() {
    // Initialize consent status
    this.updateConsentStatus();
  }

  /**
   * Check if user has given consent
   */
  hasConsent(): boolean {
    const consent = localStorage.getItem(this.COOKIE_CONSENT_KEY);
    if (!consent) return false;

    try {
      const consentData = JSON.parse(consent);
      const now = Date.now();
      const expiryTime = consentData.timestamp + (this.COOKIE_CONSENT_EXPIRY_DAYS * 24 * 60 * 60 * 1000);
      
      // Check if consent has expired
      if (now > expiryTime) {
        this.clearConsent();
        return false;
      }
      
      return consentData.consent === true;
    } catch (error) {
      console.error('Error parsing cookie consent:', error);
      this.clearConsent();
      return false;
    }
  }

  /**
   * Accept all cookies
   */
  acceptAll(): void {
    const preferences: CookiePreferences = {
      necessary: true,
      analytics: true,
      marketing: true,
      preferences: true,
      timestamp: Date.now()
    };

    this.setConsent(true, preferences);
    this.updateConsentStatus();
  }

  /**
   * Reject all non-necessary cookies
   */
  rejectAll(): void {
    const preferences: CookiePreferences = {
      necessary: true, // Always true as these are required
      analytics: false,
      marketing: false,
      preferences: false,
      timestamp: Date.now()
    };

    this.setConsent(true, preferences);
    this.updateConsentStatus();
  }

  /**
   * Set custom cookie preferences
   */
  setCustomPreferences(preferences: Partial<CookiePreferences>): void {
    const currentPrefs = this.getStoredPreferences() || this.getDefaultPreferences();
    const newPreferences: CookiePreferences = {
      ...currentPrefs,
      ...preferences,
      necessary: true, // Always true
      timestamp: Date.now()
    };

    this.setConsent(true, newPreferences);
    this.updateConsentStatus();
  }

  /**
   * Get current cookie preferences
   */
  getPreferences(): CookiePreferences | null {
    return this.getStoredPreferences();
  }

  /**
   * Check if a specific cookie type is allowed
   */
  isCookieAllowed(category: keyof Omit<CookiePreferences, 'timestamp'>): boolean {
    const preferences = this.getStoredPreferences();
    if (!preferences) return false;
    
    return preferences[category];
  }

  /**
   * Clear all cookie consent and preferences
   */
  clearConsent(): void {
    localStorage.removeItem(this.COOKIE_CONSENT_KEY);
    localStorage.removeItem(this.COOKIE_PREFERENCES_KEY);
    this.updateConsentStatus();
  }

  /**
   * Get cookie types by category
   */
  getCookieTypesByCategory(category: CookieType['category']): CookieType[] {
    return this.cookieTypes.filter(cookie => cookie.category === category);
  }

  /**
   * Get all non-necessary cookie types
   */
  getOptionalCookieTypes(): CookieType[] {
    return this.cookieTypes.filter(cookie => !cookie.necessary);
  }

  /**
   * Private method to set consent and preferences
   */
  private setConsent(consent: boolean, preferences: CookiePreferences): void {
    const consentData = {
      consent,
      timestamp: Date.now()
    };

    localStorage.setItem(this.COOKIE_CONSENT_KEY, JSON.stringify(consentData));
    localStorage.setItem(this.COOKIE_PREFERENCES_KEY, JSON.stringify(preferences));
  }

  /**
   * Private method to get stored preferences
   */
  private getStoredPreferences(): CookiePreferences | null {
    const stored = localStorage.getItem(this.COOKIE_PREFERENCES_KEY);
    if (!stored) return null;

    try {
      return JSON.parse(stored);
    } catch (error) {
      console.error('Error parsing cookie preferences:', error);
      return null;
    }
  }

  /**
   * Private method to get default preferences
   */
  private getDefaultPreferences(): CookiePreferences {
    return {
      necessary: true,
      analytics: false,
      marketing: false,
      preferences: false,
      timestamp: Date.now()
    };
  }

  /**
   * Private method to update consent status
   */
  private updateConsentStatus(): void {
    const hasConsent = this.hasConsent();
    const preferences = this.getStoredPreferences();
    
    this.cookieConsentSubject.next(hasConsent);
    this.cookiePreferencesSubject.next(preferences);
  }

  /**
   * Get consent status as observable
   */
  getConsentStatus(): Observable<boolean> {
    return this.cookieConsent$;
  }

  /**
   * Get preferences as observable
   */
  getPreferencesObservable(): Observable<CookiePreferences | null> {
    return this.cookiePreferences$;
  }
}
