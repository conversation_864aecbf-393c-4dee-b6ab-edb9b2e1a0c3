import { Injectable, inject } from '@angular/core';
import { AavaDialogService } from '@aava/play-core';
import { AgentDetailsData } from '../components/agent-details-modal';
import { AgentDetailsModalComponent } from '../components/agent-details-modal/agent-details-modal.component';

@Injectable({
  providedIn: 'root',
})
export class AgentDetailsModalService {
  private dialogService = inject(AavaDialogService);

  /**
   * Open agent details modal using AavaDialogService
   */
  openAgentDetails(agentData: AgentDetailsData): void {
    console.log(
      'AgentDetailsModalService.openAgentDetails called with:',
      agentData
    );

    const modalRef = this.dialogService.openModal(
      AgentDetailsModalComponent,
      {
        width: '90vw',
        maxWidth: '1200px',
        maxHeight: '90vh',
      },
      {
        data: agentData, // Pass data as input property
      }
    );

    // If the modal reference is a promise with a component instance, set the data
    if (modalRef && typeof modalRef.then === 'function') {
      modalRef.then((instance: AgentDetailsModalComponent) => {
        if (instance && instance.setDialogData) {
          instance.setDialogData(agentData);
        }
      });
    }

    console.log('Modal opened via AavaDialogService');
  }
}
