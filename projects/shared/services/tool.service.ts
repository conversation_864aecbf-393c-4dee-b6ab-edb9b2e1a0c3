import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { environment } from '../environments/environment';
import { UserTool, ToolConfig } from '../models/artifact.model';

@Injectable({ providedIn: 'root' })
export class ToolService {
  private readonly baseUrl = environment.baseUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get tool by ID
   */
  getById(id: number): Observable<UserTool> {
    const params = new HttpParams().set('userToolId', id.toString());
    return this.http
      .get<UserTool>(`${this.baseUrl}/tools/userTools`, { params })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get all tools with optional filtering
   */
  getAll(params?: {
    teamId?: number;
    status?: string;
    createdBy?: number;
    page?: number;
    limit?: number;
    toolType?: string;
    search?: string;
  }): Observable<UserTool[]> {
    let httpParams = new HttpParams();

    if (params?.teamId)
      httpParams = httpParams.set('teamId', params.teamId.toString());
    if (params?.status) httpParams = httpParams.set('status', params.status);
    if (params?.createdBy)
      httpParams = httpParams.set('createdBy', params.createdBy.toString());
    if (params?.page)
      httpParams = httpParams.set('page', params.page.toString());
    if (params?.limit)
      httpParams = httpParams.set('limit', params.limit.toString());
    if (params?.toolType)
      httpParams = httpParams.set('toolType', params.toolType);
    if (params?.search) httpParams = httpParams.set('search', params.search);

    return this.http
      .get<
        UserTool[]
      >(`${this.baseUrl}/tools/userTools`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  /**
   * Create a new tool
   */
  create(tool: Omit<UserTool, 'id'>): Observable<UserTool> {
    return this.http
      .post<UserTool>(`${this.baseUrl}/tools/userTools`, tool)
      .pipe(catchError(this.handleError));
  }

  /**
   * Create a new tool with custom payload
   */
  createWithCustomPayload(toolData: any): Observable<any> {
    return this.http
      .post<any>(`${this.baseUrl}/tools/userTools`, toolData)
      .pipe(catchError(this.handleError));
  }

  /**
   * Compile/Validate tool code
   */
  compile(compileData: any): Observable<any> {
    return this.http
      .post<any>(`${this.baseUrl}/instructions/core/execute`, compileData)
      .pipe(catchError(this.handleError));
  }

  /**
   * Generate tool from prompt using execution API
   */
  generateToolFromPrompt(prompt: string, userSignature: string): Observable<any> {
    const payload = {
      prompt: prompt,
      mode: 'TOOL_CREATION',
      promptOverride: false,
      executionId: crypto.randomUUID(),
      useCaseIdentifier: 'TOOL_CREATION@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER',
      userSignature: userSignature
    };

    return this.http
      .post<any>(`${this.baseUrl}/instructions/core/execute`, payload)
      .pipe(catchError(this.handleError));
  }

  /**
   * Update an existing tool
   */
  update(id: number, tool: Partial<UserTool>): Observable<UserTool> {
    return this.http
      .put<UserTool>(`${this.baseUrl}/tools/userTools`, { id, ...tool })
      .pipe(catchError(this.handleError));
  }

  /**
   * Update a tool with custom payload
   */
  updateWithCustomPayload(toolData: any): Observable<any> {
    return this.http
      .put<any>(`${this.baseUrl}/tools/userTools`, toolData)
      .pipe(catchError(this.handleError));
  }

  /**
   * Delete a tool
   */
  delete(id: number): Observable<void> {
    return this.http
      .delete<void>(`${this.baseUrl}/tools/userTools/${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Submit tool for review
   */
  submitForReview(id: number): Observable<UserTool> {
    return this.http
      .put<UserTool>(`${this.baseUrl}/tools/userTools/review`, { id })
      .pipe(catchError(this.handleError));
  }

  /**
   * Send tool for approval using the IN_REVIEW endpoint
   */
  sendForApproval(id: number): Observable<any> {
    const params = new HttpParams().set('tool-id', id.toString());
    return this.http
      .put<any>(`${this.baseUrl}/tools/userTools/IN_REVIEW`, {}, { params })
      .pipe(catchError(this.handleError));
  }

  /**
   * Approve tool
   */
  approve(id: number): Observable<UserTool> {
    return this.http
      .put<UserTool>(`${this.baseUrl}/tools/userTools/approve`, { id })
      .pipe(catchError(this.handleError));
  }

  /**
   * Reject tool
   */
  reject(id: number): Observable<UserTool> {
    return this.http
      .put<UserTool>(`${this.baseUrl}/tools/userTools/reject`, { id })
      .pipe(catchError(this.handleError));
  }

  /**
   * Test tool execution
   */
  test(id: number, userInputs?: any): Observable<any> {
    const payload = {
      executionId: '',
      userToolId: id,
      type: 'tool',
      user: 'current',
      userInputs,
    };

    return this.http
      .post(`${this.baseUrl}/tools/userTools/test`, payload)
      .pipe(catchError(this.handleError));
  }

  /**
   * Execute tool in playground
   */
  execute(
    classDefinition: string,
    className: string,
    inputs: any
  ): Observable<any> {
    const payload = {
      class_definition: classDefinition,
      class_name: className,
      inputs: inputs,
    };

    return this.http
      .post(`${this.baseUrl}/tools/userTools/execute`, payload)
      .pipe(catchError(this.handleError));
  }

  /**
   * Validate tool configuration
   */
  validate(id: number): Observable<any> {
    return this.http
      .post(`${this.baseUrl}/tools/userTools/${id}/validate`, {})
      .pipe(catchError(this.handleError));
  }

  /**
   * Run tool in playground
   */
  playgroundRun(id: number, inputs?: any): Observable<any> {
    const payload = {
      inputs: {
        userToolId: id,
        type: 'tool',
        ...inputs,
      },
    };
    return this.http
      .post(`${this.baseUrl}/playground/tools/run`, payload)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get tool configuration template
   */
  getConfigTemplate(): Partial<ToolConfig> {
    return {
      runtime: 'Python 3.10',
      libraries: ['pandas', 'numpy'],
      maxRows: 5000,
    };
  }

  /**
   * Clone tool
   */
  clone(id: number, newName: string): Observable<UserTool> {
    return this.http
      .post<UserTool>(`${this.baseUrl}/tools/${id}/clone`, { name: newName })
      .pipe(
        catchError(this.handleError),
        catchError(() => throwError(() => new Error('Failed to clone tool')))
      );
  }

  /**
   * Export tool configuration
   */
  export(id: number): Observable<any> {
    return this.http
      .get(`${this.baseUrl}/tools/userTools/${id}/export`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Import tool configuration
   */
  import(config: any): Observable<UserTool> {
    return this.http
      .post<UserTool>(`${this.baseUrl}/tools/userTools/import`, config)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get tool statistics
   */
  getStats(id: number): Observable<any> {
    return this.http
      .get(`${this.baseUrl}/tools/userTools/${id}/stats`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get tool execution history
   */
  getExecutionHistory(
    id: number,
    params?: {
      page?: number;
      limit?: number;
      startDate?: string;
      endDate?: string;
    }
  ): Observable<any[]> {
    let httpParams = new HttpParams();

    if (params?.page)
      httpParams = httpParams.set('page', params.page.toString());
    if (params?.limit)
      httpParams = httpParams.set('limit', params.limit.toString());
    if (params?.startDate)
      httpParams = httpParams.set('startDate', params.startDate);
    if (params?.endDate) httpParams = httpParams.set('endDate', params.endDate);

    return this.http
      .get<
        any[]
      >(`${this.baseUrl}/tools/userTools/${id}/executions`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get available tool categories
   */
  getCategories(): Observable<string[]> {
    return this.http
      .get<string[]>(`${this.baseUrl}/tools/categories`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get tools by category
   */
  getByCategory(category: string): Observable<UserTool[]> {
    return this.http
      .get<UserTool[]>(`${this.baseUrl}/tools/categories/${category}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Search tools by name or description
   */
  search(
    query: string,
    params?: {
      teamId?: number;
      status?: string;
      page?: number;
      limit?: number;
    }
  ): Observable<UserTool[]> {
    let httpParams = new HttpParams().set('q', query);

    if (params?.teamId)
      httpParams = httpParams.set('teamId', params.teamId.toString());
    if (params?.status) httpParams = httpParams.set('status', params.status);
    if (params?.page)
      httpParams = httpParams.set('page', params.page.toString());
    if (params?.limit)
      httpParams = httpParams.set('limit', params.limit.toString());

    return this.http
      .get<
        UserTool[]
      >(`${this.baseUrl}/tools/userTools/search`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  /**
   * Centralized error handling
   */
  private handleError(error: any): Observable<never> {
    console.error('ToolService Error:', error);

    // Preserve the original error structure for proper error display
    // This allows the playground component to access the full error details
    return throwError(() => error);
  }
}
