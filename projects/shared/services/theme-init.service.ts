import { isPlatformBrowser } from '@angular/common';
import { Injectable, Inject, PLATFORM_ID } from '@angular/core';

import { ThemeService } from './theme.service';

@Injectable({
  providedIn: 'root',
})
export class ThemeInitService {
  constructor(
    @Inject(PLATFORM_ID) private platformId: object,
    private themeService: ThemeService
  ) {}

  /**
   * Initialize theme system for the application
   * This should be called early in the application bootstrap process
   */
  initialize(): void {
    if (isPlatformBrowser(this.platformId)) {
      // The ThemeService automatically initializes when injected
      // This method ensures the service is properly initialized
      // and can be used for any additional setup if needed

      // Force theme detection and application
      this.ensureThemeApplied();

      // Add global click listener to close theme dropdowns
      this.setupGlobalClickListeners();
    }
  }

  /**
   * Ensure theme is properly applied to the document
   */
  private ensureThemeApplied(): void {
    // Get the current theme from the service
    const currentTheme = this.themeService.getCurrentTheme();

    // Ensure the document has the correct data-theme attribute
    if (document.documentElement.getAttribute('data-theme') !== currentTheme) {
      document.documentElement.setAttribute('data-theme', currentTheme);
    }
  }

  /**
   * Setup global click listeners for theme-related functionality
   */
  private setupGlobalClickListeners(): void {
    // Listen for clicks outside theme components to close dropdowns
    document.addEventListener('click', event => {
      const target = event.target as HTMLElement;

      // Close theme preference dropdowns when clicking outside
      if (!target.closest('.theme-toggle-container')) {
        const dropdowns = document.querySelectorAll(
          '.theme-preference-dropdown'
        );
        dropdowns.forEach(dropdown => {
          if (dropdown instanceof HTMLElement) {
            dropdown.style.display = 'none';
          }
        });
      }
    });

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      // The ThemeService will handle this automatically, but we can add
      // additional logic here if needed for the specific application
      console.warn('System theme changed:', e.matches ? 'dark' : 'light');
    };

    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleSystemThemeChange);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleSystemThemeChange);
    }
  }

  /**
   * Get the current theme state for debugging or external use
   */
  getThemeState(): {
    currentTheme: string;
    systemTheme: string;
    userPreference: string;
    isUsingSystem: boolean;
  } {
    return {
      currentTheme: this.themeService.getCurrentTheme(),
      systemTheme: this.themeService.getSystemTheme(),
      userPreference: this.themeService.getUserPreference(),
      isUsingSystem: this.themeService.isUsingSystemPreference(),
    };
  }
}
