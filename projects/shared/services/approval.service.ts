import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { environment } from '../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class ApprovalService {
  private baseUrl = environment.baseUrl;

  constructor(private http: HttpClient) {}

  private getAuthHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  getApprovalAgents(limit: number) {
    const url = `${this.baseUrl}/v1/api/admin/approvals`;
    const params = new HttpParams().set('limit', limit);
    return this.http.get<any[]>(url, {
      params: params,
      headers: this.getAuthHeaders(),
    });
  }

  getInReviewAgents() {
    const url = `${this.baseUrl}/api/auth/realm/IN_REVIEW`;
    return this.http.get<any[]>(url, {
      headers: this.getAuthHeaders(),
    });
  }

  approveUser(userId: number, realmId: number, comment: string) {
    const url = `${this.baseUrl}/api/auth/realm/approve`;
    const payload = {
      userId: userId,
      realmId: realmId,
      comment: comment,
    };

    return this.http.post<any>(url, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  rejectUser(userId: number, realmId: number, comment: string) {
    const url = `${this.baseUrl}/api/auth/realm/reject`;
    const payload = {
      userId: userId,
      realmId: realmId,
      comment: comment,
    };

    return this.http.post<any>(url, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  searchFilter(query: string, filters: any) {
    const params = new HttpParams()
      .set('practiceAreas', filters?.practiceArea || '')
      .set('sort', filters?.sortBy || '')
      .set('entityTypes', filters?.artifactType || '')
      .set('q', query || '');

    return this.http.get<any>(`${this.baseUrl}/v1/api/admin/approvals`, {
      params,
      headers: this.getAuthHeaders(),
    });
  }

  applyApprovalFilters(filters: any) {
    let params = new HttpParams()
      .set('practiceAreas', filters.practiceArea || '')
      .set('sort', filters.sortBy || '')
      .set('entityTypes', filters.artifactType || '')
      .set('q', filters.searchQuery || '');

    // add capabilityTags (can be multiple)
    if (filters?.capabilityTags) {
      filters?.capabilityTags.forEach((cap: string) => {
        params = params.append('capabilityTags', cap);
      });
    }

    return this.http.get<any>(`${this.baseUrl}/v1/api/admin/approvals`, {
      params,
      headers: this.getAuthHeaders(),
    });
  }

  approveAgents(id: number | null, whatWentGood: string) {
    const url = `${this.baseUrl}/agents/approval`;
    const payload = {
      id: id,
      status: 'APPROVED',
      comments: {
        whatWentGood: whatWentGood,
        whatWentWrong: '',
        improvements: '',
      },
    };

    return this.http.put<any>(url, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  rejectAgents(
    id: number | null,
    whatWentGood: string,
    whatWentWrong: string,
    improvements: string
  ) {
    const url = `${this.baseUrl}/agents/approval`;
    const payload = {
      id: id,
      status: 'REJECTED',
      comments: {
        whatWentGood: whatWentGood,
        whatWentWrong: whatWentWrong,
        improvements: improvements,
      },
    };

    return this.http.put<any>(url, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  approveWorkflows(id: number, whatWentGood: string) {
    const url = `${this.baseUrl}/workflows/approval`;
    const payload = {
      id: id,
      status: 'APPROVED',
      comments: {
        whatWentGood: whatWentGood,
        whatWentWrong: '',
        improvements: '',
      },
    };

    return this.http.put<any>(url, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  rejectWorkflows(
    id: number,
    whatWentGood: string,
    whatWentWrong: string,
    improvements: string
  ) {
    const url = `${this.baseUrl}/workflows/approval`;
    const payload = {
      id: id,
      status: 'REJECTED',
      comments: {
        whatWentGood: whatWentGood,
        whatWentWrong: whatWentWrong,
        improvements: improvements,
      },
    };

    return this.http.put<any>(url, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  approveTools(id: number, whatWentGood: string) {
    const url = `${this.baseUrl}/tools/userTools/approval`;
    const payload = {
      id: id,
      status: 'APPROVED',
      comments: {
        whatWentGood: whatWentGood,
        whatWentWrong: '',
        improvements: '',
      },
    };

    return this.http.put<any>(url, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  rejectTools(
    id: number,
    whatWentGood: string,
    whatWentWrong: string,
    improvements: string
  ) {
    const url = `${this.baseUrl}/tools/userTools/approval`;
    const payload = {
      id: id,
      status: 'REJECTED',
      comments: {
        whatWentGood: whatWentGood,
        whatWentWrong: whatWentWrong,
        improvements: improvements,
      },
    };

    return this.http.put<any>(url, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  approveKnowledge(masterId: number, comment: string) {
    const url = `${this.baseUrl}/embedding/knowledge/v2/approval`;
    const payload = {
      masterId: masterId,
      status: 'APPROVED',
      comment: comment,
    };

    return this.http.put<any>(url, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  rejectKnowledge(masterId: number, comment: string) {
    const url = `${this.baseUrl}/embedding/knowledge/v2/approval`;
    const payload = {
      masterId: masterId,
      status: 'REJECTED',
      comment: comment,
    };

    return this.http.put<any>(url, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  approveGuardrails(id: number, whatWentGood: string) {
    const url = `${this.baseUrl}/guardrails/approval`;
    const payload = {
      id: id,
      status: 'APPROVED',
    };

    return this.http.put<any>(url, payload, {
      headers: this.getAuthHeaders(),
    });
  }

  rejectGuardrails(
    id: number,
    whatWentGood: string,
    whatWentWrong: string,
    improvements: string
  ) {
    const url = `${this.baseUrl}/guardrails/approval`;
    const payload = {
      id: id,
      status: 'REJECTED',
    };

    return this.http.put<any>(url, payload, {
      headers: this.getAuthHeaders(),
    });
  }
}
