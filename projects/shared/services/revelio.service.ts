import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../environments/environment';

export interface RevelioRequest {
  mode: string;
  query: string;
  signature: {
    entityType: string;
  };
}

export interface RevelioResponse {
  message: string;
  searchType: string;
  query: string;
  messageOutput: string;
}

export interface ParsedAgentData {
  agentDetail: {
    name: string;
    role: string;
    practiceArea: number;
    tags: number[];
    goal: string;
    backstory: string;
    description: string;
    agentDetails: string;
    expectedOutput: string;
    agentConfigs: {
      temperature: string;
      topP: string;
      maxToken: string;
      maxRpm: string;
      maxExecutionTime: string;
      allowDelegation: string;
      allowCodeExecution: string;
      modelRef: Array<{
        model: string;
        modelType: string;
        aiEngine: string;
      }>;
    };
  };
}

@Injectable({ providedIn: 'root' })
export class RevelioService {
  private readonly baseUrl = `${environment.baseUrl}/search/v1/api`;

  constructor(private http: HttpClient) {}

  /**
   * Search for agent creation help using Revelio API
   */
  searchAgentCreation(query: string): Observable<RevelioResponse> {
    const payload: RevelioRequest = {
      mode: 'help',
      query: query,
      signature: {
        entityType: 'AGENT_CREATION',
      },
    };

    return this.http
      .post<RevelioResponse>(`${this.baseUrl}/revelio`, payload)
      .pipe(catchError(this.handleError));
  }

  /**
   * Parse the messageOutput JSON string into structured data
   */
  parseAgentData(response: RevelioResponse): ParsedAgentData | null {
    try {
      console.log('🔍 Parsing messageOutput JSON...');
      const parsed = JSON.parse(response.messageOutput);
      console.log('📋 Parsed JSON structure:', parsed);
      console.log('🎯 Final Output Content:', parsed.finalOutput?.content);

      const result = parsed.finalOutput?.content || null;
      console.log('✅ Parsing result:', result);
      return result;
    } catch (error) {
      console.error('❌ Error parsing Revelio response:', error);
      console.error('📄 Raw messageOutput:', response.messageOutput);
      return null;
    }
  }

  private handleError(error: any): Observable<never> {
    console.error('Revelio API Error:', error);
    return throwError(() => error);
  }
}
