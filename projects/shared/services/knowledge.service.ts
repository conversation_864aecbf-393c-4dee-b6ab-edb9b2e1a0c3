/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

import { environment } from '../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class KnowledgeService {
  private readonly baseUrl = environment.baseUrl;

  private http = inject(HttpClient);

  submitPreview(payload: FormData, formValue: any) {
    const queryParams = {
      params: this.makeParams(formValue),
    };
    const url = `${this.baseUrl}/embedding/knowledge/preview`;
    return this.http.post(url, payload, queryParams);
  }

  submitUpload(
    payload: FormData | Record<string, any>,
    formValue: any,
    blob: string
  ): Observable<any> {
    const queryParams = {
      params: this.makeParams(formValue),
    };
    const url = `${this.baseUrl}/embedding/knowledge/v2${blob}`;
    return this.http.post(url, payload, queryParams);
  }

  makeParams(data: any): any {
    const params = new BehaviorSubject<any>(null);
    if (data) {
      Object.entries(data).forEach(([key, value]) => {
        const strValue: string = `${value}`;
        if (strValue && strValue !== ' ') {
          params.next(
            params.value
              ? { ...params.value, [key]: strValue }
              : { [key]: strValue }
          );
        }
      });
    }
    return params.value;
  }

  submitApproval(id: string) {
    const url = `${this.baseUrl}/embedding/knowledge/v2/IN_REVIEW?collection_id=${id}`;
    return this.http.put(url, null);
  }

  /**
   * Get collection details by collection ID for knowledge base approval view
   * This endpoint returns complete collection information including files
   */
  getCollectionDetails(collectionId: string | number): Observable<any> {
    const url = `${this.baseUrl}/embedding/knowledge/v2/files?collection_id=${collectionId}`;
    return this.http.get<any>(url);
  }


}
