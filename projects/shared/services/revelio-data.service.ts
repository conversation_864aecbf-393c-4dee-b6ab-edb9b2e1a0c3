import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { SearchResult } from '../components/widget-box/widget-box.component';
import { environment } from '../environments/environment';

// Interface for unified search response
interface UnifiedSearchResponse {
  response?: string;
  results?: SearchResult[];
  type?: 'text' | 'markdown' | 'html' | 'json' | 'search-result';
  total?: number;
  page?: number;
  limit?: number;
}

@Injectable({ providedIn: 'root' })
export class RevelioDataService {
  private readonly revelioApiUrl = `${environment.baseUrl}/search/v1/api/revelio`;

  constructor(private http: HttpClient) {}

  /**
   * Search for content using the new Revelio API
   * @param query - The search query string
   * @param mode - The search mode (default: 'find')
   */
  search(
    query: string,
    mode: string = 'find'
  ): Observable<UnifiedSearchResponse> {
    if (!query.trim()) {
      return throwError(() => new Error('Search query cannot be empty'));
    }

    const headers = this.getApiHeaders();
    const payload = {
      mode,
      query,
    };

    return this.http
      .post<UnifiedSearchResponse>(this.revelioApiUrl, payload, { headers })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get recent search history for the current user
   * @param limit - Maximum number of recent searches to return
   */
  getSearchHistory(limit = 10): Observable<string[]> {
    return new Observable(observer => {
      try {
        const historyKey = 'revelio_search_history';
        const existingHistory = JSON.parse(
          localStorage.getItem(historyKey) || '[]'
        ) as string[];
        const limitedHistory = existingHistory.slice(0, limit);
        observer.next(limitedHistory);
        observer.complete();
      } catch (error) {
        observer.next([]);
        observer.complete();
      }
    });
  }

  /**
   * Save search query to history
   * @param query - The search query to save
   */
  saveToHistory(query: string): void {
    if (!query.trim()) {
      return;
    }

    try {
      // Save to local storage
      const historyKey = 'revelio_search_history';
      const existingHistory = JSON.parse(
        localStorage.getItem(historyKey) || '[]'
      ) as string[];

      // Remove if already exists to avoid duplicates
      const filteredHistory = existingHistory.filter(item => item !== query);

      // Add to beginning and limit to 20 items
      const updatedHistory = [query, ...filteredHistory].slice(0, 20);

      localStorage.setItem(historyKey, JSON.stringify(updatedHistory));
    } catch (error) {
      console.warn('Failed to save search history:', error);
    }
  }

  /**
   * Clear search history
   */
  clearHistory(): void {
    try {
      localStorage.removeItem('revelio_search_history');
    } catch (error) {
      console.warn('Failed to clear search history:', error);
    }
  }

  /**
   * Build HTTP headers for Revelio API requests
   */
  private getApiHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * Centralized error handling for API requests
   */
  private handleError(error: unknown): Observable<never> {
    let errorMessage = 'An error occurred while searching';

    if (error && typeof error === 'object') {
      const errorObj = error as Record<string, unknown>;
      errorMessage = this.extractErrorMessage(errorObj);
    }

    console.error('RevelioDataService Error:', error);
    return throwError(() => new Error(errorMessage));
  }

  /**
   * Extract error message from error object
   */
  private extractErrorMessage(errorObj: Record<string, unknown>): string {
    if (errorObj['error'] && typeof errorObj['error'] === 'object') {
      const errorDetail = errorObj['error'] as Record<string, unknown>;
      if (typeof errorDetail['message'] === 'string') {
        return errorDetail['message'];
      }
    }

    if (typeof errorObj['message'] === 'string') {
      return errorObj['message'];
    }

    if (typeof errorObj['status'] === 'number') {
      return this.getHttpErrorMessage(errorObj['status'] as number, errorObj);
    }

    return 'An error occurred while searching';
  }

  /**
   * Get HTTP error message based on status code
   */
  private getHttpErrorMessage(
    status: number,
    errorObj: Record<string, unknown>
  ): string {
    switch (status) {
      case 400:
        return 'Invalid search query. Please check your input.';
      case 401:
        return 'Authentication failed. Please check your access credentials.';
      case 403:
        return 'You do not have permission to perform this search.';
      case 404:
        return 'Search service not found. Please try again later.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return `HTTP ${status}: ${
          (errorObj['statusText'] as string) || 'Unknown error'
        }`;
    }
  }
}
