import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError, throwError } from 'rxjs';

export interface RealmInfo {
  realmId: number | null;
  realmName: string | null;
  orgId: number | null;
  orgName: string | null;
  domainId: number | null;
  domainName: string | null;
  projectId: number | null;
  projectName: string | null;
  teamId: number | null;
  teamName: string | null;
  status?: 'IN_REVIEW' | 'APPROVED' | 'REJECTED';
  statusMsg?: string;
}

export interface RoleInfo {
  name: string;
  pages: Record<string, string[]>;
}

export interface StudioInfo {
  name: string; // e.g., 'launchpad', 'console'
  url: string; 
}

export interface UserDetails {
  userId: number;
  userName: string;
  email: string;
  realms: RealmInfo[];
  roles: RoleInfo[];
  studioInfo: StudioInfo[];
}

export interface RealmInfoResponse {
  userId?: number;
  email?: string;
  realms: RealmInfo[];
  message?: string;
}

@Injectable({ providedIn: 'root' })
export class UserDetailsService {
  constructor(private http: HttpClient) {}

  /**
   * Fetch authenticated user's details from backend
   */
  getUserDetails(apiAuthUrl?: string): Observable<UserDetails> {
    // Use provided API URL or fallback to relative path
    const url = `${apiAuthUrl}/user/details`;

    return this.http.get<UserDetails>(url).pipe(
      catchError(err => {
        return throwError(() => err);
      })
    );
  }

  /**
   * Fetch user's realm information with status
   */
  getUserRealmInfo(
    email: string,
    apiAuthUrl?: string
  ): Observable<RealmInfoResponse> {
    // Use provided API URL or fallback to relative path
    const url = `${apiAuthUrl}/user/realm/info?email=${encodeURIComponent(email)}`;

    return this.http.get<RealmInfoResponse>(url).pipe(
      catchError(err => {
        return throwError(() => err);
      })
    );
  }
}
