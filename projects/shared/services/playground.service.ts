import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { environment } from '../environments/environment';



@Injectable({ providedIn: 'root' })
export class PlaygroundService {
  private readonly baseUrl = environment.baseUrl;

  constructor(private http: HttpClient) { }
  submitAgentExecution(
    payload: any,
    hasFiles: boolean,
    files: File[] = []
  ): Observable<any> {
    if (hasFiles && files.length > 0) {
      const formData = new FormData();

      // Ensure everything is cast to string
      formData.append('agentId', String(payload.agentId));
      formData.append('executionId', String(payload.executionId));
      formData.append('user', String(payload.user));
      formData.append('userInputs', JSON.stringify(payload.userInputs));

      // Always append files if present
      if (files) {
        files.forEach((file, idx) => {
          formData.append('files', file, file.name);
        });
      }

      return this.http.post(`${this.baseUrl}/agents/execute/files`, formData);
    } else {
      return this.http.post(`${this.baseUrl}/agents/execute`, payload);
    }
  }

  submitPipelineExecution(
    payload: any,
    hasFiles: boolean,
    files: File[] = []
  ): Observable<any> {
    if (hasFiles && files.length > 0) {
      const url = `${this.baseUrl}/workflows/execute/files`;
      const formData = new FormData();

      formData.append('workflowId', String(payload.pipeLineId));
      formData.append('executionId', String(payload.executionId));
      formData.append('user', String(payload.user));
      formData.append('userInput', JSON.stringify(payload.userInputs));

      files.forEach(file => {
        formData.append('files', file, file.name);
      });

      return this.http.post(url, formData);
    } else {
      const url = `${this.baseUrl}/workflows/execute`;
      return this.http.post(url, payload);
    }
  }

  submitGuardrailExecution(
    payload: any,
  ): Observable<any> {
    return this.http.post(`${this.baseUrl}/instructions/core/execute`, payload);
  }

  // sendForAgentApproval(agentId: string): Observable<any> {
  //     const url = `${this.baseUrl}/agents/IN_REVIEW?agent-id=${agentId}`;

  //     return this.http.put(url, {});
  // }
  // sendForPipelineApproval(workflowId: string | number, token: string): Observable<any> {
  //     const url = `${this.baseUrl}/workflows/IN_REVIEW?workflow-id=${workflowId}`;
  //     return this.http.put(url, {});
  // }

  sendForApproval(entityType: string, id: string | number, token?: string): Observable<any> {
    if (entityType === 'agent') {
      const url = `${this.baseUrl}/agents/IN_REVIEW?agent-id=${id}`;
      return this.http.put(url, {});
    } else if (entityType === 'pipeline') {
      const url = `${this.baseUrl}/workflows/IN_REVIEW?workflow-id=${id}`;
      return this.http.put(url, {});
    } else if (entityType === 'tool') {
      const params = new HttpParams().set('tool-id', id.toString());
      return this.http.put<any>(`${this.baseUrl}/tools/userTools/IN_REVIEW`, {}, { params }).pipe(
        catchError(this.handleError)
      );
    } else if(entityType === 'guardrail'){
      const url = `${this.baseUrl}/guardrails/review?guardrail-id=${id}`;
      return this.http.put(url, {});
    }
    else {
      throw new Error('Unsupported entity type for approval');
    }
  }

  private handleError(error: any): Observable<never> {
    console.error('API Error:', error);
    return throwError(() => error);
  }

  pipelineExecution(
    payload: any,
    files: File[] = []
  ): Observable<any> {
    const url = `${this.baseUrl}/workflows/workflow-executions`;
    const formData = new FormData();

    formData.append('pipelineId', String(payload.pipeLineId));
    formData.append('user', String(payload.user));
    formData.append('userInputs', JSON.stringify(payload.userInputs));
    formData.append('priority', '1');

    files.forEach(file => {
      formData.append('files', file, file.name);
    });

    return this.http.post(url, formData);
  }

  fetchPipelineExecutionStatus(executionId: string): Observable<any> {
    const url = `${this.baseUrl}/workflows/workflow-executions?execution-id=${executionId}`;
    return this.http.get(url); 
  }

  fetchPipelineExecutionResult(executionId: string): Observable<any> {
    const url = `${this.baseUrl}/workflows/workflow-executions/${executionId}/result`;
    return this.http.get(url); 
  }

  fetchPipelineExecutionLogs(executionId: string): Observable<any> {
    const url = `${this.baseUrl}/workflows/workflow-executions/${executionId}/logs`;
    return this.http.get(url); 
  }
}



