import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';

import { environment } from '../environments/environment';

export interface Model {
  id: number;
  name: string;
  model: string;
  description: string;
  type: string;
  date: string;
  aiEngine: string;
  decommissionDate: string;
  practiceArea?: number;
  goodAt?: number[];
  // Additional fields from API
  llmDeploymentName?: string;
  apiKey?: string;
  baseurl?: string;
  awsId?: string;
  azureId?: string;
  apiVersion?: string;
  bedrockModelId?: string;
  awsAccessKey?: string;
  awsSecretKey?: string;
  awsRegion?: string;
  googleAIId?: string;
  gcpModel?: string;
  gcpLocation?: string;
  gcpProjectId?: string;
  vertexAIEndpoint?: string;
  daOpenSourceAIId?: string;
  serviceUrl?: string;
  apiKeyEncoded?: string;
  headerName?: string;
}

interface ModelsResponse {
  models: Model[];
  totalNoOfRecords: number;
}

export interface RefDataItem {
  id: string;
  name: string;
  description?: string;
  image?: string;
  [key: string]: any;
}

export interface RefDataResponse {
  data: RefDataItem[];
  success: boolean;
  message?: string;
}

@Injectable({
  providedIn: 'root',
})
export class ModelsService {
  private readonly baseUrl = environment.baseUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get all models or filter by modelType/modelId
   */
  getModels(modelType?: string, modelId?: number): Observable<Model[]> {
    let params = new HttpParams();

    if (modelType) {
      params = params.set('modelType', modelType);
    }

    if (modelId) {
      params = params.set('modelId', modelId.toString());
    }

    return this.http
      .get<ModelsResponse>(`${this.baseUrl}/models`, { params })
      .pipe(map(response => response.models));
  }

  /**
   * Get model by ID
   */
  getModelById(id: number): Observable<Model> {
    return this.http.get<Model>(`${this.baseUrl}/models/${id}`);
  }

  /**
   * Create a new model
   */
  createModel(modelData: Partial<Model>): Observable<Model> {
    return this.http.post<Model>(`${this.baseUrl}/models/create`, modelData);
  }

  /**
   * Get models by type (Embedding, Generative, etc.)
   */
  getModelsByType(modelType: string): Observable<Model[]> {
    return this.getModels(modelType);
  }

  /**
   * Get reference data by key (e.g., AI_ENGINES)
   */
  getRefData(refKey: string): Observable<RefDataItem[]> {
    const params = new HttpParams().set('ref_key', refKey);

    return this.http
      .get<any>(`${this.baseUrl}/v1/api/admin/ava/force/refdata`, { params })
      .pipe(
        map(response => {
          console.log('🌐 Raw API response:', response);

          // Check if response is a single object (current API format)
          if (response && response.refKey && response.value) {
            return [response]; // Wrap single object in array
          }

          // Check if response has data array (expected format)
          if (response && response.data) {
            return response.data;
          }

          // If response is already an array
          if (Array.isArray(response)) {
            return response;
          }

          console.warn('⚠️ Unexpected API response format:', response);
          return [];
        })
      );
  }

  /**
   * Get AI engines for model types
   */
  getAIEngines(): Observable<RefDataItem[]> {
    return this.getRefData('AI_ENGINES');
  }
}
