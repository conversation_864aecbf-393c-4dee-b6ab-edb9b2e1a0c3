import { Injectable } from '@angular/core';
import { TokenStorageService } from '@shared';

import {
  UserDetailsService,
  UserDetails,
  RealmInfo,
  RealmInfoResponse,
} from './user-details.service';

export interface AppRedirectConfig {
  appName: string;
  baseUrl: string;
  defaultRoute: string;
  localPort?: number;
}

@Injectable({
  providedIn: 'root',
})
export class CentralizedRedirectService {
  // Configuration for all apps
  private readonly appConfigs: AppRedirectConfig[] = [
    {
      appName: 'launchpad',
      baseUrl: '/launchpad',
      defaultRoute: '/launchpad/',
      localPort: 4201,
    },
    {
      appName: 'console',
      baseUrl: '/console',
      defaultRoute: '/console/',
      localPort: 4202,
    },
    {
      appName: 'experience-studio',
      baseUrl: '/experience',
      defaultRoute: '/experience/',
      localPort: 4203,
    },
    {
      appName: 'product-studio',
      baseUrl: '/product',
      defaultRoute: '/product/',
      localPort: 4204,
    },
  ];

  constructor(
    private userDetailsService: UserDetailsService,
    private tokenStorage: TokenStorageService
  ) {}

  /**
   * Check if we're running in local development mode
   */
  private isLocalDevelopment(): boolean {
    const port = window.location.port;

    // For Docker setup: all apps run on localhost without ports
    if (
      window.location.hostname === 'localhost' &&
      (port === '' || port === '80' || port === '443')
    ) {
      return false; // This is Docker setup, not traditional local dev
    }

    // For traditional local development with ports
    return (
      port !== '' && ['4200', '4201', '4202', '4203', '4204'].includes(port)
    );
  }

  /**
   * Get the marketing app URL (local or production)
   */
  private getMarketingLoginUrl(): string {
    if (this.isLocalDevelopment()) {
      // In traditional local dev, marketing runs on port 4200
      return `http://localhost:4200/login`;
    } else {
      // In production or Docker setup, use same origin
      return `${window.location.origin}/login`;
    }
  }

  /**
   * Determine the appropriate redirect URL after successful login
   * Always redirect to default (Elder Wand)
   */
  getPostLoginRedirectUrl(): string {
    return '/launchpad/';
  }

  /**
   * Find app configuration based on URL (used to decide local port)
   */
  private findAppConfigByUrl(url: string): AppRedirectConfig | null {
    const cleanUrl = url.startsWith('/') ? url : `/${url}`;
    const foundConfig = this.appConfigs.find(config =>
      cleanUrl.startsWith(config.baseUrl)
    );
    return foundConfig || null;
  }

  /**
   * Redirect to marketing login (no return URL storage)
   */
  redirectToMarketingLogin(): void {
    if (window.location.pathname.includes('/login')) {
      console.warn('Already on login page, preventing redirect loop');
      return;
    }

    window.location.href = this.getMarketingLoginUrl();
  }

  /**
   * Check if user has valid realm information
   * Returns true if at least one realm has non-null values
   */
  private hasValidRealms(realms: RealmInfo[]): boolean {
    if (!realms || realms.length === 0) {
      return false;
    }

    return realms.some(
      realm =>
        realm.realmId ||
        realm.realmName ||
        realm.orgId ||
        realm.orgName ||
        realm.domainId ||
        realm.domainName ||
        realm.projectId ||
        realm.projectName ||
        realm.teamId ||
        realm.teamName
    );
  }

  handlePostLoginRedirect(apiAuthUrl?: string): void {
    // First check realm info to determine if user needs realm selection
    const userEmail = this.getUserEmail();

    if (!userEmail) {
      console.error('User email not found for realm check');
      this.performRedirect('/realm');
      return;
    }

    this.userDetailsService.getUserRealmInfo(userEmail, apiAuthUrl).subscribe({
      next: (realmResponse: RealmInfoResponse) => {
        if (realmResponse.realms && realmResponse.realms.length > 0) {
          const realm = realmResponse.realms[0];

          if (realm.status === 'APPROVED') {
            // User is approved, proceed with normal redirect flow
            this.handleApprovedUserRedirect(apiAuthUrl);
          } else if (realm.status === 'IN_REVIEW') {
            // User is in review, redirect to realm page to show status
            this.performRedirect('/realm');
          } else {
            // No status or rejected, redirect to realm page for selection
            this.performRedirect('/realm');
          }
        } else {
          // No realm data (empty array) or "Realm Data not found" message, redirect to realm page for selection
          this.performRedirect('/realm');
        }
      },
      error: error => {
        console.error('Failed to check realm info:', error);
        // On error, redirect to realm page
        this.performRedirect('/realm');
      },
    });
  }

  /**
   * Handle redirect for approved users
   */
  private handleApprovedUserRedirect(apiAuthUrl?: string): void {
    this.userDetailsService.getUserDetails(apiAuthUrl).subscribe({
      next: details => {
        // Step 1: Initial redirect based on admin status
        const isAdmin = this.hasAdminRole(details);
        const initialPath = isAdmin ? '/console/' : '/launchpad/';
        this.performRedirect(initialPath);

        // Step 2: Realm validation as a subsequent step for all users
        // Use a microtask to schedule this after the navigation kick-off.
        setTimeout(() => {
          if (!this.hasValidRealms(details.realms)) {
            this.performRedirect('/realm');
          }
        }, 0);
      },
      error: error => {
        // Do not redirect on API failure; log to diagnose the issue
        console.error(
          'Post-login redirect: failed to fetch user details',
          error
        );
        // Intentionally no redirect here so we can see the underlying issue
      },
    });
  }

  /**
   * Get user email from storage
   */
  private getUserEmail(): string | null {
    // Try to get email from various sources
    const emailFromStorage = localStorage.getItem('da_username');
    const emailFromCookie = this.tokenStorage.getCookie('da_username');
    return emailFromStorage || emailFromCookie || null;
  }

  // Use TokenStorageService for all cookie access

  public performRedirect(redirectPath: string): void {
    const redirectUrl = redirectPath || this.getPostLoginRedirectUrl();

    if (this.isLocalDevelopment()) {
      const appConfig = this.findAppConfigByUrl(redirectUrl);
      if (appConfig && appConfig.localPort) {
        window.location.href = `http://localhost:${appConfig.localPort}${redirectUrl}`;
        return;
      }
      window.location.href = `http://localhost:4200${redirectUrl}`;
    } else {
      window.location.href = `${window.location.origin}${redirectUrl}`;
    }
  }

  /**
   * Determine preferred studio route path from user details.
   * Returns a path like '/console/' or '/launchpad/' suitable for local/production mapping.
   */
  getPreferredStudioPath(details: UserDetails | null | undefined): string {
    const fallback = '/launchpad/';
    if (!details) return fallback;

    // Check studio info first
    const studioPath = this.getStudioPath(details);
    if (studioPath) return studioPath;

    // Check admin roles
    if (this.hasAdminRole(details)) return '/console/';

    return fallback;
  }

  private getStudioPath(details: UserDetails): string | null {
    const studios = Array.isArray(details.studioInfo) ? details.studioInfo : [];
    if (studios.length === 0) return null;

    // Check for console/launchpad by name
    const names = studios.map(s => (s.name || '').toLowerCase());
    if (names.includes('console')) return '/console/';
    if (names.includes('launchpad')) return '/launchpad/';

    // Extract from URL
    const first = studios[0];
    if (first?.url) {
      try {
        const url = new URL(first.url);
        return url.pathname.endsWith('/') ? url.pathname : `${url.pathname}/`;
      } catch {
        // ignore parse errors
      }
    }
    return null;
  }

  private hasAdminRole(details: UserDetails): boolean {
    const studios = Array.isArray(details.studioInfo) ? details.studioInfo : [];

    // First check studioInfo for console
    if (studios.length > 0) {
      return studios.some(s => s?.name?.toLowerCase() === 'console');
    }

    // Fallback: if studioInfo is empty, check roles for admin
    const roles = Array.isArray(details.roles) ? details.roles : [];
    return roles.some(r => r?.name?.toLowerCase().includes('admin'));
  }
}
