import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { environment } from '../environments/environment';
import {
  Artifact,
  ArtifactType,
  Agent,
  UserTool,
  Workflow,
} from '../models/artifact.model';

/**
 * @deprecated This service is deprecated and will be removed in a future version.
 * Use the specialized services instead:
 * - AgentService for agent operations
 * - ToolService for tool operations
 * - WorkflowService for workflow operations
 *
 * This service is maintained only for backward compatibility.
 *
 * @example
 * // ❌ Deprecated usage
 * constructor(private artifactService: ArtifactService) {}
 * this.artifactService.get('123');
 *
 * // ✅ Recommended usage
 * constructor(private agentService: AgentService) {}
 * this.agentService.getById(123);
 */
@Injectable({ providedIn: 'root' })
export class ArtifactService {
  private readonly baseUrl = environment.baseUrl;

  constructor(private http: HttpClient) {}

  // ==================== AGENT APIs ====================

  /**
   * Get agent by ID
   * @deprecated Use AgentService.getById() instead
   */
  getAgent(id: number): Observable<Agent> {
    return this.http
      .get<Agent>(`${this.baseUrl}/agents/${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get all agents with optional filtering
   * @deprecated Use AgentService.getAll() instead
   */
  getAgents(params?: {
    teamId?: number;
    status?: string;
    createdBy?: number;
  }): Observable<Agent[]> {
    let httpParams = new HttpParams();
    if (params?.teamId)
      httpParams = httpParams.set('teamId', params.teamId.toString());
    if (params?.status) httpParams = httpParams.set('status', params.status);
    if (params?.createdBy)
      httpParams = httpParams.set('createdBy', params.createdBy.toString());

    return this.http
      .get<Agent[]>(`${this.baseUrl}/agents`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  /**
   * Create a new agent
   * @deprecated Use AgentService.create() instead
   */
  createAgent(agent: Omit<Agent, 'id'>): Observable<Agent> {
    return this.http
      .post<Agent>(`${this.baseUrl}/agents`, agent)
      .pipe(catchError(this.handleError));
  }

  /**
   * Update an existing agent
   * @deprecated Use AgentService.update() instead
   */
  updateAgent(id: number, agent: Partial<Agent>): Observable<Agent> {
    return this.http
      .put<Agent>(`${this.baseUrl}/agents/${id}`, agent)
      .pipe(catchError(this.handleError));
  }

  /**
   * Submit agent for review
   * @deprecated Use AgentService.submitForReview() instead
   */
  submitAgentForReview(id: number): Observable<Agent> {
    return this.http
      .put<Agent>(`${this.baseUrl}/agents/review`, { id })
      .pipe(catchError(this.handleError));
  }

  /**
   * Approve agent
   * @deprecated Use AgentService.approve() instead
   */
  approveAgent(id: number): Observable<Agent> {
    return this.http
      .put<Agent>(`${this.baseUrl}/agents/approve`, { id })
      .pipe(catchError(this.handleError));
  }

  /**
   * Reject agent
   * @deprecated Use AgentService.reject() instead
   */
  rejectAgent(id: number): Observable<Agent> {
    return this.http
      .put<Agent>(`${this.baseUrl}/agents/reject`, { id })
      .pipe(catchError(this.handleError));
  }

  // ==================== TOOL APIs ====================

  /**
   * Get tool by ID
   * @deprecated Use ToolService.getById() instead
   */
  getTool(id: number): Observable<UserTool> {
    return this.http
      .get<UserTool>(`${this.baseUrl}/tools/userTools/${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get all tools with optional filtering
   * @deprecated Use ToolService.getAll() instead
   */
  getTools(params?: {
    teamId?: number;
    status?: string;
    createdBy?: number;
  }): Observable<UserTool[]> {
    let httpParams = new HttpParams();
    if (params?.teamId)
      httpParams = httpParams.set('teamId', params.teamId.toString());
    if (params?.status) httpParams = httpParams.set('status', params.status);
    if (params?.createdBy)
      httpParams = httpParams.set('createdBy', params.createdBy.toString());

    return this.http
      .get<
        UserTool[]
      >(`${this.baseUrl}/tools/userTools`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  /**
   * Create a new tool
   * @deprecated Use ToolService.create() instead
   */
  createTool(tool: Omit<UserTool, 'id'>): Observable<UserTool> {
    return this.http
      .post<UserTool>(`${this.baseUrl}/tools/userTools`, tool)
      .pipe(catchError(this.handleError));
  }

  /**
   * Update an existing tool
   * @deprecated Use ToolService.update() instead
   */
  updateTool(id: number, tool: Partial<UserTool>): Observable<UserTool> {
    return this.http
      .put<UserTool>(`${this.baseUrl}/tools/userTools`, { id, ...tool })
      .pipe(catchError(this.handleError));
  }

  /**
   * Submit tool for review
   * @deprecated Use ToolService.submitForReview() instead
   */
  submitToolForReview(id: number): Observable<UserTool> {
    return this.http
      .put<UserTool>(`${this.baseUrl}/tools/userTools/review`, { id })
      .pipe(catchError(this.handleError));
  }

  /**
   * Approve tool
   * @deprecated Use ToolService.approve() instead
   */
  approveTool(id: number): Observable<UserTool> {
    return this.http
      .put<UserTool>(`${this.baseUrl}/tools/userTools/approve`, { id })
      .pipe(catchError(this.handleError));
  }

  /**
   * Reject tool
   * @deprecated Use ToolService.reject() instead
   */
  rejectTool(id: number): Observable<UserTool> {
    return this.http
      .put<UserTool>(`${this.baseUrl}/tools/userTools/reject`, { id })
      .pipe(catchError(this.handleError));
  }

  // ==================== WORKFLOW APIs ====================

  /**
   * Get workflow by ID
   * @deprecated Use WorkflowService.getById() instead
   */
  getWorkflow(id: number): Observable<Workflow> {
    return this.http
      .get<Workflow>(`${this.baseUrl}/workflows/${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get all workflows with optional filtering
   * @deprecated Use WorkflowService.getAll() instead
   */
  getWorkflows(params?: {
    teamId?: number;
    status?: string;
    createdBy?: number;
  }): Observable<Workflow[]> {
    let httpParams = new HttpParams();
    if (params?.teamId)
      httpParams = httpParams.set('teamId', params.teamId.toString());
    if (params?.status) httpParams = httpParams.set('status', params.status);
    if (params?.createdBy)
      httpParams = httpParams.set('createdBy', params.createdBy.toString());

    return this.http
      .get<Workflow[]>(`${this.baseUrl}/workflows`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  /**
   * Create a new workflow
   * @deprecated Use WorkflowService.create() instead
   */
  createWorkflow(workflow: Omit<Workflow, 'id'>): Observable<Workflow> {
    return this.http
      .post<Workflow>(`${this.baseUrl}/workflows`, workflow)
      .pipe(catchError(this.handleError));
  }

  /**
   * Update an existing workflow
   * @deprecated Use WorkflowService.update() instead
   */
  updateWorkflow(
    id: number,
    workflow: Partial<Workflow>
  ): Observable<Workflow> {
    return this.http
      .put<Workflow>(`${this.baseUrl}/workflows`, { id, ...workflow })
      .pipe(catchError(this.handleError));
  }

  /**
   * Submit workflow for review
   * @deprecated Use WorkflowService.submitForReview() instead
   */
  submitWorkflowForReview(id: number): Observable<Workflow> {
    return this.http
      .put<Workflow>(
        `${this.baseUrl}/workflows/IN_REVIEW?workflow-id=${id}`,
        {}
      )
      .pipe(catchError(this.handleError));
  }

  /**
   * Approve workflow
   * @deprecated Use WorkflowService.approve() instead
   */
  approveWorkflow(id: number): Observable<Workflow> {
    return this.http
      .put<Workflow>(`${this.baseUrl}/workflows/APPROVED?workflow-id=${id}`, {})
      .pipe(catchError(this.handleError));
  }

  /**
   * Reject workflow
   * @deprecated Use WorkflowService.reject() instead
   */
  rejectWorkflow(id: number): Observable<Workflow> {
    return this.http
      .put<Workflow>(`${this.baseUrl}/workflows/REJECTED?workflow-id=${id}`, {})
      .pipe(catchError(this.handleError));
  }

  // ==================== GENERIC ARTIFACT APIs (Backward Compatibility) ====================

  /**
   * Generic get method for backward compatibility
   * @deprecated Use the specialized service methods instead:
   * - AgentService.getById() for agents
   * - ToolService.getById() for tools
   * - WorkflowService.getById() for workflows
   *
   * This method will be removed in a future version.
   */
  get(id: string): Observable<Artifact> {
    // Try to determine type from the ID or use a default
    return this.getAgent(parseInt(id)).pipe(
      map(agent => this.mapToArtifact(agent, 'agent')),
      catchError(() =>
        this.getTool(parseInt(id)).pipe(
          map(tool => this.mapToArtifact(tool, 'tool')),
          catchError(() =>
            this.getWorkflow(parseInt(id)).pipe(
              map(workflow => this.mapToArtifact(workflow, 'workflow')),
              catchError(() =>
                throwError(() => new Error(`Artifact with id ${id} not found`))
              )
            )
          )
        )
      )
    );
  }

  /**
   * Generic create method for backward compatibility
   * @deprecated Use the specialized service methods instead:
   * - AgentService.create() for agents
   * - ToolService.create() for tools
   * - WorkflowService.create() for workflows
   *
   * This method will be removed in a future version.
   */
  create(config: { type: ArtifactType; metadata: any }): Observable<Artifact> {
    switch (config.type) {
      case 'agent':
        return this.createAgent(config.metadata).pipe(
          map(agent => this.mapToArtifact(agent, 'agent'))
        );
      case 'tool':
        return this.createTool(config.metadata).pipe(
          map(tool => this.mapToArtifact(tool, 'tool'))
        );
      case 'workflow':
        return this.createWorkflow(config.metadata).pipe(
          map(workflow => this.mapToArtifact(workflow, 'workflow'))
        );
      default:
        return throwError(
          () => new Error(`Unsupported artifact type: ${config.type}`)
        );
    }
  }

  /**
   * Generic update method for backward compatibility
   * @deprecated Use the specialized service methods instead:
   * - AgentService.update() for agents
   * - ToolService.update() for tools
   * - WorkflowService.update() for workflows
   *
   * This method will be removed in a future version.
   */
  update(
    id: string,
    payload: { type: ArtifactType; metadata: any }
  ): Observable<Artifact> {
    const numericId = parseInt(id);
    switch (payload.type) {
      case 'agent':
        return this.updateAgent(numericId, payload.metadata).pipe(
          map(agent => this.mapToArtifact(agent, 'agent'))
        );
      case 'tool':
        return this.updateTool(numericId, payload.metadata).pipe(
          map(tool => this.mapToArtifact(tool, 'tool'))
        );
      case 'workflow':
        return this.updateWorkflow(numericId, payload.metadata).pipe(
          map(workflow => this.mapToArtifact(workflow, 'workflow'))
        );
      default:
        return throwError(
          () => new Error(`Unsupported artifact type: ${payload.type}`)
        );
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Map specific artifact types to generic Artifact interface
   * @deprecated This is an internal utility method that will be removed
   */
  private mapToArtifact<T>(data: T, type: ArtifactType): Artifact {
    return {
      id: (data as any).id?.toString() || '',
      type,
      metadata: data,
      state: this.mapStatusToFlag((data as any).status),
      createdAt: (data as any).createdAt,
      updatedAt: (data as any).modifiedAt || (data as any).updatedAt,
    };
  }

  /**
   * Map status strings to ArtifactFlag enum
   * @deprecated This is an internal utility method that will be removed
   */
  private mapStatusToFlag(status: string): any {
    switch (status) {
      case 'CREATED':
      case 'DRAFTED':
        return 'draft';
      case 'IN_REVIEW':
        return 'pending';
      case 'APPROVED':
        return 'approved';
      case 'REJECTED':
        return 'rejected';
      default:
        return 'draft';
    }
  }

  /**
   * Generic validation method
   * @deprecated Use the specialized service methods instead:
   * - AgentService.validate() for agents
   * - ToolService.validate() for tools
   * - WorkflowService.validate() for workflows
   *
   * This method will be removed in a future version.
   */
  validate(id: string, type: ArtifactType): Observable<any> {
    const numericId = parseInt(id);
    switch (type) {
      case 'agent':
        return this.http
          .post(`${this.baseUrl}/agents/${numericId}/validate`, {})
          .pipe(catchError(this.handleError));
      case 'tool':
        return this.http
          .post(`${this.baseUrl}/tools/userTools/${numericId}/validate`, {})
          .pipe(catchError(this.handleError));
      case 'workflow':
        return this.http
          .post(`${this.baseUrl}/workflows/${numericId}/validate`, {})
          .pipe(catchError(this.handleError));
      default:
        return throwError(
          () => new Error(`Unsupported artifact type: ${type}`)
        );
    }
  }

  /**
   * Generic test method
   * @deprecated Use the specialized service methods instead:
   * - AgentService.test() for agents
   * - ToolService.test() for tools
   * - WorkflowService.test() for workflows
   *
   * This method will be removed in a future version.
   */
  test(id: string, type: ArtifactType, userInputs?: any): Observable<any> {
    const numericId = parseInt(id);
    const payload = {
      executionId: '',
      id: numericId,
      type,
      user: 'current',
      userInputs,
    };

    switch (type) {
      case 'agent':
        return this.http
          .post(`${this.baseUrl}/agents/${numericId}/test`, payload)
          .pipe(catchError(this.handleError));
      case 'tool':
        return this.http
          .post(`${this.baseUrl}/tools/userTools/${numericId}/test`, payload)
          .pipe(catchError(this.handleError));
      case 'workflow':
        return this.http
          .post(`${this.baseUrl}/workflows/${numericId}/test`, payload)
          .pipe(catchError(this.handleError));
      default:
        return throwError(
          () => new Error(`Unsupported artifact type: ${type}`)
        );
    }
  }

  /**
   * Generic submit for approval method
   * @deprecated Use the specialized service methods instead:
   * - AgentService.submitForReview() for agents
   * - ToolService.submitForReview() for tools
   * - WorkflowService.submitForReview() for workflows
   *
   * This method will be removed in a future version.
   */
  submitForApproval(id: string, type: ArtifactType): Observable<any> {
    const numericId = parseInt(id);
    switch (type) {
      case 'agent':
        return this.submitAgentForReview(numericId);
      case 'tool':
        return this.submitToolForReview(numericId);
      case 'workflow':
        return this.submitWorkflowForReview(numericId);
      default:
        return throwError(
          () => new Error(`Unsupported artifact type: ${type}`)
        );
    }
  }

  /**
   * Generic approve method
   * @deprecated Use the specialized service methods instead:
   * - AgentService.approve() for agents
   * - ToolService.approve() for tools
   * - WorkflowService.approve() for workflows
   *
   * This method will be removed in a future version.
   */
  approve(id: string, type: ArtifactType): Observable<any> {
    const numericId = parseInt(id);
    switch (type) {
      case 'agent':
        return this.approveAgent(numericId);
      case 'tool':
        return this.approveTool(numericId);
      case 'workflow':
        return this.approveWorkflow(numericId);
      default:
        return throwError(
          () => new Error(`Unsupported artifact type: ${type}`)
        );
    }
  }

  /**
   * Generic reject method
   * @deprecated Use the specialized service methods instead:
   * - AgentService.reject() for agents
   * - ToolService.reject() for tools
   * - WorkflowService.reject() for workflows
   *
   * This method will be removed in a future version.
   */
  reject(id: string, type: ArtifactType): Observable<any> {
    const numericId = parseInt(id);
    switch (type) {
      case 'agent':
        return this.rejectAgent(numericId);
      case 'tool':
        return this.rejectTool(numericId);
      case 'workflow':
        return this.rejectWorkflow(numericId);
      default:
        return throwError(
          () => new Error(`Unsupported artifact type: ${type}`)
        );
    }
  }

  /**
   * Generic playground run method
   * @deprecated Use the specialized service methods instead:
   * - AgentService.playgroundRun() for agents
   * - ToolService.playgroundRun() for tools
   * - WorkflowService.playgroundRun() for workflows
   *
   * This method will be removed in a future version.
   */
  playgroundRun(id: string, type: ArtifactType): Observable<any> {
    const numericId = parseInt(id);
    const payload = { inputs: { id: numericId, type } };

    switch (type) {
      case 'agent':
        return this.http
          .post(`${this.baseUrl}/playground/agents/run`, payload)
          .pipe(catchError(this.handleError));
      case 'tool':
        return this.http
          .post(`${this.baseUrl}/playground/tools/run`, payload)
          .pipe(catchError(this.handleError));
      case 'workflow':
        return this.http
          .post(`${this.baseUrl}/playground/workflows/run`, payload)
          .pipe(catchError(this.handleError));
      default:
        return throwError(
          () => new Error(`Unsupported artifact type: ${type}`)
        );
    }
  }

  // ==================== ERROR HANDLING ====================

  /**
   * Centralized error handling
   * @deprecated This is an internal utility method that will be removed
   */
  private handleError(error: any): Observable<never> {
    let errorMessage = 'An error occurred';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.status) {
      errorMessage = `HTTP ${error.status}: ${error.statusText}`;
    }

    console.error('ArtifactService Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
