import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, of, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';

import {
  LibraryItem,
  LibraryType,
  LibraryFilters,
} from '../components/interfaces';

export interface LibraryServiceConfig {
  readonly apiUrl: string;
  readonly cacheTimeout?: number; // in milliseconds
  readonly enableCaching?: boolean;
  readonly enableRealTimeUpdates?: boolean;
}

export interface CreateLibraryItemRequest {
  readonly title: string;
  readonly description: string;
  readonly icon: string;
  readonly iconColor: string;
  readonly category?: string;
  readonly tags?: string[];
  readonly isActive?: boolean;
}

export interface UpdateLibraryItemRequest {
  readonly id: string;
  readonly title?: string;
  readonly description?: string;
  readonly icon?: string;
  readonly iconColor?: string;
  readonly category?: string;
  readonly tags?: string[];
  readonly isActive?: boolean;
  readonly usageCount?: number;
}

export interface LibrarySearchResult {
  readonly items: LibraryItem[];
  readonly totalCount: number;
  readonly hasMore: boolean;
  readonly filters: LibraryFilters;
}

@Injectable({
  providedIn: 'root',
})
export class LibraryService {
  private readonly configs = new Map<LibraryType, LibraryServiceConfig>();
  private readonly dataSubjects = new Map<
    LibraryType,
    BehaviorSubject<LibraryItem[]>
  >();
  private readonly loadingSubjects = new Map<
    LibraryType,
    BehaviorSubject<boolean>
  >();
  private readonly errorSubjects = new Map<
    LibraryType,
    BehaviorSubject<Error | null>
  >();
  private readonly cache = new Map<
    LibraryType,
    { data: LibraryItem[]; timestamp: number }
  >();

  constructor(private http: HttpClient) {
    this.initializeDefaultConfigs();
    this.initializeSubjects();
  }

  // Configuration methods
  configureLibrary(type: LibraryType, config: LibraryServiceConfig): void {
    this.configs.set(type, config);
  }

  private initializeDefaultConfigs(): void {
    const defaultConfig: LibraryServiceConfig = {
      apiUrl: '/api/library',
      cacheTimeout: 5 * 60 * 1000, // 5 minutes
      enableCaching: true,
      enableRealTimeUpdates: false,
    };

    this.configureLibrary('tools', { ...defaultConfig, apiUrl: '/api/tools' });
    this.configureLibrary('knowledge', {
      ...defaultConfig,
      apiUrl: '/api/knowledge',
    });
    this.configureLibrary('guardrails', {
      ...defaultConfig,
      apiUrl: '/api/guardrails',
    });
  }

  private initializeSubjects(): void {
    const types: LibraryType[] = ['tools', 'knowledge', 'guardrails'];

    types.forEach(type => {
      this.dataSubjects.set(type, new BehaviorSubject<LibraryItem[]>([]));
      this.loadingSubjects.set(type, new BehaviorSubject<boolean>(false));
      this.errorSubjects.set(type, new BehaviorSubject<Error | null>(null));
    });
  }

  // Observable getters
  getItems$(type: LibraryType): Observable<LibraryItem[]> {
    return this.dataSubjects.get(type)!.asObservable();
  }

  getLoading$(type: LibraryType): Observable<boolean> {
    return this.loadingSubjects.get(type)!.asObservable();
  }

  getError$(type: LibraryType): Observable<Error | null> {
    return this.errorSubjects.get(type)!.asObservable();
  }

  // CRUD operations
  getItems(
    type: LibraryType,
    filters?: LibraryFilters
  ): Observable<LibraryItem[]> {
    const config = this.configs.get(type);
    if (!config) {
      return throwError(
        () => new Error(`No configuration found for library type: ${type}`)
      );
    }

    // Check cache first
    if (config.enableCaching && this.isCacheValid(type)) {
      const cachedData = this.cache.get(type)!.data;
      this.dataSubjects.get(type)!.next(cachedData);
      return of(cachedData);
    }

    this.setLoading(type, true);
    this.setError(type, null);

    const params = this.buildQueryParams(filters);
    const url = `${config.apiUrl}${params}`;

    return this.http.get<LibraryItem[]>(url).pipe(
      map(items => this.transformItems(items)),
      tap(items => {
        this.updateCache(type, items);
        this.dataSubjects.get(type)!.next(items);
        this.setLoading(type, false);
      }),
      catchError(error => {
        this.setError(type, error);
        this.setLoading(type, false);
        return throwError(() => error);
      })
    );
  }

  getItemById(type: LibraryType, id: string): Observable<LibraryItem> {
    const config = this.configs.get(type);
    if (!config) {
      return throwError(
        () => new Error(`No configuration found for library type: ${type}`)
      );
    }

    return this.http.get<LibraryItem>(`${config.apiUrl}/${id}`).pipe(
      map(item => this.transformItem(item)),
      catchError(error => throwError(() => error))
    );
  }

  createItem(
    type: LibraryType,
    request: CreateLibraryItemRequest
  ): Observable<LibraryItem> {
    const config = this.configs.get(type);
    if (!config) {
      return throwError(
        () => new Error(`No configuration found for library type: ${type}`)
      );
    }

    this.setLoading(type, true);
    this.setError(type, null);

    const itemData = this.prepareCreateRequest(request);

    return this.http.post<LibraryItem>(config.apiUrl, itemData).pipe(
      map(item => this.transformItem(item)),
      tap(newItem => {
        const currentItems = this.dataSubjects.get(type)!.value;
        this.dataSubjects.get(type)!.next([...currentItems, newItem]);
        this.invalidateCache(type);
        this.setLoading(type, false);
      }),
      catchError(error => {
        this.setError(type, error);
        this.setLoading(type, false);
        return throwError(() => error);
      })
    );
  }

  updateItem(
    type: LibraryType,
    request: UpdateLibraryItemRequest
  ): Observable<LibraryItem> {
    const config = this.configs.get(type);
    if (!config) {
      return throwError(
        () => new Error(`No configuration found for library type: ${type}`)
      );
    }

    this.setLoading(type, true);
    this.setError(type, null);

    const itemData = this.prepareUpdateRequest(request);

    return this.http
      .put<LibraryItem>(`${config.apiUrl}/${request.id}`, itemData)
      .pipe(
        map(item => this.transformItem(item)),
        tap(updatedItem => {
          const currentItems = this.dataSubjects.get(type)!.value;
          const updatedItems = currentItems.map(item =>
            item.id === updatedItem.id ? updatedItem : item
          );
          this.dataSubjects.get(type)!.next(updatedItems);
          this.invalidateCache(type);
          this.setLoading(type, false);
        }),
        catchError(error => {
          this.setError(type, error);
          this.setLoading(type, false);
          return throwError(() => error);
        })
      );
  }

  deleteItem(type: LibraryType, id: string): Observable<void> {
    const config = this.configs.get(type);
    if (!config) {
      return throwError(
        () => new Error(`No configuration found for library type: ${type}`)
      );
    }

    this.setLoading(type, true);
    this.setError(type, null);

    return this.http.delete<void>(`${config.apiUrl}/${id}`).pipe(
      tap(() => {
        const currentItems = this.dataSubjects.get(type)!.value;
        const filteredItems = currentItems.filter(item => item.id !== id);
        this.dataSubjects.get(type)!.next(filteredItems);
        this.invalidateCache(type);
        this.setLoading(type, false);
      }),
      catchError(error => {
        this.setError(type, error);
        this.setLoading(type, false);
        return throwError(() => error);
      })
    );
  }

  // Search and filter operations
  searchItems(
    type: LibraryType,
    query: string,
    filters?: LibraryFilters
  ): Observable<LibrarySearchResult> {
    const config = this.configs.get(type);
    if (!config) {
      return throwError(
        () => new Error(`No configuration found for library type: ${type}`)
      );
    }

    const searchParams = this.buildSearchParams(query, filters);
    const url = `${config.apiUrl}/search${searchParams}`;

    return this.http.get<LibrarySearchResult>(url).pipe(
      map(result => ({
        ...result,
        items: result.items.map(item => this.transformItem(item)),
      })),
      catchError(error => throwError(() => error))
    );
  }

  // Utility methods
  getCurrentItems(type: LibraryType): LibraryItem[] {
    return this.dataSubjects.get(type)?.value || [];
  }

  clearCache(type?: LibraryType): void {
    if (type) {
      this.cache.delete(type);
    } else {
      this.cache.clear();
    }
  }

  refreshItems(type: LibraryType): Observable<LibraryItem[]> {
    this.invalidateCache(type);
    return this.getItems(type);
  }

  // Private helper methods
  private setLoading(type: LibraryType, loading: boolean): void {
    this.loadingSubjects.get(type)?.next(loading);
  }

  private setError(type: LibraryType, error: Error | null): void {
    this.errorSubjects.get(type)?.next(error);
  }

  private isCacheValid(type: LibraryType): boolean {
    const cached = this.cache.get(type);
    if (!cached) return false;

    const config = this.configs.get(type);
    const timeout = config?.cacheTimeout || 5 * 60 * 1000;

    return Date.now() - cached.timestamp < timeout;
  }

  private updateCache(type: LibraryType, items: LibraryItem[]): void {
    const config = this.configs.get(type);
    if (config?.enableCaching) {
      this.cache.set(type, {
        data: items,
        timestamp: Date.now(),
      });
    }
  }

  private invalidateCache(type: LibraryType): void {
    this.cache.delete(type);
  }

  private buildQueryParams(filters?: LibraryFilters): string {
    if (!filters) return '';

    const params = new URLSearchParams();

    if (filters.category) params.append('category', filters.category);
    if (filters.showActiveOnly) params.append('active', 'true');
    if (filters.sortBy) params.append('sortBy', filters.sortBy);
    if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);
    if (filters.tags?.length) {
      filters.tags.forEach(tag => params.append('tags', tag));
    }

    return params.toString() ? `?${params.toString()}` : '';
  }

  private buildSearchParams(query: string, filters?: LibraryFilters): string {
    const params = new URLSearchParams();
    params.append('q', query);

    if (filters) {
      const filterParams = this.buildQueryParams(filters);
      if (filterParams) {
        params.append('filters', filterParams.substring(1)); // Remove the '?'
      }
    }

    return `?${params.toString()}`;
  }

  private transformItems(items: LibraryItem[]): LibraryItem[] {
    return items.map(item => this.transformItem(item));
  }

  private transformItem(item: LibraryItem): LibraryItem {
    // Add type-specific transformations here
    return {
      ...item,
      // Ensure required fields have defaults
      isActive: item.isActive ?? true,
      usageCount: item.usageCount ?? 0,
      tags: item.tags ?? [],
    };
  }

  private prepareCreateRequest(request: CreateLibraryItemRequest): any {
    return {
      ...request,
      isActive: request.isActive ?? true,
      tags: request.tags ?? [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  private prepareUpdateRequest(request: UpdateLibraryItemRequest): any {
    return {
      ...request,
      updatedAt: new Date().toISOString(),
    };
  }
}
