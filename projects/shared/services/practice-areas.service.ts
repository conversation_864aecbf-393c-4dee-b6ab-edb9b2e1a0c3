import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../environments/environment';

export interface PracticeArea {
  id: number;
  name: string;
}

export interface PracticeAreasResponse {
  message: string;
  practiceAreaList: PracticeArea[];
}

@Injectable({
  providedIn: 'root',
})
export class PracticeAreasService {
  private readonly apiUrl = `${environment.consoleApiAuthUrl}/practice-areas`;

  constructor(private http: HttpClient) {}

  /**
   * Get all practice areas
   */
  getPracticeAreas(): Observable<PracticeArea[]> {
    return this.http.get<PracticeAreasResponse>(this.apiUrl).pipe(
      map(response => response.practiceAreaList),
      catchError(error => {
        console.error('Error fetching practice areas:', error);
        throw error;
      })
    );
  }

  /**
   * Get practice area by ID
   */
  getPracticeAreaById(id: number): Observable<PracticeArea | undefined> {
    return this.getPracticeAreas().pipe(
      map(areas => areas.find(area => area.id === id))
    );
  }

  /**
   * Get practice area by IDs
   */
  getPracticeAreasByIds(ids: number[]): Observable<PracticeArea[]> {
    return this.getPracticeAreas().pipe(
      map(areas => areas.filter(area => ids.includes(area.id)))
    );
  }
}
