# Specialized Artifact Services Usage Examples

This document provides comprehensive examples of how to use the new specialized artifact services for agents, tools, and workflows. These services provide better type safety, performance, and maintainability compared to the deprecated generic ArtifactService.

## Table of Contents
1. [Agent Service Usage](#agent-service-usage)
2. [Tool Service Usage](#tool-service-usage)
3. [Workflow Service Usage](#workflow-service-usage)
4. [Error Handling](#error-handling)
5. [Best Practices](#best-practices)
6. [Configuration](#configuration)

## Agent Service Usage

### Basic CRUD Operations

```typescript
import { Component, OnInit } from '@angular/core';
import { AgentService } from '@shared/services/agent.service';
import { Agent, AgentConfig } from '@shared/models/artifact.model';

@Component({
  selector: 'app-agent-manager',
  template: '...'
})
export class AgentManagerComponent implements OnInit {
  agents: Agent[] = [];
  
  constructor(private agentService: AgentService) {}

  ngOnInit(): void {
    this.loadAgents();
  }

  // Load all agents for a team
  loadAgents(): void {
    this.agentService.getAll({ teamId: 23, status: 'APPROVED' })
      .subscribe({
        next: (agents) => {
          this.agents = agents;
        },
        error: (error) => {
          console.error('Failed to load agents:', error);
        }
      });
  }

  // Create a new agent
  createAgent(): void {
    const agentConfig: AgentConfig = {
      temperature: 0.3,
      topP: 0.95,
      maxToken: '4000',
      levelId: 4,
      maxRpm: 5,
      maxExecutionTime: 5,
      allowDelegation: true,
      allowCodeExecution: true,
      isSafeCodeExecution: true,
      toolRef: [3, 5]
    };

    const newAgent: Omit<Agent, 'id'> = {
      agentDetails: 'Python_Data_Analyst',
      name: 'Data Analysis Agent',
      role: 'Python Developer',
      goal: 'Analyze data and provide insights using Python',
      backstory: 'You are an experienced data analyst with strong Python skills.',
      description: 'A specialized agent for data analysis tasks',
      expectedOutput: 'Comprehensive data analysis reports with insights',
      userTools: [{ toolId: 1 }, { toolId: 2 }],
      kbIds: [2, 3],
      modelId: 2,
      agentConfigs: agentConfig,
      teamId: 23,
      status: 'CREATED'
    };

    this.agentService.create(newAgent)
      .subscribe({
        next: (agent) => {
          console.log('Agent created successfully:', agent);
          this.loadAgents(); // Refresh the list
        },
        error: (error) => {
          console.error('Failed to create agent:', error);
        }
      });
  }

  // Update an existing agent
  updateAgent(agentId: number): void {
    const updates = {
      name: 'Updated Data Analysis Agent',
      temperature: 0.5
    };

    this.agentService.update(agentId, updates)
      .subscribe({
        next: (updatedAgent) => {
          console.log('Agent updated successfully:', updatedAgent);
          this.loadAgents(); // Refresh the list
        },
        error: (error) => {
          console.error('Failed to update agent:', error);
        }
      });
  }

  // Submit agent for review
  submitForReview(agentId: number): void {
    this.agentService.submitForReview(agentId)
      .subscribe({
        next: (agent) => {
          console.log('Agent submitted for review:', agent);
          this.loadAgents(); // Refresh the list
        },
        error: (error) => {
          console.error('Failed to submit agent for review:', error);
        }
      });
  }

  // Test agent execution
  testAgent(agentId: number, userInputs?: any): void {
    this.agentService.test(agentId, userInputs)
      .subscribe({
        next: (result) => {
          console.log('Agent test result:', result);
        },
        error: (error) => {
          console.error('Agent test failed:', error);
        }
      });
  }

  // Run agent in playground
  runInPlayground(agentId: number): void {
    this.agentService.playgroundRun(agentId, { input: 'test data' })
      .subscribe({
        next: (result) => {
          console.log('Playground execution result:', result);
        },
        error: (error) => {
          console.error('Playground execution failed:', error);
        }
      });
  }

  // Get agent statistics
  getAgentStats(agentId: number): void {
    this.agentService.getStats(agentId)
      .subscribe({
        next: (stats) => {
          console.log('Agent statistics:', stats);
        },
        error: (error) => {
          console.error('Failed to get agent stats:', error);
        }
      });
  }

  // Get agent execution history
  getExecutionHistory(agentId: number): void {
    this.agentService.getExecutionHistory(agentId, { 
      page: 1, 
      limit: 10,
      startDate: '2024-01-01',
      endDate: '2024-12-31'
    })
      .subscribe({
        next: (history) => {
          console.log('Execution history:', history);
        },
        error: (error) => {
          console.error('Failed to get execution history:', error);
        }
      });
  }
}
```

## Tool Service Usage

### Basic CRUD Operations

```typescript
import { Component, OnInit } from '@angular/core';
import { ToolService } from '@shared/services/tool.service';
import { UserTool, ToolConfig } from '@shared/models/artifact.model';

@Component({
  selector: 'app-tool-manager',
  template: '...'
})
export class ToolManagerComponent implements OnInit {
  tools: UserTool[] = [];
  
  constructor(private toolService: ToolService) {}

  ngOnInit(): void {
    this.loadTools();
  }

  // Load all tools for a team
  loadTools(): void {
    this.toolService.getAll({ teamId: 23, status: 'APPROVED' })
      .subscribe({
        next: (tools) => {
          this.tools = tools;
        },
        error: (error) => {
          console.error('Failed to load tools:', error);
        }
      });
  }

  // Create a new tool
  createTool(): void {
    const toolConfig: ToolConfig = {
      runtime: 'Python 3.10',
      libraries: ['pandas', 'numpy', 'matplotlib'],
      maxRows: 10000
    };

    const newTool: Omit<UserTool, 'id'> = {
      toolName: 'Advanced Data Analyzer',
      toolDescription: 'Advanced data analysis tool with visualization capabilities',
      toolConfig: toolConfig,
      version: 1,
      status: 'CREATED',
      teamId: 23
    };

    this.toolService.create(newTool)
      .subscribe({
        next: (tool) => {
          console.log('Tool created successfully:', tool);
          this.loadTools(); // Refresh the list
        },
        error: (error) => {
          console.error('Failed to create tool:', error);
        }
      });
  }

  // Search tools
  searchTools(query: string): void {
    this.toolService.search(query, { teamId: 23 })
      .subscribe({
        next: (tools) => {
          this.tools = tools;
        },
        error: (error) => {
          console.error('Tool search failed:', error);
        }
      });
  }

  // Get tools by category
  getToolsByCategory(category: string): void {
    this.toolService.getByCategory(category)
      .subscribe({
        next: (tools) => {
          this.tools = tools;
        },
        error: (error) => {
          console.error('Failed to get tools by category:', error);
        }
      });
  }

  // Test tool execution
  testTool(toolId: number, userInputs?: any): void {
    this.toolService.test(toolId, userInputs)
      .subscribe({
        next: (result) => {
          console.log('Tool test result:', result);
        },
        error: (error) => {
          console.error('Tool test failed:', error);
        }
      });
  }

  // Get tool configuration template
  getConfigTemplate(): void {
    const template = this.toolService.getConfigTemplate();
    console.log('Tool config template:', template);
  }

  // Clone tool
  cloneTool(toolId: number, newName: string): void {
    this.toolService.clone(toolId, newName)
      .subscribe({
        next: (clonedTool) => {
          console.log('Tool cloned successfully:', clonedTool);
          this.loadTools(); // Refresh the list
        },
        error: (error) => {
          console.error('Failed to clone tool:', error);
        }
      });
  }

  // Export tool configuration
  exportTool(toolId: number): void {
    this.toolService.export(toolId)
      .subscribe({
        next: (config) => {
          console.log('Tool exported:', config);
          // Handle download or further processing
        },
        error: (error) => {
          console.error('Failed to export tool:', error);
        }
      });
  }
}
```

## Workflow Service Usage

### Basic CRUD Operations

```typescript
import { Component, OnInit } from '@angular/core';
import { WorkflowService } from '@shared/services/workflow.service';
import { Workflow, WorkflowConfig, WorkflowAgent, ManagerLLM } from '@shared/models/artifact.model';

@Component({
  selector: 'app-workflow-manager',
  template: '...'
})
export class WorkflowManagerComponent implements OnInit {
  workflows: Workflow[] = [];
  
  constructor(private workflowService: WorkflowService) {}

  ngOnInit(): void {
    this.loadWorkflows();
  }

  // Load all workflows for a team
  loadWorkflows(): void {
    this.workflowService.getAll({ teamId: 23, status: 'APPROVED' })
      .subscribe({
        next: (workflows) => {
          this.workflows = workflows;
        },
        error: (error) => {
          console.error('Failed to load workflows:', error);
        }
      });
  }

  // Create a new workflow
  createWorkflow(): void {
    const managerLLM: ManagerLLM = {
      id: 40,
      modelDeploymentName: 'correct-anthropic.claude-3-sonnet-2',
      topP: 0.95,
      maxToken: 1500,
      temperature: 0.3
    };

    const workflowConfig: WorkflowConfig = {
      managerLlm: [managerLLM],
      topP: 0.95,
      maxToken: 1500,
      temperature: 0.3,
      enableAgenticMemory: false
    };

    const workflowAgents: WorkflowAgent[] = [
      { serial: 1, agentId: 24 },
      { serial: 2, agentId: 784 }
    ];

    const newWorkflow: Omit<Workflow, 'id'> = {
      name: 'Data Analysis Workflow',
      description: 'End-to-end data analysis workflow using multiple agents',
      workflowAgents: workflowAgents,
      workflowConfig: workflowConfig,
      createdBy: 101,
      modifiedBy: 101,
      teamId: 23,
      status: 'CREATED'
    };

    this.workflowService.create(newWorkflow)
      .subscribe({
        next: (workflow) => {
          console.log('Workflow created successfully:', workflow);
          this.loadWorkflows(); // Refresh the list
        },
        error: (error) => {
          console.error('Failed to create workflow:', error);
        }
      });
  }

  // Execute workflow
  executeWorkflow(workflowId: number, inputs?: any): void {
    this.workflowService.execute(workflowId, inputs)
      .subscribe({
        next: (result) => {
          console.log('Workflow execution started:', result);
        },
        error: (error) => {
          console.error('Workflow execution failed:', error);
        }
      });
  }

  // Get workflow execution status
  getExecutionStatus(executionId: string): void {
    this.workflowService.getExecutionStatus(executionId)
      .subscribe({
        next: (status) => {
          console.log('Execution status:', status);
        },
        error: (error) => {
          console.error('Failed to get execution status:', error);
        }
      });
  }

  // Get workflow execution logs
  getExecutionLogs(executionId: string): void {
    this.workflowService.getExecutionLogs(executionId)
      .subscribe({
        next: (logs) => {
          console.log('Execution logs:', logs);
        },
        error: (error) => {
          console.error('Failed to get execution logs:', error);
        }
      });
  }

  // Cancel workflow execution
  cancelExecution(executionId: string): void {
    this.workflowService.cancelExecution(executionId)
      .subscribe({
        next: (result) => {
          console.log('Execution cancelled successfully:', result);
        },
        error: (error) => {
          console.error('Failed to cancel execution:', error);
        }
      });
  }

  // Get available LLM models
  getAvailableLLMModels(): void {
    this.workflowService.getAvailableLLMModels()
      .subscribe({
        next: (models) => {
          console.log('Available LLM models:', models);
        },
        error: (error) => {
          console.error('Failed to get LLM models:', error);
        }
      });
  }

  // Validate workflow dependencies
  validateDependencies(workflowId: number): void {
    this.workflowService.validateDependencies(workflowId)
      .subscribe({
        next: (result) => {
          console.log('Dependencies validation result:', result);
        },
        error: (error) => {
          console.error('Dependencies validation failed:', error);
        }
      });
  }

  // Search workflows
  searchWorkflows(query: string): void {
    this.workflowService.search(query, { teamId: 23 })
      .subscribe({
        next: (workflows) => {
          this.workflows = workflows;
        },
        error: (error) => {
          console.error('Workflow search failed:', error);
        }
      });
  }
}
```

## Error Handling

### Built-in Error Handling

All specialized services include centralized error handling with meaningful error messages:

```typescript
// Error handling is built into all service methods
this.agentService.getById(123)
  .subscribe({
    next: (agent) => {
      console.log('Agent:', agent);
    },
    error: (error) => {
      // Error message is already formatted by the service
      console.error('Error:', error.message);
      
      // You can also show user-friendly messages
      this.showErrorMessage(error.message);
    }
  });
```

### Custom Error Handling

```typescript
import { catchError } from 'rxjs/operators';
import { of } from 'rxjs';

this.agentService.getById(123)
  .pipe(
    catchError((error) => {
      // Custom error handling
      if (error.status === 404) {
        this.showNotFoundMessage();
      } else if (error.status === 403) {
        this.showAccessDeniedMessage();
      } else {
        this.showGenericErrorMessage(error.message);
      }
      
      // Return empty observable to continue the stream
      return of(null);
    })
  )
  .subscribe((agent) => {
    if (agent) {
      console.log('Agent:', agent);
    }
  });
```

### Error Handling with Retry Logic

```typescript
import { catchError, retry } from 'rxjs/operators';
import { of } from 'rxjs';

this.agentService.getById(123)
  .pipe(
    retry(3), // Retry up to 3 times
    catchError((error) => {
      console.error('Failed after retries:', error);
      this.showErrorMessage('Service temporarily unavailable. Please try again later.');
      return of(null);
    })
  )
  .subscribe((agent) => {
    if (agent) {
      console.log('Agent:', agent);
    }
  });
```

## Best Practices

### 1. **Use Specialized Services**
- **Prefer specialized services** (AgentService, ToolService, WorkflowService) for better type safety
- **Avoid generic approaches** that require type switching
- **Leverage TypeScript interfaces** for compile-time error checking

### 2. **Error Handling**
- **Always handle errors** in your subscriptions to provide a good user experience
- **Use meaningful error messages** that help users understand what went wrong
- **Implement retry logic** for transient failures in production applications

### 3. **Loading States**
- **Use loading indicators** while waiting for API responses
- **Show progress feedback** for long-running operations
- **Disable UI elements** during operations to prevent double-submission

### 4. **Data Management**
- **Refresh data lists** after create/update operations to show the latest information
- **Implement optimistic updates** for better perceived performance
- **Use proper state management** to avoid unnecessary API calls

### 5. **Parameter Validation**
- **Validate parameters** before making API calls to avoid unnecessary requests
- **Use TypeScript types** to catch validation errors at compile time
- **Implement client-side validation** for immediate user feedback

### 6. **Memory Management**
- **Unsubscribe from observables** in ngOnDestroy to prevent memory leaks
- **Use takeUntil pattern** for automatic cleanup
- **Avoid storing observables** in component properties

### 7. **Performance Optimization**
- **Use appropriate HTTP methods** (GET for reads, POST/PUT for writes)
- **Implement caching** for frequently accessed data
- **Use pagination** for large datasets

## Configuration

### Environment Setup

Make sure your environment configuration includes the correct `baseUrl` for your API endpoints:

```typescript
// environment.ts
export const environment = {
  baseUrl: 'https://your-api-domain.com/api',
  // ... other config
};
```

The services will automatically use this base URL for all API calls.

### Service Registration

Ensure all services are properly registered in your module:

```typescript
// app.module.ts or feature module
import { AgentService } from '@shared/services/agent.service';
import { ToolService } from '@shared/services/tool.service';
import { WorkflowService } from '@shared/services/workflow.service';

@NgModule({
  providers: [
    AgentService,
    ToolService,
    WorkflowService
  ]
})
export class AppModule { }
```

### HTTP Interceptors

Consider implementing HTTP interceptors for common functionality:

```typescript
// auth.interceptor.ts
@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const token = this.authService.getToken();
    if (token) {
      req = req.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`
        }
      });
    }
    return next.handle(req);
  }
}
```

## Migration from Generic Services

If you're migrating from the deprecated ArtifactService, follow these steps:

1. **Update imports** to use specialized services
2. **Replace method calls** with type-specific equivalents
3. **Update type annotations** (string IDs → number IDs)
4. **Remove generic payload structures**
5. **Update unit tests** to mock specialized services
6. **Verify functionality** works as expected

For detailed migration guidance, see the [DEPRECATION-GUIDE.md](./DEPRECATION-GUIDE.md) document.

## Conclusion

The specialized artifact services provide a modern, type-safe, and performant approach to managing agents, tools, and workflows. By using these services instead of the deprecated generic ArtifactService, you'll benefit from:

- **Better type safety** and compile-time error checking
- **Improved performance** with optimized API calls
- **Enhanced maintainability** with single-responsibility services
- **Better developer experience** with IntelliSense support
- **Future-proof architecture** that aligns with modern Angular patterns

Start using these specialized services today to build more robust and maintainable applications.
