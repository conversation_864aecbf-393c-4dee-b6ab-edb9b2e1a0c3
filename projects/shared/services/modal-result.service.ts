import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

import { LibraryItem } from '../components/interfaces';

export interface ModalEvent {
  type: 'itemsSelected' | 'createClicked';
  data?: LibraryItem[];
}

@Injectable({
  providedIn: 'root',
})
export class ModalResultService {
  private resultSubject = new BehaviorSubject<LibraryItem[] | null>(null);
  public result$ = this.resultSubject.asObservable();

  private eventSubject = new BehaviorSubject<ModalEvent | null>(null);
  public event$ = this.eventSubject.asObservable();

  setResult(result: LibraryItem[]): void {
    this.resultSubject.next(result);
  }

  clearResult(): void {
    this.resultSubject.next(null);
  }

  emitEvent(event: ModalEvent): void {
    this.eventSubject.next(event);
  }

  clearEvent(): void {
    this.eventSubject.next(null);
  }
}
