import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { environment } from '../environments/environment';
import { Agent, AgentConfig } from '../models/artifact.model';

@Injectable({ providedIn: 'root' })
export class AgentService {
  private readonly baseUrl = environment.baseUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get agent by ID
   */
  getById(id: number): Observable<Agent> {
    return this.http
      .get<Agent>(`${this.baseUrl}/agents?agentId=${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get all agents with optional filtering
   */
  getAll(params?: {
    teamId?: number;
    status?: string;
    createdBy?: number;
    page?: number;
    limit?: number;
    isDeleted?: boolean;
    type?: string;
  }): Observable<Agent[]> {
    let httpParams = new HttpParams();

    if (params?.teamId)
      httpParams = httpParams.set('teamId', params.teamId.toString());
    if (params?.status) httpParams = httpParams.set('status', params.status);
    if (params?.createdBy)
      httpParams = httpParams.set('createdBy', params.createdBy.toString());
    if (params?.page)
      httpParams = httpParams.set('page', params.page.toString());
    if (params?.limit)
      httpParams = httpParams.set('limit', params.limit.toString());
    if (params?.isDeleted !== undefined)
      httpParams = httpParams.set('isDeleted', params.isDeleted.toString());
    if (params?.type) httpParams = httpParams.set('type', params.type);

    return this.http
      .get<Agent[]>(`${this.baseUrl}/agents`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  /**
   * Create a new agent
   */
  create(agent: Omit<Agent, 'id'>): Observable<Agent> {
    return this.http
      .post<Agent>(`${this.baseUrl}/agents`, agent)
      .pipe(catchError(this.handleError));
  }

  /**
   * Update an existing agent
   */
  update(id: number, agent: Partial<Agent>): Observable<Agent> {
    // Include the ID in the request body instead of URL path
    const agentWithId = { ...agent, id };
    return this.http
      .put<Agent>(`${this.baseUrl}/agents`, agentWithId)
      .pipe(catchError(this.handleError));
  }

  /**
   * Delete an agent
   */
  delete(id: number): Observable<void> {
    return this.http
      .delete<void>(`${this.baseUrl}/agents/${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Submit agent for review
   */
  submitForReview(id: number): Observable<Agent> {
    return this.http
      .put<Agent>(`${this.baseUrl}/agents/review`, { id })
      .pipe(catchError(this.handleError));
  }

  /**
   * Approve agent
   */
  approve(id: number): Observable<Agent> {
    return this.http
      .put<Agent>(`${this.baseUrl}/agents/approve`, { id })
      .pipe(catchError(this.handleError));
  }

  /**
   * Reject agent
   */
  reject(id: number): Observable<Agent> {
    return this.http
      .put<Agent>(`${this.baseUrl}/agents/reject`, { id })
      .pipe(catchError(this.handleError));
  }

  /**
   * Test agent execution
   */
  test(id: number, userInputs?: any): Observable<any> {
    const payload = {
      executionId: '',
      id,
      type: 'agent',
      user: 'current',
      userInputs,
    };

    return this.http
      .post(`${this.baseUrl}/agents/${id}/test`, payload)
      .pipe(catchError(this.handleError));
  }

  /**
   * Validate agent configuration
   */
  validate(id: number): Observable<any> {
    return this.http
      .post(`${this.baseUrl}/agents/${id}/validate`, {})
      .pipe(catchError(this.handleError));
  }

  /**
   * Run agent in playground
   */
  playgroundRun(id: number, inputs?: any): Observable<any> {
    const payload = { inputs: { id, type: 'agent', ...inputs } };
    return this.http
      .post(`${this.baseUrl}/playground/agents/run`, payload)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get agent configuration template
   */
  getConfigTemplate(): Partial<AgentConfig> {
    return {
      temperature: 0.3,
      topP: 0.95,
      maxToken: '4000',
      maxIter: null,
      maxRpm: 5,
      maxExecutionTime: 5,
    };
  }

  /**
   * Clone agent with new name
   */
  clone(id: number, newName: string): Observable<Agent> {
    return this.http
      .post<Agent>(`${this.baseUrl}/agents/${id}/clone`, { name: newName })
      .pipe(
        catchError(this.handleError),
        catchError(() => throwError(() => new Error('Failed to clone agent')))
      );
  }

  /**
   * Export agent configuration
   */
  export(id: number): Observable<any> {
    return this.http
      .get(`${this.baseUrl}/agents/${id}/export`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Import agent configuration
   */
  import(config: any): Observable<Agent> {
    return this.http
      .post<Agent>(`${this.baseUrl}/agents/import`, config)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get agent statistics
   */
  getStats(id: number): Observable<any> {
    return this.http
      .get(`${this.baseUrl}/agents/${id}/stats`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get agent execution history
   */
  getExecutionHistory(
    id: number,
    params?: {
      page?: number;
      limit?: number;
      startDate?: string;
      endDate?: string;
    }
  ): Observable<any[]> {
    let httpParams = new HttpParams();

    if (params?.page)
      httpParams = httpParams.set('page', params.page.toString());
    if (params?.limit)
      httpParams = httpParams.set('limit', params.limit.toString());
    if (params?.startDate)
      httpParams = httpParams.set('startDate', params.startDate);
    if (params?.endDate) httpParams = httpParams.set('endDate', params.endDate);

    return this.http
      .get<
        any[]
      >(`${this.baseUrl}/agents/${id}/executions`, { params: httpParams })
      .pipe(catchError(this.handleError));
  }

  /**
   * Centralized error handling
   */
  private handleError(error: any): Observable<never> {
    let errorMessage = 'An error occurred while processing agent operation';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.status) {
      errorMessage = `HTTP ${error.status}: ${error.statusText}`;
    }

    console.error('AgentService Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
