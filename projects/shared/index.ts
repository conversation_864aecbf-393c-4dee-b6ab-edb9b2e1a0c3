// Shared Environment
export * from './environments/environment';

// Auth Services
export * from './auth/services/auth.service';
export * from './auth/services/token-storage.service';
export * from './auth/services/permissions.service';

// Auth Guards
export * from './auth/guards/auth.guard';
export * from './auth/guards/permission.guard';

// Auth Interceptors
export * from './auth/interceptors/auth.interceptor';
export * from './auth/interceptors/error.interceptors';

// Auth Interfaces
export * from './auth/interfaces/auth-config.interface';
export * from './auth/interfaces/auth.interface';
export * from './auth/interfaces/permission.interface';

// Auth Stores
export * from './auth/stores/permissions.store';

// Auth Directives
export * from './auth/directives/permission.directive';

// Auth Pipes
export * from './auth/pipes/permission.pipe';

// Auth Components
export * from './auth/components/login/login.component';
export * from './auth/components/callback/callback.component';

// Shared Services
export * from './services';
export * from './services/cookie.service';

// ⚠️ DEPRECATED: ArtifactService is deprecated, use specialized services instead
// export * from './services/artifact.service';

// Shared Models
export * from './models/artifact.model';
export * from './models/artifact-flags';

// Shared Stores
export * from './stores/agent.store';
export * from './stores/tool.store';
export * from './stores/workflow.store';
export * from './stores/state-machine';
export * from './stores/dropdown-data.store';
export * from './stores/revelio.store';
export * from './stores/knowledge-base.store';

// Shared Components
export * from './components/revelio/revelio.component';
export * from './components/revelio-skeleton/revelio-skeleton.component';
export * from './components/widget-box/widget-box.component';
export * from './components/siri-text-box/siri-text-box.component';
export * from './components/agent-builder/agent-builder.component';
export * from './components/create-artifact/create-artifact.component';
export * from './components/create-realm/create-realm.component';
export * from './components/agent-details-modal';
export * from './components/popup-modal/popup-modal.component';
export * from './components/create-artifact-modal/create-artifact-modal.component';
export * from './components/artifact-details/artifact-details.component';
export * from './components/filter-search-component/filter-search-component.component';
export * from './components/artifact-detail-modal/artifact-detail-modal.component';
export * from './components/artifact-filter/artifact-filter.component';
export * from './components/practice-area-filter/practice-area-filter.component';
export * from './components/capability-filter/capability-filter.component';
export * from './components/radio-filter/radio-filter.component';
export * from './components/sort-filter/sort-filter.component';
export * from './components/libraries-main-layout/libraries-main-layout.component';
export * from './components/guardrail-builder/components/guardrail-builder/guardrail-builder.component';
export * from './components/tool-builder/components/tool-builder/tool-builder.component';
export * from './components/model-builder/components/model-builder/model-builder.component';
export * from './components/list-modal/list-modal.component';

// Shared Pages
export * from './pages/agents/create-agent-prompt/create-agent-prompt.component';
export * from './pages/pipeline-builder/pipeline-builder.component';
export * from './pages/knowledge-builder/knowledge-builder.component';

export type { EnvironmentConfig } from './services/environment.service';
export { ENVIRONMENT_CONFIG } from './services/environment.service';

export * from './services/centralized-redirect.service';
export * from './guards/console-permission.guard';

export * from './pages/playground/playground.component';
// Shared Pages
export * from './pages/footer/footer.component';
