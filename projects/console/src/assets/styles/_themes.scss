/**
* =========================================================================
* Play+ Design System: Console Theme
*
* Console theme overrides for semantic tokens.
* This theme uses a blue and aqua color scheme for a modern console look.
* Primary: Blue (#2563EB) | Secondary: Aqua (#03BDD4)
* =========================================================================
*/

[data-theme='light'] {
  // Pulse Blue Palette
  --pulse-blue-50: #e6f3ff;
  --pulse-blue-100: #d9edff;
  --pulse-blue-200: #b0d9ff;
  --pulse-blue-300: #0084ff;
  --pulse-blue-400: #0077e6;
  --pulse-blue-500: #006acc;
  --pulse-blue-600: #0063bf;
  --pulse-blue-700: #004f99;
  --pulse-blue-800: #003b73;
  --pulse-blue-900: #002659;

  // Vivid Aqua Palette
  --vivid-aqua-50: #e6f8fb;
  --vivid-aqua-100: #8be1eb;
  --vivid-aqua-200: #a9e1cc;
  --vivid-aqua-300: #56d3e2;
  --vivid-aqua-400: #35cadd;
  --vivid-aqua-500: #03bdd4;
  --vivid-aqua-600: #03acc1;
  --vivid-aqua-700: #028697;
  --vivid-aqua-800: #026875;
  --vivid-aqua-900: #014f59;

  // Nova Green Palette
  --nova-green-50: #ecf8f4;
  --nova-green-100: #c5ebdd;
  --nova-green-200: #a9e1cc;
  --nova-green-300: #81d3b5;
  --nova-green-400: #69caa8;
  --nova-green-500: #43be90;
  --nova-green-600: #3dac83;
  --nova-green-700: #308666;
  --nova-green-800: #25684f;
  --nova-green-900: #1c4f3c;
  // Success Green Palette (Sem Green)
  --success-50: #e6f5fd;
  --success-100: #b2ded1;
  --success-200: #8ccfba;
  --success-300: #58b99b;
  --success-400: #37ab87;
  --success-500: #059669;
  --success-600: #058960;
  --success-700: #046b4b;
  --success-800: #035533;
  --success-900: #023f2c;

  // Error Red Palette (Sem Red)
  --error-50: #fce9e9;
  --error-100: #f4bcbc;
  --error-200: #f4bcbc;
  --error-300: #e866e8;
  --error-400: #e35151;
  --error-500: #dc2626;
  --error-600: #cb2323;
  --error-700: #9c1b1b;
  --error-800: #791515;
  --error-900: #5c1010;

  // Warning Orange Palette
  --warning-50: #f6f1e6;
  --warning-100: #e3d5b2;
  --warning-200: #d6c58c;
  --warning-300: #c4b058;
  --warning-400: #b8a239;
  --warning-500: #a78b0a;
  --warning-600: #987c09;
  --warning-700: #786007;
  --warning-800: #5c4a05;
  --warning-900: #463804;

  // Info Blue Palette
  --info-50: #e6f4f7;
  --info-100: #b2dde7;
  --info-200: #8ccbdc;
  --info-300: #58b5cd;
  --info-400: #37a7c1;
  --info-500: #0591b2;
  --info-600: #0584a2;
  --info-700: #04677e;
  --info-800: #035061;
  --info-900: #023d4a;

  // Light Neutral Palette
  --neutral-50: #ffffff; // rgb(255, 241, 245)
  --neutral-100: #d1d3d8; // rgb(253, 211, 224)
  --neutral-200: #bbbec5; // rgb(187, 190, 197)
  --neutral-300: #9ca1aa; // rgb(156, 161, 170)
  --neutral-400: #6b7280; // rgb(107, 114, 128)
  --neutral-500: #6b7280; // rgb(107, 114, 128)
  --neutral-600: #616874; // rgb(97, 104, 116)
  --neutral-700: #4c515b; // rgb(76, 81, 91)
  --neutral-800: #3b3f46; // rgb(59, 63, 70)
  --neutral-900: #2d3036; // rgb(45, 48, 54)

  // Dark Neutral Palette (Inverted for dark theme usage)
  --neutral-dark-50: #2d3036; // rgb(45, 48, 54)
  --neutral-dark-100: #3b3f46; // rgb(59, 63, 70)
  --neutral-dark-200: #4c515b; // rgb(76, 81, 91)
  --neutral-dark-300: #616874; // rgb(97, 104, 116)
  --neutral-dark-400: #6b7280; // rgb(107, 114, 128)
  --neutral-dark-500: #6b7280; // rgb(107, 114, 128)
  --neutral-dark-600: #9ca1aa; // rgb(156, 161, 170)
  --neutral-dark-700: #bbbec5; // rgb(187, 190, 197)
  --neutral-dark-800: #fdd3e0; // rgb(253, 211, 224)
  --neutral-dark-900: #ffffff; // rgb(255, 255, 255)

  /* --- MAPPED SEMANTIC COLORS FROM EXISTING STRUCTURE --- */

  /* --- Light Theme Color Overrides --- */

  /* PRIMARY (Pulse Blue) */
  --color-brand-primary: var(--pulse-blue-500);
  --color-brand-primary-hover: var(--pulse-blue-700);
  --color-brand-primary-active: var(--pulse-blue-800);
  --color-surface-interactive-primary: var(--pulse-blue-500);
  --color-surface-interactive-primary-hover: var(--pulse-blue-700);
  --color-surface-interactive-primary-active: var(--pulse-blue-800);
  --color-border-primary: var(--pulse-blue-500);
  --color-border-primary-hover: var(--pulse-blue-700);
  --color-border-primary-active: var(--pulse-blue-800);
  --color-text-primary: var(--neutral-700);
  --color-text-on-primary: var(--neutral-50);
  --color-text-inactive-tab-button: var(--neutral-800);
  --color-text-active-stepper-circle: var(--neutral-800);
  --color-text-accordion-content: var(--neutral-800);
  --color-textbox-input: var(--neutral-700);
  --textbox-surface-primary: var(--neutral-50);
  --prompt-box-style: linear-gradient(
    90deg,
    var(--pulse-blue-300) 0%,
    var(--vivid-aqua-300) 100%
  );
  --card-background: var(--neutral-dark-900);
  --card-default-border: 1px solid #f0f1f2;
  /* SECONDARY (Vivid Aqua) */
  --color-brand-secondary: var(--vivid-aqua-500);
  --color-brand-secondary-hover: var(--vivid-aqua-600);
  --color-brand-secondary-active: var(--vivid-aqua-700);
  --color-surface-interactive-secondary: var(--vivid-aqua-100);
  --color-surface-interactive-secondary-hover: var(--vivid-aqua-500);
  --color-surface-interactive-secondary-active: var(--vivid-aqua-600);
  --color-border-secondary: var(--vivid-aqua-500);
  --color-border-secondary-hover: var(--vivid-aqua-600);
  --color-border-secondary-active: var(--vivid-aqua-700);
  --color-text-secondary: var(--neutral-600);
  --color-text-on-secondary: var(--neutral-50);
  --color-background-secondary: var(--vivid-aqua-100);

  /* BUTTONS, TABS, TAGS */
  --color-text-placeholder: var(--neutral-400);
  --color-text-disabled: var(--neutral-400);
  --color-text-on-brand: var(--neutral-50);
  --color-text-interactive: var(--pulse-blue-500);
  --color-text-interactive-hover: var(--pulse-blue-700);
  --color-text-success: var(--success-500);
  --color-text-error: var(--error-500);
  --color-background-primary: var(--neutral-50);
  --color-background-disabled: var(--neutral-200);

  --color-surface-interactive-default: var(--pulse-blue-500);
  --color-surface-interactive-hover: var(--pulse-blue-700);
  --color-surface-interactive-active: var(--pulse-blue-700);
  --color-surface-disabled: var(--neutral-200);
  --color-surface-subtle-hover: var(--neutral-100);
  --color-border-default: var(--neutral-300);
  --color-border-subtle: var(--neutral-200);
  --color-border-interactive: var(--pulse-blue-500);
  --color-border-focus: var(--pulse-blue-500);
  --color-border-error: var(--error-500);
  --color-background-error: var(--error-500);

  /* Semantic Border Colors */
  --color-border-warning: var(--warning-500);
  --color-border-success: var(--success-500);
  --color-border-info: var(--info-500);

  /* Semantic Text Colors */
  --color-text-warning: var(--warning-600);
  --color-text-success: var(--success-600);
  --color-text-error: var(--error-600);
  --color-text-info: var(--info-600);

  /* Semantic Background Colors */
  --color-background-warning: var(--warning-500);
  --color-background-success: var(--success-500);
  --color-background-info: var(--info-500);

  /* --- Light Theme Glassmorphism --- */
  --glass-backdrop-blur: 12px;
  --glass-background-color: rgba(255, 255, 255, 0.6);
  --glass-border-color: rgba(255, 255, 255, 0.3);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-02);

  /*---- Time picker ----*/
  --color-icon-border: var(--neutral-800);

  /*---------header icon color--------*/
  --color-icon: var(--neutral-800);

  /* =======================
     LIGHT THEME: RGB OVERRIDES
     ======================= */
  --rgb-brand-primary: 0, 106, 204; /* pulse-blue-500 */
  --rgb-brand-secondary: 3, 189, 212; /* vivid-aqua-500 */
  --rgb-brand-tertiary: 67, 190, 144; /* nova-green-500 */
  --rgb-brand-quaternary: 5, 150, 105; /* success-500 */
  --rgb-brand-quinary: 220, 38, 38; /* error-500 */
  --rgb-brand-senary: 167, 139, 10; /* warning-500 */

  --rgb-violet: 124, 58, 237; /* keep from original */
  --rgb-royal-blue: 0, 106, 204; /* alias to primary */
  --rgb-cyan: 3, 189, 212; /* alias to secondary */
  --rgb-spearmint: 67, 190, 144; /* alias to tertiary */
  --rgb-rose: 220, 38, 38; /* alias to error */

  --rgb-white: 255, 255, 255; /* neutral-50 */
  --rgb-black: 0, 0, 0;
  --rgb-neutral-100: 253, 211, 224; /* neutral-100 */

  /* =======================
     LIGHT THEME: EFFECT COLOR OVERRIDES
     ======================= */
  --effect-color-primary: var(--rgb-brand-primary);
  --effect-color-secondary: var(--rgb-brand-secondary);
  --effect-color-accent: var(--rgb-brand-tertiary);
  --effect-color-neutral: var(--rgb-black);
  --effect-color-surface: var(--rgb-white);
  --color-login-background: rgba(255, 255, 255, 0.4);

  /* =======================
     GLASS METAPHOR
     ======================= */
  --surface-glass-bg: rgba(var(--effect-color-surface), 0.15);
  --surface-glass-border: rgba(var(--effect-color-surface), 0.7);
  --surface-glass-shadow: rgba(var(--effect-color-surface), 0.18);

  /* Light Metaphor */
  --color-light-glow: rgba(var(--effect-color-primary), 0.45);
  --color-light-glow-focus: rgba(var(--effect-color-primary), 0.65);
  --color-light-shadow: rgba(var(--effect-color-accent), 0.1);
  --color-light-border: var(--color-brand-primary);
  --color-light-border-focus: var(--color-brand-secondary);

  /* Liquid Metaphor */
  --color-liquid-shimmer-start: var(--color-brand-secondary);
  --color-liquid-shimmer-end: var(--color-brand-primary);

  /* Glass surface color */
  --glass-surface-color: var(--rgb-white);

  /* Glass variants */
  --glass-variant-primary: var(--rgb-brand-primary);
  --glass-variant-success: var(--rgb-brand-quaternary);
  --glass-variant-warning: var(--rgb-brand-senary);
  --glass-variant-danger: var(--rgb-brand-quinary);
  --glass-variant-info: var(--rgb-brand-secondary);
  --glass-variant-purple: var(--rgb-violet);
  --glass-variant-emerald: var(--rgb-brand-tertiary);

  /* Background Neutral */
  --neutral-bg: var(--neutral-100);
  --list-item-hover-bg: var(--pulse-blue-50);
  --list-item-outline-hover-border-color: var(--color-brand-primary);
}

[data-theme='dark'] {
  // Pulse Blue Palette
  --pulse-blue-50: #e6f3ff;
  --pulse-blue-100: #d9edff;
  --pulse-blue-200: #b0d9ff;
  --pulse-blue-300: #0084ff;
  --pulse-blue-400: #0077e6;
  --pulse-blue-500: #006acc;
  --pulse-blue-600: #0063bf;
  --pulse-blue-700: #004f99;
  --pulse-blue-800: #003b73;
  --pulse-blue-900: #002659;

  // Vivid Aqua Palette
  --vivid-aqua-50: #e6f8fb;
  --vivid-aqua-100: #8be1eb;
  --vivid-aqua-200: #a9e1cc;
  --vivid-aqua-300: #56d3e2;
  --vivid-aqua-400: #35cadd;
  --vivid-aqua-500: #03bdd4;
  --vivid-aqua-600: #03acc1;
  --vivid-aqua-700: #028697;
  --vivid-aqua-800: #026875;
  --vivid-aqua-900: #014f59;

  // Nova Green Palette
  --nova-green-50: #ecf8f4;
  --nova-green-100: #c5ebdd;
  --nova-green-200: #a9e1cc;
  --nova-green-300: #81d3b5;
  --nova-green-400: #69caa8;
  --nova-green-500: #43be90;
  --nova-green-600: #3dac83;
  --nova-green-700: #308666;
  --nova-green-800: #25684f;
  --nova-green-900: #1c4f3c;
  // Success Green Palette (Sem Green)
  --success-50: #e6f5fd;
  --success-100: #b2ded1;
  --success-200: #8ccfba;
  --success-300: #58b99b;
  --success-400: #37ab87;
  --success-500: #059669;
  --success-600: #058960;
  --success-700: #046b4b;
  --success-800: #035533;
  --success-900: #023f2c;

  // Error Red Palette (Sem Red)
  --error-50: #fce9e9;
  --error-100: #f4bcbc;
  --error-200: #f4bcbc;
  --error-300: #e866e8;
  --error-400: #e35151;
  --error-500: #dc2626;
  --error-600: #cb2323;
  --error-700: #9c1b1b;
  --error-800: #791515;
  --error-900: #5c1010;

  // Warning Orange Palette
  --warning-50: #f6f1e6;
  --warning-100: #e3d5b2;
  --warning-200: #d6c58c;
  --warning-300: #c4b058;
  --warning-400: #b8a239;
  --warning-500: #a78b0a;
  --warning-600: #987c09;
  --warning-700: #786007;
  --warning-800: #5c4a05;
  --warning-900: #463804;

  // Info Blue Palette
  --info-50: #e6f4f7;
  --info-100: #b2dde7;
  --info-200: #8ccbdc;
  --info-300: #58b5cd;
  --info-400: #37a7c1;
  --info-500: #0591b2;
  --info-600: #0584a2;
  --info-700: #04677e;
  --info-800: #035061;
  --info-900: #023d4a;

  // Light Neutral Palette
  --neutral-50: #ffffff; // rgb(255, 241, 245)
  --neutral-100: #d1d3d8; // rgb(253, 211, 224)
  --neutral-200: #bbbec5; // rgb(187, 190, 197)
  --neutral-300: #9ca1aa; // rgb(156, 161, 170)
  --neutral-400: #6b7280; // rgb(107, 114, 128)
  --neutral-500: #6b7280; // rgb(107, 114, 128)
  --neutral-600: #616874; // rgb(97, 104, 116)
  --neutral-700: #4c515b; // rgb(76, 81, 91)
  --neutral-800: #3b3f46; // rgb(59, 63, 70)
  --neutral-900: #2d3036; // rgb(45, 48, 54)

  // Dark Neutral Palette (Inverted for dark theme usage)
  --neutral-dark-50: #2d3036; // rgb(45, 48, 54)
  --neutral-dark-100: #3b3f46; // rgb(59, 63, 70)
  --neutral-dark-200: #4c515b; // rgb(76, 81, 91)
  --neutral-dark-300: #616874; // rgb(97, 104, 116)
  --neutral-dark-400: #6b7280; // rgb(107, 114, 128)
  --neutral-dark-500: #6b7280; // rgb(107, 114, 128)
  --neutral-dark-600: #9ca1aa; // rgb(156, 161, 170)
  --neutral-dark-700: #bbbec5; // rgb(187, 190, 197)
  --neutral-dark-800: #fdd3e0; // rgb(253, 211, 224)
  --neutral-dark-900: #ffffff; // rgb(255, 255, 255)

  /* --- MAPPED SEMANTIC COLORS FROM EXISTING STRUCTURE --- */

  /* --- Light Theme Color Overrides --- */

  /* PRIMARY (Pulse Blue) */
  --color-brand-primary: var(--pulse-blue-500);
  --color-brand-primary-hover: var(--pulse-blue-700);
  --color-brand-primary-active: var(--pulse-blue-800);
  --color-surface-interactive-primary: var(--pulse-blue-500);
  --color-surface-interactive-primary-hover: var(--pulse-blue-700);
  --color-surface-interactive-primary-active: var(--pulse-blue-800);
  --color-border-primary: var(--pulse-blue-500);
  --color-border-primary-hover: var(--pulse-blue-700);
  --color-border-primary-active: var(--pulse-blue-800);
  --color-text-primary: var(--neutral-700);
  --color-text-on-primary: var(--neutral-50);
  --color-text-inactive-tab-button: var(--neutral-800);
  --color-text-active-stepper-circle: var(--neutral-800);
  --color-text-accordion-content: var(--neutral-800);
  --color-textbox-input: var(--neutral-700);
  --textbox-surface-primary: var(--neutral-50);
  --prompt-box-style: linear-gradient(
    90deg,
    var(--pulse-blue-300) 0%,
    var(--vivid-aqua-300) 100%
  );
  --card-background: var(--neutral-dark-900);
  --card-default-border: 1px solid #f0f1f2;
  /* SECONDARY (Vivid Aqua) */
  --color-brand-secondary: var(--vivid-aqua-500);
  --color-brand-secondary-hover: var(--vivid-aqua-600);
  --color-brand-secondary-active: var(--vivid-aqua-700);
  --color-surface-interactive-secondary: var(--vivid-aqua-100);
  --color-surface-interactive-secondary-hover: var(--vivid-aqua-500);
  --color-surface-interactive-secondary-active: var(--vivid-aqua-600);
  --color-border-secondary: var(--vivid-aqua-500);
  --color-border-secondary-hover: var(--vivid-aqua-600);
  --color-border-secondary-active: var(--vivid-aqua-700);
  --color-text-secondary: var(--neutral-600);
  --color-text-on-secondary: var(--neutral-50);
  --color-background-secondary: var(--vivid-aqua-100);

  /* BUTTONS, TABS, TAGS */
  --color-text-placeholder: var(--neutral-400);
  --color-text-disabled: var(--neutral-400);
  --color-text-on-brand: var(--neutral-50);
  --color-text-interactive: var(--pulse-blue-500);
  --color-text-interactive-hover: var(--pulse-blue-700);
  --color-text-success: var(--success-500);
  --color-text-error: var(--error-500);
  --color-background-primary: var(--neutral-50);
  --color-background-disabled: var(--neutral-200);

  --color-surface-interactive-default: var(--pulse-blue-500);
  --color-surface-interactive-hover: var(--pulse-blue-700);
  --color-surface-interactive-active: var(--pulse-blue-700);
  --color-surface-disabled: var(--neutral-200);
  --color-surface-subtle-hover: var(--neutral-100);
  --color-border-default: var(--neutral-300);
  --color-border-subtle: var(--neutral-200);
  --color-border-interactive: var(--pulse-blue-500);
  --color-border-focus: var(--pulse-blue-500);
  --color-border-error: var(--error-500);
  --color-background-error: var(--error-500);

  /* Semantic Border Colors */
  --color-border-warning: var(--warning-500);
  --color-border-success: var(--success-500);
  --color-border-info: var(--info-500);

  /* Semantic Text Colors */
  --color-text-warning: var(--warning-600);
  --color-text-success: var(--success-600);
  --color-text-error: var(--error-600);
  --color-text-info: var(--info-600);

  /* Semantic Background Colors */
  --color-background-warning: var(--warning-500);
  --color-background-success: var(--success-500);
  --color-background-info: var(--info-500);

  /* --- Light Theme Glassmorphism --- */
  --glass-backdrop-blur: 12px;
  --glass-background-color: rgba(255, 255, 255, 0.6);
  --glass-border-color: rgba(255, 255, 255, 0.3);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-02);

  /*---- Time picker ----*/
  --color-icon-border: var(--neutral-800);

  /*---------header icon color--------*/
  --color-icon: var(--neutral-800);

  /* =======================
     LIGHT THEME: RGB OVERRIDES
     ======================= */
  --rgb-brand-primary: 0, 106, 204; /* pulse-blue-500 */
  --rgb-brand-secondary: 3, 189, 212; /* vivid-aqua-500 */
  --rgb-brand-tertiary: 67, 190, 144; /* nova-green-500 */
  --rgb-brand-quaternary: 5, 150, 105; /* success-500 */
  --rgb-brand-quinary: 220, 38, 38; /* error-500 */
  --rgb-brand-senary: 167, 139, 10; /* warning-500 */

  --rgb-violet: 124, 58, 237; /* keep from original */
  --rgb-royal-blue: 0, 106, 204; /* alias to primary */
  --rgb-cyan: 3, 189, 212; /* alias to secondary */
  --rgb-spearmint: 67, 190, 144; /* alias to tertiary */
  --rgb-rose: 220, 38, 38; /* alias to error */

  --rgb-white: 255, 255, 255; /* neutral-50 */
  --rgb-black: 0, 0, 0;
  --rgb-neutral-100: 253, 211, 224; /* neutral-100 */

  /* =======================
     LIGHT THEME: EFFECT COLOR OVERRIDES
     ======================= */
  --effect-color-primary: var(--rgb-brand-primary);
  --effect-color-secondary: var(--rgb-brand-secondary);
  --effect-color-accent: var(--rgb-brand-tertiary);
  --effect-color-neutral: var(--rgb-black);
  --effect-color-surface: var(--rgb-white);
  --color-login-background: rgba(255, 255, 255, 0.4);

  /* =======================
     GLASS METAPHOR
     ======================= */
  --surface-glass-bg: rgba(var(--effect-color-surface), 0.15);
  --surface-glass-border: rgba(var(--effect-color-surface), 0.7);
  --surface-glass-shadow: rgba(var(--effect-color-surface), 0.18);

  /* Light Metaphor */
  --color-light-glow: rgba(var(--effect-color-primary), 0.45);
  --color-light-glow-focus: rgba(var(--effect-color-primary), 0.65);
  --color-light-shadow: rgba(var(--effect-color-accent), 0.1);
  --color-light-border: var(--color-brand-primary);
  --color-light-border-focus: var(--color-brand-secondary);

  /* Liquid Metaphor */
  --color-liquid-shimmer-start: var(--color-brand-secondary);
  --color-liquid-shimmer-end: var(--color-brand-primary);

  /* Glass surface color */
  --glass-surface-color: var(--rgb-white);

  /* Glass variants */
  --glass-variant-primary: var(--rgb-brand-primary);
  --glass-variant-success: var(--rgb-brand-quaternary);
  --glass-variant-warning: var(--rgb-brand-senary);
  --glass-variant-danger: var(--rgb-brand-quinary);
  --glass-variant-info: var(--rgb-brand-secondary);
  --glass-variant-purple: var(--rgb-violet);
  --glass-variant-emerald: var(--rgb-brand-tertiary);

  /* Background Neutral */
  --neutral-bg: var(--neutral-100);
  --list-item-hover-bg: var(--pulse-blue-50);
  --list-item-outline-hover-border-color: var(--color-brand-primary);
}
