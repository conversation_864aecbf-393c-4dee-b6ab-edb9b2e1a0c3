import {
  Component,
  OnInit,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
  ElementRef,
} from '@angular/core';
import {
  TokenStorageService,
  AuthConfig,
  AuthService,
  CentralizedRedirectService,
  ThemeInitService,
  ThemeService,
  RevelioComponent,
  FooterComponent,
} from '@shared';
import { environment } from '../environments/environment';
import {
  AavaLayoutComponent,
  AavaRailNavigationSidebarComponent,
  RailNavigationConfig,
  AavaBreadcrumbsComponent,
} from '@aava/play-core';
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import {
  BreadcrumbService,
  BreadcrumbItem,
} from './services/breadcrumb.service';
import { Subscription, filter } from 'rxjs';
import { routes } from './app.routes';
@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    AavaLayoutComponent,
    AavaRailNavigationSidebarComponent,
    AavaBreadcrumbsComponent,
    RevelioComponent,
    FooterComponent,
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit, OnDestroy {
  leftPanelOpen = false;
  currentTheme = 'light';
  private routeSubscription?: Subscription;

  toggleRevelio: boolean = false;

  sidebarConfig: RailNavigationConfig = {
    headerIcon: {
      iconName: 'svgs/sidenav/ascendionLogo_sidenav.svg',
      label: '',
      action: 'ascendion-action',
      iconType: 'url',
      tooltip: '',
    },
    menuItems: [
      {
        id: 'dashboard',
        iconName: 'layout-grid',
        label: '',
        action: 'dashboard-action',
        isSelected: true,
        tooltip: 'Dashboard',
        iconType: 'lucide',
      },
      {
        id: 'build',
        iconName: 'rocket',
        label: '',
        action: 'build-action',
        isSelected: false,
        tooltip: 'Build',
        iconType: 'lucide',
        studioHeading: 'Build',
        studioCards: [
          {
            id: 'agent',
            title: 'Agent',
            titleColor: '#CD6987',
            image: 'svgs/build/agent.svg',
          },
          {
            id: 'pipeline',
            title: 'Pipeline',
            titleColor: '#3B82F6',
            image: 'svgs/build/pipeline.svg',
          },
          {
            id: 'model',
            title: 'Model',
            titleColor: '#10B981',
            image: 'svgs/build/model.svg',
          },
          {
            id: 'tool',
            title: 'Tools',
            titleColor: '#3B82F6',
            image: 'svgs/build/tools.svg',
          },
          {
            id: 'guardrail',
            title: 'Gaurdrail',
            titleColor: '#DDB76B',
            image: 'svgs/build/guardrail.svg',
          },
          {
            id: 'knowledge',
            title: 'Knowledge',
            titleColor: '#10B981',
            image: 'svgs/build/knowledge.svg',
          },
        ],
      },
      {
        id: 'marketplace',
        iconName: 'square-m',
        label: '',
        action: 'marketplace-action',
        isSelected: false,
        tooltip: 'Marketplace',
        iconType: 'lucide',
      },
      {
        id: 'approvals',
        iconName: 'file-check',
        label: '',
        action: 'approvals-action',
        isSelected: false,
        tooltip: 'Approvals',
        iconType: 'lucide',
      },
      {
        id: 'manage',
        iconName: 'user-cog',
        label: '',
        action: 'manage-action',
        isSelected: false,
        tooltip: 'Manage',
        iconType: 'lucide',
        retainActiveState: false,
      },
      {
        id: 'analytics',
        iconName: 'chart-no-axes-combined',
        label: '',
        action: 'analytics-action',
        isSelected: false,
        tooltip: 'Analytics',
        iconType: 'lucide',
        retainActiveState: false,
      },
      {
        id: 'realm',
        iconName: 'svgs/sidenav/ascendion_sidenav.svg',
        label: '',
        action: 'realm-action',
        isSelected: false,
        tooltip: '',
        iconType: 'url',
        retainActiveState: false,
      },
    ],
    subMenuItems: [
      {
        id: 'closePanel',
        iconName: 'panel-left-close',
        label: '',
        action: 'close-panel-action',
        isSelected: false,
        iconType: 'lucide',
        tooltip: '',
        retainActiveState: false, // This item will NOT retain active state (action-based)
      },
    ],
    profile: {
      imageUrl: 'svgs/sidenav/userProfile_sidenav.svg',
      altText: 'User Profile',
      action: 'profile-action',
      tooltip: '',
      profileMenu: [
        {
          id: 'settings',
          iconName: 'Settings',
          label: '',
          action: 'open-settings',
          iconType: 'lucide',
          tooltip: '',
        },
        {
          id: 'theme',
          iconName: 'Sun',
          label: '',
          action: 'toggle-theme',
          iconType: 'lucide',
          tooltip: '',
          isToggle: true,
          subMenuItems: [
            {
              id: 'light',
              iconName: 'Sun',
              label: '',
              action: 'set-theme-light',
              iconType: 'lucide',
              tooltip: '',
            },
            {
              id: 'dark',
              iconName: 'Moon',
              label: '',
              action: 'set-theme-dark',
              iconType: 'lucide',
              tooltip: '',
            },
            {
              id: 'auto',
              iconName: 'Monitor',
              label: '',
              action: 'set-theme-auto',
              iconType: 'lucide',
              tooltip: '',
            },
          ],
          selectedSubItem: {
            id: 'light',
            iconName: 'Sun',
            label: '',
            action: 'set-theme-light',
            iconType: 'lucide',
            tooltip: '',
          },
        },
        {
          id: 'language',
          iconName: 'Languages',
          label: '',
          action: 'change-language',
          iconType: 'lucide',
          tooltip: '',
          isToggle: true,
        },
        {
          id: 'logout',
          iconName: 'LogOut',
          label: '',
          action: 'logout',
          iconType: 'lucide',
          tooltip: '',
          isDestructive: true,
        },
      ],
    },
  };

  breadcrumbList: BreadcrumbItem[] = [];
  private breadcrumbSubscription: Subscription;

  constructor(
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
    private centralizedRedirectService: CentralizedRedirectService,
    private themeInitService: ThemeInitService,
    // private themeService: ThemeService,
    private breadcrumbService: BreadcrumbService,
    private router: Router,
    private elementRef: ElementRef
  ) {
    // Subscribe to breadcrumb updates from the service
    this.breadcrumbSubscription = this.breadcrumbService.breadcrumbs$.subscribe(
      breadcrumbs => {
        this.breadcrumbList = [...breadcrumbs]; // Create new array reference for OnPush change detection

        // Update sidebar selection when breadcrumbs change
        const currentRoute = this.router.url;
        const menuItemId = this.getMenuItemIdFromRoute(currentRoute);
        if (menuItemId) {
          this.updateSidebarSelection(menuItemId);
        }
      }
    );
  }

  toggleChat(): boolean {
    this.toggleRevelio = !this.toggleRevelio;
    return this.toggleRevelio;
  }
  ngOnInit(): void {
    // Initialize theme system early
    this.themeInitService.initialize();

    // Initialize breadcrumb service with routes
    this.breadcrumbService.initializeWithRoutes(routes);

    // Sync sidebar selection with route changes
    this.syncSidebarWithRoute();

    // Initialize sidebar selection based on current route
    this.initializeSidebarSelection();

    const authConfig: AuthConfig = {
      apiAuthUrl: environment.consoleApiAuthUrl,
      redirectUrl: environment.consoleRedirectUrl,
      postLoginRedirectUrl: '/',
      appName: 'console',
    };

    this.authService.setAuthConfig(authConfig);
    this.authService.handleAuthCodeAndToken();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    if (this.breadcrumbSubscription) {
      this.breadcrumbSubscription.unsubscribe();
    }
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }
  onMenuItemClick(item: any): void {
    // Navigate to the matching route based on menu item ID
    if (item && item.id) {
      const routePath = this.getRoutePathFromMenuItemId(item.id);
      if (routePath) {
        // Navigate to the route
        this.router.navigate([routePath]);

        // Update sidebar selection to match navigation
        this.updateSidebarSelection(item.id);
      }
    }
  }

  /**
   * Update sidebar selection state
   */
  private updateSidebarSelection(selectedItemId: string): void {
    // First reset all items to false
    this.sidebarConfig.menuItems.forEach(menuItem => {
      menuItem.isSelected = false;
    });

    // Then set the selected item to true
    const selectedItem = this.sidebarConfig.menuItems.find(
      menuItem => menuItem.id === selectedItemId
    );
    if (selectedItem) {
      selectedItem.isSelected = true;
    }
  }

  /**
   * Initialize sidebar selection based on current route
   */
  private initializeSidebarSelection(): void {
    const currentRoute = this.router.url;
    const menuItemId = this.getMenuItemIdFromRoute(currentRoute);

    if (menuItemId) {
      this.updateSidebarSelection(menuItemId);
    }
  }

  /**
   * Sync sidebar selection with route changes
   */
  private syncSidebarWithRoute(): void {
    this.routeSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        const currentRoute = event.urlAfterRedirects || event.url;
        const menuItemId = this.getMenuItemIdFromRoute(currentRoute);

        if (menuItemId) {
          this.updateSidebarSelection(menuItemId);
        }
      });
  }

  /**
   * Get menu item ID from current route
   */
  private getMenuItemIdFromRoute(route: string): string | null {
    // Remove leading slash and 'console' prefix if present
    const cleanRoute = route.replace(/^\/?(console\/)?/, '');

    // Handle root route (empty string or just '/')
    if (!cleanRoute || cleanRoute === '/') {
      return 'dashboard';
    }

    // Extract the first segment of the route
    const firstSegment = cleanRoute.split('/')[0];

    // Map route segments to menu item IDs
    const routeToMenuMapping: { [key: string]: string } = {
      dashboard: '/',
      approvals: 'approvals',
      marketplace: 'marketplace',
      manage: 'manage',
      // analytics: 'analytics',
      agent: 'build/agent',
      pipeline: 'build/pipelines',
      model: 'build/model',
      tool: 'build/tools',
      guardrail: 'build/guardrails',
      knowledge: 'build/knowledge',
    };

    return routeToMenuMapping[firstSegment] || null;
  }

  /**
   * Get route path from menu item ID
   */
  private getRoutePathFromMenuItemId(menuItemId: string): string | null {
    const routeMapping: { [key: string]: string } = {
      dashboard: '/',
      approvals: 'approvals',
      marketplace: 'marketplace',
      manage: 'manage',
      // analytics: 'analytics',
      agent: 'build/agent',
      pipeline: 'build/pipelines',
      model: 'build/model',
      tool: 'build/tools',
      guardrail: 'build/guardrails',
      knowledge: 'build/knowledge',
    };

    return routeMapping[menuItemId] || null;
  }

  onSubMenuItemClick(item: any): void {
    if (item.id === 'closePanel') {
      this.leftPanelOpen = false;
    } else if (item.id === 'themetoggle') {
      this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
      document.documentElement.setAttribute('data-theme', this.currentTheme);
    }
  }

  onProfileClick(action: string): void {
    console.log('Profile clicked:', action);
  }

  onStudioCardClick(event: { menuItemId: string; card: any }): void {
    if(event.menuItemId ==='build'){
      if (event.card && event.card.id) {
        const routePath = this.getRoutePathFromMenuItemId(event.card.id);
        if (routePath) {
          // Navigate to the route
          this.router.navigate([routePath]);
  
          // Update sidebar selection to match navigation
          this.updateSidebarSelection(event.card.id);
        }
      }
    }
  }

  // Handle clicking outside of Revelio component to close it
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (this.toggleRevelio) {
      const target = event.target as HTMLElement;

      // Get the revelio component element
      const revelioElement =
        this.elementRef.nativeElement.querySelector('app-revelio');

      // Get the chat icon that triggers the revelio
      const chatIcon = this.elementRef.nativeElement.querySelector(
        '.header-right .revelio-icon'
      );

      // Check if click is outside both the revelio component and the chat icon
      if (
        revelioElement &&
        chatIcon &&
        !revelioElement.contains(target) &&
        !chatIcon.contains(target)
      ) {
        this.toggleRevelio = false;
      }
    }
  }

  onProfileMenuItemClick(item: any): void {
    console.log("item: ", item);
    if (!item) {
      return;
    }

    // Handle logout action
    if (item.action === 'logout' || item.id === 'logout') {
      // SSO logout only - clears state and navigates to provider logout URL
      this.authService.logout().subscribe({
        error: () => {
          // Fallback in case of error
          this.centralizedRedirectService.redirectToMarketingLogin();
        },
      });
      return;
    }

    if(item.action === 'open-settings'){
      this.router.navigate(['settings']);
    }
  }
  
  onProfileSubMenuItemClick(item: any): void {
    // Handle theme changes
    console.log("item: ", item);
    if (item.parentItem?.action === 'toggle-theme' && item.subItem) {
      const themeAction = item.subItem.action;

      switch (themeAction) {
        case 'set-theme-light':
          // this.themeService.setUserPreference('light');
          break;

        case 'set-theme-dark':
          // this.themeService.setUserPreference('dark');
          break;

        case 'set-theme-auto':
          // this.themeService.setUserPreference('system');
          break;

        default:
          console.warn('Unknown theme action:', themeAction);
      }

      // Update current theme property for UI updates
      // this.currentTheme = this.themeService.getCurrentTheme();
    }
  }
}
