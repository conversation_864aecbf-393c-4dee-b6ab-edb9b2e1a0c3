import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from 'projects/console/src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private apiAuthUrl = environment.consoleApiAuthUrl;

  private http = inject(HttpClient);

  getAllUser() {
    const url = `${this.apiAuthUrl}/user/mgmt/v2`;
    return this.http.get(url);
  }

  getUserDetails(id: number) {
    const url = `${this.apiAuthUrl}/user?userId=${id}`;
    return this.http.get(url);
  }

  addNewUser(payload: any) {
    const url = `${this.apiAuthUrl}/user`;
    return this.http.post(url, payload);
  }

  updateUser(payload: any, userId: number) {
    const url = `${this.apiAuthUrl}/user?userId=${userId}`;
    return this.http.put(url, payload);
  }

  getAllRoles() {
    const url = `${this.apiAuthUrl}/roles`;
    return this.http.get(url);
  }

  getAllRealms() {
    const url = `${this.apiAuthUrl}/realms`;
    return this.http.get(url);
  }
  deleteUser(id: string) {
    const url = `${this.apiAuthUrl}/user/${id}`;
    return this.http.delete(url);
  }

  getExistingAccessControl(roleId: string) {
    const url = `${this.apiAuthUrl}/access/permissions?roleId=${roleId}`;
    return this.http.get(url);
  }

  getAllPages() {
    const url = `${this.apiAuthUrl}/pages`;
    return this.http.get(url);
  }

  getAllActions() {
    const url = `${this.apiAuthUrl}/actions`;
    return this.http.get(url);
  }

  createRole(payload: any) {
    const url = `${this.apiAuthUrl}/role`;
    return this.http.post(url, payload);
  }

  getUserSearch(emailFilter = '') {
  const url = `${this.apiAuthUrl}/user/mgmt/v2`;
  return this.http.get(url, {
    params: {
      emailFilter,
    },
  });
}

}
