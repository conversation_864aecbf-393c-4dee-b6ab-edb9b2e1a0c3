import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TokenStorageService } from '@shared';
import { map } from 'rxjs';

import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class SettingsService {
  baseUrl = environment.baseUrl;

  constructor(
    private http: HttpClient,
    private tokenService: TokenStorageService
  ) {}

  public getSettingsData() {
    const url = `${this.baseUrl}/v1/api/admin/ava/force/settings`;
    return this.http.get(url).pipe(map((response: any) => response));
  }

  public updateSettingsData(payload: any) {
    const url = `${this.baseUrl}/v1/api/admin/ava/force/settings`;
    return this.http.put(url, payload).pipe(map((response: any) => response));
  }

  public getConfigurations() {
    const url = `${this.baseUrl}/appconfig/api/v1/config`;
    return this.http.get(url).pipe(map((response: any) => response));
  }

  public createApplication(applicationName: string, description: string) {
    const url = `${this.baseUrl}/appconfig/api/v1/applications`;
    const payload = {
      name: applicationName,
      description: description,
      createdBy: this.tokenService.getDaUsername() || 'admin',
    };
    return this.http.post(url, payload);
  }

  public getApplications() {
    const url = `${this.baseUrl}/appconfig/api/v1/applications`;
    return this.http.get(url).pipe(map((response: any) => response));
  }

  public getCategories() {
    const url = `${this.baseUrl}/appconfig/api/v1/categories`;
    return this.http.get(url).pipe(map((response: any) => response));
  }

  public deleteConfig(
    applicationName: string,
    categoryName: string,
    key: string
  ) {
    const url = `${this.baseUrl}/appconfig/api/v1/config/${applicationName}/${categoryName}/${key}`;
    return this.http.delete(url);
  }

  public updateConfig(
    applicationName: string,
    categoryName: string,
    key: string,
    payload: any
  ) {
    const url = `${this.baseUrl}/appconfig/api/v1/config/${applicationName}/${categoryName}/${key}`;
    return this.http.put(url, payload);
  }

  public createConfig(
    applicationName: string,
    categoryName: string,
    payload: any
  ) {
    const url = `${this.baseUrl}/appconfig/api/v1/config/${applicationName}/${categoryName}`;
    return this.http.post(url, payload);
  }

  public getHistory(id: string) {
    const url = `${this.baseUrl}/appconfig/api/v1/versions/configuration/${id}/history`;
    return this.http.get(url);
  }

  public searchAPI(text: string) {
    let url = `${this.baseUrl}/appconfig/api/v1/config/search?keyPattern=${text}`;
    if (text.trim() === '') {
      url = `${this.baseUrl}/appconfig/api/v1/config`;
    }
    return this.http.get(url);
  }
}
