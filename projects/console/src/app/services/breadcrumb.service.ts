import { Injectable } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute, Routes } from '@angular/router';
import { BehaviorSubject, Observable, filter, map, startWith } from 'rxjs';

export interface BreadcrumbItem {
  label: string;
  url: string;
  active: boolean;
  icon?: string;
  dropdownItems?: DropdownItem[];
}

export interface DropdownItem {
  label: string;
  url: string;
  icon?: string;
}

export interface RouteBreadcrumbConfig {
  path: string;
  label: string;
  icon?: string;
  parent?: string;
  dropdownItems?: DropdownItem[];
  dynamicLabel?: (params: Record<string, unknown>) => string;
  includeInBreadcrumb?: boolean;
}

@Injectable({
  providedIn: 'root',
})
export class BreadcrumbService {
  private breadcrumbsSubject = new BehaviorSubject<BreadcrumbItem[]>([]);
  public breadcrumbs$: Observable<BreadcrumbItem[]> =
    this.breadcrumbsSubject.asObservable();

  // Route configuration for breadcrumbs - will be auto-generated
  private routeConfigs: Map<string, RouteBreadcrumbConfig> = new Map();

  // Console dropdown items for the main Console breadcrumb
  private consoleDropdownItems: DropdownItem[] = [];

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
    // Router event subscription will be initialized when routes are set
  }

  /**
   * Initialize breadcrumbs by listening to route changes
   */
  private initializeBreadcrumbs(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        startWith(null),
        map(() => this.createBreadcrumbs())
      )
      .subscribe(breadcrumbs => {
        this.breadcrumbsSubject.next(breadcrumbs);
      });
  }

  /**
   * Auto-generate route configurations from app routes
   * This method should be called after routes are loaded
   */
  public generateRouteConfigsFromRoutes(
    routes: Routes,
    basePath: string = ''
  ): void {
    if (basePath === '') {
      this.routeConfigs.clear();
    }

    // First pass: collect all routes and their siblings
    const routeGroups = new Map<string, Routes>();

    routes.forEach(route => {
      if (
        route.path &&
        route.path !== '**' &&
        route.path !== 'callback' &&
        !route.redirectTo
      ) {
        const parentPath = basePath || '';
        if (!routeGroups.has(parentPath)) {
          routeGroups.set(parentPath, []);
        }
        routeGroups.get(parentPath)!.push(route);
      }
    });

    // Second pass: create route configs with dropdown items
    routes.forEach(route => this.processRoute(route, basePath));

    // Add special handling for root path - Console should show top-level routes
    if (!this.routeConfigs.has('')) {
      this.routeConfigs.set('', {
        path: '/',
        label: 'Console',
        icon: 'home',
        parent: '',
        dropdownItems: this.generateDropdownItemsFromSiblings(
          routeGroups.get('') || [],
          '',
          ''
        ),
      });
    }

    // Update console dropdown items with top-level routes
    // Check if root route should show dropdown (default: false)
    const rootRoutes = routeGroups.get('') || [];
    const rootRoute = rootRoutes.find(
      r => r.path === '' && r.pathMatch === 'full'
    );
    const rootShowDropdown =
      rootRoute?.data?.['breadcrumbConfig']?.showDropdown ?? false;

    this.consoleDropdownItems = rootShowDropdown
      ? this.generateDropdownItemsFromSiblings(rootRoutes, '', '')
      : [];
  }

  /**
   * Process a single route and add it to route configs
   */
  private processRoute(route: Routes[0], basePath: string): void {
    if (this.shouldProcessRoute(route)) {
      const fullPath = basePath ? `${basePath}/${route.path}` : route.path!;
      this.addRouteToConfigs(route, fullPath, basePath);

      if (route.children?.length) {
        this.generateRouteConfigsFromRoutes(route.children, fullPath);
      }
    }
  }

  private shouldProcessRoute(route: Routes[0]): boolean {
    return !!(
      route.path &&
      route.path !== '**' &&
      route.path !== 'callback' &&
      !route.redirectTo
    );
  }

  private addRouteToConfigs(
    route: Routes[0],
    fullPath: string,
    basePath: string
  ): void {
    const showDropdown =
      route.data?.['breadcrumbConfig']?.showDropdown || false;
    const includeInBreadcrumb =
      route.data?.['breadcrumbConfig']?.includeInBreadcrumb ?? true; // Default to true for backward compatibility

    this.routeConfigs.set(fullPath, {
      path: fullPath,
      label: this.generateLabelFromPath(route.path!),
      icon: this.determineIconFromPath(route.path!),
      parent: basePath || '',
      dropdownItems: showDropdown
        ? this.generateDropdownItemsFromChildren(route.children || [], fullPath)
        : [],
      includeInBreadcrumb,
    });
  }

  /**
   * Generate dropdown items from child routes
   */
  private generateDropdownItemsFromChildren(
    children: Routes,
    parentPath: string
  ): DropdownItem[] {
    const dropdownItems: DropdownItem[] = [];

    children.forEach(child => {
      if (child.path && !child.redirectTo) {
        // Check if this child should be excluded from dropdowns
        const shouldExclude =
          child.data?.['breadcrumbConfig']?.excludeFromDropdown;
        if (shouldExclude) {
          return; // Skip this child
        }

        const fullPath = `${parentPath}/${child.path}`;

        // Use custom label from breadcrumbConfig if available
        const customLabel = child.data?.['breadcrumbConfig']?.customLabel;
        const label = customLabel || this.generateLabelFromPath(child.path);

        // Use custom icon from breadcrumbConfig if available
        const customIcon = child.data?.['breadcrumbConfig']?.customIcon;
        const icon = customIcon || this.determineIconFromPath(child.path);

        dropdownItems.push({
          label,
          url: fullPath,
          icon,
        });
      }
    });

    return dropdownItems;
  }

  /**
   * Generate dropdown items from sibling routes (legacy - keeping for special cases)
   */
  private generateDropdownItemsFromSiblings(
    siblings: Routes,
    basePath: string,
    currentPath: string
  ): DropdownItem[] {
    const dropdownItems: DropdownItem[] = [];

    siblings.forEach(sibling => {
      if (sibling.path && sibling.path !== currentPath && !sibling.redirectTo) {
        // Check if this sibling should be excluded from dropdowns
        const shouldExclude =
          sibling.data?.['breadcrumbConfig']?.excludeFromDropdown;
        if (shouldExclude) {
          return; // Skip this sibling
        }

        const fullPath = basePath
          ? `${basePath}/${sibling.path}`
          : sibling.path;
        const label = this.generateLabelFromPath(sibling.path);
        const icon = this.determineIconFromPath(sibling.path);

        dropdownItems.push({
          label,
          url: fullPath,
          icon,
        });
      }
    });

    return dropdownItems;
  }

  /**
   * Generate human-readable label from route path
   */
  private generateLabelFromPath(path: string): string {
    if (!path) return 'Dashboard';

    // Handle special cases
    if (path === '') return 'Dashboard';
    if (path === 'agent-builder') return 'Agent Builder';

    // Convert kebab-case to Title Case
    return path
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Determine appropriate icon based on route path
   */
  private determineIconFromPath(path: string): string {
    if (!path) return 'home';

    // Icon mapping based on path patterns
    const iconMap: { [key: string]: string } = {
      // '': 'home',
      // agent: 'bot',
      // workflow: 'git-branch',
      // prompt: 'message-square',
      // tools: 'wrench',
      // guardrails: 'shield',
      // knowledge: 'book-open',
      // approvals: 'check-circle',
      // marketplace: 'shopping-cart',
      // manage: 'settings',
      // analytics: 'bar-chart',
      // realm: 'globe',
      // build: 'hammer',
      // 'agent-builder': 'bot',
    };

    // Check for exact matches first
    if (iconMap[path]) {
      return iconMap[path];
    }

    // Check for partial matches
    for (const [key, icon] of Object.entries(iconMap)) {
      if (path.includes(key)) {
        return icon;
      }
    }

    // Default icon
    return '';
  }

  /**
   * Create breadcrumbs based on current route
   */
  private createBreadcrumbs(): BreadcrumbItem[] {
    const breadcrumbs: BreadcrumbItem[] = [];

    // Always start with Console
    breadcrumbs.push({
      label: 'Console',
      url: '/',
      active: false,
      icon: '',
      dropdownItems: this.consoleDropdownItems,
    });

    // Get current route segments (exclude query parameters)
    const url = this.router.url.split('?')[0]; // Remove query parameters
    const segments = url.split('/').filter(segment => segment);

    // Remove 'console' from segments if present
    const consoleIndex = segments.indexOf('console');
    if (consoleIndex !== -1) {
      segments.splice(consoleIndex, 1);
    }

    // Handle redirects and default routes
    const processedSegments = this.processRouteSegments(segments);

    // Build breadcrumb trail
    let currentPath = '';

    processedSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const fullPath = currentPath.substring(1); // Remove leading slash

      const config = this.routeConfigs.get(fullPath);
      if (config && config.includeInBreadcrumb !== false) {
        const isLast = index === processedSegments.length - 1;
        breadcrumbs.push({
          label: config.label,
          url: currentPath.substring(1), // Remove leading slash for relative path
          active: isLast,
          icon: config.icon,
          dropdownItems: config.dropdownItems,
        });
      } else {
        // Handle dynamic segments (like IDs)
        const parentPath = processedSegments.slice(0, index).join('/');
        const parentConfig = this.routeConfigs.get(parentPath);

        if (parentConfig) {
          const isLast = index === processedSegments.length - 1;
          breadcrumbs.push({
            label: this.capitalizeFirst(segment),
            url: currentPath.substring(1), // Remove leading slash for relative path
            active: isLast,
            icon: 'hash',
          });
        }
      }
    });

    // Set the last breadcrumb as active
    if (breadcrumbs.length > 0) {
      breadcrumbs[breadcrumbs.length - 1].active = true;
      // Set Console as active if we're at root
      if (breadcrumbs.length === 1) {
        breadcrumbs[0].active = true;
      }
    }

    return breadcrumbs;
  }

  /**
   * Manually update breadcrumbs (useful for dynamic content)
   */
  public updateBreadcrumbs(breadcrumbs: BreadcrumbItem[]): void {
    this.breadcrumbsSubject.next(breadcrumbs);
  }

  /**
   * Get breadcrumbs for a specific route
   */
  public getBreadcrumbsForRoute(routePath: string): BreadcrumbItem[] {
    const breadcrumbs: BreadcrumbItem[] = [];

    breadcrumbs.push({
      label: 'Console',
      url: '/',
      active: false,
      icon: 'command',
      dropdownItems: this.consoleDropdownItems,
    });

    // Remove query parameters from route path
    const cleanRoutePath = routePath.split('?')[0];
    const segments = cleanRoutePath.split('/').filter(segment => segment);

    // Handle redirects and default routes
    const processedSegments = this.processRouteSegments(segments);
    let currentPath = '';

    processedSegments.forEach((segment: string, index: number) => {
      currentPath += `/${segment}`;
      const fullPath = currentPath.substring(1);

      const config = this.routeConfigs.get(fullPath);
      if (config && config.includeInBreadcrumb !== false) {
        const isLast = index === processedSegments.length - 1;
        breadcrumbs.push({
          label: config.label,
          url: currentPath.substring(1), // Remove leading slash for relative path
          active: isLast,
          icon: config.icon,
          dropdownItems: config.dropdownItems,
        });
      }
    });

    if (breadcrumbs.length > 0) {
      breadcrumbs[breadcrumbs.length - 1].active = true;
      if (breadcrumbs.length === 1) {
        breadcrumbs[0].active = true;
      }
    }

    return breadcrumbs;
  }

  /**
   * Get breadcrumbs for a specific route with configuration options
   */
  public getBreadcrumbsForRouteWithConfig(
    routePath: string,
    config: {
      excludeFromDropdown?: boolean;
      defaultSubPath?: string;
      customLabel?: string;
      customIcon?: string;
      includeInBreadcrumb?: boolean;
      dropdownItems?: Array<{
        label: string;
        url: string;
        icon?: string;
      }>;
    }
  ): BreadcrumbItem[] {
    const breadcrumbs: BreadcrumbItem[] = [];

    breadcrumbs.push({
      label: 'Console',
      url: '/',
      active: false,
      icon: 'command',
      dropdownItems: this.consoleDropdownItems,
    });

    // Remove query parameters from route path
    const cleanRoutePath = routePath.split('?')[0];
    const segments = cleanRoutePath.split('/').filter(segment => segment);
    let currentPath = '';

    segments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const fullPath = currentPath.substring(1);

      const routeConfig = this.routeConfigs.get(fullPath);
      if (routeConfig && (config.includeInBreadcrumb ?? routeConfig.includeInBreadcrumb ?? true) !== false) {
        const isLast = index === segments.length - 1;

        // Apply custom configuration if provided
        const label = config.customLabel || routeConfig.label;
        const icon = config.customIcon || routeConfig.icon;

        // Handle custom dropdown items or exclude from dropdown
        let dropdownItems = routeConfig.dropdownItems;
        if (config.dropdownItems) {
          dropdownItems = config.dropdownItems;
        } else if (config.excludeFromDropdown) {
          dropdownItems = [];
        }

        breadcrumbs.push({
          label,
          url: currentPath,
          active: isLast,
          icon,
          dropdownItems,
        });
      }
    });

    if (breadcrumbs.length > 0) {
      breadcrumbs[breadcrumbs.length - 1].active = true;
      if (breadcrumbs.length === 1) {
        breadcrumbs[0].active = true;
      }
    }

    return breadcrumbs;
  }

  /**
   * Add or update route configuration manually (for custom cases)
   */
  public addRouteConfig(config: RouteBreadcrumbConfig): void {
    this.routeConfigs.set(config.path, config);
  }

  /**
   * Remove route configuration
   */
  public removeRouteConfig(path: string): void {
    this.routeConfigs.delete(path);
  }

  /**
   * Get all route configurations
   */
  public getRouteConfigs(): Map<string, RouteBreadcrumbConfig> {
    return new Map(this.routeConfigs);
  }

  /**
   * Update console dropdown items
   */
  public updateConsoleDropdownItems(items: DropdownItem[]): void {
    this.consoleDropdownItems = items;
    // Refresh current breadcrumbs
    const currentBreadcrumbs = this.createBreadcrumbs();
    this.breadcrumbsSubject.next(currentBreadcrumbs);
  }

  /**
   * Get current breadcrumbs
   */
  public getCurrentBreadcrumbs(): BreadcrumbItem[] {
    return this.breadcrumbsSubject.value;
  }

  /**
   * Clear breadcrumbs
   */
  public clearBreadcrumbs(): void {
    this.breadcrumbsSubject.next([]);
  }

  /**
   * Utility method to capitalize first letter
   */
  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * Method to be called from route resolvers or guards
   */
  public resolveBreadcrumbs(): BreadcrumbItem[] {
    return this.createBreadcrumbs();
  }

  /**`
   * Initialize with routes from app configuration
   * Call this method in your app initialization
   */
  public initializeWithRoutes(routes: Routes): void {
    this.generateRouteConfigsFromRoutes(routes);

    // Initialize breadcrumb router subscription after routes are configured
    this.initializeBreadcrumbs();
  }

  /**
   * Process route segments to handle redirects and default routes
   */
  private processRouteSegments(segments: string[]): string[] {
    if (segments.length === 0) return segments;

    // Handle build route redirect to agent
    if (segments.length === 1 && segments[0] === 'build') {
      return ['build', 'agent'];
    }

    // If we're already at build/agent, keep it as is
    if (
      segments.length === 2 &&
      segments[0] === 'build' &&
      segments[1] === 'agent'
    ) {
      return segments;
    }

    // Handle other potential redirects
    if (segments.length === 1 && segments[0] === '') {
      return ['']; // Dashboard
    }

    return segments;
  }
}
