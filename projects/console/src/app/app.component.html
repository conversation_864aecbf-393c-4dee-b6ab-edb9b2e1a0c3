<div class="dashboard-layout">
  <aava-layout
    [headerHeight]="'60px'"
    [rightPanelWidth]="'250px'"
    [showLeftPanel]="true"
    [showRightPanel]="false"
    [showFooter]="true"
    [(leftPanelOpen)]="leftPanelOpen"
    [leftPanelTriggerTop]="'89%'"
  >
    <div slot="header" class="demo-header">
      <div class="header-left">
        <aava-breadcrumbs [breadcrumbs]="breadcrumbList"> </aava-breadcrumbs>
      </div>
      <div class="header-right">
        <img
          src="svgs/revelioLogo.svg"
          alt="Revelio Logo"
          class="revelio-icon"
          tabindex="0"
          (click)="toggleChat()"
          (keydown.enter)="toggleChat()"
          (keydown.space)="toggleChat()"
        />
      </div>
    </div>
    <div slot="left-panel">
      <aava-rail-navigation-sidebar
        [config]="sidebarConfig"
        (menuItemClick)="onMenuItemClick($event)"
        (profileClick)="onProfileClick($event)"
        (subMenuItemClick)="onSubMenuItemClick($event)"
        (profileMenuItemClick)="onProfileMenuItemClick($event)"
        (profileSubMenuItemClick)="onProfileSubMenuItemClick($event)"
        (studioCardClick)="onStudioCardClick($event)"
      >
      </aava-rail-navigation-sidebar>
    </div>
    <app-revelio [toggleRevelio]="toggleRevelio"></app-revelio>

    <div class="demo-main-content">
      <router-outlet></router-outlet>
    </div>
    <div slot="footer" style="width: 100%">
      <app-footer></app-footer>
    </div>
  </aava-layout>
</div>
