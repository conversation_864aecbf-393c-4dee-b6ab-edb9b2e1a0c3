import { Injectable } from '@angular/core';
import { Resolve, ActivatedRouteSnapshot } from '@angular/router';
import { Observable } from 'rxjs';

import {
  BreadcrumbService,
  BreadcrumbItem,
} from '../services/breadcrumb.service';

export interface BreadcrumbResolverConfig {
  excludeFromDropdown?: boolean; // Exclude this route from sibling dropdowns
  showDropdown?: boolean; // Whether to show dropdown for this breadcrumb (default: false)
  defaultSubPath?: string; // Default subpath to navigate to when selected from dropdown
  customLabel?: string; // Override the auto-generated label
  customIcon?: string; // Override the auto-generated icon
  dropdownItems?: Array<{
    label: string;
    url: string;
    icon?: string;
  }>; // Custom dropdown items for this route
}

@Injectable({
  providedIn: 'root',
})
export class BreadcrumbResolver implements Resolve<BreadcrumbItem[]> {
  constructor(private breadcrumbService: BreadcrumbService) {}

  resolve(
    route: ActivatedRouteSnapshot
  ):
    | Observable<BreadcrumbItem[]>
    | Promise<BreadcrumbItem[]>
    | BreadcrumbItem[] {
    // Get the route path from the activated route
    const routePath = this.getRoutePath(route);

    // Get resolver configuration from route data
    const resolverConfig: BreadcrumbResolverConfig =
      route.data['breadcrumbConfig'] || {};

    // Resolve breadcrumbs for this specific route with configuration
    const breadcrumbs = this.breadcrumbService.getBreadcrumbsForRouteWithConfig(
      routePath,
      resolverConfig
    );

    // Return as observable to match the interface
    return new Observable(observer => {
      observer.next(breadcrumbs);
      observer.complete();
    });
  }

  /**
   * Extract the route path from the activated route
   */
  private getRoutePath(route: ActivatedRouteSnapshot): string {
    const segments: string[] = [];
    let currentRoute: ActivatedRouteSnapshot | null = route;

    while (currentRoute) {
      if (currentRoute.url.length > 0) {
        segments.unshift(...currentRoute.url.map(segment => segment.path));
      }
      currentRoute = currentRoute.parent;
    }

    // Remove 'console' if it's the first segment
    if (segments[0] === 'console') {
      segments.shift();
    }

    return segments.join('/');
  }
}
