.dashboard-layout {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 100%;
  width: 100%;
  .demo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    width: 100%;
    height: 100%;

    // Left side - Breadcrumbs
    .header-left {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }

    // Right side - Revelio
    .header-right {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font-weight: 600;
      color: var(--text-primary, #333);
      font-size: 1.1rem;

      .revelio-icon {
        width: 24px;
        height: 24px;
        cursor: pointer;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 0.8;
        }

        &:focus {
          outline: 2px solid var(--primary-color, #007bff);
          outline-offset: 2px;
          border-radius: 2px;
        }
      }
    }
  }
}

.demo-main-content {
  height: 100%;
}
