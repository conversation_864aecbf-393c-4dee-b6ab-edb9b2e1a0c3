import { AavaDefaultCardComponent, AavaIconComponent } from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

@Component({
  selector: 'app-performer-card',
  standalone: true,
  imports: [AavaDefaultCardComponent, AavaIconComponent, CommonModule],
  templateUrl: './performer-card.component.html',
  styleUrl: './performer-card.component.scss'
})
export class PerformerCardComponent {
  performer = {
    iconName: 'url:svgs/dashboard/star.svg',
    iconType: 'url',
    iconColor: '#3B82F6',
    title: 'Performance',
    subtitle: 'Current Month',
    value: '977'
  };
}
