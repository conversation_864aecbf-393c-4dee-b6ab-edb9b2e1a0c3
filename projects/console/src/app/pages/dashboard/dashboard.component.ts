import { Component } from '@angular/core';
import { ActionListComponent } from './components/action-list/action-list.component';
import { InsightCardComponent } from './components/insight-card/insight-card.component';
import { PerformerCardComponent } from './components/performer-card/performer-card.component';
import { UpdatesCardComponent } from './components/updates-card/updates-card.component';
import { HeroComponent } from 'projects/shared/components/hero/hero.component';
import { ActionCardsComponent } from 'projects/shared/components/action-cards/action-cards.component';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  imports: [ActionListComponent,  InsightCardComponent, PerformerCardComponent, UpdatesCardComponent, HeroComponent, ActionCardsComponent],
})
export class DashboardComponent {}
