import { ActionConfig, TagData } from "@aava/play-core";

export interface FieldSection {
  value: string;
  subValue?: string;
  iconName?: string;
  clickable?: boolean;
  customStyle?: Record<string, string>;
}

export interface TableRow {
  id?: string;
  parentId?: string | null;
  name?: FieldSection;
  email?: FieldSection;
  access?: FieldSection;
  realm?: FieldSection;
  addedOn?: FieldSection;
  validtill?: FieldSection;
  lastLogin?: FieldSection;
  authorized?: FieldSection;
  status?: TagData[];
  action?: Record<string, ActionConfig>;
  sortOrder?: number;
  isSelected?: boolean;
  expanded?: boolean;
}

export type RowDataKey = keyof TableRow;

export interface ColumnConfig {
  field: RowDataKey | 'actions' | 'select' | 'status' | 'expand';
  label: string;
  sortable: boolean;
  filterable: boolean;
  sortingOrder?: number;
  visible: boolean;
  resizable?: boolean;
}


export interface TagDemo {
  label: string;
  color?:
    | 'default'
    | 'primary'
    | 'success'
    | 'warning'
    | 'error'
    | 'info'
    | 'custom';
  customStyle?: Record<string, string>;
  variant?: 'filled' | 'outlined';
  removable?: boolean;
  disabled?: boolean;
  icon?: string;
  iconPosition?: 'start' | 'end';
  avatar?: string;
  pill?: boolean;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  customClass?: string;
  iconColor?: string;
}

export interface Page {
  pageId: number;
  pageName: string;
  description?: string;
  createdAt?: string;
}

export interface DropdownActionOption {
  name: string;
  value: number;
}