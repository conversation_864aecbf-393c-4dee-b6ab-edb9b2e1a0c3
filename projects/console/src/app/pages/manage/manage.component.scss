.manage-container {
  display: flex;
  padding: 12px;
  flex-direction: column;
  border-radius: 12px;
  background-color: var(--color-background-primary);
  //background: var(--Surface-Fill-Light-Surface-White-9, rgba(255, 255, 255, 0.60));

  .tabs-actions-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding-bottom: 8px;
  }

  .actions-bar {
    display: flex;
    align-items: center;

    aava-textbox {
      width: 260px;
      margin-right: 16px;
    }

    .filter-icon {
      margin-right: 8px;
    }

    aava-button {
      white-space: nowrap;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin: 0;
    }
  }
  .tab-content {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .tab-content > * {
    width: 100%;
  }
}

.filter-modal {
  width: 550px;
  padding: 8px;
  border-radius: 8px;
  display: flex;
  gap: 6px;
  flex-direction: column;
}

.content {
  display: flex;
}

.left-panel,
.right-panel {
 flex: 1 0 0;
  padding: 8px;
  display: flex;
  gap: 8px;
  flex-direction: column;
}

.left-panel {
  border-right: 1px solid #eee;
}

.raido-btn,.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 16px;
}
