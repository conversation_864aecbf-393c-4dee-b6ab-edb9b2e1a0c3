.card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  padding: 24px;
  width: 100%;
  box-sizing: border-box;
}

.card-wrapper {
  width: 100%;
}

.complex-card {
  .card-content {
    min-width: 410px;
    min-height: 160px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 24px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
  }

  .icon-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 8px;
    flex-shrink: 0;
  }

  .card-title {
    margin: 0;
    overflow: hidden;
    color: var(--color-text-primary, #3b3f46);
    text-overflow: ellipsis;
    white-space: nowrap;
    font-family: var(--Global-v1-Family-Body, Inter);
    font-size: var(--Global-v1-Size-16, 16px);
    font-style: normal;
    font-weight: 600;
    line-height: var(--Global-v1-Line-height-20, 20px);
  }

  .tags-section {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    flex-shrink: 0;
  }
  .content-body {
    flex: 1;
  }
  .action-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
  }

  .action-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .action-number {
    color: var(--color-text-primary);
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }

  .action-right {
    display: flex;
    align-items: center;
    gap: 24px;
  }
}

@media (max-width: 1280px) {
  .card-grid {
    grid-template-columns: repeat(2, 410px); /* 2 columns on medium screens */
  }
}
@media (max-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: repeat(1, 410px); /* 1 column on small screens */
  }
}
@media (max-width: 600px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}
