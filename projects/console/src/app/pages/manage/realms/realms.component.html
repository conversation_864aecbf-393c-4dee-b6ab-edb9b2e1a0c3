<div class="card-grid">
  <div class="card-wrapper" *ngFor="let item of realms">
    <aava-default-card class="complex-card">
      <div class="card-content">
        <!-- Header Section -->
        <div class="card-header">
          <div class="header-left">
            <div class="icon-container">
              <aava-icon
                iconName="sparkles"
                [iconSize]="20"
                [iconColor]="computedIconColor"
              ></aava-icon>
            </div>
            <h2 class="card-title">
              {{ item.realmName }}
            </h2>
          </div>
        </div>

        <!-- Tags Section -->
        <div class="tags-section">
          <aava-tag
            [label]="item.projectName"
            color="custom"
            size="lg"
            [customStyle]="{ background: '#E6F3FF' }"
            [pill]="false"
          ></aava-tag>
          <aava-tag
            [label]="item.orgName"
            color="custom"
            [customStyle]="{ background: '#E6F3FF' }"
            size="lg"
            [pill]="false"
          ></aava-tag>

          <aava-tag
            [label]="item.domainName"
            color="custom"
            [customStyle]="{ background: '#E6F3FF' }"
            size="lg"
            [pill]="false"
          ></aava-tag>
          <aava-tag
            [label]="item.teamName"
            color="custom"
            [customStyle]="{ background: '#E6F3FF' }"
            size="lg"
            [pill]="false"
          ></aava-tag>
        </div>

        <!-- Action Section -->
        <div class="action-section">
          <div class="action-left">
            <aava-icon
              iconName="user"
              [iconColor]="computedIconColor"
              [iconSize]="16"
            ></aava-icon>
            <span class="action-number">{{ item.realmId }} +</span>
          </div>
          <div class="action-right">
            <div (click)="delete(item)">
              <aava-icon
                iconName="trash-2"
                [iconColor]="'#0084FF'"
                [iconSize]="16"
                [cursor]="true"
              ></aava-icon>
            </div>
            <div (click)="edit(item)">
              <aava-icon
                iconName="pencil"
                [iconColor]="'#0084FF'"
                [iconSize]="16"
                [cursor]="true"
              ></aava-icon>
            </div>
          </div>
        </div>
      </div>
    </aava-default-card>
  </div>
</div>
