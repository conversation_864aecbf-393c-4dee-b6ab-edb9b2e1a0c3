import {
  AavaDefaultCardComponent,
  AavaDialogService,
  AavaIconComponent,
  AavaTagComponent,
  AavaToastService,
} from '@aava/play-core';
import { Component, Input, OnInit } from '@angular/core';
import { RealmService } from '../../../services/realm.service';
import { CommonModule } from '@angular/common';
import { CreateRealmComponent } from 'projects/shared/components/create-realm/create-realm.component';

@Component({
  selector: 'app-realms',
  imports: [
    CommonModule,
    AavaDefaultCardComponent,
    AavaIconComponent,
    AavaTagComponent,
  ],
  templateUrl: './realms.component.html',
  styleUrl: './realms.component.scss',
})
export class RealmsComponent implements OnInit {
  @Input() realms: any[] = [];

  constructor(
    private RealmsService: RealmService,
    private dialogService: AavaDialogService,
    private toastService: AavaToastService
  ) {}

  ngOnInit(): void {}

  get computedIconColor(): string {
    return 'var(--color-text-primary)';
  }

  edit(item: any): void {
    // Logic to edit the item
    console.log('Edit clicked for', item);
    this.dialogService.openModal(
      CreateRealmComponent,
      {
        position: 'center',
        width: '718px',
        showCloseButton: true,
      },
      { realm: item }
    );
  }

  delete(item: any): void {
    this.dialogService
      .confirmation({
        title: `Delete ${item.realmName}`,
        message: 'Are you sure you want to delete this Realm Record',
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        confirmButtonVariant: 'danger',
        destructive: true,
      })
      .then((result: unknown) => {
        const dialogResult = result as { confirmed?: boolean };
        if (dialogResult.confirmed) {
          this.RealmsService.deleteRealm(item.realmId).subscribe({
            next: () => {
              // Remove from local array after successful delete
              this.realms = this.realms.filter(r => r.realmId !== item.realmId);
              this.success('Record');
            },
            error: err => {
              this.apiFailed(err);
            },
          });
        } else {
          console.log('User cancelled deletion');
        }
      });
  }

  success(message: string) {
    this.toastService.success({
      title: 'Deleted Successfully!',
      message: `${message} has been deleted Successfully`,
      duration: 2000,
      customWidth: '350px',
      design: 'modern',
      size: 'medium',
      showCloseButton: true,
    });
  }

  //api failed
  apiFailed(err: any) {
    const title = err?.name || 'Error Occurred';
    const message =
      err?.error?.message ||
      'Connection error. Unable to connect to the server at present';

    this.toastService.error({
      title,
      message,
      duration: 4000,
      customWidth: '300px',
      design: 'modern',
      size: 'small',
      showCloseButton: false,
    });
  }
}
