<div class="manage-container">
  <!-- Tabs + Actions Row -->
  <div class="tabs-actions-row">
    <!-- Tabs -->
    <aava-tabs
      [tabs]="buttonTabs"
      [activeTabId]="activeTabIds.button"
      variant="button"
      [showcontainerBg]="false"
      (tabChange)="onTabChange($event, 'button')"
      ariaLabel="Button style tabs"
      [showContentPanels]="false"
      size="md"
      [activeButtonTabStyles]="{
        'font-weight': '600',
        background: '#E6F3FF',
        color: '#339DFF',
        border: '1px solid #339DFF',
        padding: '10px 12px',
      }"
    ></aava-tabs>

    <!-- Actions (Search + Buttons) -->
    <div class="actions-bar">
      <aava-textbox placeholder="Search" [(ngModel)]="searchValue" size="md" (input)="onSearchInputChange($event)">
        <aava-icon slot="icon-start" iconName="search"></aava-icon>
        <aava-icon
          slot="icon-end"
          iconName="x"
          (click)="clearSearch()"
        ></aava-icon>
      </aava-textbox>

      <div #filterBtn class="flyout">
        <aava-button
          variant="default"
          iconName="ListFilter"
          iconPosition="only"
          size="md"
          iconColor="#0084FF"
          [customStyles]="{
            border: '0.5px solid var(--Colors-Border-subtle, #BBBEC5)',
            borderRadius:'var(--Global-V1-Radius-Rad3, 6px)',
            background: ' var(--Global-colors-White-White, #FDFDFD)',
          }"
          (userClick)="flyout1.open(filterBtn)"
        ></aava-button>
      </div>

      <aava-flyout #flyout1 [width]="570">
        <div class="filter-modal">
          <div class="content">
            <!-- Left Section -->
            <div class="left-panel">
              <aava-search-bar
                label="Practice Area"
                placeholder="Search with default styling..."
                (searchClick)="onSearch($event, 'default')"
                (textboxChange)="onTextboxChange($event)"
              ></aava-search-bar>
              <div class="raido-btn">
                <aava-radio-button
                  [options]="practiceAreaOptions"
                  name="practice"
                  [(selectedValue)]="selectedPractice"
                  (selectedValueChange)="onPracticeChange($event)"
                ></aava-radio-button>
              </div>
            </div>

            <!-- Right Section -->
            <div class="right-panel">
              <aava-search-bar
                label="Capability Tags"
                placeholder="Search with default styling..."
                (searchClick)="onSearch($event, 'default')"
                (textboxChange)="onTextboxChange($event)"
              ></aava-search-bar>
              <div class="tags">
                <aava-tag
                  *ngFor="let tag of removableTags"
                  [label]="tag.label"
                  [color]="tag.color ?? 'primary'"
                  [variant]="'filled'"
                  [pill]="tag.pill ?? true"
                  [size]="'md'"
                  [removable]="tag.removable ?? true"
                  (removed)="onRemove(tag.label)"
                  (clicked)="onClick(tag.label)"
                >
                </aava-tag>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="footer">
            <aava-button
              label="Cancel"
              variant="secondary"
              size="sm"
              (userClick)="flyout1.close()"
            ></aava-button>
            <aava-button
              label="Apply"
              variant="primary"
              size="sm"
              (userClick)="applyFilter(); flyout1.close()"
            ></aava-button>
          </div>
        </div>
      </aava-flyout>

      <aava-button
        *ngIf="activeTabIds.button === 'user'"
        label="Add User"
        variant="info"
        iconName="plus"
        iconPosition="right"
        iconColor="#fff"
        size="md"
        (userClick)="onCreateUser($event)"
        pressedEffect="ripple"
        [customStyles]="{
          border: '1px solid #0084FF',
          color: '#FFF',
          background: '#0084FF',
        }"
      ></aava-button>

      <aava-button
        *ngIf="activeTabIds.button !== 'user'"
        label="Add Realm"
        variant="info"
        iconName="plus"
        iconPosition="right"
        iconColor="#fff"
        size="md"
        (userClick)="onCreateRealm($event)"
        pressedEffect="ripple"
        [customStyles]="{
          border: '1px solid #0084FF',
          color: '#FFF',
          background: '#0084FF',
        }"
      ></aava-button>
    </div>
  </div>

  <!-- Tab Content -->
  <div class="tab-content">
    <ng-container *ngIf="activeTabIds.button === 'user'">
      <app-user [isLoading]="isLoading" [rowData]="rowData" (refresh)="fetchUserData()"></app-user>
    </ng-container>

    <ng-container *ngIf="activeTabIds.button === 'realms'">
      <app-realms [realms]="realms"></app-realms>
    </ng-container>
  </div>
</div>
