.add__role--container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
}
.realm__form {
  display: flex;
  flex-flow: column;
  gap: 16px;
}
.role__form-fields {
  display: flex;
  flex-flow: column;
  gap: 16px;
}
.input__field--wrapper {
  flex: 1;
  width: 360px;
}

.filter-label {
    display: block;
  font-weight: 500;
  text-align: left;
  color: #14161f;
  margin-bottom: 4px;
}
.required::after {
  content: " *";
  color: red;
  font-weight: bold;
}

.button__container {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin: 16px 0 16px 0;

  &.main {
    justify-content: flex-end;
  }
}