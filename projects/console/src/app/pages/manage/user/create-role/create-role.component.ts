import {
  AavaButtonComponent,
  AavaTextboxComponent,
  DropdownOption,
} from '@aava/play-core';
import { Component, OnInit, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { UserService } from 'projects/console/src/app/services/user.service';

@Component({
  selector: 'app-create-role',
  imports: [ReactiveFormsModule, AavaButtonComponent, AavaTextboxComponent],
  templateUrl: './create-role.component.html',
  styleUrl: './create-role.component.scss',
})
export class CreateRoleComponent implements OnInit {
  addRoleForm!: FormGroup;
  accessControlList = signal<any[]>([]);
  roleListLoading = signal<boolean>(false);
  roleList = signal<DropdownOption[]>([]);
  selectedRoleValue: string = '';
  isAccessControl = signal<boolean>(false);

  pageListLoading = signal<boolean>(false);
  constructor(
    private formBuilder: FormBuilder,
    private userService: UserService
  ) {}
  ngOnInit(): void {
    this.getRoleList();
    this.addRoleForm = this.getAddRoleForm();
  }

  getAddRoleForm() {
    return this.formBuilder.group({
      roleName: ['', [Validators.required]],
      description: ['', [Validators.required]],
    });
  }

  getRoleList() {
    this.roleListLoading.set(true);
    this.userService.getAllRoles().subscribe({
      next: (userMgmtResponse: any) => {
        this.roleList.set(
          userMgmtResponse.map((opt: any) => ({
            name: opt.roleName,
            value: opt.roleId,
          }))
        );
        this.roleListLoading.set(false);
      },
      error: e => {
        console.error(e);
        this.roleListLoading.set(false);
      },
    });
  }

  createRole() {
    if (this.addRoleForm.valid) {
      const payload = this.addRoleForm.value;
      this.userService.createRole(payload).subscribe({
        next: (res: any) => {
          this.roleList.update(currentList => [
            ...currentList,
            { name: res.roleName, value: res.roleId },
          ]);
          this.selectedRoleValue = res.roleName;
          this.onRoleSelectionChange(res.roleId);
          this.closeRealmPopup();
          this.addRoleForm.reset();
        },
        error: e => console.error(e),
      });
    }
  }

  onRoleSelectionChange(event: any) {
    const roleId = event?.selectedOptions?.[0]?.value ?? event;
    this.userService.getExistingAccessControl(roleId).subscribe({
      next: (res: any) => {
        this.accessControlList.set(res.accessControl);
      },
      error: e => console.error(e),
      complete: () => this.pageListLoading.set(false),
    });
  }

  closeRealmPopup() {
    this.addRoleForm.reset();
  }
}
