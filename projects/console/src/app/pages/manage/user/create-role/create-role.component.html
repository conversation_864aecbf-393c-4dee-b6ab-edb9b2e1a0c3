<div class="add__role--container">
    <form [formGroup]="addRoleForm" class="realm__form">
      <div class="role__form-fields">
        <div class="input__field--wrapper">
          <label for="roleName" class="filter-label required"
            >Name of the Role</label
          >
          <aava-textbox
            formControlName="roleName"
            id="roleName"
            name="roleName"
            placeholder="Enter Role Name"
            [required]="true"
            [fullWidth]="false"
            size="md"
          >
          </aava-textbox>
        </div>

        <div class="input__field--wrapper">
          <label for="description" class="filter-label required"
            >Description</label
          >
          <aava-textbox
            formControlName="description"
            id="description"
            name="description"
            placeholder="Enter Description"
            [required]="true"
            [fullWidth]="false"
            size="md"
          >
          </aava-textbox>
        </div>
      </div>
    </form>
    <div class="button__container">
      <aava-button
        label="Cancel"
        variant="secondary"
        size="md"
        (userClick)="closeRealmPopup()"
        [width]="'100%'"
        class="action-button"
      >
      </aava-button>
      <aava-button
        label="Create"
        variant="primary"
        size="md"
        (userClick)="createRole()"
        [width]="'100%'"
        class="action-button"
        [disabled]="!addRoleForm.valid"
      >
      </aava-button>
    </div>
  </div>