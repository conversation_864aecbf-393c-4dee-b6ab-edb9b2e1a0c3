import {
  AavaUserTableComponent,
  AavaSkeletonComponent,
  ActionConfig,
  TagData,
  AavaDialogService,
  AavaToastService,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UserService } from '../../../services/user.service';
import { ColumnConfig, TableRow } from '../interface/user.interface';
import { Router } from '@angular/router';

@Component({
  selector: 'app-user',
  standalone: true,
  imports: [CommonModule, AavaUserTableComponent, AavaSkeletonComponent],
  templateUrl: './user.component.html',
  styleUrl: './user.component.scss',
})
export class UserComponent implements OnInit {
  pageSize = 10;
  page = 0;
  @Input() isLoading = true;
  @Input() rowData: TableRow[] = [];
  @Output() refresh = new EventEmitter<void>();

  rowImgStyle = {
    borderRadius: '50%',
    marginRight: '8px',
    objectFit: 'cover',
    display: 'inline-block',
    verticalAlign: 'middle',
  };

  columnData: ColumnConfig[] = [
    {
      field: 'name',
      label: 'Name',
      sortable: true,
      filterable: true,
      visible: true,
    },
    {
      field: 'access',
      label: 'Access',
      sortable: true,
      filterable: true,
      visible: true,
    },
    {
      field: 'realm',
      label: 'Realm',
      sortable: true,
      filterable: false,
      visible: true,
    },
    {
      field: 'addedOn',
      label: 'Added On',
      sortable: true,
      filterable: false,
      visible: true,
    },
    {
      field: 'validtill',
      label: 'Valid Till',
      sortable: true,
      filterable: true,
      visible: true,
    },
    {
      field: 'lastLogin',
      label: 'Last Login',
      sortable: true,
      filterable: true,
      visible: true,
    },
    {
      field: 'authorized',
      label: 'Authorized By',
      sortable: false,
      filterable: false,
      visible: true,
    },
    {
      field: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      visible: true,
    },
  ];

  constructor(
    private dialogService: AavaDialogService,
    private toastService: AavaToastService,
    private UserService: UserService,
    private router:Router,
  ) {}

  ngOnInit() {}

  onActionFromTable(event: {
    row: TableRow;
    actionKey: string;
    config: ActionConfig;
  }): void {
    if (!event.row.id) {
      console.error('Row ID is missing, cannot perform action!');
      return;
    }

    if (event.actionKey === 'edit') {
      this.router.navigate(['/add-user'], {
      queryParams: { id: event.row.id, },
    });
      // handle edit
      console.log('Editing user with ID:', event.row.id);
    } else if (event.actionKey === 'delete') {
      this.deleteUser(event.row);
    }
  }

  deleteUser(user: TableRow): void {
    // Ensure id and name exist
    if (!user.id) {
      console.error('Cannot delete user: ID is missing');
      return;
    }
    const userName = user.name?.value || 'User'; // fallback if name missing
    this.dialogService
      .confirmation({
        title: `Delete ${userName}`,
        message: `Are you sure you want to delete ${userName}?`,

        destructive: true,
      })
      .then((result: unknown) => {
        const dialogResult = result as { confirmed?: boolean };
        if (dialogResult.confirmed) {
          // Call API with user.id
          this.UserService.deleteUser(user.id!).subscribe({
            next: () => {
              // Remove deleted user from local array
              this.rowData = this.rowData.filter(u => u.id !== user.id);
              this.refresh.emit();
              this.success(userName);
            },
            error: err => this.apiFailed(err),
          });
        } else {
          console.log('User cancelled deletion');
        }
      });
  }

  //success toaster

  success(userName: string) {
    this.toastService.success({
      title: 'Deleted Successfully!',
      message: `${userName} has been deleted Successfully`,
      duration: 2000,
      customWidth: '350px',
      design: 'modern',
      size: 'medium',
      showCloseButton: true,
    });
  }
  //api failed
  apiFailed(err: any) {
    const title = err?.name || 'Error Occurred';
    const message =
      err?.error?.message ||
      'Connection error. Unable to connect to the server at present';

    this.toastService.error({
      title,
      message,
      duration: 4000,
      customWidth: '300px',
      design: 'modern',
      size: 'small',
      showCloseButton: false,
    });
  }
}
