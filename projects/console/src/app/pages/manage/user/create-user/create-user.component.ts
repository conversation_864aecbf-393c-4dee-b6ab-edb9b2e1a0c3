import {
  AavaAutocompleteComponent,
  AavaButtonComponent,
  AavaDialogService,
  AavaDropdownComponent,
  AavaIconComponent,
  AavaOptionComponent,
  AavaTagComponent,
  AavaTextboxComponent,
  AavaToastService,
  DropdownOption,
} from '@aava/play-core';
import { Component, OnInit, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from 'projects/console/src/app/services/user.service';
import { TokenStorageService } from '@shared';
import { DropdownActionOption, Page } from '../../interface/user.interface';
import { CreateRoleComponent } from '../create-role/create-role.component';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-create-user',
  imports: [
    CommonModule,
    AavaIconComponent,
    ReactiveFormsModule,
    AavaTextboxComponent,
    AavaAutocompleteComponent,
    AavaButtonComponent,
    AavaOptionComponent,
    AavaTagComponent,
    AavaDropdownComponent,
  ],
  templateUrl: './create-user.component.html',
  styleUrl: './create-user.component.scss',
})
export class CreateUserComponent implements OnInit {
  userManagementForm!: FormGroup;

  userId!: number;
  roleList = signal<DropdownOption[]>([]);
  selectedRealmTag: any[] = [];
  actionList = signal<DropdownOption[]>([]);
  actionCheckboxList = signal<string[]>([]);
  selectedTagRemovable = signal<boolean>(true);
  userDataLoading = signal<boolean>(false);
  selectedRoleValue: string = '';
  isAccessControl = signal<boolean>(false);
  accessControlList = signal<any[]>([]);
  pageList = signal<Page[]>([]);
  roleListLoading = signal<boolean>(false);
  pageListLoading = signal<boolean>(false);
  realmsListLoading = signal<boolean>(false);
  orgDataLoading = signal<boolean>(false);
  selectedTags: { [key: string]: DropdownActionOption[] } = {};
  realmsList = signal<any[]>([]);
  selectedValues: { [key: string]: string[] } = {};

  constructor(
    private activatedRoute: ActivatedRoute,
    private userService: UserService,
    private formBuilder: FormBuilder,
    private tokenStorage: TokenStorageService,
    private router: Router,
    private dialogService: AavaDialogService,
    private toastService: AavaToastService
  ) {}
  ngOnInit(): void {
    this.getRoleList();
    this.getActionList();
    this.getPageList();
    this.getRealmsList();
    this.userManagementForm = this.getUserManagementForm();
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params?.id) {
        this.userId = params.id;
        this.getViewUser(params.id);
      }
    });
  }

  getViewUser(id: number) {
    this.userDataLoading.set(true);
    this.userService.getUserDetails(id).subscribe({
      next: (res: any) => {
        this.userManagementForm.patchValue({
          email: res.email,
          role: res.roles[0].roleId,
        });
        this.selectedRoleValue = res.roles[0].roleName;
        this.onRoleSelectionChange(res.roles[0].roleId);
        this.selectedRealmTag = res.realms.map((realm: any) => ({
          label: realm.realmName,
          value: realm.realmId,
        }));
        this.userDataLoading.set(false);
      },
      error: e => {
        console.error(e);
        this.userDataLoading.set(false);
      },
    });
  }

  getRoleList() {
    this.roleListLoading.set(true);
    this.userService.getAllRoles().subscribe({
      next: (userMgmtResponse: any) => {
        this.roleList.set(
          userMgmtResponse.map((opt: any) => ({
            name: opt.roleName,
            value: opt.roleId,
          }))
        );
        this.roleListLoading.set(false);
      },
      error: e => {
        console.error(e);
        this.roleListLoading.set(false);
      },
    });
  }

  getPageList() {
    this.pageListLoading.set(true);
    this.userService.getAllPages().subscribe({
      next: (pages: any) => {
        this.pageList.set(pages);
        this.initializePageControl();
        this.pageListLoading.set(false);
      },
      error: e => {
        console.error(e);
        this.pageListLoading.set(false);
      },
    });
  }

  getActionList() {
    this.userService.getAllActions().subscribe({
      next: (actions: any) => {
        this.actionList.set(
          actions.map((opt: any) => ({
            name: opt.actionName,
            value: opt.actionId,
          }))
        );
        this.actionCheckboxList.set(actions.map((opt: any) => opt.actionName));
      },
      error: e => console.error(e),
    });
  }

  getRealmsList() {
    this.realmsListLoading.set(true);
    this.userService.getAllRealms().subscribe({
      next: (realms: any) => {
        this.realmsList.set(
          realms.map((opt: any) => ({
            label: opt.realmName,
            value: opt.realmId,
          }))
        );
        this.realmsListLoading.set(false);
      },
      error: e => {
        console.error(e);
        this.realmsListLoading.set(false);
      },
    });
  }

  initializePageControl() {
    this.pageList().forEach((page: any) => {
      this.userManagementForm.addControl(
        page.pageId,
        this.formBuilder.control([])
      );
    });
  }

  onRoleSelectionChange(event: any) {
    const roleId = event?.selectedOptions?.[0]?.value ?? event;
    this.userManagementForm.get('role')?.patchValue(roleId);
    this.pageListLoading.set(true);
    this.userService.getExistingAccessControl(roleId).subscribe({
      next: (res: any) => {
        this.resetAccessControll();
        this.accessControlList.set(res.accessControl);
        if (!res.accessControl?.length) {
          this.isAccessControl.set(true);
          this.selectedTagRemovable.set(true);
          return;
        }
        this.isAccessControl.set(false);
        this.selectedTagRemovable.set(false);
        this.updatedUserManagementFormValues();
      },
      error: (e: any) => console.error(e),
      complete: () => this.pageListLoading.set(false),
    });
  }

  resetAccessControll() {
    this.pageList().forEach(page => {
      const formControlName = page.pageId.toString();
      this.selectedValues[formControlName] = [];
      this.selectedTags[formControlName] = [];
      this.userManagementForm.get(formControlName)?.patchValue([]);
    });
  }

  onSelectionChange(event: any, formControl: string) {
    const selectedValues = event.selectedOptions.map(
      (option: any) => option.value
    );
    this.userManagementForm.get(formControl)?.setValue(selectedValues);
    this.selectedTags[formControl] = event.selectedOptions;
  }

  updatedUserManagementFormValues() {
    const actionMap = new Map(
      this.accessControlList().map(item => [item.page, item.actions])
    );
    const actionIdsMap = new Map(
      this.actionList().map(item => [item.name, item.value])
    );

    this.pageList().forEach(page => {
      const pageActions = this.accessControlList().find(
        pa => pa.page === page.pageName
      );
      if (pageActions) {
        this.selectedValues[page.pageId.toString()] = pageActions.actions;
        this.selectedTags[page.pageId.toString()] = pageActions.actions.map(
          (op: string) => ({ name: op })
        );
      }

      const formControlName = page.pageId.toString();
      const formControl = this.userManagementForm.get(formControlName);
      const actionNames = actionMap.get(page.pageName);
      if (formControl && actionNames) {
        const actionIds = actionNames
          .map((name: any) => actionIdsMap.get(name))
          .filter((id: any) => id !== undefined);
        formControl.patchValue(actionIds);
      }
    });
  }

  getUserManagementForm() {
    return this.formBuilder.group({
      email: ['', [Validators.required]],
      role: ['', [Validators.required]],
    });
  }

  addNewUser() {
    if (this.userManagementForm.valid && this.selectedRealmTag?.length) {
      const { email, role, ...pagePermissions } = this.userManagementForm.value;
      const roleId = parseInt(role);
      const roleIds = [roleId];
      const payload: any = {
        email: email,
        roleIds: roleIds,
        realmIds: this.selectedRealmTag.map(realm => realm.value),
        authorizedBy:
          this.tokenStorage.getDaUsername() || '<EMAIL>',
      };

      if (this.selectedTagRemovable()) {
        const permissions: Array<{ pageId: number; actionId: number }> = [];

        Object.entries(pagePermissions).forEach(([pageId, actionIds]) => {
          if (Array.isArray(actionIds) && actionIds.length > 0) {
            actionIds.forEach((actionId: number) => {
              permissions.push({
                pageId: parseInt(pageId),
                actionId: actionId,
              });
            });
          }
        });

        payload.rolePermessionRequest = {
          roleId: roleId,
          permissions: permissions,
        };
      }

      this.dialogService
        .confirmation({
          message: `Are you sure you want to save?`,
        confirmButtonText: 'Save',
        cancelButtonText: 'Cancel',
        })
        .then((result: unknown) => {
          const dialogResult = result as { confirmed?: boolean };
          if (dialogResult.confirmed) {
            // Call API with user.id
            if (this.userId) {
              this.userService.updateUser(payload, this.userId).subscribe({
                next: (res: any) => {
                  this.success('Updated');
                },
                error: e => {
                  this.apiFailed(e);
                },
              });
            } else {
              this.userService.addNewUser(payload).subscribe({
                next: (res: any) => {
                  this.success('Added');
                },
                error: e => {
                  this.apiFailed(e);
                },
              });
            }
          } else {
            console.log('User cancelled deletion');
          }
        });
    }
  }

  success(message: string) {
    this.toastService.success({
      title: `${message} Successfully!`,
      message: `Record has been ${message} Successfully`,
      duration: 2000,
      customWidth: '350px',
      design: 'modern',
      size: 'medium',
      showCloseButton: true,
    });
  }

  apiFailed(err: any) {
    const title = err?.name || 'Error Occurred';
    const message =
      err?.error?.message ||
      'Connection error. Unable to connect to the server at present';

    this.toastService.error({
      title,
      message,
      duration: 4000,
      customWidth: '300px',
      design: 'modern',
      size: 'small',
      showCloseButton: false,
    });
  }

  onValueChange(event: any) {}
  onOptionSelected(event: any) {
    const exists = this.selectedRealmTag.some(
      item => item.value === event.value
    );
    if (!exists) {
      this.selectedRealmTag.push(event);
    }
  }

  removeRealmTag(tagValue: number) {
    this.selectedRealmTag = this.selectedRealmTag.filter(
      tag => tag.value !== tagValue
    );
  }

  removeTag(event: any, formControl: string) {
    this.selectedTags[formControl] = this.selectedTags[formControl].filter(
      option => option.value !== event.value
    );
    const values = this.selectedTags[formControl].map(option => option.value);
    const DropdownSelectedvalues = this.selectedTags[formControl].map(
      option => option.name
    );
    this.userManagementForm.get(formControl)?.setValue(values);
    this.selectedValues[formControl] = DropdownSelectedvalues;
  }

  isUserButtonEnabled(): boolean {
    return this.userManagementForm.valid && this.selectedRealmTag?.length > 0;
  }

  onExit() {
    this.router.navigate(['/manage']);
  }

  addNewRole() {
    this.dialogService.openModal(CreateRoleComponent, {
      position: 'center',
      width: '420px',
      height: '270px',
      showCloseButton: true,
    });
  }
}
