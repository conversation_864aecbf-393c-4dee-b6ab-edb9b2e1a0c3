<div class="header-bar">
  <div class="d-flex align-items-center">
     <aava-icon
      (userClick)="onExit()"
      [cursor]="true"
      [iconSize]="20"
      iconName="arrow-left"
      iconColor="#4C515B"
    ></aava-icon>
     <h4 class="mb-0">Add New User</h4>
  </div>
  <aava-button
      [label]="userId ? 'Update User' : 'Save'"
      variant="primary"
      size="md"
      (userClick)="addNewUser()"
      [disabled]="!isUserButtonEnabled()"
    >
    </aava-button>
</div>

<form [formGroup]="userManagementForm">
  <div class="container-fluid p-4">
    <div class="row">
      <!-- User Details -->
      <div class="col-md-4 mb-3">
        <div class="card p-3 h-100 user-details">
          <h5>User Details</h5>
          <div class="form-group mb-3">
            <div class="form-group">
              @if (!userDataLoading()) {
                <div class="input__field input">
                  <label for="email" class="filter-label required"
                    >User Email</label
                  >
                  <aava-textbox
                    formControlName="email"
                    id="email"
                    name="email"
                    placeholder="Enter Email"
                    size="md"
                  >
                  </aava-textbox>
                </div>
              } @else {
                <div class="input__field input">
                  <div class="skeleton-loader skeleton-label"></div>
                  <div class="skeleton-loader skeleton-input"></div>
                </div>
              }
            </div>
          </div>
          <div class="form-group">
            @if (!roleListLoading()) {
              <div class="input__field dropdown">
                <label for="role" class="filter-label required">Role</label>
                <aava-dropdown
                  id="role"
                  [options]="roleList()"
                  dropdownTitle="Select a Role"
                  [selectedValue]="selectedRoleValue"
                  (selectionChange)="onRoleSelectionChange($event)"
                >
                  <aava-option (click)="addNewRole()">
                    <span>Create New Role +</span>
                  </aava-option>
                </aava-dropdown>
              </div>
            } @else {
              <div class="input__field dropdown">
                <div class="skeleton-loader skeleton-label"></div>
                <div class="skeleton-loader skeleton-dropdown"></div>
              </div>
            }
          </div>
        </div>
      </div>

      <!-- Access Control -->
      <div class="col-md-4 bg-white p-4 rounded mb-3 user-details">
      <h5 class="mb-4">Access Control</h5>

      <div class="access-scroll">
      <div *ngIf="!pageListLoading(); else loadingTemplate">
        <div *ngFor="let page of pageList();" class="mb-4 border-bottom pb-3">
          <label class="form-label fw-bold" [for]="page.pageId">{{ page.pageName }}</label>

          <div class="mb-2">
            <aava-dropdown
              [id]="page.pageId"
              [disabled]="!isAccessControl()"
              [selectedValues]="selectedValues[page.pageId.toString()]"
              dropdownTitle="Select Permissions"
              [options]="actionList()"
              [checkboxOptions]="actionCheckboxList()"
              (selectionChange)="onSelectionChange($event, page.pageId.toString())">
            </aava-dropdown>
          </div>

          <div>
            <label class="form-label small text-muted">Selected Values:</label>
            <div class="d-flex flex-wrap gap-2">
              <aava-tag
                *ngFor="let tag of selectedTags[page.pageId.toString()];"
                [label]="tag.name"
                [customStyle]="{ background: '#E9F0FC', color: '#2D3036' }"
                [pill]="true"
                size="md"
                [removable]="selectedTagRemovable()"
                (removed)="removeTag(tag, page.pageId.toString())"
                iconColor="#000000">
              </aava-tag>
            </div>
          </div>
        </div>
      </div>
</div>
      <!-- Skeleton Loader -->
      <ng-template #loadingTemplate>
        @for (item of [1, 2, 3]; track item) {
            <div class="skeleton-section">
              <div class="skeleton-loader skeleton-section-label"></div>
              <div class="skeleton-loader skeleton-dropdown"></div>
              <div
                class="skeleton-loader skeleton-section-label"
                style="width: 80px; margin-top: 12px"
              ></div>
            </div>
          }
      </ng-template>
    </div>

      <!-- Add Realm -->
      <div class="col-md-4 mb-3">
        <div class="card p-3 h-100 add-realm">
          <h5>Add Realm</h5>

          @if (!realmsListLoading()) {
            <div class="form-group mb-3">
              <aava-autocomplete
                [options]="realmsList()"
                placeholder="Search for a Realms"
                startIcon="search"
                startIconColor="#6b7280"
                (optionSelected)="onOptionSelected($event)"
                [showDefaultOptions]="true"
                (valueChange)="onValueChange($event)"
              >
              </aava-autocomplete>
            </div>
            <div class="mb-3">
              <label>Selected Realms</label>
              <div class="d-flex flex-wrap gap-2">
              @for (tag of selectedRealmTag; track tag) {
               
                  <aava-tag
                    [label]="tag.label"
                    [customStyle]="{ background: '#EDEDF3', color: '#2D3036' }"
                    [pill]="true"
                    size="md"
                    [removable]="true"
                    iconColor="#2D3036"
                    (removed)="removeRealmTag(tag.value)"
                  ></aava-tag>
                
              }
              </div>
            </div>
          } @else {
            <div class="skeleton-realm-section">
              <div class="skeleton-loader skeleton-autocomplete"></div>
              <div class="skeleton-loader skeleton-button"></div>
            </div>
          }
          <!-- <div class="form-group mb-3">
            <label>Max Workspaces <span class="text-danger">*</span></label>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</form>
