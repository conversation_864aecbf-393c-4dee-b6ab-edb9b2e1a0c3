
/* Skeleton Loader Styles */
.skeleton-loader {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite ease-in-out;
  border-radius: 8px;
}

.skeleton-input {
  height: 42px;
  width: 100%;
  margin-bottom: 8px;
}

.skeleton-dropdown {
  height: 42px;
  width: 100%;
  margin-bottom: 8px;
}

.skeleton-label {
  height: 16px;
  width: 120px;
  margin-bottom: 8px;
}

.skeleton-access-control {
  margin-bottom: 24px;

  .skeleton-title {
    height: 20px;
    width: 150px;
    margin-bottom: 16px;
  }

  .skeleton-section {
    margin-bottom: 16px;

    .skeleton-section-label {
      height: 16px;
      width: 100px;
      margin-bottom: 8px;
    }
  }
}

.skeleton-realm-section {
  .skeleton-autocomplete {
    height: 42px;
    width: 100%;
    margin-bottom: 12px;
  }

  .skeleton-button {
    height: 42px;
    width: 100%;
    margin-bottom: 16px;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}


.card {
  width: 100%;
  border-radius: 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.user-details {
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  min-height: 768px;
}

.access-scroll {
  max-height: 683px;
  overflow-y: auto;
  padding-right: 8px;
}
.access-control {
  background-color: #fff; 
  padding: 16px;
  overflow-y: auto;
  border-radius: 8px;
  min-height: 768px;
  max-height: 768px;
}

.add-realm {
  background-color: #fff; 
  padding: 16px;
  border-radius: 8px;
  min-height: 768px;
}

.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;  
  height: 64px;
}
