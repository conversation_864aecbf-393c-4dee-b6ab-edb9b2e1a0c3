import {
  AavaButtonComponent,
  AavaDialogService,
  AavaFlyoutComponent,
  AavaIconComponent,
  AavaRadioButtonComponent,
  AavaSearchBarComponent,
  AavaTabsComponent,
  AavaTagComponent,
  AavaTextboxComponent,
  TabItem,
  TagData,
} from '@aava/play-core';
import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RealmsComponent } from './realms/realms.component';
import { UserComponent } from './user/user.component';
import { CreateRealmComponent } from 'projects/shared/components/create-realm/create-realm.component';
import { RealmService } from '../../services/realm.service';
import { TableRow } from './interface/user.interface';
import { UserService } from '../../services/user.service';
import { finalize, Subject, takeUntil } from 'rxjs';
import { Router } from '@angular/router';

@Component({
  selector: 'app-manage',
  imports: [
    AavaTabsComponent,
    AavaTextboxComponent,
    AavaIconComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    AavaButtonComponent,
    RealmsComponent,
    UserComponent,
    AavaFlyoutComponent,
    AavaSearchBarComponent,
    AavaRadioButtonComponent,
    AavaTagComponent,
  ],
  templateUrl: './manage.component.html',
  styleUrl: './manage.component.scss',
})
export class ManageComponent implements OnInit {
  allRealms: any[] = [];
  realms: any[] = [];
  searchValue = '';
  activeTabIds = {
    button: 'user',
  };

  isLoading = true;
  rowData: TableRow[] = [];
  allUsers: TableRow[] = [];
  practiceAreaOptions = [
    { label: 'Data Engineering', value: 'Data Engineering' },
    { label: 'Experience Engineering', value: 'Experience Engineering' },
    { label: 'Option 1', value: 'Option 1' },
    { label: 'Option 2', value: 'Option 2' },
    { label: 'Option 3', value: 'Option 3' },
  ];

  removableTags: TagData[] = [
    { label: 'Automation', color: 'default', removable: true, pill: true },
    { label: 'Integration', color: 'default', removable: true },
    { label: 'Validation', color: 'default', removable: true },
    { label: 'Generation', color: 'default', removable: true },
    { label: 'UX-testing', color: 'default', removable: true },
    { label: 'Review', color: 'default', removable: true },
    { label: 'Instruction', color: 'default', removable: true },
    { label: 'Parser', color: 'default', removable: true },
  ];
  selectedPractice = '';

  buttonTabs: TabItem[] = [
    {
      id: 'user',
      label: 'User',
    },
    {
      id: 'realms',
      label: 'Realms',
    },
  ];
  private destroy$ = new Subject<void>();
  constructor(
    private RealmsService: RealmService,
    private dialogService: AavaDialogService,
    private userService: UserService,
    private router:Router,
  ) {}
  ngOnInit(): void {
    this.getRealmsApi();
    this.fetchUserData();
  }

  getRealmsApi() {
    this.RealmsService.getAllRealm().subscribe({
      next: (res: any) => {
        this.realms = res;
        this.allRealms = res;
      },
      error: err => {
        console.error('Failed to fetch realms', err);
      },
    });
  }

  getUserApi() {}

  fetchUserData() {
    this.isLoading = true;
    this.userService
      .getAllUser()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: (response: any) => {
          const users = response.userMgmtResponse || [];
          const mappedUsers = users.map((user: any) => ({
            id: String(user.userId),
            name: {
              value: user.profile?.userName || 'N/A',
              subValue: user.profile?.email || '',
              iconName: user.profile?.icon || '',
            },
            access: {
              value: user.userAccess?.roles?.join(', ') || 'N/A',
              iconName: '',
            },
            realm: {
              value: user.userRealms?.realms?.[0] || 'N/A',
              subValue:
                user.userRealms.totalCount > 1
                  ? `+${user.userRealms.totalCount - 1}`
                  : '',
              iconName: '',
              customStyle: {
                background: '#EDEDF3',
                color: 'var(--color-text-primary)',
                borderRadius: '20px',
              },
            },
            addedOn: {
              value: user.addedOn?.split(' ')[0] || 'N/A',
              iconName: '',
            },
            validtill: {
              value: user.validtill?.split(' ')[0] || 'N/A',
              iconName: '',
            },
            lastLogin: {
              value: user.lastLoggedIn?.split(' ')[0] || 'N/A',
              iconName: '',
            },
            authorized: { value: user.authorizedBy || 'N/A', iconName: '' },
            action: user.action,
            sortOrder: 1,
            isSelected: false,
            expanded: false,
          }));
          this.allUsers = mappedUsers; // keep master data for search data
          this.rowData = [...mappedUsers];
        },

        error: (err: any) => {
          console.error('Failed to fetch users:', err);
        },
      });
  }

  // Event handlers
  onTabChange(tab: TabItem, context: string): void {
    if (context === 'button') {
      this.activeTabIds.button = tab.id;
      this.searchValue = '';

      if (tab.id === 'realms') {
        this.getRealmsApi();
      } else if (tab.id === 'user') {
        this.getUserApi();
      }
    }
  }

  onCreateUser(event: Event) {

    this.router.navigate(['/add-user']);
  }

  onCreateRealm(event: Event) {
    event.preventDefault();
    this.dialogService.openModal(CreateRealmComponent, {
      position: 'center',
      width: '718px',
      showCloseButton: true,
    });
  }

  //

  onPracticeChange(value: string) {
    console.log('Selected practice area:', value);
  }

  onSearch(searchTerm: string, variant: string) {
    console.log(`Search clicked with variant ${variant}:`, searchTerm);
  }

  onTextboxChange(event: Event) {
    const target = event.target as HTMLInputElement;
    console.log('Textbox changed:', target.value);
  }

  onRemove(label: string) {
    this.removableTags = this.removableTags.filter(tag => tag.label !== label);
    console.log('Removed tag:', label);
  }

  onClick(label: string) {
    console.log('Clicked tag:', label);
  }

  applyFilter() {
    console.log('Selected Practice:', this.selectedPractice);
    console.log('Tags:', this.removableTags);
  }

  //search

  onSearchInputChange(event: Event) {
    const searchText = (event.target as HTMLInputElement).value.toLowerCase();
    this.searchValue = searchText;
    if (this.activeTabIds.button === 'realms') {
      // Filter realms
      this.realms = this.allRealms.filter(realm =>
        realm.realmName.toLowerCase().includes(searchText)
      );
    } else if (this.activeTabIds.button === 'user') {
       this.searchUsers(searchText);
    }
  }


  searchUsers(searchText: string,) {
  this.isLoading = true;

  this.userService
    .getUserSearch(searchText)
    .pipe(finalize(() => (this.isLoading = false)))
    .subscribe({
      next: (response: any) => {
        const users = response.userMgmtResponse || [];
        const mappedUsers = users.map((user: any) => ({
          id: String(user.userId),
          name: {
            value: user.profile?.userName || 'N/A',
            subValue: user.profile?.email || '',
            iconName: user.profile?.icon || '',
          },
          access: {
            value: user.userAccess?.roles?.join(', ') || 'N/A',
            iconName: '',
          },
          realm: {
            value: user.userRealms?.realms?.[0] || 'N/A',
            subValue:
              user.userRealms.totalCount > 1
                ? `+${user.userRealms.totalCount - 1}`
                : '',
            iconName: '',
            customStyle: {
              background: '#EDEDF3',
              color: 'var(--color-text-primary)',
              borderRadius: '20px',
            },
          },
          addedOn: { value: user.addedOn?.split(' ')[0] || 'N/A', iconName: '' },
          validtill: { value: user.validtill?.split(' ')[0] || 'N/A', iconName: '' },
          lastLogin: { value: user.lastLoggedIn?.split(' ')[0] || 'N/A', iconName: '' },
          authorized: { value: user.authorizedBy || 'N/A', iconName: '' },
          action: user.action,
          sortOrder: 1,
          isSelected: false,
          expanded: false,
        }));

        this.rowData = mappedUsers;
      },
      error: (err: any) => {
        console.error('Failed to search users:', err);
      },
    });
}


  // clear search
  clearSearch(): void {
    this.searchValue = '';
    this.realms = this.allRealms;
  }
}
