import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { importProvidersFrom } from '@angular/core';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideRouter } from '@angular/router';
import { ENVIRONMENT_CONFIG, authInterceptor, errorInterceptor } from '@shared';
import {
  LucideAngularModule,
  AlertCircle,
  X,
  Eye,
  EyeOff,
  ArrowRight,
  Search,
  ChevronDown,
  Home,
  LayoutDashboard,
  Grid,
  ClipboardList,
  TrendingUp,
  PanelRightOpen,
  PanelRightClose,
  File,
  MoreVertical,
  Calendar,
  ChevronRight,
  Repeat2,
  Play,
  User,
  BookOpenText,
  Box,
  Shield,
  Wrench,
  Bot,
  Info,
  PanelLeftClose,
  LayoutGrid,
  Rocket,
  SquareM,
  FileCheck,
  UserCog,
  ChartNoAxesCombined,
  Sun,
  Moon,
  Plus,
  ListFilter,
  FileText,
  CalendarDays,
  Code,
  Package,
  Check,
  Workflow,
  BookText,
  Upload,
  CircleCheck,
  ArrowLeftFromLine,
  Send,
  SendHorizontal,
  Sparkles,
  FileBox,
  Copy,
  Monitor,
  Settings,
  Languages,
  LogOut,
  GripVertical,
  Undo,
  Redo,
  ZoomIn,
  ZoomOut,
  ArrowLeft,
  Eraser,
  Pencil,
  Star,
  Trash2,
  UserRoundCog,
  SquarePen,
  Trash,
  EllipsisVertical,
  Hash,
  CircleX,
  AlertTriangle,
  Save,
  XCircle,
  ChevronUp,
  Clock2,
  ArrowUpRight,
  RefreshCcw,
  History,
  KeyRound,
} from 'lucide-angular';
import { MarkdownModule } from 'ngx-markdown';
import { NGX_MONACO_EDITOR_CONFIG } from 'ngx-monaco-editor-v2';

import { environment } from '../environments/environment';

import { routes } from './app.routes';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(withInterceptors([authInterceptor, errorInterceptor])),
    provideAnimationsAsync(),
    // {
    //   provide: AGENT_ENVIRONMENT_CONFIG,
    //   useValue: {
    //     consoleApi: environment.consoleApi,
    //     consoleApiV2: environment.consoleApiV2
    //   }
    // },
    {
      provide: ENVIRONMENT_CONFIG,
      useValue: {
        consoleApi: environment.consoleApi,
        consoleApiV2: environment.consoleApiV2,
        consoleEmbeddingApi: environment.consoleEmbeddingApi,
        consoleInstructionApi: environment.consoleInstructionApi,
        baseUrl: environment.baseUrl,
        apiUrl: environment.consoleApi,
      },
    },
    {
      provide: NGX_MONACO_EDITOR_CONFIG,
      useValue: {
        baseUrl: 'assets', // or './assets' if your assets are in ./assets
        defaultOptions: { scrollBeyondLastLine: false },
      },
    },
    importProvidersFrom(
      LucideAngularModule.pick({
        AlertCircle,
        X,
        Eye,
        EyeOff,
        ArrowRight,
        Search,
        ChevronDown,
        Home,
        LayoutDashboard,
        Grid,
        ClipboardList,
        TrendingUp,
        PanelRightOpen,
        PanelRightClose,
        PanelLeftClose,
        File,
        ListFilter,
        MoreVertical,
        User,
        Play,
        Box,
        Calendar,
        ChevronRight,
        Repeat2,
        BookOpenText,
        Shield,
        Wrench,
        Bot,
        Info,
        LayoutGrid,
        Rocket,
        SquareM,
        FileCheck,
        UserCog,
        ChartNoAxesCombined,
        Sun,
        Moon,
        Plus,
        FileText,
        CalendarDays,
        Code,
        Package,
        Check,
        Workflow,
        BookText,
        Upload,
        CircleCheck,
        ArrowLeftFromLine,
        SendHorizontal,
        Send,
        Sparkles,
        FileBox,
        Copy,
        Monitor,
        Settings,
        Languages,
        LogOut,
        GripVertical,
        Undo,
        Redo,
        ZoomIn,
        ZoomOut,
        ArrowLeft,
        Eraser,
        UserRoundCog,
        Star,
        Trash2,
        Pencil,
        SquarePen,
        Trash,
        EllipsisVertical,
        Hash,
        CircleX,
        AlertTriangle,
        XCircle,
        Save,
        ChevronUp,
        Clock2,
        ArrowUpRight,
        RefreshCcw,
        History,
        KeyRound
      }),
      MarkdownModule.forRoot()
    ),
  ],
};
