import { Routes } from '@angular/router';
import { authGuard, consolePermissionGuard } from '@shared';

import {
  BreadcrumbResolver,
  BreadcrumbResolverConfig,
} from './resolvers/breadcrumb.resolver';
export const routes: Routes = [
  {
    path: 'callback',
    loadComponent: () => import('@shared').then(m => m.CallbackComponent),
  },

  // Dashboard route
  {
    path: '',
    pathMatch: 'full',
    loadComponent: () =>
      import('./pages/dashboard/dashboard.component').then(
        m => m.DashboardComponent
      ),
    resolve: { breadcrumbs: BreadcrumbResolver },
    data: {
      breadcrumbConfig: {
        customLabel: 'Console',
        customIcon: 'home',
        defaultSubPath: 'build',
      } as BreadcrumbResolverConfig,
    },
    canActivate: [authGuard, consolePermissionGuard],
  },

  // Build section
  {
    path: 'build',
    resolve: { breadcrumbs: BreadcrumbResolver },
    data: {
      breadcrumbConfig: {
        showDropdown: true,
        customLabel: 'Build',
      } as BreadcrumbResolverConfig,
    },
    canActivate: [authGuard, consolePermissionGuard],
    children: [
      { path: '', redirectTo: 'agent', pathMatch: 'full' },
      {
        path: 'agent',
        children: [
          {
            path: '',
            loadComponent: () =>
              import('@shared').then(m => m.CreateAgentPromptComponent),
            resolve: { breadcrumbs: BreadcrumbResolver },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Create Agent',
                customIcon: 'plus',
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
          {
            path: 'create',
            loadComponent: () =>
              import('@shared').then(m => m.AgentBuilderComponent), // Replace with actual AgentDetailComponent
            resolve: {
              breadcrumbs: BreadcrumbResolver,
            },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true, // Don't show in sibling dropdowns
                customLabel: 'Configure Agent',
                customIcon: 'settings',
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
          {
            path: 'playground',
            loadComponent: () =>
              import('@shared').then(m => m.PlaygroundComponent),
            resolve: {
              breadcrumbs: BreadcrumbResolver,
            },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Agent Playground',
                customIcon: 'play',
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
          {
            path: ':id',
            loadComponent: () =>
              import('@shared').then(m => m.AgentBuilderComponent),
            resolve: { breadcrumbs: BreadcrumbResolver },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Edit Agent',
                customIcon: 'edit',
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
        ],
      },
      {
        path: 'tools',
        children: [
          {
            path: '',
            loadComponent: () =>
              import('@shared').then(m => m.CreateAgentPromptComponent),
            resolve: { breadcrumbs: BreadcrumbResolver },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Create Tool',
                customIcon: 'plus',
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
          {
            path: 'create',
            loadComponent: () =>
              import('@shared').then(m => m.ToolBuilderComponent),
            resolve: { breadcrumbs: BreadcrumbResolver },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Configure Tool',
                customIcon: 'settings',
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
          {
            path: 'playground',
            loadComponent: () =>
              import('@shared').then(m => m.PlaygroundComponent),
            resolve: {
              breadcrumbs: BreadcrumbResolver,
            },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Tool Playground',
                customIcon: 'play',
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
          {
            path: ':id',
            loadComponent: () =>
              import('@shared').then(m => m.ToolBuilderComponent),
            resolve: { breadcrumbs: BreadcrumbResolver },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Edit Tool',
                customIcon: 'edit',
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
        ],
      },
      {
        path: 'guardrails',
        children: [
          {
            path: '',
            redirectTo: 'create',
            pathMatch: 'full',
          },
          {
            path: 'create',
            loadComponent: () =>
              import('@shared').then(m => m.GuardrailBuilderComponent),
            resolve: { breadcrumbs: BreadcrumbResolver },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Create Guardrail',
                customIcon: 'plus',
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
          {
            path: 'playground',
            loadComponent: () =>
              import('@shared').then(m => m.PlaygroundComponent),
            resolve: {
              breadcrumbs: BreadcrumbResolver,
            },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Guardrail Playground',
                customIcon: 'play',
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
          {
            path: ':id',
            loadComponent: () =>
              import('@shared').then(m => m.GuardrailBuilderComponent),
            resolve: { breadcrumbs: BreadcrumbResolver },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Edit Guardrail',
                customIcon: 'edit',
                includeInBreadcrumb: false,
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
        ],
      },
      {
        path: 'knowledge',
        children: [
          {
            path: '',
            redirectTo: 'create',
            pathMatch: 'full',
          },
          {
            path: 'create',
            loadComponent: () =>
              import('@shared').then(m => m.KnowledgeBuilderComponent),
            resolve: { breadcrumbs: BreadcrumbResolver },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Create Knowledge Base',
                customIcon: 'plus',
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
        ],
      },
      {
        path: 'pipelines',
        children: [
          {
            path: '',
            loadComponent: () =>
              import('@shared').then(m => m.PipelineBuilderComponent),
            resolve: { breadcrumbs: BreadcrumbResolver },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Create Pipeline',
                customIcon: 'plus',
              } as BreadcrumbResolverConfig,
            },
            canActivate: [authGuard, consolePermissionGuard],
          },
          {
            path: 'playground',
            loadComponent: () =>
              import('@shared').then(m => m.PlaygroundComponent),
            resolve: {
              breadcrumbs: BreadcrumbResolver,
            },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Pipeline Playground',
                customIcon: 'play',
              } as BreadcrumbResolverConfig,
            },
          },
        ],
      },
      {
        path: 'model',
        children: [
          {
            path: '',
            loadComponent: () =>
              import('@shared').then(m => m.ModelBuilderComponent),
            resolve: { breadcrumbs: BreadcrumbResolver },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Create Model',
                customIcon: 'plus',
              } as BreadcrumbResolverConfig,
            },
          },
          {
            path: ':id',
            loadComponent: () =>
              import('@shared').then(m => m.ModelBuilderComponent),
            resolve: { breadcrumbs: BreadcrumbResolver },
            data: {
              breadcrumbConfig: {
                excludeFromDropdown: true,
                customLabel: 'Edit Model',
                customIcon: 'edit',
              } as BreadcrumbResolverConfig,
            },
          },
        ],
      },
    ],
  },

  // Other console sections
  {
    path: 'approvals',
    loadComponent: () =>
      import('./pages/approvals/approvals.component').then(
        m => m.ApprovalsComponent
      ),
    resolve: { breadcrumbs: BreadcrumbResolver },
    data: {
      breadcrumbConfig: {
        customLabel: 'Approvals',
        customIcon: 'check-circle',
      } as BreadcrumbResolverConfig,
    },
    canActivate: [authGuard, consolePermissionGuard],
  },

  {
    path: 'marketplace',
    loadComponent: () =>
      import('./pages/marketplace/marketplace.component').then(
        m => m.MarketplaceComponent
      ),
    resolve: { breadcrumbs: BreadcrumbResolver },
    data: {
      breadcrumbConfig: {
        customLabel: 'Marketplace',
        customIcon: 'shopping-cart',
      } as BreadcrumbResolverConfig,
    },
    canActivate: [authGuard, consolePermissionGuard],
  },

  {
    path: 'manage',
    loadComponent: () =>
      import('./pages/manage/manage.component').then(m => m.ManageComponent),
    resolve: { breadcrumbs: BreadcrumbResolver },
    data: {
      breadcrumbConfig: {
        customLabel: 'Manage',
        customIcon: 'settings',
      } as BreadcrumbResolverConfig,
    },
    canActivate: [authGuard, consolePermissionGuard],
  },
  {
    path: 'add-user',
    loadComponent: () =>
      import('./pages/manage/user/create-user/create-user.component').then(
        m => m.CreateUserComponent
      ),
    resolve: { breadcrumbs: BreadcrumbResolver },
    data: {
      breadcrumbConfig: {
        customLabel: 'Manage',
        customIcon: 'settings',
      } as BreadcrumbResolverConfig,
    },
    canActivate: [authGuard, consolePermissionGuard],
  },

  {
    path: 'analytics',
    loadComponent: () =>
      import('./pages/dashboard/dashboard.component').then(
        m => m.DashboardComponent
      ),
    resolve: { breadcrumbs: BreadcrumbResolver },
    data: {
      breadcrumbConfig: {
        customLabel: 'Analytics',
        customIcon: 'bar-chart',
      } as BreadcrumbResolverConfig,
    },
    canActivate: [authGuard, consolePermissionGuard],
  },
  {
    path: 'settings',
    canActivate: [authGuard, consolePermissionGuard],
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./pages/settings/settings.component').then(
            m => m.SettingsComponent
          ),
        resolve: { breadcrumbs: BreadcrumbResolver },
        data: {
          breadcrumbConfig: {
            customLabel: 'Settings',
            customIcon: 'settings',
          } as BreadcrumbResolverConfig,
        },
      },
      {
        path: 'configuration-management',
        loadComponent: () =>
          import(
            './pages/settings/configuration-management/configuration-management.component'
          ).then(m => m.ConfigurationManagementComponent),
        resolve: { breadcrumbs: BreadcrumbResolver },
        data: {
          breadcrumbConfig: {
            excludeFromDropdown: true,
            customLabel: 'Configuration Management',
            customIcon: 'plus',
          } as BreadcrumbResolverConfig,
        },
      },
    ],
  },
  // Catch all route
  { path: '**', redirectTo: '' },
];
