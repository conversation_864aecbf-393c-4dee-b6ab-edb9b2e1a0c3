# Console Application

This application is part of the Elder Wand platform, providing a unified console experience with advanced UI components, a flexible theming system, responsive design, and integrated AI components for code generation and chat interactions.

## Features

- **Dark & Light Theme Support**: Complete theming system with seamless transitions
- **Reusable Component Library**: A suite of reusable, themed components
- **Responsive Design**: Full support for all device sizes
- **Glass Morphism Effects**: Modern UI with glass-like components
- **Accessibility**: WCAG compliant with accessibility features built-in
- **AI Chat Interface**: Interactive chat interface for AI-powered assistance
- **Code Window Component**: Integrated code editor and preview functionality

## Development

### Getting Started

1. **Install Dependencies**:

   ```bash
   npm install
   ```

2. **Start the Development Server**:

   ```bash
   npm run start:console
   ```

3. **Build for Production**:
   ```bash
   npm run build:console
   ```

## Theming System

The console application features a comprehensive theming system that supports both light and dark themes. The theming system uses CSS variables defined in `src/assets/styles/_variables.scss`.

### Using Themes

The application theme can be toggled using the theme toggle component in the navigation header:

```html
<app-theme-toggle></app-theme-toggle>
```

Themes are automatically saved to localStorage and applied on future visits.

## Typography

The console application uses **Mulish** as its primary font family throughout the interface. Mulish is a minimalist sans-serif typeface that offers excellent readability across different screen sizes and device types.

### Font Weights

The following font weights are available as CSS variables:

```scss
--font-weight-light: 300;
--font-weight-regular: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
--font-weight-extrabold: 800;
```

### Usage Guidelines

- Use proper heading hierarchy (h1-h6) for content structure
- For body text, use `font-weight: var(--font-weight-regular)`
- For emphasis or section titles, use `font-weight: var(--font-weight-medium)` or `var(--font-weight-semibold)`
- Maintain readable font sizes (minimum 14px for body text)
- Follow the application's vertical rhythm with consistent spacing

### Implementation Details

The Mulish font is loaded using both:

1. Font face declarations in `src/assets/styles/_fonts.scss`
2. Preloaded in the index.html file for optimal performance

Always use the CSS variable `var(--font-family)` instead of hardcoding the font family in component styles.

### Themed Components

The application includes numerous components with built-in theme support:

- **FormFieldComponent**: Input fields, textareas, selects, checkboxes
- **ButtonComponent**: Multiple variants and sizes with theming
- **CardComponent**: Content containers with glass effect
- **ModalComponent**: Overlay dialogs
- **DataCardComponent**: Specialized cards for data items
- **SearchBarComponent**: Themed search input
- **NavItemComponent**: Navigation items with dropdown support
- **ChatWindowComponent**: Interactive chat interface with markdown support
- **CodeWindowComponent**: Split-screen code editor and preview
- **CodeViewerComponent**: Code display with syntax highlighting

### Creating Theme-Compatible Components

Follow these guidelines when creating new components:

1. Use theme variables for all colors, backgrounds, and borders
2. Add transition effects for smooth theme switching
3. Test in both light and dark themes
4. Use the `:host-context(.dark-theme)` selector for dark theme specific styles

Example:

```scss
.my-component {
  background-color: var(--card-bg);
  color: var(--text-color);
  border: 1px solid var(--card-border);
  transition: all 0.3s ease;
}

// Dark theme specific adjustments if needed
:host-context(.dark-theme) {
  .my-component {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
}
```

For more detailed guidance on using the theming system, refer to the [Theming Guide](src/app/shared/docs/theming-guide.md).

## Project Structure

```
projects/console/
├── src/
│   ├── app/
│   │   ├── shared/           # Shared components, services, and utilities
│   │   │   ├── components/   # Reusable UI components
│   │   │   │   ├── chat-window/  # AI chat interface component
│   │   │   │   ├── code-window/  # Split-screen code editor component
│   │   │   │   └── code-viewer/  # Code display component
│   │   │   ├── services/     # Shared services including ThemeService
│   │   │   └── docs/         # Documentation
│   │   ├── dashboard/        # Dashboard module
│   │   ├── launch/           # Launch module
│   │   ├── libraries/        # Libraries module
│   │   └── manage/           # Management module
│   ├── assets/
│   │   ├── styles/           # Global styles and variables
│   │   │   └── _variables.scss # Theme variables
│   │   └── images/           # Images and icons
│   └── environments/         # Environment configuration
└── README.md                 # This file
```

## Best Practices

1. **Component Reuse**: Use shared components from the `shared/components` directory
2. **Theming**: Always use theme variables instead of hard-coded colors
3. **Responsive Design**: Test all components on multiple screen sizes
4. **Accessibility**: Ensure all components are accessible (WCAG compliance)
5. **Performance**: Optimize components for performance

## Additional Resources

- [Theming Guide](src/app/shared/docs/theming-guide.md) - Detailed guide on using the theming system
- [Typography Guide](src/app/shared/docs/typography-guide.md) - Comprehensive guide to using the Mulish typography system
- [Component Documentation](src/app/shared/docs/) - Documentation for individual components

## AI Components

### ChatWindowComponent

The `ChatWindowComponent` provides an interactive chat interface for AI-powered conversations:

```html
<app-chat-window
  [theme]="'light'"
  [defaultText]="'Ask me'"
  [rightIcons]="rightIcons"
  [(textValue)]="promptText"
  [chatMessages]="messages"
  [isCodeGenerationComplete]="true"
  (iconClicked)="handleIconClick($event)"
  (enterPressed)="handleSendMessage()"
>
</app-chat-window>
```

### CodeWindowComponent

The `CodeWindowComponent` integrates chat functionality with code editing and preview capabilities:

```html
<app-code-window></app-code-window>
```

This component creates a split-screen interface with a chat panel on the left and a code editor/preview panel on the right.

### CodeViewerComponent

The `CodeViewerComponent` provides a simple code display with syntax highlighting:

```html
<app-code-viewer
  [files]="codeFiles"
  [theme]="currentTheme"
  [showFileExplorer]="true"
>
</app-code-viewer>
```
