const { ModuleFederationPlugin } = require('webpack').container;

module.exports = {
  output: {
    publicPath: 'auto',
    uniqueName: 'console',
    globalObject: 'self',
  },
  optimization: {
    runtimeChunk: false,
  },
  experiments: {
    outputModule: true,
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.ttf$/,
        use: ['file-loader'],
      },
    ],
  },
  resolve: {
    fallback: {
      path: false,
      fs: false,
    },
  },
  externals: {
    // Exclude CommonJS dependencies from optimization warnings
    'style-loader/dist/runtime/injectStylesIntoStyleTag.js':
      'commonjs style-loader/dist/runtime/injectStylesIntoStyleTag.js',
    'style-loader/dist/runtime/insertBySelector.js':
      'commonjs style-loader/dist/runtime/insertBySelector.js',
    'style-loader/dist/runtime/insertStyleElement.js':
      'commonjs style-loader/dist/runtime/insertStyleElement.js',
    'style-loader/dist/runtime/setAttributesWithoutAttributes.js':
      'commonjs style-loader/dist/runtime/setAttributesWithoutAttributes.js',
    'style-loader/dist/runtime/styleDomAPI.js':
      'commonjs style-loader/dist/runtime/styleDomAPI.js',
    'style-loader/dist/runtime/styleTagTransform.js':
      'commonjs style-loader/dist/runtime/styleTagTransform.js',
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'console',
      library: { type: 'module' },
      filename: 'remoteEntry.js',
      exposes: {},
      shared: {
        '@angular/core': {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^19.0.0',
        },
        '@angular/common': {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^19.0.0',
        },
        '@angular/router': {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^19.0.0',
        },
        '@angular/animations': {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^19.0.0',
        },
      },
    }),
  ],
};
