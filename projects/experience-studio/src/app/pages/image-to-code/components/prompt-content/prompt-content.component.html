<div id="prompt-content-container">
  <!-- First Screen: Prompt Content Container -->
  <section id="prompt-section-container">
    <div class="container-fluid">
      <!-- Conditional Hero Section based on mode -->
      <!-- <app-hero-section-header
        *ngIf="!isUIDesignMode"
        [headerTitle]="'Dream Build Launch!'"
        [headerDescription]="'<PERSON><PERSON><PERSON><PERSON> blends Art Science to bring your ideas to life.'"
        [subHeading]="'What would you like to build today?'"></app-hero-section-header> -->
      <app-hero-section-header *ngIf="!isUIDesignMode" [headerTitle]="'Image To Application'"
        [headerDescription]="'Experience Studio blends art and science to bring your ideas to life'"></app-hero-section-header>

        <app-hero-section-header *ngIf="isUIDesignMode" [headerTitle]="uiDesignHeroSection.title"
        [headerDescription]="uiDesignHeroSection.description"
        [subHeading]="uiDesignHeroSection.subHeading"></app-hero-section-header>

      <div id="prompt-bar-container" class="container-fluid px-2">
        <div class="row mt-2 d-flex align-items-center justify-content-center">
          <div class="col-12 d-flex justify-content-center">

            <!-- todo -> uncomment for new version -->
            <awe-prompt-bar [theme]="theme" (enterPressed)="handleEnterPressed()" [(textValue)]="currentPrompt"
              (textValueChange)="handlePromptChange($event)" (iconClicked)="handleIconClick($event)"
              (fileRemoved)="onFileRemoved($event)" (filePreviewClosed)="closePreview()"
              [class.disabled-prompt-bar]="isEnhancing || isGenerating" [class.processing]="isEnhancing || isGenerating"
              appTypewriterPlaceholder [texts]="animatedTexts">

              <div class="custom-content">
                <!-- Selected Files Display -->
                <div class="mb-2 selected-files" *ngIf="selectedFiles.length > 0">
                  <div class="py-1 px-2 file-item" *ngFor="let file of selectedFiles">
                    <!-- Image file preview (for Image to Application flow) -->
                    <ng-container *ngIf="isImageFile(file.type); else textFileTemplate">
                      <div class="file-preview" (click)="showFilePreview(file)">
                        <img class="mr-2 file-preview-image previewable-image" [src]="file.url" [alt]="file.name"
                          title="Preview image" loading="lazy" decoding="async" />
                        <span class="file-name">{{ truncateFileName(file.name) }}</span>
                      </div>
                    </ng-container>

                    <!-- Text file preview (for Prompt to Application flow) -->
                    <ng-template #textFileTemplate>
                      <div class="file-preview text-file-preview">
                        <exp-icons iconName="awe_document" class="mr-2" iconColor="blue"></exp-icons>
                        <span class="file-name">{{ truncateFileName(file.name) }}</span>
                      </div>
                    </ng-template>

                    <exp-icons iconName="awe_close" (click)="removeFile(file.id)" class="pt-2" role="button"
                      tabindex="0" iconColor="blue" [attr.aria-label]="'Remove ' + file.name"></exp-icons>
                  </div>
                </div>

                <div class="d-flex align-items-center justify-content-between pe-2 tools-container">
                  <div class="d-flex align-items-center gap-2 me-3 pills-container">
                    <!-- File attach pill hidden in UI Design mode -->
                    <exp-file-attach-pill  [options]="fileOptions" [mainText]="getFileAttachText()"
                      [fileType]="getFileType()" [maxAllowedFiles]="getMaxAllowedFiles()"
                      (optionSelected)="onFileOptionSelected($event)" (filesSelected)="onFilesSelected($event)"
                      [class.disabled]="isEnhancing"
                      tooltipPosition="top"></exp-file-attach-pill>

                    <!-- UI Design Mode: Show Viewport Selection (Web/Mobile) -->
                    <awe-icon-pill *ngIf="isUIDesignMode" [options]="applicationTargetOptions"
                      [selectedOption]="selectedApplicationTarget" [class.disabled]="isEnhancing"
                      (selectionChange)="onApplicationTargetSelected($event)"></awe-icon-pill>

                    <!-- Regular Mode: Show Tech Options -->
                    <awe-icon-pill *ngIf="!isUIDesignMode" [options]="techOptions" [selectedOption]="selectedTech"
                      [class.disabled]="isEnhancing" (selectionChange)="onTechSelected($event)"></awe-icon-pill>

                    <!-- Design Options (shown only in regular mode, hidden in UI Design mode) -->
                    <awe-icon-pill *ngIf="!isUIDesignMode" [options]="designOptions" [selectedOption]="selectedDesign"
                      [class.disabled]="isEnhancing" (selectionChange)="onDesignSelected($event)"></awe-icon-pill>
                  </div>
                  <div class="d-flex align-items-center gap-3 enhance-icons">
                    <!-- Show loading spinner when enhancing, otherwise show enhance icon -->
                    <ng-container *ngIf="!isEnhancing; else loadingEnhance">
                      <exp-icons iconName="awe_enhance" (click)="handleEnhanceText()" role="button" tabindex="0"
                        [attr.aria-label]="'Enhance'" [class.disabled]="
                          !currentPrompt ||
                          currentPrompt.trim() === '' ||
                          enhanceClickCount >= maxEnhanceClicks
                        " [style.cursor]="
                          !currentPrompt ||
                          currentPrompt.trim() === '' ||
                          enhanceClickCount >= maxEnhanceClicks
                            ? 'not-allowed'
                            : 'pointer'
                        " [color]="getIconColor()"></exp-icons>
                    </ng-container>
                    <ng-template #loadingEnhance>
                      <div class="enhance-loading-container">

                        <div class="enhance-loading-text">
                          {{ getEnhanceLoadingText() }}
                        </div>

                        <div class="d-flex align-items-center justify-content-center loading-spinner">
                          <div class="spinner"></div>
                        </div>
                      </div>
                    </ng-template>
                     <exp-icons
                      iconName="awe_enhanced_send"
                      (click)="handleEnhancedSend()"
                      class="cursor-pointer"
                      role="button"
                      tabindex="0"
                      [attr.aria-label]="'Enhanced Send'"
                      [class.disabled]="
                        !currentPrompt ||
                        currentPrompt.trim() === '' ||
                        isEnhancing
                      "
                      [style.cursor]="
                        !currentPrompt ||
                        currentPrompt.trim() === '' ||
                        isEnhancing
                          ? 'not-allowed'
                          : 'pointer'
                      "
                      [color]="getIconColor()"
                    ></exp-icons>
                  </div>
                </div>
              </div>
            </awe-prompt-bar>
          </div>
        </div>
      </div>
      <!-- Suggestion Buttons -->
      <div id="suggestions-container" class="px-2">
        <div class="row mt-5 d-flex justify-content-center align-items-center g-2"
          [class.invisible]="currentPrompt && currentPrompt.trim() !== ''">
          <div class="col-12 col-sm-auto text-center mb-2" *ngFor="let button of buttons">
            <awe-button [label]="button.label" [variant]="button.variant" [loadingType]="button.loadingType"
              [animation]="button.buttonAnimation" (click)="handleSuggestionClick(button.label)">
            </awe-button>
          </div>
        </div>
      </div>

      <!-- Divider Section -->
      <!-- <div id="divider-section-container">
        <div class="d-flex align-items-center justify-content-center">
          <img
            [src]="'assets/icons/' + (theme === 'dark' ? 'divider-dark.svg' : 'divider-light.svg')"
            class="divider-image"
            tabindex="0"
            alt="divider" />
        </div>
      </div> -->
    </div>
  </section>

  <!-- Second Screen: Projects Section removed and moved to landing page -->
</div>

<!-- Simple Full Screen Image Preview Overlay -->
<div class="image-overlay" *ngIf="showPreview && previewFile">
  <exp-icons iconName="awe_close" (click)="closePreview()" role="button" tabindex="0"
    [attr.aria-label]="'Close preview'"></exp-icons>
  <img [src]="previewFile.url" [alt]="previewFile.name" loading="eager" decoding="async" fetchpriority="high" />
</div>
