export interface ProjectInfo {
  name: string;
  description: string;
  targetPage: string;
}

export interface TechStack {
  framework: string;
  styling: string;
  componentLibrary: string;
}

export interface ColorPalette {
  background: string;
  surface: string;
  primaryText: string;
  secondaryText: string;
  accent: string;
  accentSecondary: string;
  error: string;
  border: string;
  notes: string;
}

export interface Typography {
  primaryFont: string;
  heading: {
    tailwind: string;
    notes: string;
  };
  subheading: {
    tailwind: string;
    notes: string;
  };
  inputLabel: {
    tailwind: string;
    notes: string;
  };
  buttonPrimary: {
    tailwind: string;
    notes: string;
  };
  link: {
    tailwind: string;
    notes: string;
  };
  notes: string;
}

export interface DesignSystem {
  colorPalette: ColorPalette;
  typography: Typography;
  spacing: {
    base: string;
    commonGaps: string[];
    commonPadding: string[];
    notes: string;
  };
  effects: {
    borderRadius: {
      default: string;
      buttons: string;
      notes: string;
    };
    shadows: {
      default: string;
      buttons: string;
      notes: string;
    };
  };
}

export interface LayoutStructure {
  layoutType: string;
  layoutExplanation: string;
  overall: {
    type: string;
    definition: string;
    sizing: {
      mainContent: string;
    };
    notes: string;
  };
  mainContent: {
    layout: string;
    container: string;
    notes: string;
  };
}

export interface Component {
  name: string;
  composition?: string[];
  notes: string;
}

export interface ComponentBreakdown {
  organisms: Component[];
  templates: Component[];
  pages: Component[];
}

export interface ProjectOverview {
  projectInfo: ProjectInfo;
  techStack: TechStack;
  designSystem: DesignSystem;
  layoutStructure: LayoutStructure;
  componentBreakdown: ComponentBreakdown;
}