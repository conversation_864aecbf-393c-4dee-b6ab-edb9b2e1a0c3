<div class="artifacts-view">
  <div *ngIf="isLoading" class="loading">
    <div class="spinner-border text-primary"></div>
    <span>Loading project overview...</span>
  </div>

  <div *ngIf="!isLoading && projectOverview" class="project-overview">
    <!-- Project Info Section -->
    <section class="section">
      <h2>Project Information</h2>
      <div class="content">
        <h3>{{ projectOverview.projectInfo.name }}</h3>
        <p class="description">{{ projectOverview.projectInfo.description }}</p>
        <div class="meta">
          <strong>Target Page:</strong> {{ projectOverview.projectInfo.targetPage }}
        </div>
      </div>
    </section>

    <!-- Tech Stack Section -->
    <section class="section">
      <h2>Technology Stack</h2>
      <div class="content">
        <div class="tech-item">
          <strong>Framework:</strong> {{ projectOverview.techStack.framework }}
        </div>
        <div class="tech-item">
          <strong>Styling:</strong> {{ projectOverview.techStack.styling }}
        </div>
        <div class="tech-item">
          <strong>Component Library:</strong> {{ projectOverview.techStack.componentLibrary }}
        </div>
      </div>
    </section>

    <!-- Design System Section -->
    <section class="section">
      <h2>Design System</h2>
      
      <!-- Color Palette -->
      <div class="subsection">
        <h3>Color Palette</h3>
        <div class="color-grid">
          <div class="color-item" *ngFor="let color of getColorPaletteItems()">
            <div class="color-preview" [style.backgroundColor]="color.value"></div>
            <div class="color-info">
              <div class="color-name">{{ color.name }}</div>
              <div class="color-value">{{ color.value }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Typography -->
      <div class="subsection">
        <h3>Typography</h3>
        <div class="typography-list">
          <div class="typography-item">
            <strong>Primary Font:</strong> {{ projectOverview.designSystem.typography.primaryFont }}
          </div>
          <div class="typography-item">
            <strong>Heading:</strong> 
            <div class="typography-details">
              <div>{{ projectOverview.designSystem.typography.heading.tailwind }}</div>
              <div class="notes">{{ projectOverview.designSystem.typography.heading.notes }}</div>
            </div>
          </div>
          <!-- Add other typography items similarly -->
        </div>
      </div>

      <!-- Spacing -->
      <div class="subsection">
        <h3>Spacing</h3>
        <div class="spacing-info">
          <div><strong>Base:</strong> {{ projectOverview.designSystem.spacing.base }}</div>
          <div>
            <strong>Common Gaps:</strong>
            <ul class="spacing-list">
              <li *ngFor="let gap of projectOverview.designSystem.spacing.commonGaps">{{ gap }}</li>
            </ul>
          </div>
          <div>
            <strong>Common Padding:</strong>
            <ul class="spacing-list">
              <li *ngFor="let padding of projectOverview.designSystem.spacing.commonPadding">{{ padding }}</li>
            </ul>
          </div>
          <div class="notes">{{ projectOverview.designSystem.spacing.notes }}</div>
        </div>
      </div>

      <!-- Effects -->
      <div class="subsection">
        <h3>Effects</h3>
        <div class="effects-info">
          <div class="border-radius">
            <strong>Border Radius:</strong>
            <div>Default: {{ projectOverview.designSystem.effects.borderRadius.default }}</div>
            <div>Buttons: {{ projectOverview.designSystem.effects.borderRadius.buttons }}</div>
            <div class="notes">{{ projectOverview.designSystem.effects.borderRadius.notes }}</div>
          </div>
          <div class="shadows">
            <strong>Shadows:</strong>
            <div>Default: {{ projectOverview.designSystem.effects.shadows.default }}</div>
            <div>Buttons: {{ projectOverview.designSystem.effects.shadows.buttons }}</div>
            <div class="notes">{{ projectOverview.designSystem.effects.shadows.notes }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Layout Structure -->
    <section class="section">
      <h2>Layout Structure</h2>
      <div class="content">
        <div class="layout-type">
          <strong>Type:</strong> {{ projectOverview.layoutStructure.layoutType }}
        </div>
        <div class="layout-explanation">{{ projectOverview.layoutStructure.layoutExplanation }}</div>
        
        <div class="overall-layout">
          <h3>Overall Layout</h3>
          <div><strong>Type:</strong> {{ projectOverview.layoutStructure.overall.type }}</div>
          <div><strong>Definition:</strong> {{ projectOverview.layoutStructure.overall.definition }}</div>
          <div class="sizing">
            <strong>Main Content:</strong> {{ projectOverview.layoutStructure.overall.sizing.mainContent }}
          </div>
          <div class="notes">{{ projectOverview.layoutStructure.overall.notes }}</div>
        </div>

        <div class="main-content">
          <h3>Main Content</h3>
          <div><strong>Layout:</strong> {{ projectOverview.layoutStructure.mainContent.layout }}</div>
          <div><strong>Container:</strong> {{ projectOverview.layoutStructure.mainContent.container }}</div>
          <div class="notes">{{ projectOverview.layoutStructure.mainContent.notes }}</div>
        </div>
      </div>
    </section>

    <!-- Component Breakdown -->
    <section class="section">
      <h2>Component Breakdown</h2>
      
      <div class="subsection">
        <h3>Organisms</h3>
        <div class="component-list">
          <div *ngFor="let component of projectOverview.componentBreakdown.organisms" class="component-item">
            <h4>{{ component.name }}</h4>
            <div *ngIf="component.composition" class="composition">
              <strong>Composition:</strong>
              <ul>
                <li *ngFor="let part of component.composition">{{ part }}</li>
              </ul>
            </div>
            <div class="notes">{{ component.notes }}</div>
          </div>
        </div>
      </div>

      <div class="subsection">
        <h3>Templates</h3>
        <div class="component-list">
          <div *ngFor="let component of projectOverview.componentBreakdown.templates" class="component-item">
            <h4>{{ component.name }}</h4>
            <div *ngIf="component.composition" class="composition">
              <strong>Composition:</strong>
              <ul>
                <li *ngFor="let part of component.composition">{{ part }}</li>
              </ul>
            </div>
            <div class="notes">{{ component.notes }}</div>
          </div>
        </div>
      </div>

      <div class="subsection">
        <h3>Pages</h3>
        <div class="component-list">
          <div *ngFor="let component of projectOverview.componentBreakdown.pages" class="component-item">
            <h4>{{ component.name }}</h4>
            <div *ngIf="component.composition" class="composition">
              <strong>Composition:</strong>
              <ul>
                <li *ngFor="let part of component.composition">{{ part }}</li>
              </ul>
            </div>
            <div class="notes">{{ component.notes }}</div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <div *ngIf="!isLoading && !projectOverview" class="no-data">
    <p>No project overview data available.</p>
  </div>
</div>