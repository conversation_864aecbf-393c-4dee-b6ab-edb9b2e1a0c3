.artifacts-view {
  padding: 1.5rem;
  max-height: 100%;
  overflow-y: auto;

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
  }

  .section {
    margin-bottom: 2rem;
    background-color: var(--surface-color);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    h2 {
      margin-bottom: 1rem;
      color: var(--primary-text);
      font-size: 1.5rem;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 0.5rem;
    }

    .content {
      color: var(--secondary-text);
    }
  }

  .subsection {
    margin-top: 1.5rem;

    h3 {
      font-size: 1.2rem;
      color: var(--primary-text);
      margin-bottom: 1rem;
    }
  }

  // Project Info styles
  .project-info {
    h3 {
      color: var(--primary-text);
      margin-bottom: 0.5rem;
    }

    .description {
      margin-bottom: 1rem;
    }

    .meta {
      font-size: 0.9rem;
    }
  }

  // Tech Stack styles
  .tech-item {
    margin-bottom: 0.5rem;
    display: flex;
    gap: 0.5rem;
  }

  // Design System styles
  .color-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;

    .color-item {
      display: flex;
      align-items: center;
      gap: 1rem;

      .color-preview {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        border: 1px solid var(--border-color);
      }

      .color-info {
        .color-name {
          font-weight: 500;
          color: var(--primary-text);
        }

        .color-value {
          font-size: 0.9rem;
          color: var(--secondary-text);
        }
      }
    }
  }

  .typography-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .typography-item {
      .typography-details {
        margin-left: 1rem;
        margin-top: 0.25rem;
      }
    }
  }

  .spacing-info {
    .spacing-list {
      list-style: none;
      padding-left: 1rem;
      margin: 0.5rem 0;
    }
  }

  .effects-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;

    .border-radius,
    .shadows {
      background-color: var(--surface-secondary);
      padding: 1rem;
      border-radius: 6px;
    }
  }

  // Layout Structure styles
  .layout-type,
  .layout-explanation {
    margin-bottom: 1rem;
  }

  .overall-layout,
  .main-content {
    background-color: var(--surface-secondary);
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
  }

  // Component Breakdown styles
  .component-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;

    .component-item {
      background-color: var(--surface-secondary);
      padding: 1rem;
      border-radius: 6px;

      h4 {
        color: var(--primary-text);
        margin-bottom: 0.5rem;
      }

      .composition {
        margin: 0.5rem 0;

        ul {
          list-style: none;
          padding-left: 1rem;
        }
      }

      .notes {
        font-size: 0.9rem;
        color: var(--secondary-text);
        margin-top: 0.5rem;
      }
    }
  }

  .no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: var(--secondary-text);
  }

  // Utility classes
  .notes {
    font-size: 0.9rem;
    color: var(--secondary-text);
    margin-top: 0.25rem;
    font-style: italic;
  }
}