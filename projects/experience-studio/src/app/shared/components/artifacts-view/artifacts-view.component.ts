import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { ProjectOverview } from '@shared/interfaces';
import { CodeWindowArtifactsStateService } from '../code-window/services/code-window-artifacts-state.service';

interface ColorPaletteItem {
  name: string;
  value: string;
}

@Component({
  selector: 'exp-artifacts-view',
  templateUrl: './artifacts-view.component.html',
  styleUrls: ['./artifacts-view.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class ArtifactsViewComponent implements OnInit, OnDestroy {
  projectOverview: ProjectOverview | null = null;
  isLoading = false;
  private subscriptions = new Subscription();

  constructor(private artifactsStateService: CodeWindowArtifactsStateService) {}

  ngOnInit(): void {
    this.subscriptions.add(
      this.artifactsStateService.projectOverview.subscribe(overview => {
        this.projectOverview = overview;
      })
    );

    this.subscriptions.add(
      this.artifactsStateService.projectOverviewLoading.subscribe(loading => {
        this.isLoading = loading;
      })
    );
  }

  getColorPaletteItems(): ColorPaletteItem[] {
    if (!this.projectOverview) return [];

    const colorPalette = this.projectOverview.designSystem.colorPalette;
    return Object.entries(colorPalette).map(([name, value]) => ({
      name: name.replace(/([A-Z])/g, ' $1').trim(), // Convert camelCase to Title Case
      value: value
    }));
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
}