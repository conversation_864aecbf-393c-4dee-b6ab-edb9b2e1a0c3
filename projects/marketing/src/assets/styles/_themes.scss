/**
* =========================================================================
* Play+ Design System: Console Theme
*
* Console theme overrides for semantic tokens.
* This theme uses a blue and aqua color scheme for a modern console look.
* Primary: Blue (#2563EB) | Secondary: Aqua (#03BDD4)
* =========================================================================
*/

[data-theme='light'] {
  // Pulse Blue Palette (adjusted for dark theme)
  --pulse-blue-50: #002659;
  --pulse-blue-100: #003b73;
  --pulse-blue-200: #004f99;
  --pulse-blue-300: #0063bf;
  --pulse-blue-400: #006acc;
  --pulse-blue-500: #0077e6;
  --pulse-blue-600: #0084ff;
  --pulse-blue-700: #b0d9ff;
  --pulse-blue-800: #d9edff;
  --pulse-blue-900: #e6f3ff;

  // Vivid Aqua Palette (adjusted for dark theme)
  --vivid-aqua-50: #014f59;
  --vivid-aqua-100: #026875;
  --vivid-aqua-200: #028697;
  --vivid-aqua-300: #03acc1;
  --vivid-aqua-400: #03bdd4;
  --vivid-aqua-500: #35cadd;
  --vivid-aqua-600: #56d3e2;
  --vivid-aqua-700: #a9e1cc;
  --vivid-aqua-800: #8be1eb;
  --vivid-aqua-900: #e6f8fb;

  // Nova Green Palette (adjusted for dark theme)
  --nova-green-50: #1c4f3c;
  --nova-green-100: #25684f;
  --nova-green-200: #308666;
  --nova-green-300: #3dac83;
  --nova-green-400: #43be90;
  --nova-green-500: #69caa8;
  --nova-green-600: #81d3b5;
  --nova-green-700: #a9e1cc;
  --nova-green-800: #c5ebdd;
  --nova-green-900: #ecf8f4;

  // Success Green Palette (adjusted for dark theme)
  --success-50: #023f2c;
  --success-100: #035533;
  --success-200: #046b4b;
  --success-300: #058960;
  --success-400: #059669;
  --success-500: #37ab87;
  --success-600: #58b99b;
  --success-700: #8ccfba;
  --success-800: #b2ded1;
  --success-900: #e6f5fd;

  // Error Red Palette (adjusted for dark theme)
  --error-50: #5c1010;
  --error-100: #791515;
  --error-200: #9c1b1b;
  --error-300: #cb2323;
  --error-400: #dc2626;
  --error-500: #e35151;
  --error-600: #e866e;
  --error-700: #f4bcbc;
  --error-800: #f4bcbc;
  --error-900: #fce9e9;

  // Warning Orange Palette (adjusted for dark theme)
  --warning-50: #463804;
  --warning-100: #5c4a05;
  --warning-200: #786007;
  --warning-300: #987c09;
  --warning-400: #a78b0a;
  --warning-500: #b8a239;
  --warning-600: #c4b058;
  --warning-700: #d6c58c;
  --warning-800: #e3d5b2;
  --warning-900: #f6f1e6;

  // Info Blue Palette (adjusted for dark theme)
  --info-50: #023d4a;
  --info-100: #035061;
  --info-200: #04677e;
  --info-300: #0584a2;
  --info-400: #0591b2;
  --info-500: #37a7c1;
  --info-600: #58b5cd;
  --info-700: #8ccbdc;
  --info-800: #b2dde7;
  --info-900: #e6f4f7;

  // Dark Neutral Palette (proper dark theme colors)
  --neutral-50: #2d3036; // rgb(45, 48, 54)
  --neutral-100: #d1d3d8; // rgb(59, 63, 70)
  --neutral-200: #4c515b; // rgb(76, 81, 91)
  --neutral-300: #616874; // rgb(97, 104, 116)
  --neutral-400: #6b7280; // rgb(107, 114, 128)
  --neutral-500: #9ca1aa; // rgb(156, 161, 170)
  --neutral-600: #bbbec5; // rgb(187, 190, 197)
  --neutral-700: #d1d5db; // rgb(209, 213, 219)
  --neutral-800: #e5e7eb; // rgb(229, 231, 235)
  --neutral-900: #ffffff; // rgb(255, 255, 255)

  // Light Neutral Palette (for dark theme usage)
  --neutral-light-50: #ffffff; // rgb(255, 255, 255)
  --neutral-light-100: #f9fafb; // rgb(249, 250, 251)
  --neutral-light-200: #f3f4f6; // rgb(243, 244, 246)
  --neutral-light-300: #e5e7eb; // rgb(229, 231, 235)
  --neutral-light-400: #d1d5db; // rgb(209, 213, 219)
  --neutral-light-500: #9ca1aa; // rgb(156, 161, 170)
  --neutral-light-600: #6b7280; // rgb(107, 114, 128)
  --neutral-light-700: #4b5563; // rgb(75, 85, 99)
  --neutral-light-800: #374151; // rgb(55, 65, 81)
  --neutral-light-900: #1f2937; // rgb(31, 41, 55)

  /* --- MAPPED SEMANTIC COLORS FROM EXISTING STRUCTURE --- */

  /* --- Light Theme Color Overrides --- */

  /* PRIMARY (Pulse Blue) */
  --color-brand-primary: var(--pulse-blue-500);
  --color-brand-primary-hover: var(--pulse-blue-700);
  --color-brand-primary-active: var(--pulse-blue-800);
  --color-surface-interactive-primary: var(--pulse-blue-500);
  --color-surface-interactive-primary-hover: var(--pulse-blue-700);
  --color-surface-interactive-primary-active: var(--pulse-blue-800);
  --color-border-primary: var(--pulse-blue-500);
  --color-border-primary-hover: var(--pulse-blue-700);
  --color-border-primary-active: var(--pulse-blue-800);
  --color-text-primary: var(--neutral-100);
  --color-text-on-primary: var(--neutral-light-100);
  --color-text-inactive-tab-button: var(--neutral-800);
  --color-text-active-stepper-circle: var(--neutral-800);
  --color-text-accordion-content: var(--neutral-800);
  --color-textbox-input: var(--neutral-700);
  --textbox-surface-primary: var(--neutral-50);

  /* SECONDARY (Vivid Aqua) */
  --color-brand-secondary: var(--vivid-aqua-500);
  --color-brand-secondary-hover: var(--vivid-aqua-600);
  --color-brand-secondary-active: var(--vivid-aqua-700);
  --color-surface-interactive-secondary: var(--vivid-aqua-100);
  --color-surface-interactive-secondary-hover: var(--vivid-aqua-500);
  --color-surface-interactive-secondary-active: var(--vivid-aqua-600);
  --color-border-secondary: var(--vivid-aqua-500);
  --color-border-secondary-hover: var(--vivid-aqua-600);
  --color-border-secondary-active: var(--vivid-aqua-700);
  --color-text-secondary: var(--neutral-600);
  --color-text-on-secondary: var(--neutral-50);
  --color-background-secondary: var(--vivid-aqua-100);

  /* BUTTONS, TABS, TAGS */
  --color-text-placeholder: var(--neutral-400);
  --color-text-disabled: var(--neutral-400);
  --color-text-on-brand: var(--neutral-50);
  --color-text-interactive: var(--pulse-blue-500);
  --color-text-interactive-hover: var(--pulse-blue-700);
  --color-text-success: var(--success-500);
  --color-text-error: var(--error-500);
  --color-background-primary: var(--neutral-50);
  --color-background-disabled: var(--neutral-200);
  --global-background: #1e1e1e;
  --color-surface-interactive-default: var(--pulse-blue-500);
  --color-surface-interactive-hover: var(--pulse-blue-700);
  --color-surface-interactive-active: var(--pulse-blue-700);
  --color-surface-disabled: var(--neutral-200);
  --color-surface-subtle-hover: var(--pulse-blue-50);
  --color-border-default: var(--neutral-200);
  --color-border-subtle: var(--neutral-200);
  --color-border-interactive: var(--pulse-blue-500);
  --color-border-focus: var(--pulse-blue-500);
  --color-border-error: var(--error-500);
  --color-background-error: var(--error-500);

  /* Semantic Border Colors */
  --color-border-warning: var(--warning-500);
  --color-border-success: var(--success-500);
  --color-border-info: var(--info-500);

  /* Semantic Text Colors */
  --color-text-warning: var(--warning-600);
  --color-text-success: var(--success-600);
  --color-text-error: var(--error-600);
  --color-text-info: var(--info-600);

  /* Semantic Background Colors */
  --color-background-warning: var(--warning-500);
  --color-background-success: var(--success-500);
  --color-background-info: var(--info-500);

  /* --- Light Theme Glassmorphism --- */
  --glass-backdrop-blur: 12px;
  --glass-background-color: rgba(255, 255, 255, 0.6);
  --glass-border-color: rgba(255, 255, 255, 0.3);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-02);

  /*---- Time picker ----*/
  --color-icon-border: var(--neutral-800);

  /*---------header icon color--------*/
  --color-icon: var(--neutral-800);

  /* =======================
     LIGHT THEME: RGB OVERRIDES
     ======================= */
  --rgb-brand-primary: 0, 106, 204; /* pulse-blue-500 */
  --rgb-brand-secondary: 3, 189, 212; /* vivid-aqua-500 */
  --rgb-brand-tertiary: 67, 190, 144; /* nova-green-500 */
  --rgb-brand-quaternary: 5, 150, 105; /* success-500 */
  --rgb-brand-quinary: 220, 38, 38; /* error-500 */
  --rgb-brand-senary: 167, 139, 10; /* warning-500 */

  --rgb-violet: 124, 58, 237; /* keep from original */
  --rgb-royal-blue: 0, 106, 204; /* alias to primary */
  --rgb-cyan: 3, 189, 212; /* alias to secondary */
  --rgb-spearmint: 67, 190, 144; /* alias to tertiary */
  --rgb-rose: 220, 38, 38; /* alias to error */

  --rgb-white: 255, 255, 255; /* neutral-50 */
  --rgb-black: 0, 0, 0;
  --rgb-neutral-100: 253, 211, 224; /* neutral-100 */

  /* =======================
     LIGHT THEME: EFFECT COLOR OVERRIDES
     ======================= */
  --effect-color-primary: var(--rgb-brand-primary);
  --effect-color-secondary: var(--rgb-brand-secondary);
  --effect-color-accent: var(--rgb-brand-tertiary);
  --effect-color-neutral: var(--rgb-black);
  --effect-color-surface: var(--rgb-white);
  --color-login-background: rgba(30, 30, 30, 0.4);

  /* =======================
     GLASS METAPHOR
     ======================= */
  --surface-glass-bg: rgba(var(--effect-color-surface), 0.15);
  --surface-glass-border: rgba(var(--effect-color-surface), 0.7);
  --surface-glass-shadow: rgba(var(--effect-color-surface), 0.18);

  /* Light Metaphor */
  --color-light-glow: rgba(var(--effect-color-primary), 0.45);
  --color-light-glow-focus: rgba(var(--effect-color-primary), 0.65);
  --color-light-shadow: rgba(var(--effect-color-accent), 0.1);
  --color-light-border: var(--color-brand-primary);
  --color-light-border-focus: var(--color-brand-secondary);

  /* Liquid Metaphor */
  --color-liquid-shimmer-start: var(--color-brand-secondary);
  --color-liquid-shimmer-end: var(--color-brand-primary);

  /* Glass surface color */
  --glass-surface-color: var(--rgb-white);

  /* Glass variants */
  --glass-variant-primary: var(--rgb-brand-primary);
  --glass-variant-success: var(--rgb-brand-quaternary);
  --glass-variant-warning: var(--rgb-brand-senary);
  --glass-variant-danger: var(--rgb-brand-quinary);
  --glass-variant-info: var(--rgb-brand-secondary);
  --glass-variant-purple: var(--rgb-violet);
  --glass-variant-emerald: var(--rgb-brand-tertiary);

  /* Background Neutral */
  --neutral-bg: var(--neutral-100);
}

[data-theme='dark'] {
  // Pulse Blue Palette (adjusted for dark theme)
  --pulse-blue-50: #002659;
  --pulse-blue-100: #003b73;
  --pulse-blue-200: #004f99;
  --pulse-blue-300: #0063bf;
  --pulse-blue-400: #006acc;
  --pulse-blue-500: #0077e6;
  --pulse-blue-600: #0084ff;
  --pulse-blue-700: #b0d9ff;
  --pulse-blue-800: #d9edff;
  --pulse-blue-900: #e6f3ff;

  // Vivid Aqua Palette (adjusted for dark theme)
  --vivid-aqua-50: #014f59;
  --vivid-aqua-100: #026875;
  --vivid-aqua-200: #028697;
  --vivid-aqua-300: #03acc1;
  --vivid-aqua-400: #03bdd4;
  --vivid-aqua-500: #35cadd;
  --vivid-aqua-600: #56d3e2;
  --vivid-aqua-700: #a9e1cc;
  --vivid-aqua-800: #8be1eb;
  --vivid-aqua-900: #e6f8fb;

  // Nova Green Palette (adjusted for dark theme)
  --nova-green-50: #1c4f3c;
  --nova-green-100: #25684f;
  --nova-green-200: #308666;
  --nova-green-300: #3dac83;
  --nova-green-400: #43be90;
  --nova-green-500: #69caa8;
  --nova-green-600: #81d3b5;
  --nova-green-700: #a9e1cc;
  --nova-green-800: #c5ebdd;
  --nova-green-900: #ecf8f4;

  // Success Green Palette (adjusted for dark theme)
  --success-50: #023f2c;
  --success-100: #035533;
  --success-200: #046b4b;
  --success-300: #058960;
  --success-400: #059669;
  --success-500: #37ab87;
  --success-600: #58b99b;
  --success-700: #8ccfba;
  --success-800: #b2ded1;
  --success-900: #e6f5fd;

  // Error Red Palette (adjusted for dark theme)
  --error-50: #5c1010;
  --error-100: #791515;
  --error-200: #9c1b1b;
  --error-300: #cb2323;
  --error-400: #dc2626;
  --error-500: #e35151;
  --error-600: #e866e;
  --error-700: #f4bcbc;
  --error-800: #f4bcbc;
  --error-900: #fce9e9;

  // Warning Orange Palette (adjusted for dark theme)
  --warning-50: #463804;
  --warning-100: #5c4a05;
  --warning-200: #786007;
  --warning-300: #987c09;
  --warning-400: #a78b0a;
  --warning-500: #b8a239;
  --warning-600: #c4b058;
  --warning-700: #d6c58c;
  --warning-800: #e3d5b2;
  --warning-900: #f6f1e6;

  // Info Blue Palette (adjusted for dark theme)
  --info-50: #023d4a;
  --info-100: #035061;
  --info-200: #04677e;
  --info-300: #0584a2;
  --info-400: #0591b2;
  --info-500: #37a7c1;
  --info-600: #58b5cd;
  --info-700: #8ccbdc;
  --info-800: #b2dde7;
  --info-900: #e6f4f7;

  // Dark Neutral Palette (proper dark theme colors)
  --neutral-50: #2d3036; // rgb(45, 48, 54)
  --neutral-100: #d1d3d8; // rgb(59, 63, 70)
  --neutral-200: #4c515b; // rgb(76, 81, 91)
  --neutral-300: #616874; // rgb(97, 104, 116)
  --neutral-400: #6b7280; // rgb(107, 114, 128)
  --neutral-500: #9ca1aa; // rgb(156, 161, 170)
  --neutral-600: #bbbec5; // rgb(187, 190, 197)
  --neutral-700: #d1d5db; // rgb(209, 213, 219)
  --neutral-800: #e5e7eb; // rgb(229, 231, 235)
  --neutral-900: #ffffff; // rgb(255, 255, 255)

  // Light Neutral Palette (for dark theme usage)
  --neutral-light-50: #ffffff; // rgb(255, 255, 255)
  --neutral-light-100: #f9fafb; // rgb(249, 250, 251)
  --neutral-light-200: #f3f4f6; // rgb(243, 244, 246)
  --neutral-light-300: #e5e7eb; // rgb(229, 231, 235)
  --neutral-light-400: #d1d5db; // rgb(209, 213, 219)
  --neutral-light-500: #9ca1aa; // rgb(156, 161, 170)
  --neutral-light-600: #6b7280; // rgb(107, 114, 128)
  --neutral-light-700: #4b5563; // rgb(75, 85, 99)
  --neutral-light-800: #374151; // rgb(55, 65, 81)
  --neutral-light-900: #1f2937; // rgb(31, 41, 55)

  /* --- MAPPED SEMANTIC COLORS FROM EXISTING STRUCTURE --- */

  /* --- Light Theme Color Overrides --- */

  /* PRIMARY (Pulse Blue) */
  --color-brand-primary: var(--pulse-blue-500);
  --color-brand-primary-hover: var(--pulse-blue-700);
  --color-brand-primary-active: var(--pulse-blue-800);
  --color-surface-interactive-primary: var(--pulse-blue-500);
  --color-surface-interactive-primary-hover: var(--pulse-blue-700);
  --color-surface-interactive-primary-active: var(--pulse-blue-800);
  --color-border-primary: var(--pulse-blue-500);
  --color-border-primary-hover: var(--pulse-blue-700);
  --color-border-primary-active: var(--pulse-blue-800);
  --color-text-primary: var(--neutral-100);
  --color-text-on-primary: var(--neutral-light-100);
  --color-text-inactive-tab-button: var(--neutral-800);
  --color-text-active-stepper-circle: var(--neutral-800);
  --color-text-accordion-content: var(--neutral-800);
  --color-textbox-input: var(--neutral-700);
  --textbox-surface-primary: var(--neutral-50);
  --global-background: #1e1e1e; /* SECONDARY (Vivid Aqua) */
  --color-brand-secondary: var(--vivid-aqua-500);
  --color-brand-secondary-hover: var(--vivid-aqua-600);
  --color-brand-secondary-active: var(--vivid-aqua-700);
  --color-surface-interactive-secondary: var(--vivid-aqua-100);
  --color-surface-interactive-secondary-hover: var(--vivid-aqua-500);
  --color-surface-interactive-secondary-active: var(--vivid-aqua-600);
  --color-border-secondary: var(--vivid-aqua-500);
  --color-border-secondary-hover: var(--vivid-aqua-600);
  --color-border-secondary-active: var(--vivid-aqua-700);
  --color-text-secondary: var(--neutral-600);
  --color-text-on-secondary: var(--neutral-50);
  --color-background-secondary: var(--vivid-aqua-100);

  /* BUTTONS, TABS, TAGS */
  --color-text-placeholder: var(--neutral-400);
  --color-text-disabled: var(--neutral-400);
  --color-text-on-brand: var(--neutral-50);
  --color-text-interactive: var(--pulse-blue-500);
  --color-text-interactive-hover: var(--pulse-blue-700);
  --color-text-success: var(--success-500);
  --color-text-error: var(--error-500);
  --color-background-primary: var(--neutral-50);
  --color-background-disabled: var(--neutral-200);

  --color-surface-interactive-default: var(--pulse-blue-500);
  --color-surface-interactive-hover: var(--pulse-blue-700);
  --color-surface-interactive-active: var(--pulse-blue-700);
  --color-surface-disabled: var(--neutral-200);
  --color-surface-subtle-hover: var(--pulse-blue-50);
  --color-border-default: var(--neutral-200);
  --color-border-subtle: var(--neutral-200);
  --color-border-interactive: var(--pulse-blue-500);
  --color-border-focus: var(--pulse-blue-500);
  --color-border-error: var(--error-500);
  --color-background-error: var(--error-500);

  /* Semantic Border Colors */
  --color-border-warning: var(--warning-500);
  --color-border-success: var(--success-500);
  --color-border-info: var(--info-500);

  /* Semantic Text Colors */
  --color-text-warning: var(--warning-600);
  --color-text-success: var(--success-600);
  --color-text-error: var(--error-600);
  --color-text-info: var(--info-600);

  /* Semantic Background Colors */
  --color-background-warning: var(--warning-500);
  --color-background-success: var(--success-500);
  --color-background-info: var(--info-500);

  /* --- Light Theme Glassmorphism --- */
  --glass-backdrop-blur: 12px;
  --glass-background-color: rgba(255, 255, 255, 0.6);
  --glass-border-color: rgba(255, 255, 255, 0.3);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-02);

  /*---- Time picker ----*/
  --color-icon-border: var(--neutral-800);

  /*---------header icon color--------*/
  --color-icon: var(--neutral-800);

  /* =======================
     LIGHT THEME: RGB OVERRIDES
     ======================= */
  --rgb-brand-primary: 0, 106, 204; /* pulse-blue-500 */
  --rgb-brand-secondary: 3, 189, 212; /* vivid-aqua-500 */
  --rgb-brand-tertiary: 67, 190, 144; /* nova-green-500 */
  --rgb-brand-quaternary: 5, 150, 105; /* success-500 */
  --rgb-brand-quinary: 220, 38, 38; /* error-500 */
  --rgb-brand-senary: 167, 139, 10; /* warning-500 */

  --rgb-violet: 124, 58, 237; /* keep from original */
  --rgb-royal-blue: 0, 106, 204; /* alias to primary */
  --rgb-cyan: 3, 189, 212; /* alias to secondary */
  --rgb-spearmint: 67, 190, 144; /* alias to tertiary */
  --rgb-rose: 220, 38, 38; /* alias to error */

  --rgb-white: 255, 255, 255; /* neutral-50 */
  --rgb-black: 0, 0, 0;
  --rgb-neutral-100: 253, 211, 224; /* neutral-100 */

  /* =======================
     LIGHT THEME: EFFECT COLOR OVERRIDES
     ======================= */
  --effect-color-primary: var(--rgb-brand-primary);
  --effect-color-secondary: var(--rgb-brand-secondary);
  --effect-color-accent: var(--rgb-brand-tertiary);
  --effect-color-neutral: var(--rgb-black);
  --effect-color-surface: var(--rgb-white);
  --color-login-background: rgba(30, 30, 30, 0.4);

  /* =======================
     GLASS METAPHOR
     ======================= */
  --surface-glass-bg: rgba(var(--effect-color-surface), 0.15);
  --surface-glass-border: rgba(var(--effect-color-surface), 0.7);
  --surface-glass-shadow: rgba(var(--effect-color-surface), 0.18);

  /* Light Metaphor */
  --color-light-glow: rgba(var(--effect-color-primary), 0.45);
  --color-light-glow-focus: rgba(var(--effect-color-primary), 0.65);
  --color-light-shadow: rgba(var(--effect-color-accent), 0.1);
  --color-light-border: var(--color-brand-primary);
  --color-light-border-focus: var(--color-brand-secondary);

  /* Liquid Metaphor */
  --color-liquid-shimmer-start: var(--color-brand-secondary);
  --color-liquid-shimmer-end: var(--color-brand-primary);

  /* Glass surface color */
  --glass-surface-color: var(--rgb-white);

  /* Glass variants */
  --glass-variant-primary: var(--rgb-brand-primary);
  --glass-variant-success: var(--rgb-brand-quaternary);
  --glass-variant-warning: var(--rgb-brand-senary);
  --glass-variant-danger: var(--rgb-brand-quinary);
  --glass-variant-info: var(--rgb-brand-secondary);
  --glass-variant-purple: var(--rgb-violet);
  --glass-variant-emerald: var(--rgb-brand-tertiary);

  /* Background Neutral */
  --neutral-bg: var(--neutral-100);
}
