export const environment = {
  production: true,
  baseUrl: '${BASE_URL}',
  apiVersion: '${API_VERSION}',
  consoleApi: '${CONSOLE_API}',
  consoleApiV2: '${CONSOLE_API_V2}',
  consoleApiAuthUrl: '${CONSOLE_API_AUTH_URL}',
  consoleEmbeddingApi: '${CONSOLE_EMBEDDING_API}',
  consoleInstructionApi: '${CONSOLE_INSTRUCTION_API}',
  consoleLangfuseUrl: '${CONSOLE_LANGFUSE_URL}',
  consoleTruelensUrl: '${CONSOLE_TRUELENS_URL}',
  consolePipelineApi: '${CONSOLE_PIPELINE_API}',
  experienceApiUrl: '${EXPERIENCE_API_URL}',
  productApiUrl: '${PRODUCT_API_URL}',
  experienceBaseUrl: '${EXPERIENCE_BASE_URL}',
  pipelineApiBaseUrl: '${PIPELINE_API_BASE_URL}',
  enableLogStreaming: '${ENABLE_LOG_STREAMING}',
  logStreamingApiUrl: '${LOGSTREAMING_API_URL}',
  appVersion: '${APP_VERSION}',
  workflowExecutionMode: '${WORKFLOW_EXECUTION_MODE}',
  useBasicLogin: '${USE_BASIC_LOGIN}',
  accessKey: '${ACCESS_KEY}'
};
