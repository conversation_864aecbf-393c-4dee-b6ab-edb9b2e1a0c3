(function (window) {
  window.env = window.env || {};

  // Base URL from environment
  const baseUrl = '${BASE_URL}';

  // Common environment variables
  window['env']['accessKey'] = '${ACCESS_KEY}';
  window['env']['baseUrl'] = baseUrl;
  window['env']['apiVersion'] = '${API_VERSION}';
  window['env']['appVersion'] = '${APP_VERSION}';
  window['env']['workflowExecutionMode'] = '${WORKFLOW_EXECUTION_MODE}';
  window['env']['useBasicLogin'] = '${USE_BASIC_LOGIN}';
  window.env.LOADED_AT = new Date().toISOString();

  // Console-specific environment variables
  window['env']['consoleApi'] = baseUrl + '${CONSOLE_API}';
  window['env']['consoleApiV2'] = baseUrl + '${CONSOLE_API_V2}';
  window['env']['consoleApiAuthUrl'] = baseUrl + '${CONSOLE_API_AUTH_URL}';
  window['env']['consoleEmbeddingApi'] = baseUrl + '${CONSOLE_EMBEDDING_API}';
  window['env']['consoleInstructionApi'] =
    baseUrl + '${CONSOLE_INSTRUCTION_API}';
  window['env']['consoleLangfuseUrl'] = '${CONSOLE_LANGFUSE_URL}';
  window['env']['consoleTruelensUrl'] = '${TRUELENS_URL}';
  window['env']['consolePipelineApi'] = baseUrl + '${CONSOLE_PIPELINE_API}';
  window['env']['consoleRedirectUrl'] = baseUrl + '${CONSOLE_REDIRECT_URL}';
  window['env']['consoleUrl'] = baseUrl + '${CONSOLE_URL}';
  window['env']['consoleRedirectUri'] = baseUrl + '${CONSOLE_REDIRECT_URL}';
  window['env']['enableLogStreaming'] = '${ENABLE_LOG_STREAMING}';
  window['env']['logStreamingApiUrl'] = '${LOGSTREAMING_API_URL}';

  // Experience-specific environment variables
  window['env']['experienceStudioUrl'] = baseUrl + '${EXPERIENCE_STUDIO_URL}';
  window['env']['experienceApiUrl'] = baseUrl + '${EXPERIENCE_API_URL}';
  window['env']['experienceBaseUrl'] = '${EXPERIENCE_BASE_URL}';
  // Product-specific environment variables
  window['env']['productStudioUrl'] = baseUrl + '${PRODUCT_STUDIO_URL}';
  window['env']['productApiUrl'] = baseUrl + '${PRODUCT_API_URL}';
  window['env']['pipelineApiBaseUrl'] =
    baseUrl + '${PRODUCT_PIPELINE_API_BASE_URL}';

  // Elder Wand-specific environment variables
  window['env']['elderWandUrl'] = baseUrl + '${ELDER_WAND_URL}';

  console.log('🌍 Environment configuration loaded:', window.env);
})(this);
