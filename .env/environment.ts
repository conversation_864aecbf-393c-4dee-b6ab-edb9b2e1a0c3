export const environment = {
  production: false,
  baseUrl: 'http://localhost:4202',
  apiVersion: 'v1',
  consoleApi: 'http://localhost:3000/api',
  consoleApiV2: 'http://localhost:3000/api/v2',
  consoleApiAuthUrl: 'http://localhost:3000/auth',
  consoleEmbeddingApi: 'http://localhost:3000/embedding',
  consoleInstructionApi: 'http://localhost:3000/instruction',
  consoleLangfuseUrl: 'http://localhost:3000/langfuse',
  consoleTruelensUrl: 'http://localhost:3000/truelens',
  consolePipelineApi: 'http://localhost:3000/pipeline',
  experienceApiUrl: 'http://localhost:3001/api',
  productApiUrl: 'http://localhost:3002/api',
  experienceBaseUrl: 'http://localhost:4203',
  pipelineApiBaseUrl: 'http://localhost:3000/pipeline',
  enableLogStreaming: 'false',
  logStreamingApiUrl: 'http://localhost:3000/logs',
  appVersion: '1.0.0',
  workflowExecutionMode: 'local',
  useBasicLogin: 'true',
  accessKey: 'dev-access-key'
};
